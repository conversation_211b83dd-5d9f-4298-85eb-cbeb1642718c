import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/utils/dbhelper.dart';
import 'package:mobile_crm/data/providers/db/database.dart';
import 'package:mobile_crm/main.dart' as app;

void main() {
  group('prevent-create-database-multiple-times-test', () {
    testWidgets('single-database-insert', (tester) async {
      // Setup
      Get.testMode = true;
      await app.init();
      await tester.pumpWidget(const app.MyApp());
      await tester.pump();
      // await tester.pumpAndSettle();
      final db = DatabaseHelper.instance;
      int result = await db.database.inboxDao
          .insertInbox(InboxData(notification_id: 1).toCompanion(true));
      await db.database.inboxDao.deleteAll();
      expect(1, result);
    });

    testWidgets('multiple-database-insert', (tester) async {
      // Setup
      Get.testMode = true;
      await app.init();
      await app.init();
      await tester.pumpWidget(const app.MyApp());
      await tester.pump();
      // await tester.pumpAndSettle();
      final db = DatabaseHelper.instance;
      int result = await db.database.inboxDao
          .insertInbox(InboxData(notification_id: 1).toCompanion(true));
      await db.database.inboxDao.deleteAll();
      expect(1, result);
    });
  });
}
