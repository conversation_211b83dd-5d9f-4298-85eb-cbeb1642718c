import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:integration_test/integration_test.dart';
import 'package:mobile_crm/app/modules/login/login_export.dart';
import 'package:mobile_crm/main.dart' as app;

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  Get.testMode = true;
  testWidgets('process', (tester) async {
    // Setup
    final Finder homeLoginKey = find.byKey(const Key('loginKey'));
    final Finder newRegisterButtonKey =
        find.byKey(const Key('newRegisterButtonKey'));

    // Form Key Field
    final Finder inputNameKey = find.byKey(const Key('inputNameKey'));
    final Finder inputEmailKey = find.byKey(const Key('inputEmailKey'));
    final Finder inputPhoneNumberKey =
        find.byKey(const Key('inputPhoneNumberKey'));
    final Finder inputDateOfBirthKey =
        find.byKey(const Key('inputDateOfBirthKey'));
    final Finder inputGenderKey = find.byKey(const Key('inputGenderKey'));
    final Finder submitRegisterButtonKey =
        find.byKey(const Key('submitRegisterButtonKey'));

    late RegisterController registerController;

    const name = 'tester';
    const email = '<EMAIL>';
    const phoneNumber = '081234567778';

    await app.init();
    await tester.pumpWidget(const app.MyApp());
    await tester.pumpAndSettle();

    await tester.tap(homeLoginKey);
    await tester.pumpAndSettle();

    await Future.delayed(const Duration(seconds: 1));
    await tester.tap(newRegisterButtonKey);
    await tester.pumpAndSettle();

    // Fill Field
    await tester.enterText(inputNameKey, name);
    await Future.delayed(const Duration(seconds: 1));
    await tester.enterText(inputEmailKey, email);
    await Future.delayed(const Duration(seconds: 1));
    await tester.enterText(inputPhoneNumberKey, phoneNumber);
    await Future.delayed(const Duration(seconds: 1));
    await tester.tap(inputDateOfBirthKey);
    await tester.pumpAndSettle();
    await Future.delayed(const Duration(seconds: 1));
    await tester.tap(find.text('OK'));
    await tester.pumpAndSettle();
    await Future.delayed(const Duration(seconds: 1));
    await tester.tap(inputGenderKey);
    await tester.pumpAndSettle();
    await Future.delayed(const Duration(seconds: 1));
    await tester.tap(find.widgetWithText(IndexedSemantics, 'Laki-laki'));
    await tester.pumpAndSettle();
    await tester.pumpAndSettle();
    // await tester.enterText(inputDateOfBirthKey, dateBirth);
    // await Future.delayed(const Duration(seconds: 1));
    // await tester.enterText(inputGenderKey, gender);

    await Future.delayed(const Duration(seconds: 1));
    await tester.pumpAndSettle();
    await tester.tap(submitRegisterButtonKey);
    await tester.pumpAndSettle();

    registerController = Get.find<RegisterController>();

    await Future.delayed(const Duration(seconds: 15));

    expect(name, registerController.textNameController.value.text);
  });
}
