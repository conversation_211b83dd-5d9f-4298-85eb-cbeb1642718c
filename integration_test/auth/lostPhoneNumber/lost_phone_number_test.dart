import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:integration_test/integration_test.dart';
import 'package:mobile_crm/app/modules/login/login_export.dart';
import 'package:mobile_crm/main.dart' as app;

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  Get.testMode = true;
  testWidgets('process', (tester) async {
    // Setup
    final Finder homeLoginKey = find.byKey(const Key('loginKey'));
    final Finder lostPhoneNumberButtonKey =
        find.byKey(const Key('lostPhoneNumberButtonKey'));

    // Form Key Field
    final Finder inputEmailKey = find.byKey(const Key('inputEmailKey'));
    final Finder inputPhoneNumberKey =
        find.byKey(const Key('inputPhoneNumberKey'));

    final Finder submitLostPhoneNumberButtonKey =
        find.byKey(const Key('submitLostPhoneNumberButtonKey'));

    late LostPhoneNumberController controller;

    const email = '<EMAIL>';
    const phoneNumber = '081234567778';

    await app.init();
    await tester.pumpWidget(const app.MyApp());
    await tester.pumpAndSettle();

    await tester.tap(homeLoginKey);
    await tester.pumpAndSettle();

    await Future.delayed(const Duration(seconds: 1));
    await tester.tap(lostPhoneNumberButtonKey);
    await tester.pumpAndSettle();

    // Fill Field
    await tester.enterText(inputEmailKey, email);
    await Future.delayed(const Duration(seconds: 1));
    await tester.enterText(inputPhoneNumberKey, phoneNumber);
    await Future.delayed(const Duration(seconds: 1));
    await tester.pumpAndSettle();
    // await tester.enterText(inputDateOfBirthKey, dateBirth);
    // await Future.delayed(const Duration(seconds: 1));
    // await tester.enterText(inputGenderKey, gender);

    await Future.delayed(const Duration(seconds: 1));
    await tester.tap(submitLostPhoneNumberButtonKey);
    await tester.pumpAndSettle();
    controller = Get.find<LostPhoneNumberController>();

    await Future.delayed(const Duration(seconds: 10));

    expect(true, controller.testingResultResponse?.status);
  });
}
