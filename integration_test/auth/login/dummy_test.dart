import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:integration_test/integration_test.dart';
import 'package:mobile_crm/app/modules/login/controller/login_controller.dart';
import 'package:mobile_crm/app/modules/login/login_export.dart';
import 'package:mobile_crm/main.dart' as app;

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  Get.testMode = true;
  testWidgets('process', (tester) async {
    // Setup
    await app.init();
    await tester.pumpWidget(const app.MyApp());
    await tester.pumpAndSettle();
    final Finder homeLoginKey = find.byKey(const Key('loginKey'));
    final Finder loginButtonKey = find.byKey(const Key('loginButtonKey'));
    final Finder loginPhoneNumberFieldKey =
        find.byKey(const Key('loginPhoneNumberFieldKey'));
    final Finder loginPasswordFieldKey =
        find.byKey(const Key('loginPasswordFieldKey'));
    final Finder loginDummyButtonKey =
        find.byKey(const Key('loginDummyButtonKey'));
    late LoginController controller;

    // do
    // go to login page
    await tester.tap(homeLoginKey);
    await tester.pumpAndSettle();
    // ignoreException();
    // test
    expect(loginPhoneNumberFieldKey, findsWidgets);

    // Setup
    controller = Get.find<LoginController>();

    // do
    await tester.enterText(
        loginPhoneNumberFieldKey, controller.demoAccountPhone);
    await tester.pump();
    await tester.tap(loginButtonKey);
    await tester.pumpAndSettle();

    // test
    expect(loginPasswordFieldKey, findsOneWidget);

    // do
    await tester.enterText(
        loginPasswordFieldKey, controller.demoAccountPassword);
    await tester.pump();
    await tester.tap(loginDummyButtonKey);
    await tester.pumpAndSettle();

    // test
    expect(true, controller.testingResultResponse?.status);
  });
}
