import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:integration_test/integration_test.dart';
import 'package:mobile_crm/app/modules/login/controller/login_controller.dart';
import 'package:mobile_crm/app/modules/login/login_export.dart';
import 'package:mobile_crm/main.dart' as app;

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  Get.testMode = true;
  testWidgets('process', (tester) async {
    // Setup
    final Finder homeLoginKey = find.byKey(const Key('loginKey'));
    final Finder loginPhoneNumberFieldKey =
        find.byKey(const Key('loginPhoneNumberFieldKey'));
    final Finder sendAuthLinkButtonKey =
        find.byKey(const Key('sendAuthLinkButtonKey'));
    final Finder loginButtonKey = find.byKey(const Key('loginButtonKey'));
    late LoginController controller;

    const adminAccountNumber = '***********';

    await app.init();
    await tester.pumpWidget(const app.MyApp());
    await tester.pumpAndSettle();

    await tester.tap(homeLoginKey);
    await tester.pumpAndSettle();

    await tester.enterText(loginPhoneNumberFieldKey, adminAccountNumber);
    await tester.pump();
    await tester.tap(loginButtonKey);
    await tester.pumpAndSettle();

    await tester.tap(sendAuthLinkButtonKey);
    await tester.pumpAndSettle();

    await Future.delayed(const Duration(seconds: 15));
    controller = Get.find<LoginController>();

    expect(true, controller.testingResultResponse?.status);
  });
}
