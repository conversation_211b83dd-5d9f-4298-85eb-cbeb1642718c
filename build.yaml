targets:
  $default:
    builders:
      envied_generator:envied:
        enabled: true
        generate_for:
          - lib/core/env/**.dart
      build_web_compilers:entrypoint:
        generate_for:
          - web/**.dart
        options:
          compiler: dart2js
        dev_options:
          dart2js_args:
            - --no-minify
        release_options:
          dart2js_args:
            - -O4
      drift_dev:
        # These options change how drift generates code
        generate_for:
          - lib/data/providers/db/**.dart
        options:
          # This allows us to share a drift database across isolates (or different tabs on the web)
          #          generate_connect_constructor: true
          # These options are generally recommended: https://drift.simonbinder.eu/docs/advanced-features/builder_options/#recommended-options
          apply_converters_on_variables: true
          generate_values_in_copy_with: true
          scoped_dart_components: true
          mutable_classes: true
      json_serializable:
        enabled: true
        options:
          include_if_null: false
        generate_for:
          - lib/data/models/**.dart