# Mobile CRM

mobile app (IOS & Android) for CRM

## Installation.  
Some files need to be generated before installing:   

```
flutter pub run build_runner build --delete-conflicting-outputs
```


### Create keystore configuration.

Create file **android/key.properties**,     
copy the value from *android/key.properties.example*

Generate keystore file

```
keytool -genkey -v -keystore android/app/debug.keystore -storepass android -alias android -keypass android -keyalg RSA -keysize 2048 -validity 10000 
```    

### Build Flavor

need additional arguments to 'flutter run'

```
flutter run --flavor {flavor-name}
```

| flavor        | Description |
| :-----        | :---------- |
| `dev`         | -           |
| `prod`        | -           |
| `yamiepanda`  | -           |
| `alive`       | -           |


### Firebase Hosting  
Login Firebase :    
```
firebase login --no-localhost
```


Preview release:
```
firebase hosting:channel:deploy CHANNEL_ID
```    
Replace CHANNEL_ID with a string with no spaces (for example, feature_mission-2-mars). This ID will be used to construct the preview URL associated with the preview channel.    


**Server Static Files Locally**    
 - Using npm :     
	```
	npm install -g serve
	serve -s build/web
	``` 
- Python
	```
	python3 -m http.server --directory build/web 8000
	```
- dhttpd package
	```
	flutter pub global activate dhttpd
	cd build/web
	dhttpd '--headers=Cross-Origin-Embedder-Policy=credentialless;Cross-Origin-Opener-Policy=same-origin'
	```


### Configure Firebase (optional)

Install Flutter CLI

```
dart pub global activate flutterfire_cli
```

Run the configuration script:

```
flutterfire configure 
```

available options:

```
flutterfire configure --project=uniq-crm --token=xxxx
``` 

### FIX ERROR (IOS)

#### CocoaPods

if encounter such error: *"CocoaPods's specs repository is too out-of-date to satisfy...."* .    
Run:

```
flutter clean && rm -rf ios/Pods && rm ios/Podfile.lock && flutter pub get && cd ios && pod install
```

#### Dynamic Links

default link https://uniqcrm.page.link

On the Signing & Capabilities page, change or add Associated Domains list:

```
applinks:example.page.link
```

If you using custom shortlink to open.  
make sure you add the url & app identifier into **URL types** in your info.plist.  
example:  
custom shortlink : https//y.uniq.id  
identifier : id.uniq.example

```
<key>CFBundleURLTypes</key>
	<array>
		...
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>id.uniq.example</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>y.uniq.id</string>
			</array>
		</dict>
        ...
	</array>
```

#### Dynamic Links and Sign In With Apple

Depending on this features, iOS apps may require adding entitlements to a .entitlements file.  
make sure Runner.entitlements is in Xcode project file

Example Runner.entitlements :

```
<dict>
	...
	<key>com.apple.developer.applesignin</key>
	<array>
		<string>Default</string>
	</array>
	<key>com.apple.developer.associated-domains</key>
	<array>
		<string>applinks:example.page.link</string>
	</array>
    ...
</dict>
```

#### Use Specific Version of Flutter      
locate where flutter installed:    
```
which flutter
```

go to that directory, then checkout to the version you want ([release page](https://docs.flutter.dev/release/archive?tab=linux)):    
```
git checkout [version]
```

### Generating Web Asset 
To Generate Web App Asset, including manifest and icons, you can you this website: https://manifest-gen.netlify.app/