# This file contains the fastlane.tools configuration
# You can find the documentation at https://docs.fastlane.tools
#
# For a list of all available actions, check out
#
#     https://docs.fastlane.tools/actions
#
# For a list of all available plugins, check out
#
#     https://docs.fastlane.tools/plugins/available-plugins
#

# Uncomment the line if you want fastlane to automatically update itself
# update_fastlane

default_platform(:android)

platform :android do
  desc "Runs all the tests"
  lane :test do
    gradle(task: "test")
  end

  desc "Submit a new Beta Build to Crashlytics Beta"
  lane :beta do
    gradle(task: "clean assembleRelease")
    crashlytics

    # sh "your_script.sh"
    # You can also use other beta testing services here
  end

  desc "Deploy a new version to the Google Play"
  lane :deploy do
    gradle(task: "clean assembleRelease")
    upload_to_play_store
  end

  lane :distribute_app do |options|
    puts options[:variant]
    artifact_path = ''
    Dir.chdir("..") do
      files = Dir['../build/app/outputs/flutter-apk/*'+options[:variant]+'-release.apk']
      files.each do |file_path|
        artifact_path = file_path
      end
    end

    puts 'use artifact: '+artifact_path
    firebase_app_distribution(
        app: ENV['APP_ID'],
        groups: "uniq-qa-team,uniq-crm-team",
        release_notes_file: "../release_note.txt",
        firebase_cli_token: ENV['FIREBASE_TOKEN'],
        android_artifact_type: "APK",
        android_artifact_path: artifact_path
    )

    # notify_to_slack(options)
  end

  desc "uploading the app to google play"
  lane :publishGooglePlay do |options|
    puts options[:variant]
    artifact_path = ''
    Dir.chdir("..") do
      files = Dir['../build/app/outputs/bundle/*/*.aab']
      files.each do |file_path|
        artifact_path = file_path
      end
    end

    puts "aab file path: " + artifact_path
    # upload_to_gofile(artifact_path)

    upload_to_play_store(
        track: 'beta',
        aab: artifact_path,
        skip_upload_apk: true,
        skip_upload_metadata: true,
        skip_upload_images: true,
        skip_upload_screenshots: true
    )

    # upload_to_play_store(
    #   track: 'beta',
    #   track_promote_to: 'internal'
    # )

  #   upload_to_play_store(
  #     track: 'internal',
  #     aab: artifact_path,
  #     skip_upload_apk: true,
  #     skip_upload_metadata: true,
  #     skip_upload_images: true,
  #     skip_upload_screenshots: true
  #  )

  end

  lane :promote_release do
    upload_to_play_store(
      track: 'beta',
      track_promote_to: 'internal'
    )
  end 

  desc "Upload a file to Gofile.io"
  lane :upload_to_gofile do |options|
    artifact_path = ''
    Dir.chdir("..") do
      files = Dir['../build/app/outputs/bundle/*/*.aab']
      files.each do |file_path|
        artifact_path = file_path
      end
    end
    
    # Get available server
    server = sh("curl -s https://api.gofile.io/servers | jq -r '.data.servers[0].name'")
    server = server.strip

    # Upload file
    response = sh("curl -X POST -F file=@#{artifact_path} https://#{server}.gofile.io/uploadfile")
    
    # Parse response to get download link
    download_url = JSON.parse(response)["data"]["downloadPage"]
    
    UI.success("File uploaded successfully!")
    UI.message("Download URL: #{download_url}")
    
    download_url
  end

end
