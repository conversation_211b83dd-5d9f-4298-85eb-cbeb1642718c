                    -HC:\Users\<USER>\fvm\versions\3.29.3\packages\flutter_tools\gradle\src\main\groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=23
-<PERSON>ANDROID_PLATFORM=android-23
-DANDROID_ABI=x86
-DCMAKE_ANDROID_ARCH_ABI=x86
-DANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\sdk\ndk\23.1.7779620
-DCMAKE_ANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\sdk\ndk\23.1.7779620
-DCMAKE_TOOLCHAIN_FILE=C:\Users\<USER>\AppData\Local\Android\sdk\ndk\23.1.7779620\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=C:\Users\<USER>\AppData\Local\Android\sdk\cmake\3.18.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\flutter_projects\mobile-crm\build\app\intermediates\cxx\Debug\5s1t5p43\obj\x86
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\flutter_projects\mobile-crm\build\app\intermediates\cxx\Debug\5s1t5p43\obj\x86
-DCMAKE_BUILD_TYPE=Debug
-BD:\flutter_projects\mobile-crm\android\app\.cxx\Debug\5s1t5p43\x86
-GNinja
-Wno-dev
--no-warn-unused-cli
                    Build command args: []
                    Version: 2