<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.example.mobile_crm">
    <!-- Flut<PERSON> needs it to communicate with the running application
         to allow setting breakpoints, to provide hot reload, etc.
    -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />

    <!-- Provide required visibility configuration for API level 30 and above -->
    <queries>
        <!-- If your app checks for SMS support -->
        <intent>
            <action android:name="android.intent.action.VIEW" />
            <data android:scheme="sms" />
        </intent>
        <!-- If your app checks for call support -->
        <intent>
            <action android:name="android.intent.action.VIEW" />
            <data android:scheme="tel" />
        </intent>

        <!--        <intent>-->
        <!--            <action android:name="android.intent.action.VIEW" />-->
        <!--            <category android:name="android.intent.category.BROWSABLE" />-->
        <!--            <data android:scheme="https" />-->
        <!--        </intent>-->
    </queries>
</manifest>
