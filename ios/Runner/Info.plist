<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>$(APP_DISPLAY_NAME)</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>NSCameraUsageDescription</key>
    <string>Need to access your camera to capture a photo.</string>
    <key>NSPhotoLibraryUsageDescription</key>
    <string>Need to access your photo library to select a photo</string>
	<key>NSMicrophoneUsageDescription</key>
    <string>Allow user to give feedback using voice</string>
	<key>NSSpeechRecognitionUsageDescription</key>
    <string>Converting voice to text to easy give feedback</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>LSApplicationQueriesSchemes</key>
    <array>
      <string>tel</string>
      <string>whatsapp</string>
    </array>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.googleusercontent.apps.129617012485-m36m4pq62cv35p1e85c4l2uka9vn2mgc</string>
				<string>com.googleusercontent.apps.375540508523-gpd9or2ncj7v90k43da51erks9dmdsit</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.googleusercontent.apps.129617012485-s5lqd2fsrmj2rsngtgs8nd0q5efhf8sj</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.googleusercontent.apps.375540508523-gpd9or2ncj7v90k43da51erks9dmdsit</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.googleusercontent.apps.129617012485-m36m4pq62cv35p1e85c4l2uka9vn2mgc</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>id.uniq.crm</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>y.uniq.id</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>id.uniq.crm.yamiepanda</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>y.uniq.id</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>FirebaseDeepLinkPasteboardRetrievalEnabled</key>
	<false/>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>Turning on location services allows us to give you the approximate distance of the stores</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>Get Nearby Stores</string>
	<key>UIBackgroundModes</key>
	<array>
		<string>fetch</string>
		<string>remote-notification</string>
	</array>
	<key>UILaunchScreen</key>
	<dict>
		<key>UIColorName</key>
		<string>background-color</string>
		<key>UIImageName</key>
		<string>LaunchImage</string>
		<key>UIImageRespectsSafeAreaInsets</key>
		<false/>
		<key>UILaunchScreen</key>
		<dict>
			<key>UIColorName</key>
			<string>background-color</string>
			<key>UIImageName</key>
			<string>LaunchImage</string>
			<key>UIImageRespectsSafeAreaInsets</key>
			<false/>
		</dict>
	</dict>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
</dict>
</plist>
