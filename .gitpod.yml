image: uniqdev/android-fastlane:flutter-gitpod-jdk17-3.29.0

tasks:
  - name: permissions
    command: |
      sudo chown -R $(whoami) /home/<USER>/flutter/ 
      sudo chown -R $(whoami) /root/
      sudo chown -R $(whoami) /sdk/
      sudo chown -R $(whoami) /var/lib/gems/
      sudo chown -R $(whoami) /usr/local/bin/
      export PATH="$PATH:/sdk/platform-tools/"
      export PATH="$PATH:/home/<USER>/sdk/platform-tools/"
  - name: tailscaled
    command: |
      if [ -n "${TAILSCALE_STATE_MYPROJECT}" ]; then
        # restore the tailscale state from gitpod user's env vars
        echo "restore the tailscale state from gitpod user's env vars..."
        sudo mkdir -p /var/lib/tailscale
        echo "${TAILSCALE_STATE_MYPROJECT}" | sudo tee /var/lib/tailscale/tailscaled.state > /dev/null
      fi
      sudo tailscaled
  - name: tailscale
    command: |
      if [ -n "${TAILSCALE_STATE_MYPROJECT}" ]; then
        echo "tailscale from env..."
        sudo -E tailscale up
      else
        sudo -E tailscale up --hostname "gitpod-${GITPOD_GIT_USER_NAME// /-}-$(echo ${GITPOD_WORKSPACE_CONTEXT} | jq -r .repository.name)"
        # store the tailscale state into gitpod user
        gp env TAILSCALE_STATE_MYPROJECT="$(sudo cat /var/lib/tailscale/tailscaled.state)"
      fi  

vscode:
  extensions:
    - Dart-Code.dart-code
    - Dart-Code.flutter
