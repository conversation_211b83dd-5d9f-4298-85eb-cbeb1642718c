FROM uniqdev/android-fastlane:flutter-jdk11-3.0.2

USER root
#RUN curl -fsSL https://pkgs.tailscale.com/stable/ubuntu/focal.gpg | sudo apt-key add - \
#     && curl -fsSL https://pkgs.tailscale.com/stable/ubuntu/focal.list | sudo tee /etc/apt/sources.list.d/tailscale.list \
#     && apt-get update \
#     && apt-get install -y tailscale

RUN curl -fsSL https://pkgs.tailscale.com/stable/debian/bullseye.noarmor.gpg | sudo tee /usr/share/keyrings/tailscale-archive-keyring.gpg >/dev/null

RUN curl -fsSL https://pkgs.tailscale.com/stable/debian/bullseye.tailscale-keyring.list | sudo tee /etc/apt/sources.list.d/tailscale.list

RUN sudo apt-get update && sudo apt-get install -y tailscale     
RUN apt-get install -y jq

ENV PATH="${PATH}:/home/<USER>/flutter/bin:/sdk/platform-tools"
