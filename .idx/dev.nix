{pkgs}: {
  channel = "stable-23.11";
  packages = [
    pkgs.nodePackages.firebase-tools
    pkgs.jdk17
    pkgs.unzip
    pkgs.python3
  ];
  idx.extensions = [
    
  ];
  idx.previews = {
    previews = {
      web = {
        command = [
          "flutter"
          "run"
          "--machine"
          "-d"
          "web-server"
          "--web-hostname"
          "0.0.0.0"
          "--web-port"
          "$PORT"
        ];
        manager = "flutter";
      };
      android = {
        command = [
          "flutter"
          "devices"
          # "flutter"
          # "run"
          # "--machine"
          # "-d"
          # "android"
          # "-d"
          # "emulator-5554"
          # "--flavor"
          # "dev"
        ];
        manager = "flutter";
      };
    };
  };
}