import 'dart:async';

import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:mobile_crm/app/helper/sentry_helper.dart';
import 'package:mobile_crm/app/utils/dependency.dart';
import 'package:mobile_crm/app/utils/logger.dart';
import 'package:mobile_crm/core/env/env.dart';
import 'package:mobile_crm/core/translation/app_translation.dart';
import 'package:mobile_crm/data/services/analytics_service.dart';
import 'package:mobile_crm/routes/app_pages.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

import 'app/utils/firebase_remote_helper.dart';
import 'data/services/dynamic_links_service.dart';
import 'data/services/notification_service.dart';
import 'firebase_options.dart';

@pragma('vm:entry-point')
Future<void> firebaseMessagingBackgroundHandler(RemoteMessage message) async {}

@pragma('vm:entry-point')
void notificationTapBackground(NotificationResponse notificationResponse) {
  selectNotificationStream.add(notificationResponse);
}

Future<void> _onDidReceiveNotificationResponse(
  NotificationResponse notificationResponse,
) async {
  selectNotificationStream.add(notificationResponse);
}

Future init() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp(
    // name:  "mobile_crm",
    options: DefaultFirebaseOptions.currentPlatform,
  );

  // kIsWeb
  //     ? await Firebase.initializeApp(
  //         options: DefaultFirebaseOptions.currentPlatform,
  //       )
  //     : await Firebase.initializeApp(
  //         name: 'mobile_crm',
  //         options: DefaultFirebaseOptions.currentPlatform,
  //       );
  await NotificationService.initializeNotificationService(
      _onDidReceiveNotificationResponse);
  FirebaseMessaging.onBackgroundMessage(firebaseMessagingBackgroundHandler);
  if (!kIsWeb) {
    DynamicLinksService.initDynamicLinks();
  }

  FirebaseRemoteHelper.instance.initial();
  await GetStorage.init('user');
  await GetStorage.init('deeplink');
  await GetStorage.init('app');
  await GetStorage.init('transaction');
  AnalyticsService();
  await DependencyCreator.init();

  infoLogger('baseUrl: ${Env.baseUrl}');

  SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);
}

void main() async {
  await init();
  await ScreenUtil.ensureScreenSize();
  if (kIsWeb){
    print('running without sentry..');
    runApp(const MyApp());
  }else{
  await SentryFlutter.init(
      (options) {
        options.beforeSend = SentryHelper().beforeSend;
        options.dsn = Env.sentryKey;
        options.sampleRate = 0.8;
        options.enableNativeCrashHandling = true;
        options.enableMemoryPressureBreadcrumbs = true;
        options.enableAutoPerformanceTracing = true;
        options.enableUserInteractionBreadcrumbs = true;
        options.tracesSampleRate = 0.8;
      },
      appRunner: () => runApp(const MyApp()), //(const MyApp()),
    );
  }
  
}

class MyApp extends StatelessWidget {
  const MyApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    var shortestSide = MediaQuery.of(context).size.shortestSide;    
    final bool useMobileLayout = shortestSide < 600;
    return ScreenUtilInit(
        designSize: useMobileLayout ? Size(360, 690) : Size(768, 1024),
        ensureScreenSize: true,
        // minTextAdapt: true,
        // splitScreenMode: true,
        builder: (context, child) {
          return GestureDetector(
            onTap: () {
              FocusScopeNode currentFocus = FocusScope.of(context);

              if (!currentFocus.hasPrimaryFocus &&
                  currentFocus.focusedChild != null) {
                FocusManager.instance.primaryFocus?.unfocus();
              }
            },
            child: GetMaterialApp(
                debugShowCheckedModeBanner: false,
                title: 'UNIQ CRM',
                navigatorObservers: [AnalyticsService.observer],
                translations: AppTranslation(),
                defaultTransition: Transition.cupertino,
                smartManagement: SmartManagement.full,
                transitionDuration: const Duration(milliseconds: 100),
                locale: Get.deviceLocale,
                theme: ThemeData(
                    useMaterial3: true,
                    fontFamily: kIsWeb ? null : "Poppins",
                    textTheme:
                        TextTheme(bodyMedium: TextStyle(fontSize: 20.sp)),
                    primarySwatch: Colors.blueGrey,
                    iconTheme: const IconThemeData(color: Colors.white),
                    iconButtonTheme: IconButtonThemeData(
                        style: ButtonStyle(
                            iconColor: MaterialStateProperty.all(Colors.white),
                            textStyle: MaterialStateProperty.all(
                                const TextStyle(color: Colors.white)))),
                    appBarTheme: const AppBarTheme(
                        color: Colors.black,
                        iconTheme: IconThemeData(color: Colors.white),
                        foregroundColor: Colors.white)),
                initialRoute: AppPages.initialRoute,
                getPages: AppPages.routes),
          );
        });
  }
}

final StreamController<NotificationResponse?> selectNotificationStream =
    StreamController<NotificationResponse?>.broadcast();
