import 'package:flutter_screenutil/flutter_screenutil.dart';

class AppDimen {
  //Dimension Width
  static double w2 = 2.0.w;
  static double w4 = 4.0.w;
  static double w6 = 6.0.w;
  static double w8 = 8.0.w;
  static double w10 = 10.0.w;
  static double w12 = 12.0.w;
  static double w14 = 14.0.w;
  static double w16 = 16.0.w;
  static double w18 = 18.0.w;
  static double w20 = 20.0.w;
  static double w22 = 22.0.w;
  static double w24 = 24.0.w;
  static double w26 = 26.0.w;
  static double w28 = 28.0.w;
  static double w30 = 30.0.w;
  static double w32 = 32.0.w;
  static double w34 = 34.0.w;
  static double w36 = 36.0.w;
  static double w38 = 38.0.w;
  static double w40 = 40.0.w;
  static double w64 = 64.0.w;

  //Dimension Height
  static double h2 = 2.0.h;
  static double h4 = 4.0.h;
  static double h6 = 6.0.h;
  static double h8 = 8.0.h;
  static double h10 = 10.0.h;
  static double h12 = 12.0.h;
  static double h14 = 14.0.h;
  static double h16 = 16.0.h;
  static double h18 = 18.0.h;
  static double h20 = 20.0.h;
  static double h22 = 22.0.h;
  static double h24 = 24.0.h;
  static double h26 = 26.0.h;
  static double h28 = 28.0.h;
  static double h30 = 30.0.h;
  static double h32 = 32.0.h;
  static double h34 = 34.0.h;
  static double h36 = 36.0.h;
  static double h38 = 38.0.h;
  static double h40 = 40.0.h;
  static double h42 = 42.0.h;
  static double h44 = 44.0.h;
  static double h46 = 46.0.h;
  static double h48 = 48.0.h;
  static double h50 = 50.0.h;
  static double h52 = 52.0.h;
  static double h54 = 54.0.h;
  static double h56 = 56.0.h;
  static double h58 = 58.0.h;
  static double h60 = 60.0.h;
  static double h62 = 62.0.h;
  static double h64 = 64.0.h;
  static double h128 = 128.0.h;
  static double h192 = 192.0.h;
}
