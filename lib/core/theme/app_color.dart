import 'dart:ui';

class AppColor {
  // Black
  static const Color black = Color(0xFF191D21);
  static const Color black5 = Color(0x0D191D21);
  static const Color black10 = Color(0x1A191D21);
  static const Color black20 = Color(0x33191D21);
  static const Color black30 = Color(0x4D191D21);
  static const Color black50 = Color(0x80191D21);
  static const Color black70 = Color(0xB3191D21);
  static const Color black90 = Color(0xE6191D21);

  static const Color ink02 = Color(0xFF656F77);
  static const Color ink03 = Color(0xFFACB8C2);
  static const Color ink04 = Color(0xFFCDD9E3);
  static const Color ink05 = Color(0xFFE8EEF3);

  static const Color whiteGrey = Color(0xFFFCFCFC);
  static const Color navy = Color(0xFF27374D);

  // White
  static const Color white = Color(0xFFFFFFFF);
  static const Color ink0610 = Color(0x1AFFFFFF);
  static const Color ink0630 = Color(0x4DFFFFFF);
  static const Color ink0650 = Color(0x80FFFFFF);
  static const Color ink0670 = Color(0xB3FFFFFF);
  static const Color ink0690 = Color(0xE6FFFFFF);

  static const Color utilityActive = Color(0xFF1814E4);
  static const Color utilitySuccess = Color(0xFF54B435);
  static const Color utilityInfo = Color(0xFF91D7E0);
  static const Color utilityWarning = Color(0xFFFFAC4B);
  static const Color utilityDark = Color(0xFF191D21);
  static const Color utilityDanger = Color(0xFFFF5A5A);

  static const Color couponBGYellow = Color(0xFFFFE261);
  static const Color couponFree = Color(0xFF3699F4);
  static const Color couponDiscount = Color(0xFFF44336);

  static const Color disable = Color(0xFFDDDDDD);

  static const Color toastSuccess = Color(0xFFBAEDE1);
  static const Color toastInfo = Color(0xFFCDEDF6);
  static const Color toastWarning = Color(0xFFFFEACA);
  static const Color toastError = Color(0xFFFFCACA);

  static const Color accentBlue = Color(0xFFCBE3FF);
  static const Color accentGreen = Color(0xFFD9FFF8);
  static const Color accentPurple = Color(0xFFF2E8FF);
  static const Color accentOrange = Color(0xFFFFD1B6);
  static const Color accentPink = Color(0xFFFFDBF5);
  static const Color accentYellow = Color(0xFFFFF3B6);

  static const Color fieldForm = Color(0xFFF9F9FD);
  static const Color fieldBGError = Color(0xFFFFD8D8);
  static const Color fieldStrokeError = Color(0xFFFFF5F5);
  static const Color fieldStrokeFocused = Color(0xFF605DEC);

  static const Color warning = Color(0xFFFFA726);
  static const Color ink005 = Color(0xFFF8F9FA);
  static const Color ink01 = Color(0xFFE9ECEF);
}
