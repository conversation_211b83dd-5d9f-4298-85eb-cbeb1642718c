import 'package:flutter/material.dart';
import 'package:mobile_crm/core/theme/app_color.dart';

import 'app_font.dart';

class AppInput {
  static InputDecoration get defaultTheme => InputDecoration(
        filled: true,
        hintStyle: AppFont.componentSmall.copyWith(color: AppColor.black50),
        fillColor: AppColor.fieldForm,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10.0),
          borderSide: const BorderSide(
            width: 0,
            style: BorderStyle.none,
          ),
        ),
      );
}
