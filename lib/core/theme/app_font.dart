import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class AppFont {
  static TextStyle get hero => TextStyle(
      fontSize: 72.sp, fontWeight: FontWeight.w700, color: Colors.black);

  static TextStyle get display1 => TextStyle(
      fontSize: 56.sp, fontWeight: FontWeight.w700, color: Colors.black);

  static TextStyle get display2 => TextStyle(
      fontSize: 40.sp, fontWeight: FontWeight.w700, color: Colors.black);

  static TextStyle get display3 => TextStyle(
      fontSize: 32.sp, fontWeight: FontWeight.w700, color: Colors.black);

  static TextStyle get heading0 => TextStyle(
      fontSize: 72.sp, fontWeight: FontWeight.w700, color: Colors.black);

  static TextStyle get heading1 => TextStyle(
      fontSize: 56.sp, fontWeight: FontWeight.w700, color: Colors.black);

  static TextStyle get heading2 => TextStyle(
      fontSize: 40.sp, fontWeight: FontWeight.w700, color: Colors.black);

  static TextStyle get heading3 => TextStyle(
      fontSize: 32.sp, fontWeight: FontWeight.w700, color: Colors.black);

  static TextStyle get heading4 => TextStyle(
      fontSize: 24.sp, fontWeight: FontWeight.w700, color: Colors.black);

  static TextStyle get heading5 => TextStyle(
      fontSize: 20.sp, fontWeight: FontWeight.w700, color: Colors.black);

  static TextStyle get paragraphLarge => TextStyle(
      fontSize: 16.sp, fontWeight: FontWeight.w400, color: Colors.black);

  static TextStyle get paragraphLargeBold => TextStyle(
      fontSize: 16.sp, fontWeight: FontWeight.w700, color: Colors.black);

  static TextStyle get paragraphMedium => TextStyle(
      fontSize: 14.sp, fontWeight: FontWeight.w400, color: Colors.black);

  static TextStyle get paragraphMediumBold => TextStyle(
      fontSize: 14.sp, fontWeight: FontWeight.w700, color: Colors.black);

  static TextStyle get paragraphSmall => TextStyle(
      fontSize: 12.sp, fontWeight: FontWeight.w400, color: Colors.black);

  static TextStyle get paragraphSmallBold => TextStyle(
      fontSize: 12.sp, fontWeight: FontWeight.w600, color: Colors.black);

  static TextStyle get buttonLarge => TextStyle(
      fontSize: 16.sp, fontWeight: FontWeight.w600, color: Colors.black);

  static TextStyle get buttonMedium => TextStyle(
      fontSize: 14.sp, fontWeight: FontWeight.w600, color: Colors.black);

  static TextStyle get buttonSmall => TextStyle(
      fontSize: 12.sp, fontWeight: FontWeight.w600, color: Colors.black);

  static TextStyle get componentLarge => TextStyle(
      fontSize: 16.sp, fontWeight: FontWeight.w600, color: Colors.black);

  static TextStyle get componentMediumBold => TextStyle(
      fontSize: 14.sp, fontWeight: FontWeight.w600, color: Colors.black);

  static TextStyle get componentMedium => TextStyle(
      fontSize: 14.sp, fontWeight: FontWeight.w400, color: Colors.black);

  static TextStyle get componentSmallBold => TextStyle(
      fontSize: 12.sp, fontWeight: FontWeight.w600, color: Colors.black);

  static TextStyle get componentSmall => TextStyle(
      fontSize: 12.sp, fontWeight: FontWeight.w500, color: Colors.black);

  static Style get htmlStyle => Style(
      fontFamily: kIsWeb ? null : 'Poppins',
      margin: Margins.symmetric(horizontal: 0));
}
