// lib/env/env.dart
import 'package:envied/envied.dart';

part 'env.g.dart';

@Envied(path: '.env')
abstract class Env {
  @EnviedField(varName: 'BASE_URL', obfuscate: true)
  static final String baseUrl = _Env.baseUrl;
  @EnviedField(varName: 'PUBLIC_KEY', obfuscate: true)
  static final String publicKey = _Env.publicKey;
  @EnviedField(varName: 'AUTHORIZATION_KEY', obfuscate: true)
  static final String authorizationKey = _Env.authorizationKey;
  @EnviedField(varName: 'SENTRY_KEY', obfuscate: true)
  static final String sentryKey = _Env.sentryKey;
}
