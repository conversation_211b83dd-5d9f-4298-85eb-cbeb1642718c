import 'package:flutter/material.dart';

class HexColor extends Color {
  static int _getColorFromHex(String? hexColor) {
    if (hexColor != null) {
      hexColor = hexColor != '' ? hexColor : '#000000';
      hexColor = hexColor.toUpperCase().replaceAll("#", "");
      if (hexColor.length == 6) {
        hexColor = "FF$hexColor";
      }
      return int.parse(hexColor, radix: 16);
    }
    return int.parse("FF000000", radix: 16);
  }

  HexColor(String? hexColor) : super(_getColorFromHex(hexColor));
}

extension ColorExtension on Color {
  (Color, Brightness) changeColorBasedOnBackgroundColor(
          {bool reverse = false}) =>
      (computeLuminance() > 0.55)
          ? reverse
              ? (Colors.white, Brightness.light)
              : (Colors.black, Brightness.dark)
          : (Colors.white, Brightness.light);
}
