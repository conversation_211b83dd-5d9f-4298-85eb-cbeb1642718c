import 'package:intl/intl.dart';
import 'package:mobile_crm/app/utils/utils.dart';

extension StringExtension on String {
  /// Convert Phone Number [toLocal] return "081xxxxxxx"
  /// turn +62 to 0
  String get toLocal => Utils.convertPhoneNumber(this, reverse: true);

  /// add area code Phone Number
  /// turn 0 to +62
  String get toInternational => Utils.convertPhoneNumber(this, reverse: false);

  /// turn 23:59:00 to 23:59
  String get toTimeFormat => Utils.toTimeFormat(time: this);

  String get removeUnderscore => replaceAll('_', ' ');

  /// The regular expression is simplified for an HTML tag (opening or
  /// closing) or an HTML escape. We might want to skip over such expressions
  /// when estimating the text directionality.
  String get removeHtmlTag => Utils.removeHtmlTag(this);

  bool get dayIsToday => _dayIsToday(this);

  String get takeFirstWord {
    List<String> parts = split(' ');
    if(parts.length > 2){
      parts = parts.take(2).toList();
    }

    if (parts.length == 1) {
      try {
        return this[0];
      } catch (e, s) {
        return 'N';
      }
    } else {
      String initials = parts.map((part) => part[0].toUpperCase()).join();
      return initials;
    }
  }
}

bool _dayIsToday(String day) {
  String nowDay =
      DateFormat(DateFormat.WEEKDAY).format(DateTime.now()).toLowerCase();
  if (day.toLowerCase() == nowDay) {
    return true;
  }
  return false;
}
