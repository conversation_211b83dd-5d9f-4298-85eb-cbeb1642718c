import 'dart:convert';

import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:mobile_crm/app/utils/logger.dart';

extension RemoteMap on RemoteConfigValue {
  /// Get value as Map
  ///
  /// This method will try to cast to your return type
  /// If errors occur, this method will return [onError] if it's not null or
  /// throw an errors if [onError] is null.
  ///
  /// If you met [ArgumentError] then you're using unsupported return types
  Map<String, T> asMap<T>({Map<String, T>? onError}) {
    try {
      final json = jsonDecode(asString()) as Map<String, dynamic>?;
      infoLogger("Ext", "asMap json: $json");

      if (json != null) {
        return json.cast<String, T>();
      }
    } catch (e) {
      errorLogger(pos: 'Ext', error: 'asMap ERROR: $e');
      if (onError != null) return onError;
      rethrow;
    }

    return {};
  }

  /// Get value as List
  ///
  /// List<T> with T is bool, number, string
  ///
  /// If errors occur, this method will return [onError] if it's not null or
  /// throw an errors if [onError] is null.
  ///
  /// If you met [ArgumentError] then you're using unsupported return types
  List<T> asList<T>({List<T>? onError}) {
    try {
      final json = jsonDecode(asString()) as List<dynamic>?;
      infoLogger("Ext", "asList json: $json");

      if (json != null) {
        return json.cast<T>();
      }
    } catch (e) {
      errorLogger(pos: 'Ext', error: 'asList ERROR: $e');
      if (onError != null) return onError;
      rethrow;
    }

    return [];
  }
}