import 'package:mobile_crm/app/utils/utils.dart';

extension IntExtension on int? {
  /// [toDateAndTime] need millisecond to return date with time "01 Jan 2023 00:00"
  String get toDateAndTime => Utils.millisecondToDate(this, withTime: true);

  /// [toDate] need millisecond to return date only "01 Jan 2023"
  String get toDate => Utils.millisecondToDate(this, withTime: false);

  /// [toTime] need millisecond to return time only "00:00"
  String get toTime => Utils.millisecondToDate(this, timeOnly: true);

  /// [toTimeLeft] need millisecond to return left time remaining "10 Hours left"
  String get toTimeLeft => Utils.timeLeft(this);

  /// [toDayAgo] need millisecond to return "today" / "yesterday" / "one week ago"
  String get toDayAgo => Utils.dayAgo(this);
  String get toDayAgoV2 => Utils.dayAgoV2(this);

  /// [toTimeAgo] need millisecond to return "10 Seconds ago" / "one hours ago"
  String get toTimeAgo => Utils.timeAgo(this);

  /// [toCurrency] turn int to currency format "123.456.789"
  String get toCurrency => Utils.formatCurrency(this);
}
