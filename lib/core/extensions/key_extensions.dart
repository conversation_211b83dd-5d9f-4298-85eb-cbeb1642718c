import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';

extension KeyExtension on GlobalKey {
  Rect get getRect => getRectFromKey(this);
}

Rect getRectFromKey(GlobalKey<State<StatefulWidget>> key) {
  var renderObject = key.currentContext?.findRenderObject();
  if (renderObject != null) {
    var renderAbstract = RenderAbstractViewport.of(renderObject)
        .getOffsetToReveal(renderObject, 0.0);
    return renderAbstract.rect;
  }
  Rect defRect = const Offset(0.0, 0.0) & const Size(0.0, 0.0);
  return defRect;
}
