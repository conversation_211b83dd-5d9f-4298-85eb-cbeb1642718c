import 'package:flutter/material.dart';
import 'package:mobile_crm/app/animation/fadein_animation.dart';

import '../../app/utils/animations.dart';
import '../theme/app_dimen.dart';
import '../values/app_constants.dart';

extension WidgetExts on Widget {
  Widget fadeIn({Duration? duration, Duration? delay}) => FadeInAnimation(
      duration: duration ?? const Duration(milliseconds: 500),
      delay: delay ?? const Duration(milliseconds: 100),
      child: this);

  Widget tooltip(String message) => Tooltip(
        message: message,
        child: this,
      );

  ChangeModalBottomSheetWrapper get toModalBottomSheet =>
      ChangeModalBottomSheetWrapper(this);

  ChangeModalBottomSheetWrapper get toModalBottomSheetNoMinHeight =>
      ChangeModalBottomSheetWrapper(this, removeMinHeight: true);

  ChangeFullDialogWrapper get toFullDialog => ChangeFullDialogWrapper(this);
  ChangeDialogWrapper toDialog({bool barrierDismissible = false}) =>
      ChangeDialogWrapper(this, barrierDismissible);
}

class ChangeModalBottomSheetWrapper {
  final Widget widget;
  final bool removeMinHeight;
  ChangeModalBottomSheetWrapper(this.widget, {this.removeMinHeight = false});
  Future of(BuildContext context) {
    return showModalBottomSheet(
      isScrollControlled: true,
      context: context,
      constraints: removeMinHeight
          ? BoxConstraints(
              minWidth: Constants.defaultMaxWidth,
              maxWidth: Constants.defaultMaxWidth,
              maxHeight: MediaQuery.of(context).size.height * 0.9)
          : BoxConstraints(
              minWidth: Constants.defaultMaxWidth,
              maxWidth: Constants.defaultMaxWidth,
              minHeight: MediaQuery.of(context).size.height * 0.4,
              maxHeight: MediaQuery.of(context).size.height * 0.9),
      backgroundColor: Colors.white,
      showDragHandle: true,
      enableDrag: true,
      isDismissible: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
            topLeft: Radius.circular(AppDimen.h6),
            topRight: Radius.circular(AppDimen.h6)),
      ),
      builder: (context) {
        return widget;
      },
    );
  }
}

class ChangeFullDialogWrapper {
  final Widget widget;
  ChangeFullDialogWrapper(this.widget);
  Future of(BuildContext context) {
    return showGeneralDialog(
      context: context,
      pageBuilder: (context, animation, secondaryAnimation) {
        return Animations.fromBottom(animation, secondaryAnimation, widget);
      },
      transitionDuration: const Duration(milliseconds: 200),
    );
  }
}

class ChangeDialogWrapper {
  final Widget widget;
  final bool barrierDismissible;
  ChangeDialogWrapper(this.widget, this.barrierDismissible);
  Future of(BuildContext context) {
    return showDialog(
      context: context,
      barrierDismissible: barrierDismissible,
      builder: (BuildContext context) {
        return Dialog(
          // shape: const RoundedRectangleBorder(),
          child: widget,
        );
      },
    );
  }
}
