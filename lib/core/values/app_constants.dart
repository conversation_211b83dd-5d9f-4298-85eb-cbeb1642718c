import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mobile_crm/core/theme/themes.dart';

import '../../responsive.dart';

class Constants {
  static Radius shapeRadius = Radius.circular(AppDimen.h10);

  static double fontSizeSmall(BuildContext context) {
    return 12.sp;
  }

  static double buttonHeight() => 50.h;

  static double buttonHeightContent() => 30.h;

  static double iconSize(BuildContext context) => AppDimen.h14;

  static double iconSizeSmall(BuildContext context) => AppDimen.h10;

  static double defaultBoxConstraints(BuildContext context) {
    return 720;
  }

  static double get defaultMaxWidth => 1068; //768

  static double get defaultPadding => 8;
  static double get defaultPadding2 => AppDimen.h16;

  static double alertWidth(BuildContext context) {
    return defaultMaxWidth;
  }

  static double appToolBarHeight(BuildContext context) {
    return 135;
  }

  static double containerMenuHeight(BuildContext context) {
    return (Responsive.isDesktop4K(context))
        ? 80
        : (Responsive.isDesktop(context))
            ? 70
            : (Responsive.isTablet(context))
                ? 80
                : (Responsive.isMobileLarge(context))
                    ? 85
                    : (Responsive.isMobileMedium(context))
                        ? 65
                        : (Responsive.isMobileSmall(context))
                            ? 60.h
                            : 65;
  }

  static double tabMenuHeight(BuildContext context) {
    return (Responsive.isDesktop4K(context))
        ? 50
        : (Responsive.isDesktop(context))
            ? 50
            : (Responsive.isTablet(context))
                ? 50
                : (Responsive.isMobileLarge(context))
                    ? MediaQuery.of(context).size.width * 0.08
                    : (Responsive.isMobileSmall(context))
                        ? MediaQuery.of(context).size.width * 0.08
                        : MediaQuery.of(context).size.width * 0.08;
  }

  static Size productItemSize = Size.square(70.h);

  // Order
  static double slideUpHeight(BuildContext context) =>
      (Responsive.isMobileLarge(context))
          ? 100
          : (Responsive.isMobileMedium(context))
              ? 85
              : (Responsive.isMobileSmall(context))
                  ? 80
                  : 90.h;

  static double barcodeSize(BuildContext context) =>
      (Responsive.isMobileLarge(context))
          ? 250.h
          : (Responsive.isMobileMedium(context))
              ? 250.h
              : (Responsive.isMobileSmall(context))
                  ? 250.h
                  : 300.h;

  //
  static Size logoSize(BuildContext context) => Size(150.h, 150.h);

  static double voucherContainerWidth(BuildContext context) =>
      (Responsive.isMobile(context))
          ? MediaQuery.of(context).size.width / 2
          : Constants.defaultMaxWidth / 2;

  // Home Screen
  static double bannerHeight(BuildContext context) {
    return (Responsive.isDesktop4K(context))
        ? 250
        : (Responsive.isDesktop(context))
            ? 250
            : (Responsive.isTablet(context))
                ? 250
                : MediaQuery.of(context).size.width / 2;
  }

  static double containerUserDetailHeight(BuildContext context) {
    return (Responsive.isDesktop4K(context))
        ? 85
        : (Responsive.isDesktop(context))
            ? 85
            : (Responsive.isTablet(context))
                ? 85
                : 70.h;
  }

  static double marginSpaceHeight(BuildContext context) {
    return (!kIsWeb)
        ? 70.h + MediaQuery.of(context).padding.top
        : (Responsive.isDesktop4K(context))
            ? 50
            : (Responsive.isDesktop(context))
                ? 50
                : (Responsive.isTablet(context))
                    ? 50
                    : MediaQuery.of(context).size.width * 0.12;
  }

  static double collapseHeight(BuildContext context) {
    return (Responsive.isDesktop4K(context))
        ? 125
        : (Responsive.isDesktop(context))
            ? 125
            : (Responsive.isTablet(context))
                ? 125
                : 45.h + MediaQuery.of(context).padding.top;
  }

  static double expandedHeight(BuildContext context) {
    return (marginSpaceHeight(context) +
        containerUserDetailHeight(context) +
        bannerHeight(context) -
        10.h);
  }

  // OTP Screen
  static double pinputHeight(BuildContext context) {
    return (Responsive.isDesktop4K(context))
        ? 50
        : (Responsive.isDesktop(context))
            ? 50
            : (Responsive.isTablet(context))
                ? 50
                : (Responsive.isMobileLarge(context))
                    ? 55
                    : (Responsive.isMobileSmall(context))
                        ? 35
                        : 35;
  }

  static double pinputWidth(BuildContext context) {
    return (Responsive.isDesktop4K(context))
        ? 50
        : (Responsive.isDesktop(context))
            ? 50
            : (Responsive.isTablet(context))
                ? 50
                : (Responsive.isMobileLarge(context))
                    ? 50
                    : (Responsive.isMobileSmall(context))
                        ? 35
                        : 35;
  }
}
