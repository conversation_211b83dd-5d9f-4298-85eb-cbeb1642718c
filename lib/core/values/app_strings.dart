class Strings {
  // GoStorageBox
  static const String tokenFirebase = "box_token_firebase";
  static const String tokenAuth = "box_token_login";
  static const String authToken = "box_auth_token";
  static const String keyDeleteAccount = "box_key_delete_account";
  static const String tokenFCM = "box_token_fcm";
  static const String tokenMessage = "box_token_messaging";

  static const String user = "box_user";
  static const String lastTransaction = "box_transaction";
  static const String orderTransaction = "box_order_transaction";
  static const String listOrder = "box_list_order";
  static const String timeOrder = "box_time_order";
  static const String outlet = "box_current_outlet";
  static const String tax = "box_tax_order";
  static const String authState = "box_state_auth";
  static const String authPhone = "box_state_phone";
  static const String authChangePhone = "box_state_change_phone";
  static const String authEmail = "box_state_email";
  static const String authName = "box_state_name";
  static const String authLostPhone = "box_state_lost_phone";
  static const String authRegister = "box_state_register";
  static const String authKey = "box_state_key";
  static const String orderTypeNext = "box_order_type";
  static const String productDetail = "box_product_detail";
  static const String mainAddress = "box_main_address";

  static const String deepProduct = "box_deep_product";
  static const String deepType = "box_deep_type";

  // Translation
  static const String login = "Login";
  static const String needLogin = "Need to login, to use this feature";
  static const String next = "Next";
  static const String signup = "Sign Up";
  static const String search = "Search";
  static const String listPromotion = "Promotion list";
  static const String invalidPhoneNumber = "Invalid Phone Number";
  static const String memberNotFound =
      "Oops! We couldn't find your membership. Please sign up";
  static const String getNew = "Get New";
  static const String later = "Later";
  static const String toVerifyUsedLoginButton =
      "To verify you can use the login button by filling in the phone number";
  static const String createNew = "Create a new account";
  static const String welcome = "Welcome back";
  static const String welcomeGuest = "Hello, Guest";
  static const String guest = "Guest";
  static const String dontMissOpportunityToEarnPoint =
      "Don't miss the opportunity to earn points! Enjoy exclusive offers and tempting promotions for various types of products. Log in now!";
  static const String dontMissOpportunityToEarnValuePoint =
      "Don't miss the opportunity to earn value points! Enjoy exclusive offers and tempting promotions for various types of products. Log in now!";
  static const String successRegisterBusiness =
      "Success! You're now a registered member of our business.";
  static const String lostNumber = "Lost phone number?";
  static const String dontLetDataLost =
      "Don't let your data be lost, come on, replace your lost number immediately";
  static const String register = "Register";
  static const String valueNotVerified = "value not verified";
  static const String valueAlreadyVerified = "value already verified";
  static const String phoneNumber = "Phone Number";
  static const String whatsappNumber = "WhatsApp Number";
  static const String verifyValue = "Verify value";
  static const String newAccount = "New account";
  static const String signInGoogle = "Sign In With Google";
  static const String signInApple = "Sign In With Apple";
  static const String dontHaveAccount = "don't have an account?";
  static const String didntReceiveCode = "Didn't Receive Code?";
  static const String listOutlet = "Outlet List";
  static const String name = "Name";
  static const String whatsAppWontOpen =
      "WhatsApp won't open? Make sure it's properly installed";
  static const String email = "Email";
  static const String alreadyRegisterBusiness =
      "Login to your existing account. You're already registered here!";
  static const String registrationIsSuccessfulCheckWhatsapp =
      "All set with registration! Click link sent to your WhatsApp Number.";
  static const String sendWhatsappLink = "send WhatsApp link";
  static const String needHelpWithOTPCode =
      "Need help with the OTP code? Send a link to WhatsApp or wait for the message";
  static const String resend = "Resend";
  static const String yourNumberHasBeenChanged =
      "Your number has been successfully changed. Log in to continue";
  static const String addressLabel = "Address Label";
  static const String verification = "Verification";
  static const String enterCodeSent = "Enter the code sent to the";
  static const String address = "Address";
  static const String gender = "Gender";
  static const String dateOfBirth = "Date of Birth";
  static const String accountDeleted = "Account Deleted Successfully";
  static const String phoneNumberUsed =
      "The phone number is already registered, please use another phone number";
  static const String emailVerifyHasSent =
      "Email verification has been sent to your email";
  static const String outletClosed = "Outlets closed";
  static const String exampleNoteOrder =
      "e.g please give it to the security guard";
  static const String exampleNoteOrder2 = "Any note for store?";
  static const String notification = "Notification";
  static const String notificationDetail = "Notification detail";
  static const String textCopied = "Text copied";
  static const String openWork = "Open at";
  static const String open = "Open";
  static const String sorryTheValueIsClosed = "Sorry, the value is closed";
  static const String sorryTheValueIsNotAvailableAtThisTime =
      "Sorry, this value order type is not available at this time";
  static const String validityPeriod = "Validity period";
  static const String listItem = "List Item";
  static const String closeWork = "Closes at";
  static const String close = "Close";
  static const String noInformation = "No information";
  static const String notes = "Notes";
  static const String optional = "Optional";
  static const String openingHours = "opening Hours";
  static const String noInternetConnection = "No Internet Connection";
  static const String connectionTimeOut =
      "Connection timed out. Please check your internet connection";
  static const String internetConnectionRestored =
      "Internet Connection Restored";
  static const String orderCreated = "Order placed";
  static const String order = "Order";
  static const String yourOrder = "Your order";
  static const String order2 = "Order2";
  static const String createAnOrder = "create an Order";
  static const String orderCode = "Order Code";
  static const String outletName = "Outlet Name";
  static const String customerName = "Customer Name";
  static const String totalTaxes = "Total Taxes";
  static const String subTotal = "Sub Total";
  static const String totalBill = "Total Bill";
  static const String pickupTime = "Pick up time";
  static const String termsAndConditions = "Terms and conditions";
  static const String aboutPromo = "About this promo";
  static const String cancel = "Cancel";
  static const String promoApplied = "Promo applied";
  static const String delete = "Delete";
  static const String saving = "Saving";
  static const String noActivePayment =
      "No pending or active payments. You're all set";
  static const String activeTransaction = "Active Transaction";
  static const String transferAmount = "Transfer amount";
  static const String payAmount = "Pay amount";
  static const String selectProofOfPayment =
      "Select the proof of payment image";
  static const String sendProofOfPayment = "Send Proof of Payment";
  static const String changeImage = "Change Image";
  static const String save = "Save";
  static const String saved = "Saved";
  static const String category = "Category";
  static const String newCategory = "New Category";
  static const String categoryName = "Category name";
  static const String nameYourCategory = "Name your category";
  static const String addCategory = "Add category";
  static const String valueSavedWishlist = "value saved wishlists";
  static const String profOfDeliveryReceipt = "Prof of delivery receipt";
  static const String shipmentCourier = "Courier";
  static const String tapAgainToReturnValue = "Tap again to return value";
  static const String shipmentReceiptNumber = "Receipt Number";
  static const String addNoteToNote = "Add notes to your";
  static const String addNote = "Add notes";
  static const String exampleNoteTo = "e.g please, add extra tomato sauce";
  static const String terms = "Terms";
  static const String availableUntil = "Available until";
  static const String minimumTransaction = "Minimum transaction";
  static const String viewMore = "View more";
  static const String redeemPeriod = "Redeem Period";
  static const String done = "Done";
  static const String selectOne = "Select one";
  static const String selectMultiple = "Select multiple";
  static const String orderIsProcessed =
      "Please wait, your order will be processed";
  static const String removeItemFromCart = "Remove Item from cart?";
  static const String fillNumber =
      "Please fill in your number in the phone number field";
  static const String addToCart = "Add To Cart";
  static const String saveConversion = "Save conversion";
  static const String unitConversion = "Unit conversion";
  static const String updateCart = "Update cart";
  static const String addAnother = "Add another";
  static const String orderMessage =
      "Orders will not be processed until you show this QR code to the cashier";
  static const String orderMessage2 = "Payment can only be made at the cashier";
  static const String removeFromCart = "Remove From Cart";
  static const String productNotAvailable = "Product is not available";
  static const String notifyMe = "Notify Me";
  static const String verificationCode = "Verification Code";
  static const String scanToEarnPoint =
      "Earn more points by scanning during checkout!";
  static const String back = "Back";
  static const String unavailableCart =
      "Unable to proceed with the purchase because the item in the cart is currently unavailable";
  static const String someCartUnavailable =
      "The items below are currently unavailable, would you like to continue?";
  static const String noProductsToOrder = "There are no products to order";
  static const String confirmLogout = "do you want to logout?";
  static const String paymentSummary = "Payment summary";
  static const String paymentMethod = "Payment method";
  static const String selectPaymentMethod = "Choose a payment method";
  static const String orderType = "Choose order type";
  static const String cancelTransaction =
      "Are you sure you want to cancel the transaction?";
  static const String productQuantity = "Product quantity";
  static const String productPrice = "Product price";
  static const String additionalMenu = "Additional Menu";
  static const String haveNotReceivedOTP =
      "Haven't received the OTP code? Check messages or WhatsApp!";
  static const String chooseTime =
      "Choose a time, when your order will be taken";
  static const String chooseVariant = "Choose variant";
  static const String pickupWhenReady =
      "You can pickup your order once it ready";
  static const String whereDestination = "Where is the delivery destination?";
  static const String selfOrderDesc =
      "Place orders without queuing, select menus without having to go to the cashier, just show the QR code and make payments";
  static const String pleaseMakePayment = "Please make payment before";
  static const String searchHistory = "Search History";
  static const String unit = "Unit";
  static const String noPhoneNumberLinkedToThisEmail =
      "No phone number linked to this email. Please use a different Google account";
  static const String yourCodeHasBeenSent = "Your code is on its way";
  static const String all = "All";
  static const String valueAlreadyUsePleaseUseAnotherValue =
      "value Already Use Please Use Another Value";
  static const String verify = "Verify";
  static const String verifyNow = "Verify now";
  static const String addressBasedOnProfile = "Address based on profile";
  static const String logout = "Logout";
  static const String receiptPhoneNumber = "Recipient's phone number";
  static const String getLocation = "Get Location";
  static const String orSignInUsing = "or Sign in using";
  static const String exTableNumber8 = "e.g table number 8";
  static const String exAddress = "e.g Jl. Indonesia Barat 01";
  static const String orSignIn = "or Sign in";
  static const String deleteAccount = "Delete Account";
  static const String changePhoneNumberWillReqOTP =
      "Change phone number will require One Time Password verification on the current email";
  static const String updateValue = "Update value";
  static const String newValue = "New value";
  static const String sumPromosAreAvailable = "Sum Promos are available";
  static const String valueUpdated = "value Updated";
  static const String pleaseInputYourNewValue = "Please input your new value";
  static const String pleaseInputYourValue = "Please input your value";
  static const String pleaseInputYourPaymentValue =
      "Please input your payment value";
  static const String profile = "Profile";
  static const String backHome = "Back home";
  static const String waitingForPayment = "Waiting for Payment";
  static const String waitingForValue = "Waiting for value";
  static const String paymentSuccess = "Payment success";
  static const String paymentFailed = "Payment failed";
  static const String continuePayment = "Continue payment";
  static const String proceedToOrder = "Proceed to order";
  static const String refreshPaymentStatus = "Update payment status";
  static const String waitingForPaymentStatus = "Waiting for payment status";
  static const String updateEmailUnverified =
      "Take the first step to update your email - verify your account!";
  static const String destination = "Destination";
  static const String description = "Description";
  static const String shippingCosts = "Shipping costs";
  static const String makeSureTheOrderIsCorrect =
      "Make sure the order is correct";
  static const String availableAt = "Available at outlets";
  static const String openEWallet =
      "Open your favorite E-Wallet application and Scan the QR Code to make payments";
  static const String thankYouBuyingPromo =
      "Thank you for buying our products. Please complete payment before";
  static const String choosePayment = "Choose payment";
  static const String chooseAddress = "Choose Address";
  static const String payBtn = "Pay";
  static const String makeSureAddress =
      "Make sure you input your Domiciled Address, not your original address";

  static const String yesMyDomiciled = "Yes, it's my Domiciled Address";

  // static const String waitingForPayment = "Waiting payment";
  static const String proofPayment = "Proof of payment";
  static const String appliedVouchers = "Applied vouchers";
  static const String availableDeals = "available deals";
  static const String minimumOrder = "Minimum order";
  static const String maxDiscount = "Maximum Discount";
  static const String specialPrice = "Special price";
  static const String maxPurchaseAmount = "Max. purchase amount";
  static const String minPurchaseAmount = "Min. purchase amount";
  static const String termProduct = "Product terms";
  static const String until = "Until";
  static const String hoursLeft = "hours left";
  static const String promoQuotaIsOver = "Promo quota is over";
  static const String accountRegisteredSuccessfully =
      "Account Registered Successfully";
  static const String discount = "Discount";
  static const String addNewAddress = "Add new address";
  static const String purchaseTime = "Purchase date";
  static const String whereOrderBeDelivered =
      "Where will the order be delivered? address is required";
  static const String available = "Available";
  static const String notAvailable = "Not available";
  static const String availableIn = "Available in";
  static const String availableOn = "Available on";
  static const String dealsNotYetOpened = "Deals not yet opened";
  static const String buyDeal = "Buy deals";
  static const String okay = "Okay";
  static const String deleteItem = "Delete item";
  static const String promoWillBeOpenSoon =
      "The promo you are looking for will be open soon";
  static const String emailAlreadyUsedRegistered =
      "Email already used/registered";
  static const String phoneAlreadyUsedRegistered =
      "Number phone already used/registered";
  static const String province = "Province";
  static const String district = "District";
  static const String currentTotalSpending = "Current total spending";
  static const String collectPointToReachValue =
      "collect point points to reach value";
  static const String spending = "Spending";
  static const String confirmBuyDeal =
      "Are you sure you want to buy this deal?";
  static const String alreadyHaveDeal =
      "You already have this deal. want to buy again?";
  static const String useDeals = "Use Deals";
  static const String remainingPoint = "remaining point";
  static const String valueEarned = "value earned";
  static const String passedThisLevel =
      "Awesome achievement! You've passed the level!";
  static const String myVoucher = "My voucher";
  static const String refundMessage =
      "Are you absolutely certain about refunding this voucher?";
  static const String voucherOnlyValid = "voucher is only valid until";
  static const String redeemVoucherByShowingQR =
      "Redeem your voucher by showing the QR code to the cashier!";
  static const String keepYourLevelEveryPurchase =
      "Keep your level with every purchase!";
  static const String reachNewLevelMakingPurchase =
      "Reach new levels by making purchases!";
  static const String spendMoreToLevelUp =
      "Spend more to reach the next level!";
  static const String keepEnjoyBenefit =
      "Keep enjoying your benefits by earning points regularly";
  static const String buy = "Buy";
  static const String provinceFieldEmpty =
      "Province field empty. Please fill it in to complete your profile";
  static const String add = "Add";
  static const String free = "Free";
  static const String noPromotionFound =
      "No promotions at the moment. Check back soon";
  static const String youAchievedHigherLevel =
      "You've achieved a higher level! Keep it up";
  static const String price = "Price";
  static const String getSecretId = "Get secret id";
  static const String remainingQuota = "Remaining quota";
  static const String promotionDescription = "Promo Descriptions";
  static const String transactionSuccess = "Purchase Transaction Successfully";
  static const String costBuyDeal =
      "It will cost you 0 points from your account.";
  static const String selectAProvinceBelow =
      "please select your domicile province";
  static const String selectADistrictOrCityBelow =
      "Please select a district/city below";
  static const String promoDoNotMissIt =
      "Promo opens in 5 minutes, you know... don't miss it";
  static const String reminderHasBeenCreated =
      "ready, reminder has been created";
  static const String reminderAlreadyCreated =
      "Don't worry, your reminder has been created";
  static const String pleaseLoginToContinue = "Please login to continue";
  static const String payment = "Payment";
  static const String valueNotFound = "value not found";
  static const String voucherWithCastCantRefund =
      "Vouchers with cash purchases are non-refundable";
  static const String thankYou = "Thank You";
  static const String cancelled = "Cancelled";
  static const String submit = "submit";
  static const String retry = "retry";
  static const String changePhoto = "change photo";
  static const String addPhoto = "add photo";
  static const String selectImageFrom = "select image from";
  static const String camera = "Camera";
  static const String gallery = "Gallery";
  static const String selectVerificationCodeMethod =
      "Select Verification Code Delivery Method";
  static const String sendViaValue = "Send via value";
  static const String overQuota = "over quota";
  static const String details = "details";
  static const String orderIsBeingConfirmed =
      "order is being confirmed, please wait";
  static const String orderRejected = "Order Rejected";
  static const String paymentBeingVerified =
      "payment is being verified, cannot change the payment method";
  static const String cannotChangePayment = "cannot change the payment method";
  static const String tableNumber = "Table number";
  static const String errorReason =
      "Something went wrong, make sure your internet is connected";
  static const String orderStatus = "Order status";
  static const String orderPickupDesc =
      "You'll collect your order at the store";
  static const String orderDeliveryDesc =
      "Have order delivered to your doorstep";
  static const String orderSelfOrderDesc =
      "Customize your order at the counter";
  static const String orderDineInDesc = "Enjoy table service at the store";

  // Order Status
  static const String statusPending = "Waiting for confirmation";
  static const String statusCancel = "Canceled";
  static const String statusAccept = "Waiting for payment";
  static const String statusPaymentVerification =
      "Waiting for Payment Verification";
  static const String statusProcess = "Process";
  static const String statusPaymentVerified = "Verified Payment";
  static const String statusPaymentReject = "Payment Rejected";
  static const String statusReject = "Rejected";
  static const String statusReceived = "Finished";
  static const String statusArrived = "Arrive at Destination";
  static const String statusExpired = "Expired";
  static const String statusAlreadyPaid = "Already Pain";
  static const String statusReady = "Your order is ready";
  static const String theItemInCartIsUnavailable =
      "The item in your cart is currently unavailable. However, we are working to replenish it soon. Thank you for your patience!";

  // Time
  static const String timeToday = "Today";
  static const String timeYesterday = "Yesterday";
  static const String timeDaysAgo = "value days ago";
  static const String timeWeeksAgo = "value weeks ago";
  static const String timeMonthsAgo = "value months ago";
  static const String timeYearsAgo = "value years ago";
  static const String timePrev7D = "Previous 7 Days";
  static const String timePrev30D = "Previous 30 Days";

  //Days
  static const String sunday = "Sunday";
  static const String monday = "Monday";
  static const String tuesday = "Tuesday";
  static const String wednesday = "Wednesday";
  static const String thursday = "Thursday";
  static const String friday = "Friday";
  static const String saturday = "Saturday";
  static const String everyDay = "Every day";

  // Validator
  static const String valueCannotEmpty = "Value cannot be empty";
  static const String invalidValue = "Invalid value";

  // Feedback
  static const String veryBad = "Very Bad";
  static const String bad = "bad";
  static const String normal = "normal";
  static const String good = "good";
  static const String excellent = "excellent";
  static const String thankYouForFeedback =
      "Thank you for your feedback! We appreciate your input and look forward to serving you again.";
  static const String describeYourExperience = "Describe your experience here";
  static const String howWasYourOrder = "How was your order? Tell us about it";
  static const String clickAppWithTimeValue =
      "Click on the OVO app and open the notification to proceed with your payment within";
  static const String loveToHearHowYourOrderWas =
      "We'd love to hear how your order was! Please share your rating and any feedback you have. You can even upload a photo of your order to help us better understand your experience.";

  // network
  static const String errorOccurredWhileCommunicationWithServer =
      "Can't communicate with the server right now. Please check back later";

  // Gender
  static const String male = "Male";
  static const String female = "Female";

  // Method
  static const String sms = "Message";
  static const String whatsapp = "WhatsApp";

  // In App Update Exception
  static const String noErrorOccurred = "No error occurred";
  static const String anUnknownErrorOccurred = "An unknown error occurred";
  static const String theAPIisNotAvailableOnThisDevice =
      "The API is not available on this device";
  static const String theRequestThatWasSentByTheAppIsMalformed =
      "The request that was sent by the app is malformed";
  static const String theInstallIsUnavailableToThisUserOrDevice =
      "The install is unavailable to this user or device";
  static const String theDownloadInstallIsNotAllowed =
      "The download/install is not allowed, due to the current device state (e.g. low battery, low disk space, ...)";
  static const String theInstallUpdateHasNotBeenDownloaded =
      "The install/update has not been (fully) downloaded yet";
  static const String theInstallIsAlreadyInProgress =
      "The install is already in progress and there is no UI flow to resume";
  static const String thePlayStoreAppIsEitherNotInstalled =
      "The Play Store app is either not installed or not the official version";
  static const String theAppIsNotOwnedByAnyUserOnThisDevice =
      "The app is not owned by any user on this device. An app is \"owned\" if it has been acquired from Play";
  static const String anInternalErrorHappenedInThePlayStore =
      "An internal error happened in the Play Store";

  // Firebase Auth Exception
  static const String yourEmailAddressIncorrect =
      "Your email address appears to be incorrect or mistyped";
  static const String thePasswordIncorrect =
      "The password you entered is incorrect. Please try again";
  static const String theUserDoesNotExist =
      "The user account associated with this email does not exist";
  static const String theUserHasBeenDisabled =
      "The user account associated with this email has been disabled. Please contact support for further assistance";
  static const String thereHaveBeenTooManyAttempts =
      "There have been too many attempts to access your account due to unusual activity. Please try again later or contact support";
  static const String theOptionToSignInNotAvailable =
      "The option to sign in with an email and password is currently unavailable. Please try again later or contact support";
  static const String thisEmailAlreadyBeenRegistered =
      "This email address has already been registered with an account";
  static const String invalidCertHash = "Login failed. Please try again later";
  static const String thePhoneNumberInvalidFormatTooLong =
      "The phone number you entered is invalid. Phone number is too long";
  static const String thePhoneNumberInvalidFormat =
      "The phone number you entered is invalid. Please check the number and try again";
  static const String theLinkIsInvalidExpiredOrAlreadyUsed =
      "The link you used to sign in is either invalid, expired, or has already been used. Please try signing in again with a valid link";
  static const String theVersionAppNotAuthorized =
      "The version of the application you are using is not authorized. Please update the application to continue";
  static const String weAreUnableToPerformRequest =
      "We are unable to perform your request at this time. Please try again later";
  static const String theOTPCodeIsInvalid =
      "The OTP code you entered is invalid. Please check the code and try again";
  static const String theSmsCodeYouEnteredHasExpired =
      "The SMS code you entered has expired. Please re-send the verification code to try again";
  static const String pleaseLogInAgain =
      "Please log in again after changing your email address";
  static const String yourSessionHasExpired =
      "Your session has expired. Please sign in again to continue";
  static const String networkErrorOccurred =
      "A network error occurred. Please check your connection and try again";

  // Wishlist
  static const String valueRemovedFromWishlist =
      "Wishlist updated. Value removed from wishlist";
  static const String valueAddedToWishlist =
      "Wishlist updated. Value added to wishlist";
  static const String valueAlreadyInYourWishlist =
      "Value already in your wishlist";
  static const String valueAddedToYourNewWishlistCategory =
      "Success! value added to your new wishlist category";

  //
  static const String scheduleDelivery = "Schedule Delivery";
  static const String pickDateTime = "pick time";
  static const String deliveryPrice = "Delivery Price";

  // Recording related strings
  static const String tapToStartRecording = "tapToStartRecording";
  static const String tapToStopRecording = "tapToStopRecording";
  static const String listening = "listening";

  static const String speechNotAvailable =
      'Speech recognition is not available';
  static const String speechInitError = 'Error initializing speech recognition';
  static const String speechListenError = 'Error while listening';
  static const String speechError =
      'Something went wrong with speech recognition';

  // terms & conditions
  static const String byRegistering = "By Registering";
  static const String byEntering = "By Entering the application";
  static const String agreeWith = "you agree with";
  static const String termCondition = "Terms & Conditions";
  static const String and = "and";
  static const String privacyPolicy = "Privacy Policy";

  static const String rounding = "Rounding";

  static const String pickup = "Buy & Pickup";
  static const String delivery = "Buy & Delivery Now";

  static const String profileDetail = "profile_detail";
  static const String nameCannotBeChanged = "name_cannot_be_changed";
  static const String contactAdminToChangeName = "contact_admin_to_change_name";
  static const String dateOfBirthCannotBeChanged =
      "date_of_birth_cannot_be_changed";
  static const String contactAdminToChangeDOB = "contact_admin_to_change_dob";
  static const String updateGender = "update_gender";

  static const String pleaseVerifyInformation = "please_verify_information";
  static const String nameAndDOBCannotBeChanged =
      "name_and_dob_cannot_be_changed";
  static const String isInformationCorrect = "is_information_correct";
  static const String noNeedToEdit = "no_need_to_edit";
  static const String yesContinue = "yes_continue";

  static const String contactAdmin = "Contact Admin";
}
