import 'package:mobile_crm/core/values/app_strings.dart';

Map<String, String> langEn = {
  Strings.login: 'Login',
  Strings.signup: 'Sign Up',
  Strings.welcome: 'Welcome back, @user',
  Strings.welcomeGuest: 'Hello, Guest',
  Strings.provinceFieldEmpty:
      'Province field empty. Please fill it in to complete your profile',
  Strings.lostNumber: 'Lost Phone Number?',
  Strings.register: 'Register',
  Strings.phoneNumber: 'Phone Number',
  Strings.whatsappNumber: 'WhatsApp Number',
  Strings.signInGoogle: 'Sign In With Google',
  Strings.memberNotFound: "Oops! We couldn't find your member. Please sign up",
  Strings.signInApple: 'Sign In With Apple',
  Strings.selectVerificationCodeMethod:
      'Select verification code delivery method',
  Strings.dontHaveAccount: 'Don\'t have an account?',
  Strings.listOutlet: 'Outlet List',
  Strings.sendViaValue: 'Send via @value',
  Strings.sms: 'Message',
  Strings.youAchievedHigherLevel:
      "You have reached the @value level! Keep it up",
  Strings.noPromotionFound: "No deals at the moment. Check back soon",
  Strings.notes: 'Notes',
  Strings.optional: 'Optional',
  Strings.noActivePayment: 'No payments to worry about',
  Strings.activeTransaction: 'Active Transaction',
  Strings.whatsapp: 'WhatsApp',
  Strings.name: 'Name',
  Strings.email: 'Email',
  Strings.verificationCode: 'Verification Code',
  Strings.getSecretId: 'Get secret id',
  Strings.scanToEarnPoint: 'Earn more points by scanning during checkout!',
  Strings.resend: 'Resend',
  Strings.verification: 'Verification',
  Strings.enterCodeSent: 'Enter the code sent to @value',
  Strings.didntReceiveCode: 'Didn\'t Receive Code?',
  Strings.address: 'Address',
  Strings.gender: 'Gender',
  Strings.dateOfBirth: 'Date of Birth',
  Strings.newAccount: 'New account',
  Strings.back: 'Back',
  Strings.promoQuotaIsOver: 'Promo quota is over',
  Strings.accountRegisteredSuccessfully: 'Account Registered Successfully',
  Strings.emailAlreadyUsedRegistered: 'Email already used/registered',
  Strings.phoneAlreadyUsedRegistered: 'Phone number already used/registered',
  Strings.province: 'Province',
  Strings.district: 'District / City',
  Strings.selectAProvinceBelow: 'please select your domicile province',
  Strings.selectADistrictOrCityBelow:
      'Please select a district/city from "@parent"',
  Strings.textCopied: 'Text Copied',
  Strings.updateEmailUnverified:
      "Take the first step to update your email - verify your account",
  Strings.promoApplied: 'Promo applied',
  Strings.saving: 'Saving',
  Strings.all: 'All',
  Strings.verify: 'Verify',
  Strings.verifyNow: 'Verify now',
  Strings.profile: 'Profile',
  Strings.logout: 'Logout',
  Strings.addressLabel: 'Address label',
  Strings.notAvailable: 'Not available',
  Strings.deleteAccount: 'Delete Account',
  Strings.waitingForPayment: 'Waiting for payment',
  Strings.waitingForValue: 'Waiting for @value',
  Strings.waitingForPaymentStatus: 'Waiting for payment status',
  Strings.sumPromosAreAvailable: '@sum promos are available',
  Strings.updateValue: 'Update @value',
  Strings.continuePayment: 'Continue payment',
  Strings.proceedToOrder: 'Proceed to order',
  Strings.backHome: 'Back to home',
  Strings.paymentSuccess: 'Payment successful',
  Strings.orSignInUsing: 'or Sign in using',
  Strings.orSignIn: 'or Sign in',
  Strings.paymentFailed: 'Payment failed',
  Strings.newValue: 'New @value',
  Strings.changePhoneNumberWillReqOTP:
      'Change phone number will require One Time Password verification on the current email',
  Strings.pleaseInputYourNewValue: 'Please input your new @value',
  Strings.pleaseInputYourValue: 'Please input your @value',
  Strings.pleaseInputYourPaymentValue: 'Please input your @payment @value',
  Strings.transferAmount: 'Transfer amount',
  Strings.payAmount: 'Pay amount',
  Strings.valueUpdated: '@value Updated',
  Strings.addressBasedOnProfile: 'Address based on profile',
  Strings.getLocation: 'Get Location',
  Strings.guest: 'Guest',
  Strings.dontMissOpportunityToEarnPoint:
      'Don\'t miss the opportunity to earn points! Enjoy exclusive offers and tempting promotions for various types of products. Log in now!',
  Strings.dontMissOpportunityToEarnValuePoint:
      'Don\'t miss the opportunity to earn @value points! Enjoy exclusive offers and tempting promotions for various types of products. Log in now!',
  Strings.exTableNumber8: 'e.g table number 8',
  Strings.exAddress: 'e.g Jl. Indonesia Barat 01',
  Strings.receiptPhoneNumber: 'Recipient\'s phone number',
  Strings.termsAndConditions: 'Terms and conditions',
  Strings.aboutPromo: 'About this promo',
  Strings.selectProofOfPayment: 'Select the proof of payment image',
  Strings.sendProofOfPayment: 'Send Proof of Payment',
  Strings.changeImage: 'Change Image',
  Strings.notification: 'Notification',
  Strings.orderCreated: 'Order successfully created',
  Strings.order: 'Order',
  Strings.yourOrder: 'Your order',
  Strings.order2: 'Order',
  Strings.createAnOrder: 'Create an order',
  Strings.listPromotion: 'Promotion list',
  Strings.addToCart: 'Add to cart',
  Strings.saveConversion: 'Save conversion',
  Strings.unitConversion: 'Unit conversion',
  Strings.updateCart: 'Update cart',
  Strings.addAnother: 'Add another',
  Strings.orderMessage:
      'Orders will not be processed until you show this QR code to the cashier',
  Strings.orderMessage2: 'Payment can only be made at the cashier',
  Strings.unavailableCart:
      'Unable to proceed with the purchase because the item in the cart is currently unavailable',
  Strings.someCartUnavailable:
      'The items below are currently unavailable, would you like to continue?',
  Strings.confirmLogout: 'do you want to logout?',
  Strings.chooseTime: 'Choose a time, when your order will be taken',
  Strings.chooseVariant: 'Choose variant',
  Strings.pickupWhenReady: 'You can pickup your order once it ready',
  Strings.whereDestination: 'Where is the delivery destination?',
  Strings.selfOrderDesc:
      'Place orders without queuing, select menus without having to go to the cashier, just show the QR code and make payments',
  Strings.statusPending: 'Waiting for confirmation',
  Strings.statusCancel: 'Canceled',
  Strings.redeemVoucherByShowingQR:
      'Redeem your voucher by showing the QR code to the cashier!',
  Strings.statusAccept: 'Waiting for payment',
  Strings.noProductsToOrder: 'There are no products to order',
  Strings.openWork: 'Open @time',
  Strings.sorryTheValueIsClosed: 'Sorry, the @value is closed',
  Strings.sorryTheValueIsNotAvailableAtThisTime:
      'Sorry, this "@value" order type is not available at this time',
  Strings.open: 'Open',
  Strings.add: 'Add',
  Strings.retry: 'Retry',
  Strings.price: 'Price',
  Strings.orderPickupDesc: "You'll collect your order at the store",
  Strings.orderDeliveryDesc: 'Have order delivered to your doorstep',
  Strings.orderSelfOrderDesc: 'Customize your order at the counter',
  Strings.orderDineInDesc: 'Enjoy table service at the store',
  Strings.removeFromCart: 'Remove From Cart',
  Strings.noInformation: 'No information, ',
  Strings.invalidPhoneNumber: 'invalid phone number',
  Strings.reachNewLevelMakingPurchase: 'Reach new levels by making purchases!',
  Strings.myVoucher: 'My voucher',
  Strings.refundMessage:
      'Are you absolutely certain about refunding this voucher?',
  Strings.connectionTimeOut:
      'Connection timed out. Please check your internet connection',
  Strings.internetConnectionRestored: 'Internet Connection Restored',
  Strings.noInternetConnection: 'No Internet Connection',
  Strings.openingHours: 'Opening hours',
  Strings.closeWork: 'Closes @time',
  Strings.available: 'Available',
  Strings.promotionDescription: 'Promotion Description',
  Strings.voucherOnlyValid: 'Voucher is only valid until @time',
  Strings.remainingPoint: 'Remaining @name point',
  Strings.valueEarned: '@value earned',
  Strings.passedThisLevel: 'Awesome achievement! You\'ve passed the level!',
  Strings.spendMoreToLevelUp:
      'Spend Rp.@value or get @point Points to reach the next level!',
  Strings.keepEnjoyBenefit:
      'Keep enjoying @value benefits by earning points regularly',
  Strings.keepYourLevelEveryPurchase: 'Keep your level with every purchase!',
  Strings.collectPointToReachValue: 'Collect @point points to reach @value',
  Strings.currentTotalSpending: 'Current total spending',
  Strings.spending: 'Spending',
  Strings.availableIn: 'Available in',
  Strings.availableOn: 'Available on',
  Strings.buyDeal: 'Buy deals',
  Strings.dealsNotYetOpened: 'Deals not yet opened',
  Strings.tableNumber: 'Table number',
  Strings.orderStatus: 'Order status',
  Strings.close: 'Close',
  Strings.orderType: 'Choose order type',
  Strings.male: 'Male',
  Strings.female: 'Female',
  Strings.cancelTransaction: 'Are you sure you want to cancel the transaction?',
  Strings.removeItemFromCart: 'Remove Item @name from cart?',
  Strings.accountDeleted: 'Account Deleted Successfully',
  Strings.phoneNumberUsed:
      'The phone number is already registered, please use another phone number',
  Strings.emailVerifyHasSent: 'Email verification has been sent to your email',
  Strings.outletClosed: 'Outlets closed',
  Strings.exampleNoteOrder: 'e.g please give it to the security guard',
  Strings.exampleNoteOrder2: 'Any note for store?',
  Strings.statusPaymentVerification: 'Waiting for Payment Verification',
  Strings.statusProcess: 'Process',
  Strings.statusPaymentVerified: 'Verified Payment',
  Strings.statusPaymentReject: 'Payment Rejected',
  Strings.statusReject: 'Rejected',
  Strings.statusReceived: 'Finished',
  Strings.statusArrived: 'Arrive at Destination',
  Strings.statusExpired: 'Expired',
  Strings.statusAlreadyPaid: 'Already Paid',
  Strings.statusReady: 'Your order is ready',
  Strings.theItemInCartIsUnavailable:
      'The item in your cart is currently unavailable, and will be automatically removed from the cart. However, we are working to fill it soon. Thank you for your patience!',
  Strings.searchHistory: 'Search History',
  Strings.proofPayment: 'Proof of payment',
  Strings.purchaseTime: 'Purchase date',
  Strings.whereOrderBeDelivered:
      'Where will the order be delivered? address is required',
  Strings.availableDeals: 'Available Deals',
  Strings.appliedVouchers: 'Applied vouchers',
  Strings.validityPeriod: 'Validity period',
  Strings.minimumOrder: 'Minimum order',
  Strings.discount: 'Discount',
  Strings.maxDiscount: 'Maximum discount @discount}',
  Strings.specialPrice: 'Special price',
  Strings.maxPurchaseAmount: 'Max. purchase amount @count',
  Strings.minPurchaseAmount: 'Min. purchase amount @count',
  Strings.termProduct: 'Product terms',
  Strings.fillNumber: 'Please fill in your number in the phone number field',
  Strings.listItem: 'List Item',
  'Add to cart': 'Add to cart',
  'Not Informed': 'Not Informed',
  Strings.destination: 'Destination',
  Strings.description: 'Description',
  Strings.shippingCosts: 'Shipping costs',
  Strings.makeSureTheOrderIsCorrect: 'Make sure the order is correct',
  'Cart Detail': 'Cart Detail',
  'Shipping': 'Shipping',
  Strings.free: 'Free',
  Strings.later: 'Later',
  Strings.toVerifyUsedLoginButton:
      'To verify, you can use the login button by filling in the phone number',
  Strings.valueNotVerified: '@value not verified',
  Strings.timeToday: 'Today',
  Strings.verifyValue: 'Verify @value',
  Strings.timeYesterday: 'Yesterday',
  Strings.timeDaysAgo: '@value day ago',
  Strings.timeWeeksAgo: '@value week ago',
  Strings.timeMonthsAgo: '@value month ago',
  Strings.timeYearsAgo: '@value year ago',
  Strings.timePrev7D: 'Previous 7 Days',
  Strings.timePrev30D: 'Previous 30 Days',
  Strings.unit: 'Unit : @unit',
  Strings.availableAt: 'Available at @outlet',
  Strings.openEWallet:
      'Open your favorite E-Wallet application and Scan the QR Code to make payments',
  Strings.thankYouBuyingPromo:
      'Thank you for buying our products. Please complete payment before @date',
  Strings.productPrice: 'Product Price',
  Strings.additionalMenu: 'Additional Menu',
  Strings.productQuantity: 'Product Quantity',
  Strings.paymentSummary: 'Payment Summary',
  Strings.outletName: '@outlet Name',
  Strings.customerName: 'Customer Name',
  Strings.orderCode: 'Order Code',
  Strings.subTotal: 'Sub Total',
  Strings.totalTaxes: 'Total Taxes',
  Strings.done: 'Done',
  Strings.selectOne: 'Select one',
  Strings.selectMultiple: 'Select Multiple',
  Strings.save: 'Save',
  Strings.saved: 'Saved',
  Strings.newCategory: 'New category',
  Strings.addCategory: 'Add category',
  Strings.nameYourCategory: 'Name your category',
  Strings.categoryName: 'Category name',
  Strings.valueSavedWishlist: '@value saved wishlists',
  Strings.category: 'Category',
  Strings.addNoteToNote: 'Add notes to your @note',
  Strings.addNote: 'Add notes',
  Strings.exampleNoteTo: 'e.g please, add extra tomato sauce',
  Strings.terms: 'Terms',
  Strings.availableUntil: 'Available until',
  Strings.minimumTransaction: 'Min. transaction',
  Strings.viewMore: 'View more',
  Strings.redeemPeriod: 'Redeem Period',
  Strings.productNotAvailable: 'Product is not available',
  Strings.notifyMe: 'Notify Me',
  Strings.choosePayment: 'Choose payment',
  Strings.chooseAddress: 'Choose address',
  Strings.payBtn: 'Pay',
  Strings.paymentMethod: 'Payment method',
  Strings.details: 'Details',
  Strings.selectPaymentMethod: 'Choose a payment method',
  Strings.totalBill: 'Total bill',
  Strings.pickupTime: 'Pick up time',
  Strings.cancel: 'Cancel',
  Strings.successRegisterBusiness:
      "Success! You're now a registered member of our business.",
  Strings.valueAlreadyVerified: '@value already verified',
  Strings.noPhoneNumberLinkedToThisEmail:
      'No phone number linked to this email. Please use a different Google account',
  Strings.whatsAppWontOpen:
      'WhatsApp won\'t open? Make sure it\'s properly installed',
  Strings.yourCodeHasBeenSent: 'Your code is on its way via message',
  Strings.haveNotReceivedOTP:
      'Haven\'t received the OTP code? Check messages or WhatsApp!',
  Strings.okay: 'Ok',
  Strings.alreadyRegisterBusiness:
      'Login to your existing account. You\'re already registered here!',
  Strings.registrationIsSuccessfulCheckWhatsapp:
      'All set with registration! Click link sent to your WhatsApp Number.',
  Strings.sendWhatsappLink: 'Send WhatsApp link',
  Strings.needHelpWithOTPCode:
      'Need help with the OTP code? Send a link to WhatsApp or wait for the message',
  Strings.yourNumberHasBeenChanged:
      'Your number has been successfully changed. Log in to continue.',
  Strings.deleteItem: 'Delete item',
  Strings.valueAlreadyUsePleaseUseAnotherValue:
      '@value already used. Please use another @value',
  Strings.delete: 'Delete',
  Strings.promoWillBeOpenSoon:
      'The promo you are looking for will be open soon',
  Strings.needLogin: 'Need to login, to use this feature',
  Strings.promoDoNotMissIt: 'Promo @deal opens in 5 minutes... don\'t miss it',
  Strings.reminderHasBeenCreated: 'Okay, reminder has been created',
  Strings.reminderAlreadyCreated:
      'Don\'t worry, your reminder has been created',
  Strings.shipmentCourier: 'Courier',
  Strings.tapAgainToReturnValue: 'Tap again to return @value',
  Strings.confirmBuyDeal: 'Are you sure you want to buy this deal?',
  Strings.costBuyDeal: 'It will cost you @value points from your account.',
  Strings.alreadyHaveDeal: 'You already have this deal. want to buy again?',
  Strings.useDeals: 'Use Deals',
  Strings.buy: 'Buy',
  Strings.until: 'Until',
  Strings.hoursLeft: 'Hours left',
  Strings.overQuota: 'Over Quota',
  Strings.orderIsBeingConfirmed: 'Order is being confirmed, please wait',
  Strings.orderRejected: 'Order Rejected',
  Strings.paymentBeingVerified:
      'Payment is being verified, cannot change the payment method',
  Strings.cannotChangePayment: 'cannot change the payment method',
  Strings.remainingQuota: 'Remaining quota',
  Strings.transactionSuccess: 'Purchase Transaction Successfully',
  Strings.shipmentReceiptNumber: 'Receipt Number',
  Strings.profOfDeliveryReceipt: 'Prof of delivery receipt',
  // Strings.waitingForPayment: 'Waiting for payment',
  Strings.pleaseMakePayment: 'Please make payment before @date',
  Strings.orderIsProcessed: 'Please wait, your order will be processed',
  Strings.pleaseLoginToContinue: 'Please login to continue',
  Strings.search: 'Search',
  Strings.getNew: 'Get new',
  Strings.camera: 'Camera',
  Strings.changePhoto: 'Change photo',
  Strings.addPhoto: 'Add photo',
  Strings.valueNotFound: '@value not found',
  Strings.voucherWithCastCantRefund:
      'Vouchers with cash purchases are non-refundable',
  Strings.gallery: 'Gallery',
  Strings.submit: 'Submit',
  Strings.selectImageFrom: 'Select image from',
  Strings.createNew: 'Create a new account',
  Strings.next: 'Continue',
  Strings.payment: 'Payment',
  Strings.thankYou: 'Thank You',
  Strings.cancelled: 'Cancelled',
  Strings.refreshPaymentStatus: 'Update payment status',
  Strings.clickAppWithTimeValue:
      'Click on the @app app and open the notification to proceed with your payment within',
  Strings.dontLetDataLost:
      'Don\'t let your data be lost, come on, replace your lost number immediately',
  Strings.addNewAddress: 'Add new address',
  'Add Order Note': 'Add Order Note',
  'Make Sure You Log In First': 'Make Sure You Log In First',
  'Enter 6 digits verification code sent to your number':
      'Enter 6 digits verification code sent to your number @number',
  'Time Left': 'Time Left',
  'Secret ID': 'Secret ID @secret',
  'Show the barcode to the cashier to order':
      'Show the barcode to the cashier to order',
  'Order Code :': 'Order Code : @value',
  'Confirm': 'Confirm',
  'Do you want to change the order or want to cancel the order?':
      'Do you want to change the order or want to cancel the order?',
  'Make sure the qrcode has been scanned by the cashier':
      'Make sure the qrcode has been scanned by the cashier',

  // Days
  Strings.sunday: "Sunday",
  Strings.monday: "Monday",
  Strings.tuesday: "Tuesday",
  Strings.wednesday: "Wednesday",
  Strings.thursday: "Thursday",
  Strings.friday: "Friday",
  Strings.saturday: "Saturday",
  Strings.everyDay: "Every day",

  // validator
  Strings.valueCannotEmpty: "@value cannot be empty",
  Strings.invalidValue: "Invalid @value",

  Strings.makeSureAddress:
      "Make sure you input your Domiciled Address, not your original address",
  Strings.yesMyDomiciled: "Yes, it's my Domiciled Address",

  // In App Update
  Strings.noErrorOccurred: "No error occurred",
  Strings.anUnknownErrorOccurred: "An unknown error occurred",
  Strings.theAPIisNotAvailableOnThisDevice:
      "The API is not available on this device",
  Strings.theRequestThatWasSentByTheAppIsMalformed:
      "The request that was sent by the app is malformed",
  Strings.theInstallIsUnavailableToThisUserOrDevice:
      "The install is unavailable to this user or device",
  Strings.theDownloadInstallIsNotAllowed:
      "The download/install is not allowed, due to the current device state (e.g. low battery, low disk space, ...)",
  Strings.theInstallUpdateHasNotBeenDownloaded:
      "The install/update has not been (fully) downloaded yet",
  Strings.theInstallIsAlreadyInProgress:
      "The install is already in progress and there is no UI flow to resume",
  Strings.thePlayStoreAppIsEitherNotInstalled:
      "The Play Store app is either not installed or not the official version",
  Strings.theAppIsNotOwnedByAnyUserOnThisDevice:
      "The app is not owned by any user on this device. An app is \"owned\" if it has been acquired from Play",
  Strings.anInternalErrorHappenedInThePlayStore:
      "An internal error happened in the Play Store",

// Firebase Auth Exception
  Strings.yourEmailAddressIncorrect:
      "Your email address appears to be incorrect or mistyped",
  Strings.thePasswordIncorrect:
      "The password you entered is incorrect. Please try again",
  Strings.theUserDoesNotExist:
      "The user account associated with this email does not exist",
  Strings.theUserHasBeenDisabled:
      "The user account associated with this email has been disabled. Please contact support for further assistance",
  Strings.thereHaveBeenTooManyAttempts:
      "There have been too many attempts to access your account due to unusual activity. Please try again later or contact support",
  Strings.theOptionToSignInNotAvailable:
      "The option to sign in with an email and password is currently unavailable. Please try again later or contact support",
  Strings.thisEmailAlreadyBeenRegistered:
      "This email address has already been registered with an account",
  Strings.invalidCertHash: "Login failed. Please try again later",
  Strings.thePhoneNumberInvalidFormatTooLong:
      "The phone number you entered is invalid. Phone number is too long",
  Strings.thePhoneNumberInvalidFormat:
      "The phone number you entered is invalid. Please check the number and try again",
  Strings.theLinkIsInvalidExpiredOrAlreadyUsed:
      "The link you used to sign in is either invalid, expired, or has already been used. Please try signing in again with a valid link",
  Strings.theVersionAppNotAuthorized:
      "The version of the application you are using is not authorized. Please update the application to continue",
  Strings.weAreUnableToPerformRequest:
      "We are unable to perform your request at this time. Please try again later",
  Strings.theOTPCodeIsInvalid:
      "The OTP code you entered is invalid. Please check the code and try again",
  Strings.theSmsCodeYouEnteredHasExpired:
      "The SMS code you entered has expired. Please re-send the verification code to try again",
  Strings.pleaseLogInAgain:
      "Please log in again after changing your email address",
  Strings.yourSessionHasExpired:
      "Your session has expired. Please sign in again to continue",
  Strings.networkErrorOccurred:
      "A network error occurred. Please check your connection and try again",

  // Network
  Strings.errorOccurredWhileCommunicationWithServer:
      "Can't communicate with the server right now. Please check back later",

  // Feedback
  Strings.veryBad: "Very Bad",
  Strings.bad: "Bad",
  Strings.normal: "Normal",
  Strings.good: "Good",
  Strings.excellent: "Excellent",
  Strings.thankYouForFeedback:
      "Thank you for your feedback! We appreciate your input and look forward to serving you again.",
  Strings.describeYourExperience: "Describe your experience here...",
  Strings.howWasYourOrder: "How was your order? Tell us about it",
  Strings.loveToHearHowYourOrderWas:
      "We'd love to hear how your order was! Please share your rating and any feedback you have. You can even upload a photo of your order to help us better understand your experience.",

  // Wishlist
  Strings.valueRemovedFromWishlist:
      "Wishlist updated. @value removed from wishlist",
  Strings.valueAddedToWishlist: "Wishlist updated. @value added to wishlist",
  Strings.valueAlreadyInYourWishlist: "@value already in your wishlist",
  Strings.valueAddedToYourNewWishlistCategory:
      "Success! \"@value\" added to your new wishlist category",

  // Recording related translations
  Strings.tapToStartRecording: 'Tap to start recording',
  Strings.tapToStopRecording: 'Tap to stop recording',
  Strings.listening: 'Listening...',

  // terms & conditions
  Strings.byRegistering: "By Registering",
  Strings.byEntering: "By Entering the application",
  Strings.agreeWith: "you agree with",
  Strings.termCondition: "Terms & Conditions",
  Strings.and: "and",
  Strings.privacyPolicy: "Privacy Policy",

  Strings.rounding: "Rounding",

  Strings.pickup: "Buy & Pickup",
  Strings.delivery: "Buy & Delivery Now",
  Strings.profileDetail: 'Profile Detail',
  Strings.nameCannotBeChanged: 'Name Cannot Be Changed',
  Strings.contactAdminToChangeName: 'Please contact admin to change your name',
  Strings.dateOfBirthCannotBeChanged: 'Date of Birth Cannot Be Changed',
  Strings.contactAdminToChangeDOB:
      'Please contact admin to change your date of birth',
  Strings.updateGender: 'Update Gender?',
  Strings.pleaseVerifyInformation: 'Please Verify Your Information',
  Strings.nameAndDOBCannotBeChanged:
      'Name and Date of Birth cannot be changed after registration. Please make sure they are correct.',
  Strings.isInformationCorrect: 'Is this information correct?',
  Strings.noNeedToEdit: 'No, I Need to Edit',
  Strings.yesContinue: 'Yes, Continue',
};
