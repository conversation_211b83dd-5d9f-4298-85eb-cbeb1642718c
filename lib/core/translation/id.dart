// ignore_for_file: equal_keys_in_map

import 'package:mobile_crm/core/values/app_strings.dart';

Map<String, String> langId = {
  Strings.login: 'Masuk',
  Strings.signup: 'Daftar',
  Strings.welcome: 'Selamat Datang, @user',
  Strings.welcomeGuest: 'Halo, Tamu',
  Strings.lostNumber: 'Nomor telepon hilang?',
  Strings.provinceFieldEmpty:
      'Tampaknya Anda lupa mengisi provinsi Anda. Silakan isi untuk melengkapi profil Anda',
  Strings.phoneNumber: 'Nomor telepon',
  Strings.whatsappNumber: 'Nomor WhatsApp',
  Strings.newAccount: 'Akun baru',
  Strings.tableNumber: 'Nomor meja',
  Strings.orderStatus: 'Status pesanan',
  Strings.next: 'Selanjutnya',
  Strings.tapAgainToReturnValue: 'Ketuk lagi untuk kembali ke @value',
  Strings.selectVerificationCodeMethod:
      '<PERSON><PERSON><PERSON> metode pengiriman <PERSON> verifikasi',
  Strings.dontLetDataLost:
      'Jangan sampai data kamu hilang, ayo segera ganti nomor kamu yang hilang',
  Strings.signInGoogle: 'Login dengan Google',
  Strings.signInApple: 'Login dengan Apple',
  Strings.dontHaveAccount: 'Tidak Punya Akun?',
  Strings.listOutlet: 'Daftar Outlet',
  Strings.sendViaValue: 'Kirim melalui @value',
  Strings.noPromotionFound: "Tidak ada deals saat ini. Cek lagi nanti ya",
  Strings.name: 'Nama',
  Strings.youAchievedHigherLevel:
      "Anda telah mencapai level @value! Pertahankan",
  Strings.sms: 'Pesan',
  Strings.whatsapp: 'WhatsApp',
  Strings.invalidPhoneNumber: 'Nomor telepon tidak valid',
  Strings.memberNotFound:
      'Ups! Kami tidak dapat menemukan akun Anda. Silakan mendaftar',
  Strings.register: 'Daftar',
  Strings.email: 'Email',
  Strings.getSecretId: 'Dapatkan kode rahasia',
  Strings.verificationCode: 'Kode verifikasi',
  Strings.scanToEarnPoint:
      'Dapatkan lebih banyak poin dengan melakukan pemindaian saat melakukan checkout!',
  Strings.resend: 'Kirim ulang',
  Strings.verification: 'Verifikasi',
  Strings.enterCodeSent: 'Masukkan kode yang dikirim ke @value',
  Strings.didntReceiveCode: 'Tidak Menerima Kode?',
  Strings.address: 'Alamat',
  Strings.listPromotion: 'Daftar promosi',
  Strings.retry: 'Coba lagi',
  Strings.gender: 'Jenis Kelamin',
  Strings.openingHours: 'Jam buka',
  Strings.noInternetConnection: 'Tidak ada koneksi internet',
  Strings.internetConnectionRestored: 'Koneksi Internet Dipulihkan',
  Strings.back: 'Kembali',
  Strings.dateOfBirth: 'Tanggal lahir',
  Strings.emailAlreadyUsedRegistered: 'Email sudah digunakan/terdaftar',
  Strings.phoneAlreadyUsedRegistered: 'Nomor telepon sudah digunakan/terdaftar',
  Strings.province: 'Provinsi',
  Strings.district: 'Kabupaten / Kota',
  Strings.selectAProvinceBelow: 'Pilih provinsi domisili kamu',
  Strings.selectADistrictOrCityBelow: 'Pilih kabupaten / kota dari "@parent"',
  Strings.promoQuotaIsOver: 'Kuota promo habis',
  Strings.accountRegisteredSuccessfully: 'Akun Berhasil Didaftarkan',
  Strings.textCopied: 'Teks Disalin',
  Strings.spendMoreToLevelUp:
      'Belanjakan Rp.@value atau dapatkan @point Point untuk mencapai level berikutnya!',
  Strings.keepEnjoyBenefit:
      'Terus nikmati manfaat @value dengan mengumpulkan poin secara teratur.',
  Strings.keepYourLevelEveryPurchase:
      'Pertahankan levelmu dengan setiap pembelian yang kamu lakukan!',
  Strings.orderCreated: 'Pesanan berhasil dibuat',
  Strings.order: 'Pesanan',
  Strings.yourOrder: 'Pesanan anda',
  Strings.order2: 'Pesan',
  Strings.notification: 'Notifikasi',
  Strings.changeImage: 'Ganti gambar',
  Strings.selectProofOfPayment: 'Pilih gambar bukti pembayaran',
  Strings.sendProofOfPayment: 'Kirim Bukti Pembayaran',
  Strings.saving: 'Hemat',
  Strings.transferAmount: 'Jumlah transfer',
  Strings.payAmount: 'Jumlah pembayaran',
  Strings.termsAndConditions: 'Syarat dan Ketentuan',
  Strings.aboutPromo: 'Tentang promo ini',
  Strings.promoApplied: 'Promo diaplikasikan',
  Strings.createAnOrder: 'Buat pesanan',
  Strings.reachNewLevelMakingPurchase:
      'Capai level baru dengan melakukan pembelian!',
  Strings.collectPointToReachValue:
      'Kumpulkan @point poin untuk mencapai @value',
  Strings.currentTotalSpending: 'Total belanja saat ini',
  Strings.spending: 'Pengeluaran',
  Strings.myVoucher: 'Voucher saya',
  Strings.refundMessage:
      'Apakah Anda benar-benar yakin ingin mengembalikan voucher ini?',
  Strings.redeemVoucherByShowingQR:
      'Tukarkan vouchermu dengan menunjukkan kode QR ke kasir!',
  Strings.remainingPoint: '@name poin yang tersisa',
  Strings.passedThisLevel:
      'Pencapaian luar biasa! Kamu telah melewati level ini!',
  Strings.addToCart: 'Tambah keranjang',
  Strings.saveConversion: 'Konversi satuan',
  Strings.updateCart: 'Ubah keranjang',
  Strings.addAnother: 'Tambahkan yang lain',
  Strings.orderMessage:
      'Pesanan belum akan diproses sebelum anda menunjukkan qr code ini ke kasir',
  Strings.orderMessage2: 'Pembayaran hanya dapat dilakukan di kasir',
  Strings.unavailableCart:
      'Tidak dapat melanjutkan pembelian karena barang di keranjang saat ini tidak tersedia',
  Strings.someCartUnavailable:
      'item di bawah saat ini tidak tersedia, apakah Anda ingin melanjutkan?',
  Strings.noProductsToOrder: 'Tidak ada produk yang bisa dipesan',
  Strings.openWork: 'Buka @time',
  Strings.sorryTheValueIsClosed: 'maaf, @value tutup',
  Strings.sorryTheValueIsNotAvailableAtThisTime:
      'Maaf, jenis pesanan "@value" ini tidak tersedia saat ini',
  Strings.available: 'Tersedia',
  Strings.verifyNow: 'Verifikasi sekarang',
  Strings.voucherOnlyValid: 'Voucher hanya berlaku hingga @time',
  Strings.buyDeal: 'Beli deals',
  Strings.dealsNotYetOpened: 'Deals belum dibuka',
  Strings.availableIn: 'Tersedia dalam',
  Strings.availableOn: 'Tersedia pada',
  Strings.open: 'Buka',
  Strings.noInformation: 'Tidak ada informasi',
  Strings.closeWork: 'Tutup @time',
  Strings.close: 'Tutup',
  Strings.add: 'Tambah',
  Strings.price: 'Harga',
  Strings.removeFromCart: 'Hapus Dari Keranjang',
  Strings.promotionDescription: 'Deskripsi Promosi',
  Strings.confirmLogout: 'Apakah anda ingin logout?',
  Strings.chooseTime: 'pilih waktu, akan diambil kapan pesanan kamu',
  Strings.chooseVariant: 'Pilih varian',
  Strings.pickupWhenReady: 'Anda dapat mengambil pesanan Anda setelah siap',
  Strings.whereDestination: 'Kemana tujuan pengiriman?',
  Strings.selfOrderDesc:
      'Lakukan pemesanan tanpa antri, pilih menu tanpa harus ke kasir, cukup tunjukkan QR code dan lakukan pembayaran',
  Strings.statusPending: 'Menunggu Konfirmasi',
  Strings.statusCancel: 'Dibatalkan',
  Strings.statusAccept: 'Menunggu Pembayaran',
  Strings.orderType: 'Pilih jenis pesanan',
  Strings.male: 'Laki-laki',
  Strings.female: 'Perempuan',
  Strings.cancelTransaction: 'Anda yakin ingin membatalkan transaksi?',
  Strings.removeItemFromCart: 'Hapus item @name dari keranjang?',
  Strings.refreshPaymentStatus: 'Perbarui status pembayaran',
  Strings.clickAppWithTimeValue:
      'Klik ke aplikasi @app dan buka notifikasi untuk melanjutkan pembayaran anda dalam waktu',
  Strings.accountDeleted: 'Akun Berhasil Dihapus',
  Strings.phoneNumberUsed:
      'Nomor telepon sudah terdaftar, silakan gunakan nomor telepon lain',
  Strings.emailVerifyHasSent: 'Verifikasi email telah dikirim ke email Anda',
  Strings.outletClosed: 'Outlet tutup',
  Strings.exampleNoteOrder: 'contoh tolong kasih ke satpam ya',
  Strings.exampleNoteOrder2: 'Ada catatan untuk toko?',
  Strings.statusPaymentVerification: 'Menunggu Verifikasi Pembayaran',
  Strings.statusPaymentVerified: 'Pembayaran Terverifikasi',
  Strings.statusProcess: 'Proses',
  Strings.orderPickupDesc: "Ambil pesanan di toko atau tempat yang ditentukan",
  Strings.orderDeliveryDesc: 'Pesan antar makanan ke pintu rumah Anda',
  Strings.orderSelfOrderDesc: 'Pesan sesuai keinginan di tempat',
  Strings.orderDineInDesc: 'Nikmati layanan meja di restoran',
  Strings.statusPaymentReject: 'Pembayaran Ditolak',
  Strings.statusReject: 'Ditolak',
  Strings.notes: 'Catatan',
  Strings.optional: 'Opsional',
  Strings.statusReceived: 'Selesai',
  Strings.statusArrived: 'Tiba di Tujuan',
  Strings.statusExpired: 'Expired',
  Strings.statusAlreadyPaid: 'Sudah dibayar',
  Strings.statusReady: 'Pesanan Anda sudah siap',
  Strings.theItemInCartIsUnavailable:
      'Item di keranjang Anda saat ini tidak tersedia, dan akan dihapus secara otomatis dari keranjang. Namun, kami berupaya untuk segera mengisinya. Terima kasih atas kesabaran Anda!',
  Strings.noActivePayment: 'Tidak ada pembayaran yang tertunda atau aktif',
  Strings.activeTransaction: 'Transaksi AKtif',
  Strings.searchHistory: 'Riwayat Pencarian',
  Strings.proofPayment: 'Bukti pembayaran',
  Strings.purchaseTime: 'Tanggal pembelian',
  Strings.whereOrderBeDelivered:
      'Kemana pesanan akan diantar? alamat diperlukan',
  Strings.availableDeals: 'Voucher yang tersedia',
  Strings.appliedVouchers: 'Voucher yang dipakai',
  Strings.minimumOrder: 'Pemesanan minimum',
  Strings.discount: 'Diskon',
  Strings.maxDiscount: 'Diskon maksimal @discount',
  Strings.specialPrice: 'Harga spesial',
  Strings.maxPurchaseAmount: 'Maks. jumlah pembelian @count',
  Strings.minPurchaseAmount: 'Min. jumlah pembelian @count',
  Strings.termProduct: 'Ketentuan produk',
  Strings.validityPeriod: 'Masa berlaku',
  Strings.fillNumber: 'Silakan isi nomor Anda di kolom nomor telepon',
  Strings.listItem: 'Daftar barang',
  'Cart Detail': 'Detail Keranjang',
  'Add to cart': 'Tambah Keranjang',
  'Remove From Cart': 'Hapus Keranjang',
  'Not Informed': 'Tidak DIInfokan',
  Strings.destination: 'Lokasi tujuan',
  Strings.description: 'Deskripsi',
  Strings.shippingCosts: 'Biaya pengiriman',
  Strings.makeSureTheOrderIsCorrect: 'Pastikan pesanan sudah sesuai',
  'Shipping': 'Ongkos Kirim',
  Strings.free: 'Gratis',
  Strings.timeToday: 'Hari Ini',
  Strings.all: 'Semua',
  Strings.updateValue: 'Update @value',
  Strings.newValue: '@value Baru',
  Strings.valueUpdated: '@value Diperbarui',
  Strings.addressBasedOnProfile: 'Alamat berdasarkan profil',
  Strings.getLocation: 'Dapatkan Lokasi',
  Strings.receiptPhoneNumber: 'Nomor telepon penerima',
  Strings.addressLabel: 'Label alamat',
  Strings.orSignInUsing: 'atau Masuk menggunakan',
  Strings.exTableNumber8: 'contoh meja nomor 8',
  Strings.guest: 'Tamu',
  Strings.valueEarned: '@value diperoleh',
  Strings.dontMissOpportunityToEarnPoint:
      'Jangan lewatkan kesempatan untuk mendapatkan poin! Nikmati penawaran khusus serta promo menggoda untuk berbagai jenis barang. Segera login sekarang!',
  Strings.dontMissOpportunityToEarnValuePoint:
      'Jangan lewatkan kesempatan untuk mendapatkan @value poin! Nikmati penawaran khusus serta promo menggoda untuk berbagai jenis barang. Segera login sekarang!',
  Strings.exAddress: 'contoh Jl. Indonesia Barat 01',
  Strings.orSignIn: 'atau Masuk',
  Strings.notAvailable: 'Tidak tersedia',
  Strings.changePhoneNumberWillReqOTP:
      'Mengubah nomor telepon akan memerlukan OTP verifikasi pada email saat ini',
  Strings.pleaseInputYourNewValue: 'Silahkan masukan @value baru anda',
  Strings.pleaseInputYourValue: 'Silahkan masukan @value anda',
  Strings.pleaseInputYourPaymentValue: 'Silahkan masukan @value @payment anda',
  Strings.profile: 'Profil',
  Strings.continuePayment: 'Lanjutkan pembayaran',
  Strings.proceedToOrder: 'Lanjutkan untuk memesan',
  Strings.backHome: 'Kembali ke Home',
  Strings.paymentSuccess: 'Pembayaran berhasil',
  Strings.paymentFailed: 'Pembayaran gagal',
  Strings.waitingForPayment: 'Menunggu pembayaran',
  Strings.waitingForValue: 'Menunggu @value',
  Strings.waitingForPaymentStatus: 'Menunggu status pembayaran',
  Strings.sumPromosAreAvailable: 'Tersedia @sum promo',
  Strings.updateEmailUnverified:
      'Langkah pertama untuk memperbarui emailmu - verifikasi akunmu terlebih dahulu',
  Strings.logout: 'Keluar',
  Strings.verify: 'Verifikasi',
  Strings.later: 'Nanti',
  Strings.toVerifyUsedLoginButton:
      'Untuk memverifikasi, Anda dapat menggunakan tombol login dengan mengisi nomor telepon',
  Strings.valueNotVerified: '@value tidak terverifikasi',
  Strings.deleteAccount: 'Hapus akun',
  Strings.timeYesterday: 'Kemarin',
  Strings.timeDaysAgo: '@value hari yang lalu',
  Strings.timeWeeksAgo: '@value minggu yang lalu',
  Strings.timeMonthsAgo: '@value bulan yang lalu',
  Strings.timeYearsAgo: '@value tahun yang lalu',
  Strings.verifyValue: 'Verifikasi @value',
  Strings.timePrev7D: '7 Hari Sebelumnya',
  Strings.timePrev30D: '30 Hari Sebelumnya',
  Strings.unit: 'Satuan : @unit',
  Strings.availableAt: 'Tersedia di @outlet',
  Strings.openEWallet:
      'Buka aplikasi E-Wallet favorit Anda dan Scan QR Code untuk melakukan pembayaran',
  Strings.thankYouBuyingPromo:
      'Terima kasih telah membeli produk kami. Harap selesaikan pembayaran sebelum @date',
  Strings.productPrice: 'Harga Barang',
  Strings.additionalMenu: 'Menu Tambahan',
  Strings.productQuantity: 'Jumlah Barang',
  Strings.paymentSummary: 'Ringkasan Pembayaran',
  Strings.outletName: 'Nama @outlet',
  Strings.customerName: 'Nama Pelanggan',
  Strings.orderCode: 'Kode Pesanan',
  Strings.subTotal: 'Sub Total',
  Strings.totalTaxes: 'Jumlah Pajak',
  Strings.cancel: 'Batal',
  Strings.delete: 'Hapus',
  Strings.done: 'Selesai',
  Strings.selectOne: 'Pilih salah satu',
  Strings.selectMultiple: 'Pilih banyak',
  Strings.save: 'Simpan',
  Strings.saved: 'Tersimpan',
  Strings.newCategory: 'Kategori baru',
  Strings.addCategory: 'Tambah kategori',
  Strings.categoryName: 'Nama kategori',
  Strings.nameYourCategory: 'Beri nama kategori Anda',
  Strings.valueSavedWishlist: '@value wishlist tersimpan',
  Strings.category: 'Kategori',
  Strings.addNoteToNote: 'Tambahkan catatan ke @note',
  Strings.addNote: 'Tambah catatan',
  Strings.exampleNoteTo: 'misalnya tolong, tambahkan saus tomat ekstra',
  Strings.terms: 'Ketentuan',
  Strings.availableUntil: 'Tersedia sampai',
  Strings.minimumTransaction: 'Min. transaksi',
  Strings.viewMore: 'Lihat lainnya',
  Strings.redeemPeriod: 'Periode penggunaan',
  Strings.choosePayment: 'Pilih pembayaran',
  Strings.chooseAddress: 'Pilih alamat',
  Strings.payBtn: 'Bayar',
  Strings.paymentMethod: 'Metode Pembayaran',
  Strings.details: 'Detail',
  Strings.selectPaymentMethod: 'Pilih metode pembayaran',
  Strings.totalBill: 'Total tagihan',
  Strings.pickupTime: 'Waktu pengambilan',
  Strings.productNotAvailable: 'Produk tidak tersedia',
  Strings.notifyMe: 'Notify Me',
  Strings.shipmentCourier: 'Kurir',
  Strings.orderIsBeingConfirmed: 'Pesanan sedang dikonfirmasi, harap tunggu',
  Strings.orderRejected: 'Pesanan Ditolak',
  Strings.paymentBeingVerified:
      'Pembayaran sedang diverifikasi, tidak dapat mengubah metode pembayaran',
  Strings.cannotChangePayment: 'Tidak dapat mengubah metode pembayaran',
  Strings.shipmentReceiptNumber: 'Nomor Resi',
  Strings.profOfDeliveryReceipt: 'Bukti tanda terima pengiriman',
  Strings.pleaseMakePayment: 'Silahkan melakukan pembayaran sebelum @date',
  Strings.confirmBuyDeal: 'Apakah Anda yakin ingin membeli Deal ini?',
  Strings.costBuyDeal: 'Ini akan dikenakan biaya @value poin dari akun Anda.',
  Strings.alreadyHaveDeal: 'Anda sudah memiliki deal ini. mau beli lagi?',
  Strings.useDeals: 'Gunakan Deals',
  Strings.buy: 'Beli',
  Strings.until: 'Sampai',
  Strings.hoursLeft: 'Jam tersisa',
  Strings.overQuota: 'Melebihi Kuota',
  Strings.remainingQuota: 'Sisa kuota',
  Strings.transactionSuccess: 'Transaksi Pembelian Berhasil',
  Strings.orderIsProcessed: 'Mohon tunggu, pesanan Anda akan diproses',
  Strings.promoWillBeOpenSoon: 'Promo yang kamu cari akan segera dibuka',
  Strings.promoDoNotMissIt:
      'Promo @deal dibuka 5 menit lagi lho... jangan sampai kehabisan',
  Strings.reminderHasBeenCreated: 'Siap, pengingat telah dibuat',
  Strings.reminderAlreadyCreated:
      'Jangan khawatir, pengingat Anda telah dibuat',
  Strings.needLogin: 'Perlu login, untuk menggunakan fitur ini',
  Strings.pleaseLoginToContinue: 'Silahkan masuk untuk melanjutkan',
  Strings.search: 'Pencarian',
  Strings.getNew: 'Dapatkan yang baru',
  Strings.payment: 'Pembayaran',
  Strings.thankYou: 'Terima kasih',
  Strings.cancelled: 'Dibatalkan',
  Strings.okay: 'Oke',
  Strings.camera: 'Kamera',
  Strings.valueNotFound: '@value tidak ditemukan',
  Strings.voucherWithCastCantRefund:
      'Voucher dengan pembelian melalui uang tunai tidak dapat dikembalikan',
  Strings.gallery: 'Galeri',
  Strings.changePhoto: 'Ganti foto',
  Strings.addPhoto: 'Tambah foto',
  Strings.selectImageFrom: 'Pilih gambar dari',
  Strings.connectionTimeOut:
      'Waktu koneksi habis. Silakan periksa koneksi internet Anda.',
  Strings.successRegisterBusiness:
      'Success! Anda sekarang terdaftar dari bisnis kami.',
  Strings.submit: 'Kirim',
  Strings.noPhoneNumberLinkedToThisEmail:
      'Tidak ada nomor telepon yang terhubung dengan email ini. Silakan gunakan akun Google yang berbeda.',
  Strings.whatsAppWontOpen:
      'Tidak bisa membuka WhatsApp? Pastikan aplikasinya terinstal dengan benar',
  Strings.alreadyRegisterBusiness:
      'Login ke akun Anda yang sudah terdaftar. Anda sudah terdaftar di bisnis ini',
  Strings.sendWhatsappLink: 'kirim link Whatsapp',
  Strings.needHelpWithOTPCode:
      'Butuh bantuan dengan kode OTP? Kirim tautan ke WhatsApp atau kirim ulang kode otp dan tunggu pesan masuk',
  Strings.deleteItem: 'Hapus item',
  Strings.valueAlreadyVerified: '@value sudah diverifikasi',
  Strings.yourNumberHasBeenChanged:
      'Nomor kamu berhasil diubah. Silakan masuk untuk melanjutkan.',
  Strings.yourCodeHasBeenSent: 'Kode kamu sedang dikirim melalui pesan',
  Strings.haveNotReceivedOTP:
      'Belum menerima kode OTP? Cek pesan atau WhatsApp-mu ya',
  Strings.valueAlreadyUsePleaseUseAnotherValue:
      '@value sudah digunakan. Harap gunakan @value lain',
  Strings.createNew: 'Buat akun baru',
  Strings.registrationIsSuccessfulCheckWhatsapp:
      'Registrasi selesai! Klik link yang telah dikirimkan ke nomor WhatsApp.',
  Strings.addNewAddress: 'Tambahkan alamat baru',
  'Add Order Note': 'Tambahan Catatan Pesanan',
  'Make Sure You Log In First': 'Pastikan anda login terlebih dahulu',
  'Verification Code': 'Kode Verifikasi',
  'Time Left': 'Waktu Tersisa',
  'Secret ID': 'Secret ID @secret',
  'Show the barcode to the cashier to order':
      'Tunjukan barcode ke kasir untuk order',
  'Order Code :': 'Kode Pesanan : @value',
  'Confirm': 'Konfirmasi',
  'Do you want to change the order or want to cancel the order?':
      'Apakah Anda ingin mengubah pesanan atau ingin membatalkan pesanan?',
  'Make sure the qrcode has been scanned by the cashier':
      'Pastikan qrcode sudah di scan oleh kasir',

  // Days
  Strings.sunday: "Minggu",
  Strings.monday: "Senin",
  Strings.tuesday: "Selasa",
  Strings.wednesday: "Rabu",
  Strings.thursday: "Kamis",
  Strings.friday: "Jum'at",
  Strings.saturday: "Sabtu",
  Strings.everyDay: "Setiap hari",

  // Validator
  Strings.valueCannotEmpty: "@value tidak boleh kosong",
  Strings.invalidValue: "@value tidak valid",

  Strings.makeSureAddress:
      "Pastikan kamu memasukan Alamat Domisili kamu ya, bukan alamat asal!",
  Strings.yesMyDomiciled: "Ya, itu adalah Alamat Domisili saya",

  // In App Update
  Strings.noErrorOccurred: "Tidak ada kesalahan yang terjadi",
  Strings.anUnknownErrorOccurred: "Terjadi kesalahan yang tidak diketahui",
  Strings.theAPIisNotAvailableOnThisDevice:
      "Aplikasi yang ingin Anda gunakan tidak tersedia di perangkat Anda",
  Strings.theRequestThatWasSentByTheAppIsMalformed:
      "Ada masalah dengan permintaan yang dikirim oleh aplikasi sehingga tidak dapat diselesaikan",
  Strings.theInstallIsUnavailableToThisUserOrDevice:
      "Aplikasi tidak tersedia untuk diinstal pada perangkat Anda",
  Strings.theDownloadInstallIsNotAllowed:
      "Unduhan/pemasangan tidak dimungkinkan karena beberapa kondisi perangkat saat ini, seperti baterai lemah atau ruang penyimpanan yang tidak mencukupi.",
  Strings.theInstallUpdateHasNotBeenDownloaded:
      "Aplikasi belum sepenuhnya diunduh atau diinstal",
  Strings.theInstallIsAlreadyInProgress:
      "Pemasangan sudah berlangsung dan tidak ada antarmuka pengguna untuk melanjutkannya",
  Strings.thePlayStoreAppIsEitherNotInstalled:
      "Aplikasi Google Play Store tidak terinstal atau tidak versi resmi",
  Strings.theAppIsNotOwnedByAnyUserOnThisDevice:
      "Aplikasi yang ingin Anda instal tidak diunduh atau didapatkan melalui Google Play Store resmi",
  Strings.anInternalErrorHappenedInThePlayStore:
      "Terjadi kesalahan internal dalam Google Play Store yang mencegah instalasi dari selesai dilakukan",

  // Firebase Auth Exception
  Strings.yourEmailAddressIncorrect: "Alamat email Anda terlihat salah",
  Strings.thePasswordIncorrect: "Kata sandi Anda salah",
  Strings.theUserDoesNotExist: "Pengguna dengan email ini tidak ditemukan",
  Strings.theUserHasBeenDisabled:
      "Pengguna dengan email ini telah dinonaktifkan",
  Strings.thereHaveBeenTooManyAttempts:
      "Terlalu banyak permintaan karena aktivitas yang tidak biasa. Coba lagi nanti",
  Strings.theOptionToSignInNotAvailable:
      "Pilihan metode login tidak diaktifkan",
  Strings.thisEmailAlreadyBeenRegistered: "Email tersebut sudah terdaftar",
  Strings.invalidCertHash: "Masuk gagal, coba lagi nanti",
  Strings.thePhoneNumberInvalidFormatTooLong:
      "Nomor telepon tidak valid karena terlalu panjang",
  Strings.thePhoneNumberInvalidFormat:
      "Nomor telepon tidak valid/format tidak valid",
  Strings.theLinkIsInvalidExpiredOrAlreadyUsed:
      "Tautan tidak valid/kedaluwarsa/sudah digunakan",
  Strings.theVersionAppNotAuthorized:
      "Versi aplikasi ini tidak diotorisasi, silakan perbarui aplikasi",
  Strings.weAreUnableToPerformRequest:
      "Tidak dapat melakukan permintaan Anda, coba lagi nanti",
  Strings.theOTPCodeIsInvalid: "Kode otp tidak valid",
  Strings.theSmsCodeYouEnteredHasExpired:
      "Kode SMS telah kedaluwarsa. Silakan kirim ulang kode verifikasi untuk mencoba lagi",
  Strings.pleaseLogInAgain: "Silakan masuk lagi setelah mengubah email",
  Strings.yourSessionHasExpired: "Silakan login lagi",
  Strings.networkErrorOccurred:
      "Terjadi kesalahan jaringan. Silakan periksa koneksi Anda dan coba lagi",

  // Network
  Strings.errorOccurredWhileCommunicationWithServer:
      "Tidak dapat berkomunikasi dengan server saat ini. Silakan cek kembali nanti",

  // Feedback
  Strings.veryBad: "Sangat buruk",
  Strings.bad: "Buruk",
  Strings.normal: "Normal",
  Strings.good: "Bagus",
  Strings.excellent: "Bagus sekali",
  Strings.thankYouForFeedback:
      "Terima kasih atas masukan Anda! Kami menghargai setiap masukan dan berharap dapat melayani Anda kembali",
  Strings.describeYourExperience: "Ceritakan pengalaman Anda di sini...",
  Strings.howWasYourOrder: "Bagaimana pesanan Anda? Beritahu kami dong",
  Strings.loveToHearHowYourOrderWas:
      "Kami sangat ingin mendengar pendapat Anda mengenai pesanan Anda! Silakan berikan rating dan umpan balik Anda. Anda bahkan dapat mengunggah foto pesanan Anda untuk membantu kami memahami pengalaman Anda dengan lebih baik",
  Strings.valueRemovedFromWishlist:
      "wishlist diperbarui. @value dihapus dari wishlist",
  Strings.valueAddedToWishlist:
      "wishlist diperbarui. @value ditambahkan ke wishlist",
  Strings.valueAlreadyInYourWishlist: "@value sudah ada di wishlist",
  Strings.valueAddedToYourNewWishlistCategory:
      "Berhasil! \"@value\" telah ditambahkan ke kategori wishlist baru Anda",

  // Recording related translations
  Strings.tapToStartRecording: 'Ketuk untuk mulai merekam',
  Strings.tapToStopRecording: 'Ketuk untuk berhenti merekam',
  Strings.listening: 'Mendengarkan...',

  // terms & conditions
  Strings.byRegistering: "Dengan Mendaftar",
  Strings.byEntering: "Dengan Masuk ke aplikasi",
  Strings.agreeWith: "anda setuju dengan",
  Strings.termCondition: "Syarat & Ketentuan",
  Strings.and: "dan",
  Strings.privacyPolicy: "Kebijakan Privasi",

  Strings.rounding: "Pembulatan",

  Strings.pickup: "Beli & Pickup sendiri",
  Strings.delivery: "Beli & Diantar Sekarang",

  Strings.profileDetail: 'Detail Profil',
  Strings.nameCannotBeChanged: 'Nama Tidak Dapat Diubah',
  Strings.contactAdminToChangeName:
      'Silakan hubungi admin untuk mengubah nama Anda',
  Strings.dateOfBirthCannotBeChanged: 'Tanggal Lahir Tidak Dapat Diubah',
  Strings.contactAdminToChangeDOB:
      'Silakan hubungi admin untuk mengubah tanggal lahir Anda',
  Strings.updateGender: 'Perbarui Jenis Kelamin?',
  Strings.pleaseVerifyInformation: 'Mohon Verifikasi Informasi Anda',
  Strings.nameAndDOBCannotBeChanged:
      'Nama dan Tanggal Lahir tidak dapat diubah setelah pendaftaran. Pastikan data sudah benar.',
  Strings.isInformationCorrect: 'Apakah informasi ini sudah benar?',
  Strings.noNeedToEdit: 'Tidak, Saya Perlu Edit',
  Strings.yesContinue: 'Ya, Lanjutkan',
};
