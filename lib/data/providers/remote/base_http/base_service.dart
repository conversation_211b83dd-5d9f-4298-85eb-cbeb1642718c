// ignore_for_file: avoid_print, unnecessary_null_comparison, prefer_typing_uninitialized_variables

import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/utils/dbhelper.dart';
import 'package:mobile_crm/app/utils/logger.dart';
import 'package:mobile_crm/core/env/env.dart';
import 'package:mobile_crm/data/providers/db/database.dart';

import '../../../../app/exception/response_code_exception.dart';

class BaseService extends GetConnect {
  final AppDb db = DatabaseHelper.instance.database;

  @override
  void onInit() {
    httpClient.baseUrl = Env.baseUrl;
    httpClient.maxAuthRetries = 3;
    httpClient.timeout = const Duration(minutes: 1);
    httpClient.defaultContentType = "application/x-www-form-urlencoded";
    httpClient.errorSafety = false;
    httpClient.addRequestModifier<dynamic>((request) async {
      request.headers['Public-Key'] = Env.publicKey.toString();
      try {
        var device = await getPlatformDevices();
        device.forEach((key, value) {
          request.headers[key.removeAllWhitespace] = value;
        });
      } catch (e) {
        request.headers['Platform'] = getPlatformOnly();
      }
      return request;
    });

    httpClient.addResponseModifier((request, response) {
      if (kDebugMode && response != null) {
        debugLogger(request, response);
      }
      responseCodeExceptionHandler(response);
      return response;
    });
    super.onInit();
  }

  String getPlatformOnly() {
    if (GetPlatform.isMobile) {
      if (GetPlatform.isAndroid) {
        return 'ANDROID';
      }

      if (GetPlatform.isIOS) {
        return 'IOS';
      }
    }

    if (GetPlatform.isDesktop) {
      if (GetPlatform.isWindows) {
        return 'WINDOWS';
      }

      if (GetPlatform.isLinux) {
        return 'LINUX';
      }

      if (GetPlatform.isMacOS) {
        return 'MACOS';
      }
    }

    if (GetPlatform.isWeb) {
      return "WEB";
    }
    return "UNKNOWN";
  }

  Future<Map<String, String>> getPlatformDevices() async {
    DeviceInfoPlugin deviceInfoPlugin = DeviceInfoPlugin();
    Map<String, String> header = {"Platform": "UNKNOWN"};
    if (GetPlatform.isWeb || kIsWeb) {
      var platform = await deviceInfoPlugin.webBrowserInfo;
      header.addAll({'Platform': 'WEB'});
      header.addAll({'BrowserName': platform.browserName.name});
      header.addAll({'UserAgent': platform.userAgent ?? ''});
      return header;
    }

    if (GetPlatform.isMobile && (GetPlatform.isAndroid || GetPlatform.isIOS)) {
      if (GetPlatform.isAndroid) {
        var platform = await deviceInfoPlugin.androidInfo;
        header.addAll({'Platform': 'ANDROID'});
        header
            .addAll({'isPhysicalDevice': platform.isPhysicalDevice.toString()});
        header.addAll({'Version': platform.version.sdkInt.toString()});
        header.addAll({'Brand': platform.brand});
        header.addAll({'Manufacture': platform.manufacturer});
        header.addAll({'Model': platform.model});
        return header;
      }

      if (GetPlatform.isIOS) {
        var platform = await deviceInfoPlugin.iosInfo;
        header.addAll({'Platform': 'IOS'});
        header
            .addAll({'isPhysicalDevice': platform.isPhysicalDevice.toString()});
        header.addAll({'SystemName': platform.systemName});
        header.addAll({'SystemVersion': platform.systemVersion});
        header.addAll({'Model': platform.model});
        header.addAll({'LocalizedModel': platform.localizedModel});
        return header;
      }
    }

    if (GetPlatform.isDesktop) {
      if (GetPlatform.isWeb || kIsWeb) {
        var platform = await deviceInfoPlugin.webBrowserInfo;
        header.addAll({'Platform': 'WEB'});
        header.addAll({'BrowserName': platform.browserName.name});
        header.addAll({'UserAgent': platform.userAgent ?? ''});
        return header;
      }

      if (GetPlatform.isWindows) {
        var platform = await deviceInfoPlugin.windowsInfo;
        header.addAll({'Platform': 'WINDOWS'});
        header.addAll({'ComputerName': platform.computerName});
        header.addAll({'ProductName': platform.productName});
        header.addAll({'BuildNumber': platform.buildNumber.toString()});
        return header;
      }

      if (GetPlatform.isLinux) {
        var platform = await deviceInfoPlugin.linuxInfo;
        header.addAll({'Platform': 'LINUX'});
        header.addAll({'ComputerName': platform.name});
        return header;
      }

      if (GetPlatform.isMacOS) {
        var platform = await deviceInfoPlugin.macOsInfo;
        header.addAll({'Platform': 'MAC OS'});
        header.addAll({'ComputerName': platform.computerName});
        header.addAll({'Model': platform.model});
        header.addAll({'OS': platform.osRelease});
        return header;
      }
    }
    return header;
  }
}
