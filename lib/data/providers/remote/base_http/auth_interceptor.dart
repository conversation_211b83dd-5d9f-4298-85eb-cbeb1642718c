// ignore_for_file: prefer_typing_uninitialized_variables

import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:mobile_crm/app/utils/dbhelper.dart';
import 'package:mobile_crm/app/utils/logger.dart';
import 'package:mobile_crm/core/env/env.dart';
import 'package:mobile_crm/core/values/app_strings.dart';
import 'package:mobile_crm/data/models/auth_token_model.dart';
import 'package:mobile_crm/data/models/server_response.dart';
import 'package:mobile_crm/data/providers/db/database.dart';
import 'package:mobile_crm/data/services/local_storage.dart';
import 'package:mobile_crm/routes/app_pages.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

import '../../../../app/utils/toast.dart';
import '../base_http/base_service.dart';

class AuthInterceptor extends BaseService {
  final FirebaseAuth auth = FirebaseAuth.instance;
  final FirebaseMessaging firebaseMessaging = FirebaseMessaging.instance;
  final GoogleSignIn googleSignIn = GoogleSignIn();
  final AppDb _db = DatabaseHelper.instance.database;
  final store = LocalStorageService();
  final box = GetStorage();
  bool isTriggered = false;

  @override
  void onInit() async {
    //add authorization
    // ignore: unnecessary_question_mark
    httpClient.addRequestModifier<dynamic?>((request) {
      final token = getTokenData();
      request.headers['Authorization'] = "Bearer $token";
      return request;
    });

    httpClient.addResponseModifier((request, response) async {
      // exceptionHandler(response);
      if (response.unauthorized == true || response.statusCode == 401) {
        await requestNewTokenFirebase();
        await requestNewTokenData();
        final token = getTokenData();
        request.headers['Authorization'] = "Bearer $token";
        if (store.token == null) {
          Sentry.captureMessage(
              "Unauthorized access\nUrl ${request.url} - ${request.method}",
              level: SentryLevel.warning,
              withScope: (p0) => p0.setExtra('request', request.toString()));
        }
        return response;
      } else {
        isTriggered = false;
        return response;
      }
    });

    // ignore: unnecessary_question_mark
    httpClient.addAuthenticator<dynamic?>((request) async {
      await requestNewTokenFirebase();
      await requestNewTokenData();
      final token = getTokenData();
      request.headers['Authorization'] = "Bearer $token";
      return request;
    });

    httpClient.maxAuthRetries = 3;
    super.onInit();
  }

  Future requestNewTokenFirebase() async {
    await FirebaseAuth.instance.currentUser?.getIdToken().then((value) {
      box.write(Strings.tokenFirebase, value);
    }).catchError((e, s) {
      errorLogger(
          pos: "AUTH INTERCEPTOR _ REQUEST NEW TOKEN FIREBASE",
          error: e,
          stackTrace: s);
    });
  }

  Future requestNewTokenData() async {
    var tokenFirebase = await FirebaseAuth.instance.currentUser?.getIdToken();
    final body = 'id_token=$tokenFirebase';
    var response = await post(
      '/v1/auth/token/phone',
      body,
      contentType: 'application/x-www-form-urlencoded',
    );
    infoLogger('requestNewTokenData', 'start...');
    try {
      final data = ServerResponse<AuthTokenModel>.fromJson(
          response.body, (json) => AuthTokenModel.fromJsonModel(json));
      print('status code: ${data.code} | ${data.status}');
      if (!data.status && data.code == 52) {
        infoLogger('requestNewTokenData, data', data);
         print('request new token unauthorized...');
        await logout();
        return;
      }
      if (!data.status && store.token == null && !isTriggered) {
        isTriggered = true;
        store.token = null;
        Toast.show("Unauthorized access, please log in",
            type: ToastType.warning, duration: 3);
        print('request new token failed... ${data}');
        infoLogger('requestNewTokenData, data', data);
        await logout();
        return;
      }
      print('request new token success...');
      store.token = (data.data?.token ?? Env.authorizationKey);
    } catch (e, s) {
      print('request new token err... ${e}');
      infoLogger('requestNewTokenData, error', e);
    }
  }

  String getTokenData() {
    return store.token ?? Env.authorizationKey;
  }

  Future<void> logout() async {
    box.remove('state');
    box.remove('phone');
    box.remove('email');
    box.remove('key');
    box.remove(Strings.tokenAuth);
    box.remove(Strings.tokenFirebase);
    store.user = null;
    store.token = null;
    store.clearApp();
    store.clearUser();
    _db.cartDao.deleteAllCart();
    _db.dealTransactionDao.deleteAll();
    _db.inboxDao.deleteAll();
    await _db.inboxDao.deleteAll();
    await _db.dealTransactionDao.deleteAll();
    await _db.wishlistDao.deleteAllWishlist();
    await _db.wishlistCategoryDao.deleteAllWishlistCategory();
    await _db.cartDao.deleteAllCart();
    await _db.transactionNewDao.deleteAllTrans();
    auth.signOut();
    googleSignIn.signOut();
    Get.offAllNamed(Routes.HOME);
  }
}
