import 'package:logger/logger.dart';
import 'package:mobile_crm/app/utils/logger.dart';
import 'package:mobile_crm/data/models/product/link_menu_model.dart';
import 'package:mobile_crm/data/models/product/product_recomendation_model.dart';
import 'package:mobile_crm/data/models/product_model.dart';
import 'package:mobile_crm/data/models/search_model.dart';
import 'package:mobile_crm/data/models/server_response.dart';
import 'package:mobile_crm/data/models/tax_model.dart';
import 'package:mobile_crm/data/models/unit_conversion_model.dart';
import 'package:mobile_crm/data/providers/db/database.dart';
import 'package:mobile_crm/data/providers/remote/base_http/auth_interceptor.dart';

class ProductService extends AuthInterceptor {
  var className = "Product Service";

  /// Get All Product using API V2
  Future<ServerResponseList<Product>> getAllProductAvailability() async {
    try {
      var response = await get('/v2/product/availability');
      // print('>>>>>> product: ${response.body}');
      // infoLogger('getAllProductAvailability', ProductModel.fromJson(response.body));
      return ServerResponseList<Product>.fromJson(
          response.body, (json) => ProductModel.fromJsonModel(json));
    } catch (exception, stackTrace) {
      errorLogger(
          pos: "$className get All Product Availability",
          error: exception,
          stackTrace: stackTrace);
      return Future.error(exception);
    }
  }

  Future<ServerResponseList<ProductRecomendationModel>>
      getAllProductRecomendation(int outletId) async {
    try {
      var resp = await get("/v1/product/recommendation?outlet_id=$outletId");
      return ServerResponseList<ProductRecomendationModel>.fromJson(
          resp.body, (json) => ProductRecomendationModel.fromJsonModel(json));
    } catch (e, s) {
      errorLogger(
          pos: "$className get all product recomendation",
          error: e,
          stackTrace: s);
      return Future.error(e);
    }
  }

  Future<ServerResponseList<TaxModel>> getAllTaxesRemote() async {
    try {
      var response = await get('/v1/product/gratuity');
      var logger = Logger(printer: PrettyPrinter());
      logger.i("gratuity ${response.body['data']}");
      return ServerResponseList<TaxModel>.fromJson(
          response.body, (json) => TaxModel.fromJsonModel(json));
    } catch (exception, stackTrace) {
      errorLogger(
          pos: "$className get All Taxes",
          error: exception,
          stackTrace: stackTrace);
      return Future.error(exception);
    }
  }

  /// Get All Product using API V2
  Future<ServerResponseList<Product>> getAllProductAvailabilityV3() async {
    try {
      var response = await get('/v3/product');
      return ServerResponseList<Product>.fromJson(
          response.body, (json) => ProductModel.fromJsonModel(json));
    } catch (exception, stackTrace) {
      errorLogger(
          pos: "$className get All Product Availability",
          error: exception,
          stackTrace: stackTrace);
      return Future.error(exception);
    }
  }

  /// Get Gratuity / Tax Product by outlet id using API V1
  Future<ServerResponseList<TaxModel>> getGratuityByOutlet(
      {required String outletId, String? query}) async {
    try {
      var response = await get('/v1/product/gratuity/$outletId',
          query: {'tax_status': query});
      return ServerResponseList<TaxModel>.fromJson(
          response.body, (json) => TaxModel.fromJsonModel(json));
    } catch (exception, stackTrace) {
      errorLogger(
          pos: "$className get Gratuity Product by outlet id",
          error: exception,
          stackTrace: stackTrace);
      return Future.error(exception);
    }
  }

  /// Get Search Product History using API V1
  Future<ServerResponseList<SearchModel>> getSearchProductHistory() async {
    try {
      var response = await get('/v1/product/search');
      return ServerResponseList<SearchModel>.fromJson(
          response.body, (json) => SearchModel.fromJsonModel(json));
    } catch (exception, stackTrace) {
      errorLogger(
          pos: "$className Get Search Product History",
          error: exception,
          stackTrace: stackTrace);
      return Future.error(exception);
    }
  }

  /// Save Search Product History using API V1
  Future<ServerResponse> saveSearchProductHistory({String? search}) async {
    try {
      var body = 'text=$search';
      var response = await post('/v1/product/search', body);
      return ServerResponse.fromJson(response.body, (json) => json);
    } catch (exception, stackTrace) {
      errorLogger(
          pos: "$className Save Search Product History",
          error: exception,
          stackTrace: stackTrace);
      return Future.error(exception);
    }
  }

  /// Get All Product using API V2
  Future<ServerResponseList<LinkMenuModel>> getAllLinkMenuRemote() async {
    try {
      var response = await get('/v1/product-link-menu');
      return ServerResponseList<LinkMenuModel>.fromJson(
          response.body, (json) => LinkMenuModel.fromJsonModel(json));
    } catch (exception, stackTrace) {
      errorLogger(
          pos: "$className get All Link Menu",
          error: exception,
          stackTrace: stackTrace);
      return Future.error(exception);
    }
  }

  /// Get All Product using API V2
  Future<ServerResponseList<UnitConversionModel>>
      getUnitConversionRemote() async {
    try {
      var response = await get('/v1/product-unit-convertion');
      return ServerResponseList<UnitConversionModel>.fromJson(
          response.body, (json) => UnitConversionModel.fromJsonModel(json));
    } catch (exception, stackTrace) {
      errorLogger(
          pos: "$className get All Unit Conversion",
          error: exception,
          stackTrace: stackTrace);
      return Future.error(exception);
    }
  }

  /// Notify Product
  Future<ServerResponse> addNotifyProductRemote(
      {required int productDetailId, required int qty}) async {
    try {
      final body = {'product_detail_id': productDetailId, 'qty': qty};
      infoLogger("addNotifyProductRemote", "Body $body");
      var response = await post('/v1/product/notify', body,
          contentType: 'application/x-www-form-urlencoded');
      return ServerResponse.fromJson(response.body, (json) => json);
    } catch (exception, stackTrace) {
      errorLogger(
          pos: "$className add notify product",
          error: exception,
          stackTrace: stackTrace);
      return Future.error(exception);
    }
  }
}
