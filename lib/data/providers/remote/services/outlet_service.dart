import 'package:mobile_crm/app/exception/get_http_exception.dart';
import 'package:mobile_crm/app/utils/logger.dart';
import 'package:mobile_crm/data/models/delivery_price_model.dart';
import 'package:mobile_crm/data/models/outlet_model.dart';
import 'package:mobile_crm/data/models/server_response.dart';
import 'package:mobile_crm/data/providers/db/database.dart';
import 'package:mobile_crm/data/providers/remote/base_http/base_service.dart';

class OutletService extends BaseService {
  var className = "Outlet Service";

  /// Get Delivery price
  Future<ServerResponse<DeliveryPrice>> getDeliveryPrice(
      {Map<String, dynamic>? query, int? outletId}) async {
    try {
      var response = await get(
          "/v2/outlet/$outletId/delivery/price?latitude=${query!['latitude']}&longitude=${query['longitude']}");

      return ServerResponse<DeliveryPrice>.fromJson(
        response.body,
        (json) {
          var data = DeliveryPrice.fromJsonModel(json);
          return data;
        },
      );
    } catch (exception, stackTrace) {
      errorLogger(
          pos: "$className get delivery price",
          error: exception,
          stackTrace: stackTrace);
      return Future.error(exception);
    }
  }

  /// Get All Outlet using API V1
  Future<ServerResponseList<Outlet>> getAllOutlet(
      {Map<String, dynamic>? query}) async {
    try {
      var response = await get('/v1/outlet', query: query);
      return ServerResponseList<Outlet>.fromJson(
          response.body, (json) => OutletModel.fromJsonModel(json));
    } catch (exception, stackTrace) {
      errorLogger(
          pos: "$className get All Outlet",
          error: exception,
          stackTrace: stackTrace);
      return Future.error(exception);
    }
  }

  /// Get Detail Outlet by outlet id using API V1
  Future<ServerResponse<Outlet>> getDetailOutlet(
      {required String outletId}) async {
    try {
      var response = await get('/v1/outlet/detail/$outletId');
      return ServerResponse<Outlet>.fromJson(
          response.body, (json) => OutletModel.fromJsonModel(json));
    } catch (exception, stackTrace) {
      getHttpExceptionHandler(exception);
      errorLogger(
          pos: "$className get Detail Outlet by outlet id",
          error: exception,
          stackTrace: stackTrace);
      return Future.error(exception);
    }
  }
}
