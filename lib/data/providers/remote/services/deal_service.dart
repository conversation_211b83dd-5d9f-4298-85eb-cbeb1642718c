import 'dart:io';
import 'dart:isolate';

import 'package:get/get_connect/http/src/response/response.dart';
import 'package:mobile_crm/app/utils/logger.dart';
import 'package:mobile_crm/data/models/deal_model.dart';
import 'package:mobile_crm/data/models/deal_payment_model.dart';
import 'package:mobile_crm/data/models/deals_payment_model.dart';
import 'package:mobile_crm/data/models/server_response.dart';
import 'package:mobile_crm/data/providers/remote/base_http/auth_interceptor.dart';

import '../../../../app/exception/get_http_exception.dart';
import '../../../models/secret_id_model.dart';
import '../../db/database.dart';

class DealService extends AuthInterceptor {
  var className = "Deal Service";

  /// Get All Promotion using API V2
  Future<ServerResponseList<DealData>> getAllPromotion(
      {Map<String, dynamic>? query}) async {
    try {
      var response = await get('/v2/promotion', query: query);
      return ServerResponseList<DealData>.fromJson(
          response.body, (json) => DealModel.fromJsonModel(json));
    } catch (exception, stackTrace) {
      errorLogger(
          pos: "$className get All Promotion",
          error: exception,
          stackTrace: stackTrace);
      return Future.error(exception);
    }
  }

  /// Get Detail Promotion using API V2
  Future<ServerResponse<DealData>> getDetailPromotion(
      {required String promotionId}) async {
    try {
      var response = await get('/v2/promotion/$promotionId');
      return ServerResponse<DealData>.fromJson(
          response.body, (json) => DealModel.fromJsonModel(json));
    } catch (exception, stackTrace) {
      getHttpExceptionHandler(exception);
      errorLogger(
          pos: "$className get Detail Promotion",
          error: exception,
          stackTrace: stackTrace);
      return Future.error(exception);
    }
  }

  /// Get All Deals using API V1
  Future<ServerResponseList<DealData>> getAllDeals() async {
    try {
      var response = await get(
        '/v1/promo/deals',
      );
      return ServerResponseList<DealData>.fromJson(
          response.body, (json) => DealModel.fromJsonModel(json));
    } catch (exception, stackTrace) {
      errorLogger(
          pos: "$className get All Deals",
          error: exception,
          stackTrace: stackTrace);
      return Future.error(exception);
    }
  }

  /// Get Detail Deals using API V1
  Future<ServerResponse<DealData>> getDetail({required String id}) async {
    try {
      var response = await get('/v1/promo/deals/detail/$id');
      return ServerResponse<DealData>.fromJson(
          response.body, (json) => DealModel.fromJsonModel(json));
    } catch (exception, stackTrace) {
      getHttpExceptionHandler(exception);
      errorLogger(
          pos: "$className get Deals Detail id",
          error: exception,
          stackTrace: stackTrace);
      return Future.error(exception);
    }
  }

  /// Send Notification Remainder Unpublish deals API V1
  Future<ServerResponse<DealData>> sendMeReminder({required String id}) async {
    try {
      var response = await post('/v1/promotion/$id/notify', '');
      return ServerResponse<DealData>.fromJson(
          response.body, (json) => DealModel.fromJsonModel(json));
    } catch (exception, stackTrace) {
      errorLogger(
          pos: "$className send notify me",
          error: exception,
          stackTrace: stackTrace);
      return Future.error(exception);
    }
  }

  /// Buy Deals API V2
  Future<ServerResponse<DealPaymentModel>> buy({required String id}) async {
    try {
      var response = await post('/v2/promotion/$id/buy', '');
      return ServerResponse<DealPaymentModel>.fromJson(
          response.body, (json) => DealPaymentModel.fromJsonModel(json));
    } on SocketException catch (e) {
      errorLogger(pos: "socket exception", error: e);
      return Future.error(e);
    } catch (exception, stackTrace) {
      getHttpExceptionHandler(exception);
      errorLogger(
          pos: "$className buy deal", error: exception, stackTrace: stackTrace);
      return Future.error(exception);
    }
  }

  /// Buy Deals payment
  Future<ServerResponse<DealsPaymentModel>> getDealsPaymentRemote(
      {required String promotionId,
      required String paymentType,
      String? phoneNumber}) async {
    try {
      Map<String, dynamic>? query = {'payment_type': paymentType};

      if (phoneNumber != null) {
        query.addAll({'phone_number': phoneNumber});
      }

      var response =
          await get('/v1/promotion/$promotionId/payment', query: query);
      return ServerResponse<DealsPaymentModel>.fromJson(
          response.body, (json) => DealsPaymentModel.fromJsonModel(json));
    } on SocketException catch (e) {
      errorLogger(pos: "socket exception", error: e);
      return Future.error(e);
    } catch (exception, stackTrace) {
      getHttpExceptionHandler(exception);
      errorLogger(
          pos: "$className get deals payment",
          error: exception,
          stackTrace: stackTrace);
      return Future.error(exception);
    }
  }

  /// MY Voucher
  /// Get All My Voucher
  Future<ServerResponseList<DealData>> getMyVoucherRemote() async {
    Response? resp;
    try {
      resp = await get('/v1/promo/deals/myvoucher');
      if (resp.body == null) {
        return ServerResponseList(data: [], status: false);
      }
      return ServerResponseList<DealData>.fromJson(
          resp.body, (json) => DealModel.fromJsonModel(json));
    } catch (exception, stackTrace) {
      getHttpExceptionHandler(exception);
      errorLogger(
          pos: "$className Get All My Voucher",
          error: exception,
          stackTrace: stackTrace);
      return Future.error(exception);
    }
  }

  /// Get Detail My Voucher, [promotionId] required
  Future<ServerResponse<DealData>> getDetailMyVouherRemote(
      {required String promotionId}) async {
    try {
      var response = await get('/v1/promo/deals/myvoucher/detail/$promotionId');
      return ServerResponse<DealData>.fromJson(
          response.body, (json) => DealModel.fromJsonModel(json));
    } catch (exception, stackTrace) {
      getHttpExceptionHandler(exception);
      errorLogger(
          pos: "$className Get Detail My Voucher",
          error: exception,
          stackTrace: stackTrace);
      return Future.error(exception);
    }
  }

  /// Get Secret Id Voucher, [promotionId] required
  Future<ServerResponse<SecretIdModel>> getVoucherSecretId(
      String promotionId) async {
    try {
      var response = await get('/v1/promotion-deals/$promotionId/secret-code');
      var data = ServerResponse<SecretIdModel>.fromJson(
          response.body, (json) => SecretIdModel.fromJsonModel(json));
      return data;
    } catch (exception, stackTrace) {
      getHttpExceptionHandler(exception);
      errorLogger(
          pos: "$className ~ Get Voucher Secret Id",
          error: exception,
          stackTrace: stackTrace);
      return Future.error(exception);
    }
  }

  /// Get refund Voucher, [promotionBuyId] required
  Future<ServerResponse> refundVoucherRemote(String promotionBuyId) async {
    try {
      var response = await post('/v1/promotion/$promotionBuyId/refund', '');
      var data =
          ServerResponse.fromJson(response.body, (json) => json.toString());
      return data;
    } catch (exception, stackTrace) {
      getHttpExceptionHandler(exception);
      errorLogger(
          pos: "$className ~ Refund voucher",
          error: exception,
          stackTrace: stackTrace);
      return Future.error(exception);
    }
  }
}
