import 'package:mobile_crm/app/exception/get_http_exception.dart';
import 'package:mobile_crm/app/utils/logger.dart';
import 'package:mobile_crm/data/models/auth_token_model.dart';
import 'package:mobile_crm/data/models/otp_model.dart';
import 'package:mobile_crm/data/models/server_response.dart';
import 'package:mobile_crm/data/providers/remote/base_http/auth_interceptor.dart';

class AuthService extends AuthInterceptor {
  var className = "Auth Service";

  /// Login with link Required parameter [phoneNumber]
  Future<ServerResponse<AuthTokenModel>> sendAuthLink(
      {required String phoneNumber}) async {
    try {
      String body = 'contact=$phoneNumber';
      var response = await post('/v1/auth/send-auth-link', body,
          contentType: 'application/x-www-form-urlencoded');
      return ServerResponse<AuthTokenModel>.fromJson(
          response.body, (json) => AuthTokenModel.fromJsonModel(json));
    } catch (exception, stackTrace) {
      getHttpExceptionHandler(exception);
      errorLogger(
          pos: "$className Send Auth Linkr",
          error: exception,
          stackTrace: stackTrace);
      return Future.error(exception);
    }
  }

  /// Get Auth Token To Acquire Authorized Access Required parameter [token] that get token from firebase
  Future<ServerResponse<AuthTokenModel>> getToken(
      {required String token}) async {
    try {
      String body = 'id_token=$token';
      var response = await post('/v1/auth/token/phone', body,
          contentType: 'application/x-www-form-urlencoded');
      return ServerResponse<AuthTokenModel>.fromJson(
          response.body, (json) => AuthTokenModel.fromJsonModel(json));
    } catch (exception, stackTrace) {
      errorLogger(
          pos: "$className Get Auth Token",
          error: exception,
          stackTrace: stackTrace);
      return Future.error(exception);
    }
  }

  /// Login With Phone Number Required parameter [token] that get token from firebase
  Future<ServerResponse> loginWithPhoneNumber({required String token}) async {
    try {
      String body = 'id_token=$token';
      var response = await post('/v1/auth/login/sms', body,
          contentType: 'application/x-www-form-urlencoded');
      return ServerResponse.fromJson(response.body, (json) => json);
    } catch (exception, stackTrace) {
      getHttpExceptionHandler(exception);
      errorLogger(
          pos: "$className Login With Phone Number",
          error: exception,
          stackTrace: stackTrace);
      return Future.error(exception);
    }
  }

  Future<ServerResponse<OtpModel>> sendOtp(
      {required String phone}) async {
    try {      
      String body = 'contact=${phone.trim()}';
      var response = await post('/v1/auth/send-otp', body,
          contentType: 'application/x-www-form-urlencoded');
      return ServerResponse<OtpModel>.fromJson(
          response.body, (json) => OtpModel.fromJsonModel(json));
    } catch (exception, stackTrace) {
      errorLogger(
          pos: "$className Get Auth Token",
          error: exception,
          stackTrace: stackTrace);
      return Future.error(exception);
    }
  }
}
