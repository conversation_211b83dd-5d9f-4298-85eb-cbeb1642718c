import 'package:mobile_crm/app/exception/get_http_exception.dart';
import 'package:mobile_crm/app/utils/logger.dart';
import 'package:mobile_crm/data/models/cart_model.dart';
import 'package:mobile_crm/data/models/server_response.dart';
import 'package:mobile_crm/data/providers/db/database.dart';
import 'package:mobile_crm/data/providers/remote/base_http/auth_interceptor.dart';

class CartService extends AuthInterceptor {
  var className = "Outlet Service";

  /// Get All Cart using API V1
  Future<ServerResponseList<Cart>> getAllCartRemote() async {
    try {
      var response = await get('/v1/cart');
      return ServerResponseList<Cart>.fromJson(
          response.body, (json) => CartModel.fromJsonModel(json));
    } catch (exception, stackTrace) {
      errorLogger(
          pos: "$className get All Cart",
          error: exception,
          stackTrace: stackTrace);
      return Future.error(exception);
    }
  }

  /// Add cart using API V1
  Future<ServerResponse> addCartRemote({required int productDetailId}) async {
    try {
      var response = await post(
          '/v1/cart', 'product_detail_id=$productDetailId',
          contentType: 'application/x-www-form-urlencoded');
      return ServerResponse<void>.fromJson(response.body, (json) => json);
    } catch (exception, stackTrace) {
      getHttpExceptionHandler(exception);
      errorLogger(
          pos: "$className Add Cart", error: exception, stackTrace: stackTrace);
      return Future.error(exception);
    }
  }

  /// Remove Cart using API V1
  Future<ServerResponse> removeCartRemote(
      {required int productDetailId}) async {
    try {
      var response = await request('/v1/cart', 'DELETE',
          body: [
            {'product_detail_id': productDetailId}
          ],
          contentType: 'application/json');
      return ServerResponse.fromJson(response.body, (json) => json);
    } catch (exception, stackTrace) {
      getHttpExceptionHandler(exception);
      errorLogger(
          pos: "$className remove Cart",
          error: exception,
          stackTrace: stackTrace);
      return Future.error(exception);
    }
  }

  /// Update Single Cart using API V1
  Future<ServerResponse> updateCartRemote(
      {required int productDetailId, required int newQty}) async {
    try {
      final body = {'product_detail_id': productDetailId, 'qty': newQty};
      var response = await put('/v1/cart', body,
          contentType: 'application/x-www-form-urlencoded');
      return ServerResponse.fromJson(response.body, (json) => json);
    } catch (exception, stackTrace) {
      errorLogger(
          pos: "$className update Cart",
          error: exception,
          stackTrace: stackTrace);
      return Future.error(exception);
    }
  }

  /// Update Batch Cart using API V1
  Future<ServerResponse> updateBatchCartRemote(
      {required List<Cart> cart}) async {
    try {
      List<Map<String, dynamic>>? body = [];
      for (var item in cart) {
        body.add(
            {'product_detail_id': item.product_detail_fkid, 'qty': item.qty});
      }
      var response =
          await put('/v1/cart', body, contentType: 'application/json');
      return ServerResponse.fromJson(response.body, (json) => json);
    } catch (exception, stackTrace) {
      errorLogger(
          pos: "$className update Batch Cart",
          error: exception,
          stackTrace: stackTrace);
      return Future.error(exception);
    }
  }

// /// Get Detail Cart by outlet id using API V1
// Future<ServerResponse<Outlet>> getDetailOutlet(
//     {required String outletId}) async {
//   try {
//     var response = await get('/v1/outlet/detail/$outletId');
//     return ServerResponse<Outlet>.fromJson(
//         response.body, (json) => OutletModel.fromJsonModel(json));
//   } catch (exception, stackTrace) {
//     errorLogger(
//         pos: "$className get Detail Outlet by outlet id",
//         error: exception,
//         stackTrace: stackTrace);
//     return Future.error(exception);
//   }
// }
}
