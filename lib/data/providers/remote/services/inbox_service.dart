import 'package:mobile_crm/app/utils/logger.dart';
import 'package:mobile_crm/data/models/inbox_model.dart';
import 'package:mobile_crm/data/models/server_response.dart';
import 'package:mobile_crm/data/providers/db/database.dart';
import 'package:mobile_crm/data/providers/remote/base_http/auth_interceptor.dart';

class InboxService extends AuthInterceptor {
  var className = "Inbox Service";

  /// Get All Inbox
  Future<ServerResponseList<InboxData>> getAllInboxRemote() async {
    try {
      var response = await get('/v1/inbox');
      return ServerResponseList<InboxData>.fromJson(
          response.body, (json) => InboxModel.fromJsonModel(json));
    } catch (exception, stackTrace) {
      errorLogger(
          pos: "$className get All Inbox",
          error: exception,
          stackTrace: stackTrace);
      return Future.error(exception);
    }
  }

  /// Get Detail Inbox
  Future<ServerResponse<InboxData>> getDetailInboxRemote(String id) async {
    try {
      var response = await get('/v2/inbox/$id');
      return ServerResponse<InboxData>.fromJson(
          response.body, (json) => InboxModel.fromJsonModel(json));
    } catch (exception, stackTrace) {
      errorLogger(
          pos: "$className get Detail Inbox",
          error: exception,
          stackTrace: stackTrace);
      return Future.error(exception);
    }
  }

  /// Get All Inbox Paging
  Future<ServerResponseList<InboxData>> getInboxPagingRemote(
      {required int page}) async {
    try {
      if (page == 0) {
        page = 1;
      }
      var response = await get('/v1/inbox/page/$page');
      return ServerResponseList<InboxData>.fromJson(
          response.body, (json) => InboxModel.fromJsonModel(json));
    } catch (exception, stackTrace) {
      errorLogger(
          pos: "$className get Inbox Paging Page $page",
          error: exception,
          stackTrace: stackTrace);
      return Future.error(exception);
    }
  }

  /// Update Inbox
  Future<int> updateInboxRemote({required String notificationId}) async {
    try {
      var body = {'notif_id': notificationId};
      var response = await put('/v1/inbox', body,
          contentType: 'application/x-www-form-urlencoded');
      var result = ServerResponse.fromJson(response.body, (json) => json);
      return (result.status == true) ? 1 : 0;
    } catch (exception, stackTrace) {
      errorLogger(
          pos: "$className update inbox $notificationId",
          error: exception,
          stackTrace: stackTrace);
      return Future.error(exception);
    }
  }

  /// get Total Inbox
  Future<int> getTotalInboxRemote() async {
    try {
      var response = await get('/v1/inbox/count/unread');
      var result = ServerResponse.fromJson(
          response.body, (json) => int.tryParse(json.toString()) ?? 0);
      return (result.status == true) ? (result.data ?? 0) : 0;
    } catch (exception, stackTrace) {
      errorLogger(
          pos: "$className get total inbox",
          error: exception,
          stackTrace: stackTrace);
      return Future.error(exception);
    }
  }
}
