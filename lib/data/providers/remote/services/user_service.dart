import 'package:mobile_crm/app/exception/get_http_exception.dart';
import 'package:mobile_crm/app/utils/logger.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/data/models/address_model.dart';
import 'package:mobile_crm/data/models/point_model.dart';
import 'package:mobile_crm/data/models/secret_id_model.dart';
import 'package:mobile_crm/data/models/server_response.dart';
import 'package:mobile_crm/data/models/user_code_model.dart';
import 'package:mobile_crm/data/providers/db/database.dart';
import 'package:mobile_crm/data/providers/remote/base_http/auth_interceptor.dart';

import '../../../models/deal_model.dart';
import '../../../models/user_model.dart';

class UserService extends AuthInterceptor {
  var className = "User Service";

  /// Get User Detail need Login
  Future<ServerResponse<UserModel>> getUserDetailRemote() async {
    try {
      var response = await get('/v1/user/detail');
      return ServerResponse<UserModel>.fromJson(
          response.body, (json) => UserModel.fromJsonModel(json));
    } catch (exception, stackTrace) {
      getHttpExceptionHandler(exception);
      errorLogger(
          pos: "$className Get User Detail",
          error: exception,
          stackTrace: stackTrace);
      return Future.error(exception);
    }
  }

  /// Remove account
  Future<ServerResponse> validateRemoveAccountRemote(
      {required UserCodeModel user}) async {
    try {
      final body = user.toJson();
      var response = await request('/v1/user', 'DELETE',
          body: body, contentType: 'application/x-www-form-urlencoded');
      return ServerResponse.fromJson(response.body, (json) => json);
    } catch (exception, stackTrace) {
      getHttpExceptionHandler(exception);
      errorLogger(
          pos: "$className Validate remove account",
          error: exception,
          stackTrace: stackTrace);
      return Future.error(exception);
    }
  }

  /// Remove account
  Future<ServerResponse> requestRemoveAccountRemote() async {
    try {
      var response = await request('/v1/user/remove-account', 'POST',
          contentType: 'application/x-www-form-urlencoded');
      return ServerResponse.fromJson(response.body, (json) => json);
    } catch (exception, stackTrace) {
      getHttpExceptionHandler(exception);
      errorLogger(
          pos: "$className request remove account",
          error: exception,
          stackTrace: stackTrace);
      return Future.error(exception);
    }
  }

  /// Get User Secret Id
  Future<ServerResponse<SecretIdModel>> getSecretId() async {
    try {
      var response = await get('/v1/user/secret_id');
      return ServerResponse<SecretIdModel>.fromJson(
          response.body, (json) => SecretIdModel.fromJsonModel(json));
    } catch (exception, stackTrace) {
      getHttpExceptionHandler(exception);
      errorLogger(
          pos: "$className Get User Secret Id",
          error: exception,
          stackTrace: stackTrace);
      return Future.error(exception);
    }
  }

  /// Get User Total Point
  Future<ServerResponse<int>> getTotalPoint() async {
    try {
      var response = await get('/v1/user/point');
      return ServerResponse<int>.fromJson(response.body, (json) => json as int);
    } catch (exception, stackTrace) {
      errorLogger(
          pos: "$className Get User Total Point",
          error: exception,
          stackTrace: stackTrace);
      return Future.error(exception);
    }
  }

  /// Get Point History
  Future<ServerResponseList<PointData>> getUserPointHistoryRemote() async {
    try {
      var response = await get('/v1/user/point-history');
      return ServerResponseList<PointData>.fromJson(
          response.body, (json) => PointModel.fromJsonModel(json));
    } catch (exception, stackTrace) {
      errorLogger(
          pos: "$className Get User Point History",
          error: exception,
          stackTrace: stackTrace);
      return Future.error(exception);
    }
  }

  /// update token FCM / Push Notification
  Future<ServerResponse> updateTokenFCM({String? token}) async {
    try {
      final body = 'firebase_token=$token';
      var response = await post(
        '/v1/user/firebase_token',
        body,
        contentType: 'application/x-www-form-urlencoded',
      );
      infoLogger("Token FMessage", "${response.body}");
      return ServerResponse.fromJson(response.body, (json) => json);
    } catch (exception, stackTrace) {
      errorLogger(
          pos: "$className update token FCM",
          error: exception,
          stackTrace: stackTrace);
      return Future.error(exception);
    }
  }

  /// Check user exist or not
  Future<ServerResponse<UserModel>> checkExistingUser(
      {String? phoneNumber, String? email}) async {
    try {
      Map<String, dynamic>? query;

      if (phoneNumber != null) {
        query = {"phone": phoneNumber.trim().toLocal};
      }

      if (email != null) {
        query = {"email": email};
      }

      if (phoneNumber != null && email != null) {
        query = {"phone": phoneNumber.toLocal, "email": email};
      }

      var response = await get('/v1/user/check/phone', query: query);
      return ServerResponse<UserModel>.fromJson(
          response.body, (json) => UserModel.fromJsonModel(json));
    } catch (exception, stackTrace) {
      errorLogger(
          pos: "$className Check user exist or not",
          error: exception,
          stackTrace: stackTrace);
      return Future.error(exception);
    }
  }

  /// Update user
  Future<ServerResponse<void>> updateUserProfileRemote(
      {String? token, required UserModel user}) async {
    try {
      infoLogger("$className Update User Profile Remote", user.toJson());
      String body = 'name=${user.name}';
      if (user.dateOfBirth != null) {
        body += '&date_birth=${user.dateOfBirth}';
      }
      if (user.gender != null) {
        body += '&gender=${user.gender}';
      }
      if (user.email != null) {
        body += '&email=${user.email}';
      }
      if (user.province != null) {
        body += '&province=${user.province}';
      }
      if (user.city != null) {
        body += '&city=${user.city}';
      }
      if (user.address != null) {
        body += '&address=${user.address}';
      }
      if (user.postalCode != null) {
        body += '&postal_code=${user.postalCode}';
      }
      if (user.additionalData != null) {
        body += '&additional_data=${user.additionalData}';
      }
      if (token != null) {
        body += '&gender=${user.gender}';
      }
      var response = await put('/v1/user', body,
          contentType: 'application/x-www-form-urlencoded');
      return ServerResponse<void>.fromJson(response.body, (json) => json);
    } catch (exception, stackTrace) {
      getHttpExceptionHandler(exception);
      errorLogger(
          pos: "$className Update User",
          error: exception,
          stackTrace: stackTrace);
      return Future.error(exception);
    }
  }

  /// Create user
  /// using api V1
  Future<ServerResponse<void>> registerNewMember(
      {required String token, required UserModel user}) async {
    try {
      String body = 'name=${user.name}';
      if (user.dateOfBirth != null) {
        body += '&date_of_birth=${user.dateOfBirth}';
      }
      if (user.gender != null) {
        body += '&gender=${user.gender}';
      }
      if (user.phone != null) {
        body += '&phone=${user.phone}';
      }
      if (user.email != null) {
        body += '&email=${user.email}';
      }
      if (user.province != null) {
        body += '&province=${user.province}';
      }
      if (user.city != null) {
        body += '&city=${user.city}';
      }
      if (user.address != null) {
        body += '&address=${user.address}';
      } else {
        body += '&address=';
      }
      if (user.postalCode != null) {
        body += '&postal_code=${user.postalCode}';
      }
      if (user.additionalData != null) {
        body += '&additional_data=${user.additionalData}';
      }
      body += '&id_token=$token';
      infoLogger("Create member v1", body);
      var response = await post('/v1/user/register/phone', body,
          contentType: 'application/x-www-form-urlencoded');
      return ServerResponse<void>.fromJson(response.body, (json) => json);
    } catch (exception, stackTrace) {
      getHttpExceptionHandler(exception);
      errorLogger(
          pos: "$className Register User",
          error: exception,
          stackTrace: stackTrace);
      return Future.error(exception);
    }
  }

  /// Create user
  /// using api V2
  Future<ServerResponse<void>> registerNewMemberV2(
      {required UserModel user}) async {
    try {
      String body = 'name=${user.name}';
      if (user.dateOfBirth != null) {
        body += '&date_of_birth=${user.dateOfBirth}';
      }
      if (user.gender != null) {
        body += '&gender=${user.gender}';
      }
      if (user.phone != null) {
        body += '&phone=${user.phone}';
      }
      if (user.email != null) {
        body += '&email=${user.email}';
      }
      if (user.province != null) {
        body += '&province=${user.province}';
      }
      if (user.city != null) {
        body += '&city=${user.city}';
      }
      if (user.address != null) {
        body += '&address=${user.address}';
      } else {
        body += '&address=';
      }
      if (user.postalCode != null) {
        body += '&postal_code=${user.postalCode}';
      }
      if (user.additionalData != null) {
        body += '&additional_data=${user.additionalData}';
      }
      infoLogger("Create member v2", body);
      var response = await post('/v2/user/register', body,
          contentType: 'application/x-www-form-urlencoded');
      return ServerResponse<void>.fromJson(response.body, (json) => json);
    } catch (exception, stackTrace) {
      getHttpExceptionHandler(exception);
      errorLogger(
          pos: "$className Register User V2",
          error: exception,
          stackTrace: stackTrace);
      return Future.error(exception);
    }
  }

  /// Validate code email verification
  /// Retrieve code from email inbox
  /// Usually used for email verification
  /// Required KEY and CODE
  Future<ServerResponse<UserCodeModel>> validateCodeEmailVerification(
      {required String key, required String code}) async {
    try {
      String body = 'key=$key';
      body += '&code=$code';

      var response = await post('/v1/user/verification/email/code', body,
          contentType: 'application/x-www-form-urlencoded');
      return ServerResponse<UserCodeModel>.fromJson(
          response.body, (json) => UserCodeModel.fromJsonModel(json));
    } catch (exception, stackTrace) {
      getHttpExceptionHandler(exception);
      errorLogger(
          pos: "$className Validate code email verification",
          error: exception,
          stackTrace: stackTrace);
      return Future.error(exception);
    }
  }

  /// request code email verification
  /// Usually used for email verification
  Future<ServerResponse<UserCodeModel>> requestCodeEmailVerification() async {
    try {
      var response = await get('/v1/user/verification/email/code');
      return ServerResponse<UserCodeModel>.fromJson(
          response.body, (json) => UserCodeModel.fromJsonModel(json));
    } catch (exception, stackTrace) {
      getHttpExceptionHandler(exception);
      errorLogger(
          pos: "$className request code email verification",
          error: exception,
          stackTrace: stackTrace);
      return Future.error(exception);
    }
  }

  /// Usually used for change phone number
  /// Required KEY and CODE
  Future<ServerResponse<UserCodeModel>> validateChangePhoneNumber(
      {required String key, required String code}) async {
    try {
      String body = 'key=$key';
      body += '&code=$code';

      var response = await post('/v2/user/change_phone', body,
          contentType: 'application/x-www-form-urlencoded');
      return ServerResponse<UserCodeModel>.fromJson(
          response.body, (json) => UserCodeModel.fromJsonModel(json));
    } catch (exception, stackTrace) {
      getHttpExceptionHandler(exception);
      errorLogger(
          pos: "$className validate change phone number",
          error: exception,
          stackTrace: stackTrace);
      return Future.error(exception);
    }
  }

  /// Resend Email Confirmation to get code
  /// Required [email]
  Future<ServerResponse<void>> resendEmailConfirmationRemote(
      {required String email}) async {
    try {
      String body = 'email=$email';

      var response = await post('/v1/user/resend_confirmation', body,
          contentType: 'application/x-www-form-urlencoded');
      return ServerResponse<void>.fromJson(response.body, (json) => json);
    } catch (exception, stackTrace) {
      getHttpExceptionHandler(exception);
      errorLogger(
          pos: "$className Resend Email Confirmation",
          error: exception,
          stackTrace: stackTrace);
      return Future.error(exception);
    }
  }

  /// Used for forgot or lost phone number
  /// [lostPhoneNumber] required current email address
  /// and required 'new phone number'.
  /// using api v2
  Future<ServerResponse<void>> lostPhoneNumberRemote(
      {required String email, required String phoneNumber}) async {
    try {
      String body = 'email=$email';
      body += '&phone=${phoneNumber.toLocal}';

      var response = await post('/v2/user/lost_phone', body,
          contentType: 'application/x-www-form-urlencoded');
      return ServerResponse<void>.fromJson(response.body, (json) => json);
    } catch (exception, stackTrace) {
      getHttpExceptionHandler(exception);
      errorLogger(
          pos: "$className Lost Phone Number",
          error: exception,
          stackTrace: stackTrace);
      return Future.error(exception);
    }
  }

  Future<ServerResponse<void>> joinOtherBusinessRemote(
      {required String token}) async {
    try {
      String body = 'id_token=$token';

      var response = await post('/v1/user/join/phone', body,
          contentType: 'application/x-www-form-urlencoded');
      return ServerResponse<void>.fromJson(response.body, (json) => json);
    } catch (exception, stackTrace) {
      getHttpExceptionHandler(exception);
      errorLogger(
          pos: "$className joinOtherBusiness",
          error: exception,
          stackTrace: stackTrace);
      return Future.error(exception);
    }
  }

  // Address
  /// Get Address
  Future<ServerResponseList<AddressModel>> getAddressRemote() async {
    try {
      var response = await get('/v1/user/address');
      return ServerResponseList<AddressModel>.fromJson(
          response.body, (json) => AddressModel.fromJsonModel(json));
    } catch (exception, stackTrace) {
      errorLogger(
          pos: "$className Get Address",
          error: exception,
          stackTrace: stackTrace);
      return Future.error(exception);
    }
  }

  /// Add Address
  Future<ServerResponse<void>> addAddressRemote(
      {required AddressModel address}) async {
    try {
      String body = 'label=${address.label}';
      if (address.province != null) {
        body += '&province=${address.province}';
      }
      if (address.city != null) {
        body += '&city=${address.city}';
      }
      if (address.address != null) {
        body += '&address=${address.address}';
      }
      if (address.phone != null) {
        body += '&phone=${address.phone}';
      }
      if (address.latitude != null) {
        body += "&latitude=${address.latitude}";
      }
      if (address.latitude != null) {
        body += "&longitude=${address.longitude}";
      }
      var response = await post('/v1/user/address', body,
          contentType: 'application/x-www-form-urlencoded');
      return ServerResponse<void>.fromJson(response.body, (json) => json);
    } catch (exception, stackTrace) {
      getHttpExceptionHandler(exception);
      errorLogger(
          pos: "$className Add Address",
          error: exception,
          stackTrace: stackTrace);
      return Future.error(exception);
    }
  }

  /// Delete Address
  Future<ServerResponse<void>> deleteAddressRemote(
      {required String addressId}) async {
    try {
      var response = await delete('/v1/user/address/$addressId');
      return ServerResponse<void>.fromJson(response.body, (json) => json);
    } catch (exception, stackTrace) {
      getHttpExceptionHandler(exception);
      errorLogger(
          pos: "$className Delete Address",
          error: exception,
          stackTrace: stackTrace);
      return Future.error(exception);
    }
  }

  /// Update Address
  Future<ServerResponse<void>> updateAddressRemote(
      {required AddressModel address}) async {
    try {
      infoLogger(address.latitude.toString());
      infoLogger(address.longitude.toString());
      String body = 'label=${address.label}';
      if (address.province != null) {
        body += '&province=${address.province}';
      }
      if (address.city != null) {
        body += '&city=${address.city}';
      }
      if (address.address != null) {
        body += '&address=${address.address}';
      }
      if (address.phone != null) {
        body += '&phone=${address.phone}';
      }
      if (address.latitude != null) {
        body += "&latitude=${address.latitude}";
      }
      if (address.latitude != null) {
        body += "&longitude=${address.longitude}";
      }
      var response = await put(
          '/v1/user/address/${address.membersAddressId}', body,
          contentType: 'application/x-www-form-urlencoded');
      return ServerResponse<void>.fromJson(response.body, (json) => json);
    } catch (exception, stackTrace) {
      getHttpExceptionHandler(exception);
      errorLogger(
          pos: "$className Update Address",
          error: exception,
          stackTrace: stackTrace);
      return Future.error(exception);
    }
  }

  // Voucher
  /// Get All Voucher
  Future<ServerResponseList<DealData>> getAllVoucher(
      {bool group = true}) async {
    try {
      if (group) {
        var response = await get('/v1/promo/deals/myvoucher/group');
        return ServerResponseList<DealData>.fromJson(
            response.body, (json) => DealModel.fromJsonModel(json));
      } else {
        var response = await get('/v1/promo/deals/myvoucher');
        return ServerResponseList<DealData>.fromJson(
            response.body, (json) => DealModel.fromJsonModel(json));
      }
    } catch (exception, stackTrace) {
      errorLogger(
          pos: "$className Get All Voucher",
          error: exception,
          stackTrace: stackTrace);
      return Future.error(exception);
    }
  }

  /// Get All Voucher
  Future<ServerResponse<DealData>> getDetailVoucherRemote(
      {required String promotionId}) async {
    try {
      var response = await get('/v1/promo/deals/myvoucher/detail/$promotionId');
      return ServerResponse<DealData>.fromJson(
          response.body, (json) => DealModel.fromJsonModel(json));
    } catch (exception, stackTrace) {
      getHttpExceptionHandler(exception);
      errorLogger(
          pos: "$className Get Detail Voucher",
          error: exception,
          stackTrace: stackTrace);
      return Future.error(exception);
    }
  }
}
