import 'package:mobile_crm/app/enum/wishlist_enum.dart';
import 'package:mobile_crm/app/exception/get_http_exception.dart';
import 'package:mobile_crm/app/utils/logger.dart';
import 'package:mobile_crm/data/models/server_response.dart';
import 'package:mobile_crm/data/models/wishlist_model.dart';
import 'package:mobile_crm/data/providers/db/database.dart';
import 'package:mobile_crm/data/providers/remote/base_http/auth_interceptor.dart';

class WishlistService extends AuthInterceptor {
  var className = "Wishlist Service";

  /// Add Wishlist
  Future<ServerResponse<WishlistData>> addWishListRemote(
      {required String productId}) async {
    try {
      String body = 'product_id=$productId';
      var response = await post('/v1/wishlist', body,
          contentType: 'application/x-www-form-urlencoded');
      return ServerResponse.fromJson(
          response.body, (json) => WishlistModel.fromJsonModel(json));
    } catch (exception, stackTrace) {
      getHttpExceptionHandler(exception);
      errorLogger(
          pos: "$className Add Wishlist Remote",
          error: exception,
          stackTrace: stackTrace);
      return Future.error(exception);
    }
  }

  /// Get Wishlist
  Future<ServerResponseList<WishlistData>> getWishListRemote(
      {WishlistEnum format = WishlistEnum.none}) async {
    try {
      Map<String, dynamic>? query;
      if (format == WishlistEnum.top_item) {
        query = ({'format': format.name.replaceAll("_", "-")});
      }

      var response = await get('/v1/wishlist', query: query);
      return ServerResponseList<WishlistData>.fromJson(
          response.body, (json) => WishlistModel.fromJsonModel(json));
    } catch (exception, stackTrace) {
      errorLogger(
          pos: "$className Get Wishlist Remote",
          error: exception,
          stackTrace: stackTrace);
      return Future.error(exception);
    }
  }

  /// Delete Wishlist
  Future<ServerResponse> deleteWishListRemote(
      {required String crmProductWishlistId}) async {
    try {
      var response = await delete('/v1/wishlist/$crmProductWishlistId');
      return ServerResponse.fromJson(response.body, (json) => json);
    } catch (exception, stackTrace) {
      getHttpExceptionHandler(exception);
      errorLogger(
          pos: "$className Delete Wishlist Remote",
          error: exception,
          stackTrace: stackTrace);
      return Future.error(exception);
    }
  }

  /// Count Wishlist
  Future<ServerResponse<int>> countWishListRemote() async {
    try {
      var response = await get('/v1/wishlist');
      return ServerResponse.fromJson(response.body, (json) {
        var convert = json as Map<String, dynamic>;
        return convert['count'] as int? ?? 0;
      });
    } catch (exception, stackTrace) {
      errorLogger(
          pos: "$className Count Wishlist Remote",
          error: exception,
          stackTrace: stackTrace);
      return Future.error(exception);
    }
  }

  /// Set Wishlist Category
  Future<ServerResponse> setWishListCategoryRemote(
      {required String categoryId, required String wishlistId}) async {
    try {
      String body = 'category_id=$categoryId';
      var response = await put('/v1/wishlist/$wishlistId/category', body,
          contentType: 'application/x-www-form-urlencoded');
      return ServerResponse.fromJson(response.body, (json) => json);
    } catch (exception, stackTrace) {
      getHttpExceptionHandler(exception);
      errorLogger(
          pos: "$className set Wishlist Category Remote",
          error: exception,
          stackTrace: stackTrace);
      return Future.error(exception);
    }
  }

  /// Get Wishlist Category
  Future<ServerResponseList<WishlistCategoryData>>
      getWishListCategoryRemote() async {
    try {
      var response = await get('/v1/wishlist-category',
          contentType: 'application/x-www-form-urlencoded');
      return ServerResponseList<WishlistCategoryData>.fromJson(
          response.body, (json) => WishlistCategoryModel.fromJsonModel(json));
    } catch (exception, stackTrace) {
      errorLogger(
          pos: "$className get Wishlist Category Remote",
          error: exception,
          stackTrace: stackTrace);
      return Future.error(exception);
    }
  }

  /// Set Wishlist Category
  Future<ServerResponse<WishlistCategoryData>> addWishListCategoryRemote(
      {required String categoryName}) async {
    try {
      String body = 'name=$categoryName';
      var response = await post('/v1/wishlist-category', body,
          contentType: 'application/x-www-form-urlencoded');
      return ServerResponse<WishlistCategoryData>.fromJson(
          response.body, (json) => WishlistCategoryModel.fromJsonModel(json));
    } catch (exception, stackTrace) {
      getHttpExceptionHandler(exception);
      errorLogger(
          pos: "$className add Wishlist Category Remote",
          error: exception,
          stackTrace: stackTrace);
      return Future.error(exception);
    }
  }
}
