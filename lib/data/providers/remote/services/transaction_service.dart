import 'dart:convert';
import 'dart:io' show File;

import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:mobile_crm/app/exception/get_http_exception.dart';
import 'package:mobile_crm/app/helper/sentry_helper.dart';
import 'package:mobile_crm/app/utils/logger.dart';
import 'package:mobile_crm/data/models/feedback_model.dart';
import 'package:mobile_crm/data/models/order_model.dart';
import 'package:mobile_crm/data/models/outlet_model.dart';
import 'package:mobile_crm/data/models/payment_method_model.dart';
import 'package:mobile_crm/data/models/self_order_model.dart';
import 'package:mobile_crm/data/models/server_response.dart';
import 'package:mobile_crm/data/models/transaction/feedback/rating_summary_model.dart';
import 'package:mobile_crm/data/models/transaction_new_model.dart';
import 'package:mobile_crm/data/providers/db/database.dart';
import 'package:mobile_crm/data/providers/remote/base_http/auth_interceptor.dart';

import '../../../models/order_detail_model.dart';
import '../../../models/payments_model.dart';
import '../../../models/transaction/feedback/rating_review_model.dart';
import '../../../models/transaction_history_model.dart';

class TransactionService extends AuthInterceptor {
  var className = "Transaction Service";

  /// Create Transaction Sef Order using V1
  Future<ServerResponse<OrderModel>> createTransactionSelfOrder(
      {required SelfOrder selfOrder}) async {
    Map<String, dynamic> body = {
      "outlet_id": selfOrder.outlet_id,
      "customer_name": ((selfOrder.customer_name?.length ?? 0) > 24)
          ? selfOrder.customer_name?.substring(0, 20)
          : selfOrder.customer_name,
      "point": selfOrder.point,
      'receipt_receiver': selfOrder.receiptReceiver ?? '',
      'dining_table': selfOrder.diningTable ?? '',
      "order_list": selfOrder.order_list
          ?.map((e) => {
                "product_detail_id": e.product_detail_fkid,
                "qty": e.qty,
                "note": e.note
              })
          .toList()
    };
    try {
      var result = await post("/v1/transaction/self_order", body,
          contentType: 'application/json');
      return ServerResponse<OrderModel>.fromJson(
          result.body, (json) => OrderModel.fromJsonModel(json));
    } catch (e, s) {
      await SentryHelper.logException(e, s, tags: body);
      getHttpExceptionHandler(e);
      errorLogger(
          pos: '$className _ Create Transaction Self Order',
          error: e,
          stackTrace: s);
      return Future.error(e);
    }
  }

  Future<ServerResponse<TransactionNewModel>> createTransactionSelfOrder2(
      {required TransactionNewModel transactionNewModel}) async {
    Map<String, dynamic> body = transactionNewModel.toJson();
    try {
      var resp = await post("/v1/transaction/self_order", body,
          contentType: 'application/json');
      var result = ServerResponse<OrderModel>.fromJson(
          resp.body, (json) => OrderModel.fromJsonModel(json));

      if (result.status) {
        transactionNewModel.selfOrder = result.data;
        return ServerResponse<TransactionNewModel>(
            status: result.status,
            code: result.code,
            message: result.message,
            data: transactionNewModel);
      } else {
        return ServerResponse<TransactionNewModel>(
            status: result.status,
            code: result.code,
            message: result.message,
            data: transactionNewModel);
      }
    } catch (e, s) {
      await SentryHelper.logException(e, s, tags: body);
      getHttpExceptionHandler(e);
      errorLogger(
          pos: '$className _ Create Transaction Self Order',
          error: e,
          stackTrace: s);
      return Future.error(e);
    }
  }

  /// Create Transaction Delivery / Pickup / Dine In using V2
  Future<ServerResponse<OrderDetailModel>>
      createTransactionDeliveryPickupDineIn(
          {required SelfOrder selfOrder}) async {
    Map<String, dynamic> body = {
      'outlet_id': selfOrder.outlet_id,
      'order_type': selfOrder.orderType,
      'order_note': selfOrder.orderNote,
      'point': selfOrder.point,
      'order_list': selfOrder.order_list,
    };

    body.addIf(
        selfOrder.shipment != null, 'shipment', selfOrder.shipment?.toJson());
    body.addIf(selfOrder.pickup != null, 'pickup', selfOrder.pickup?.toJson());
    body.addIf(selfOrder.deal != null, 'deal', selfOrder.deal?.toJson());

    try {
      var result =
          await post("/v2/transaction", body, contentType: 'application/json');
      return ServerResponse<OrderDetailModel>.fromJson(
          result.body, (json) => OrderDetailModel.fromJsonModel(json));
    } catch (e, s) {
      await SentryHelper.logException(e, s, tags: body);
      getHttpExceptionHandler(e);
      errorLogger(
          pos: '$className _ Create Transaction Delivery / Pickup / Dine In',
          error: e,
          stackTrace: s);
      return Future.error(e);
    }
  }

  /// Create Transaction Delivery / Pickup / Dine In using V2.01
  Future<ServerResponse<TransactionNewModel>>
      createTransactionDeliveryPickupDineIn2(
          {required TransactionNewModel transactionNewModel}) async {
    transactionNewModel.orderList?.forEach((element) {
      element.filterLinkMenu();
    });
    Map<String, dynamic> body = transactionNewModel.toJson();
    printLongString(jsonEncode(body));

    infoLogger(body.toString());

    try {
      var resp =
          await post("/v2/transaction", body, contentType: 'application/json');
      var result = ServerResponse<TransactionNewModel>.fromJson(
          resp.body, (json) => TransactionNewModel.fromJsonModel(json));
      if (result.status) {
        transactionNewModel.id = result.data?.id;
        transactionNewModel.orderSalesId = result.data?.orderSalesId;
        result.data = transactionNewModel;
      }
      return result;
    } catch (e, s) {
      await SentryHelper.logException(e, s, tags: body);
      getHttpExceptionHandler(e);
      errorLogger(
          pos: '$className _ Create Transaction Delivery / Pickup / Dine In',
          error: e,
          stackTrace: s);
      return Future.error(e);
    }
  }

  /// Create Transaction Payment
  Future<ServerResponse<PaymentMethodDetail>> createTransactionPaymentRemote(
      {required TransactionNewModel newModel,
      required PaymentMethodDetail paymentMethodDetail}) async {
    var body =
        'type=${paymentMethodDetail.methodDetailType}&id=${paymentMethodDetail.id}';
    infoLogger("payment body", body);
    try {
      var result =
          await post("/v2/transaction/${newModel.orderSalesId}/payment", body);
      return ServerResponse<PaymentMethodDetail>.fromJson(
          result.body, (json) => PaymentMethodDetail.fromJsonModel(json));
    } catch (e, s) {
      await SentryHelper.logException(e, s);
      getHttpExceptionHandler(e);
      errorLogger(
          pos: '$className _ Create Transaction Payment',
          error: e,
          stackTrace: s);
      return Future.error(e);
    }
  }

  /// Create Transaction Payment
  Future<ServerResponse<OrderDetailModel>> updateTransactionOrder(
      {required String orderSalesId,
      required String status,
      XFile? file}) async {
    try {
      MultipartFile? multiFile = (file?.path == '' || file?.path == null)
          ? null
          : MultipartFile(await File(file!.path).readAsBytes(),
              filename: file.name);

      FormData formData = FormData({'status': status});
      if (multiFile != null) {
        formData.files.add(MapEntry('attachment', multiFile));
      }
      Response response = await put(
          "/v1/transaction/order_online/$orderSalesId", formData,
          contentType: "multipart/form-data");
      return ServerResponse.fromJson(
          response.body, (json) => OrderDetailModel.fromJsonModel(json));
    } catch (e, s) {
      await SentryHelper.logException(e, s);
      getHttpExceptionHandler(e);
      errorLogger(
          pos: '$className _ Update Transaction Payment',
          error: e,
          stackTrace: s);
      return Future.error(e);
    }
  }

  /// Get Status Transaction Sef Order using V1
  Future<ServerResponse<OrderModel>> getStatusTransactionSelfOrder(
      {required TransactionNewModel transactionNewModel}) async {
    try {
      var result = await get(
          "/v1/transaction/self_order/${transactionNewModel.outletId}/${transactionNewModel.selfOrder?.orderCode}");
      return ServerResponse<OrderModel>.fromJson(
          result.body, (json) => OrderModel.fromJsonModel(json));
    } catch (e, s) {
      getHttpExceptionHandler(e);
      errorLogger(
          pos: '$className _ Get Status Transaction Self Order',
          error: e,
          stackTrace: s);
      return Future.error(e);
    }
  }

  /// Get Status Transaction Order Detail using V1
  Future<ServerResponse<OrderDetailModel>> getStatusTransactionOrderDetail(
      {required String id}) async {
    try {
      var result = await get("/v1/transaction/order_online/$id");
      return ServerResponse<OrderDetailModel>.fromJson(
          result.body, (json) => OrderDetailModel.fromJsonModel(json));
    } catch (e, s) {
      getHttpExceptionHandler(e);
      errorLogger(
          pos: '$className _ Get Status Transaction Order Detail',
          error: e,
          stackTrace: s);
      return Future.error(e);
    }
  }

  /// Get Status Transaction Order Detail using V2
  Future<ServerResponse<TransactionNewModel>> getStatusTransactionOrderDetailV2(
      {required String id}) async {
    try {
      var result = await get("/v2/transaction/$id");
      return ServerResponse<TransactionNewModel>.fromJson(
          result.body, (json) => TransactionNewModel.fromJsonModel(json));
    } catch (e, s) {
      getHttpExceptionHandler(e);
      errorLogger(
          pos: '$className _ Get Status Transaction Order Detail V2',
          error: e,
          stackTrace: s);
      return Future.error(e);
    }
  }

  /// Get Payment Detail using V1
  Future<ServerResponse<PaymentDataModel>> getDetailPayment(
      {required String invoice,
      required PaymentDataModel paymentDataModel}) async {
    try {
      String param = "?type=${paymentDataModel.type}&id=${paymentDataModel.id}";
      var result = await get(
          "/v1/transaction/order_online/$invoice/payment_detail$param");
      return ServerResponse<PaymentDataModel>.fromJson(
          result.body, (json) => PaymentDataModel.fromJsonModel(json));
    } catch (e, s) {
      getHttpExceptionHandler(e);
      errorLogger(
          pos: '$className _ Get Status Payment Detail',
          error: e,
          stackTrace: s);
      return Future.error(e);
    }
  }

  /// Get Transaction History using V1
  Future<ServerResponse<TransactionHistoryModel>> getTransactionHistory(
      {required String id}) async {
    try {
      var result = await get("/v1/transaction/history/sales/$id");
      return ServerResponse<TransactionHistoryModel>.fromJson(
          result.body, (json) => TransactionHistoryModel.fromJsonModel(json));
    } catch (e, s) {
      getHttpExceptionHandler(e);
      errorLogger(
          pos: '$className _ Get Transaction History', error: e, stackTrace: s);
      return Future.error(e);
    }
  }

  /// Get Order/Transaction Setting using API V1
  Future<ServerResponse<Outlet>> getOrderTransactionSetting(
      {required String outletId}) async {
    try {
      var response = await get('/v1/transaction/order-online-setting',
          query: {"outlet_id": outletId});
      return ServerResponse<Outlet>.fromJson(
          response.body, (json) => OutletModel.fromJsonModel(json));
    } catch (exception, stackTrace) {
      errorLogger(
          pos: "$className Get Order/Transaction Setting by outlet id",
          error: exception,
          stackTrace: stackTrace);
      return Future.error(exception);
    }
  }

  /// Get Payment Method
  Future<ServerResponseList<PaymentMethodModel>> getPaymentMethodRemote(
      {String? orderType, String? outletId}) async {
    try {
      Map<String, dynamic>? query = {};
      if (orderType != null) {
        query.addAll({'type': orderType});
      }

      if (outletId != null) {
        query.addAll({'outlet_id': outletId});
      }

      var response = await get('/v1/payment-method', query: query);
      return ServerResponseList<PaymentMethodModel>.fromJson(
          response.body, (json) => PaymentMethodModel.fromJsonModel(json));
    } catch (exception, stackTrace) {
      getHttpExceptionHandler(exception);
      errorLogger(
          pos: "$className Get Payment Method",
          error: exception,
          stackTrace: stackTrace);
      return Future.error(exception);
    }
  }

  /// Give feedback v2 with attachment
  Future<ServerResponse<Outlet>> createFeedBackV2(
      {required FeedbackModel feedback}) async {
    try {
      MultipartFile? multipartFile = feedback.file?.path == ''
          ? null
          : MultipartFile(await File(feedback.file!.path).readAsBytes(),
              filename: feedback.file?.name ?? 'feedback_${feedback.salesId}');

      FormData formData =
          FormData({"stars": "${feedback.stars}", "comment": feedback.comment});

      if (multipartFile != null) {
        formData.files.add(MapEntry("attachments", multipartFile));
      }

      var response = await post(
          '/v2/transaction/${feedback.salesId}/feedback', formData,
          contentType: 'multipart/form-data');
      return ServerResponse<Outlet>.fromJson(
          response.body, (json) => OutletModel.fromJsonModel(json));
    } catch (exception, stackTrace) {
      getHttpExceptionHandler(exception);
      errorLogger(
          pos: "$className Create Feedback V2",
          error: exception,
          stackTrace: stackTrace);
      return Future.error(exception);
    }
  }

  /// Get Rating Summary
  Future<ServerResponse<RatingSummaryModel>> getRatingSummaryRemote(
      {required int outletId}) async {
    try {
      var response = await get('/v1/transaction-feedback/$outletId/rating');
      return ServerResponse<RatingSummaryModel>.fromJson(
          response.body, (json) => RatingSummaryModel.fromJsonModel(json));
    } catch (exception, stackTrace) {
      getHttpExceptionHandler(exception);
      errorLogger(
          pos: "$className get Rating Summary",
          error: exception,
          stackTrace: stackTrace);
      return Future.error(exception);
    }
  }

  /// Get Review
  Future<ServerResponseList<RatingReviewModel>> getReviewRemote(
      {required int outletId}) async {
    try {
      var response = await get('/v1/transaction-feedback/$outletId/review');
      return ServerResponseList<RatingReviewModel>.fromJson(
          response.body, (json) => RatingReviewModel.fromJsonModel(json));
    } catch (exception, stackTrace) {
      getHttpExceptionHandler(exception);
      errorLogger(
          pos: "$className get Rating Review",
          error: exception,
          stackTrace: stackTrace);
      return Future.error(exception);
    }
  }
}
