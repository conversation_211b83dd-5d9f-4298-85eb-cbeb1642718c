import 'package:drift/drift.dart';
import 'package:mobile_crm/data/models/available_model.dart';
import 'package:mobile_crm/data/models/business_hour_model.dart';
import 'package:mobile_crm/data/models/config_model.dart';
import 'package:mobile_crm/data/models/deal_payment_model.dart';
import 'package:mobile_crm/data/models/inbox_model.dart';
import 'package:mobile_crm/data/models/order_detail_model.dart';
import 'package:mobile_crm/data/models/outlet_feature_model.dart';
import 'package:mobile_crm/data/models/outlet_model.dart';
import 'package:mobile_crm/data/models/product_model.dart';
import 'package:mobile_crm/data/models/self_order_model.dart';
import 'package:mobile_crm/data/models/tax_group_model.dart';
import 'package:mobile_crm/data/models/variant_model.dart';
import 'package:mobile_crm/data/models/order_model.dart';
import 'package:mobile_crm/data/models/tax_model.dart';
import 'package:mobile_crm/data/providers/db/local/dao/faq_dao.dart';
import 'package:mobile_crm/data/providers/db/local/dao/last_type_dao.dart';
import 'package:mobile_crm/data/providers/db/local/dao/product_recomendation_dao.dart';
import 'package:mobile_crm/data/providers/db/local/entity/last_type_entity.dart';
import 'package:mobile_crm/data/providers/db/local/entity/product/product_recomendation.dart';
import '../../models/cart_model.dart';
import '../../models/deal_group_detail_model.dart';
import '../../models/deal_model.dart';
import '../../models/product/link_menu_detail_model.dart';
import '../../models/product/link_menu_model.dart';
import '../../models/product/subcategory_config_model.dart';
import '../../models/term_product_model.dart';
import '../../models/time_active_model.dart';
import '../../models/shipment_model.dart';
import '../../models/user_model.dart';
import 'connection/connection.dart' as impl;

import 'local/entity/entity.dart';
import 'local/dao/dao.dart';
import 'local/converter/converter.dart';

part 'database.g.dart';

@DriftDatabase(tables: [
  BannerApp,
  Products,
  Outlets,
  Carts,
  NotificationMe,
  Config,
  Inbox,
  Membership,
  TransactionData,
  TransactionNewTable,
  Deal,
  Point,
  Wishlist,
  AddressTable,
  WishlistCategory,
  LinkMenu,
  UnitConversionEntity,
  TaxEntity,
  Faq,
  ProductRecomendation,
  LastTypeEntity
], daos: [
  BannerDao,
  ProductDao,
  OutletDao,
  NotificationMeDao,
  TaxDao,
  CartDao,
  ConfigDao,
  DealDao,
  InboxDao,
  MembershipDao,
  WishlistDao,
  WishlistCategoryDao,
  PointDao,
  DealTransactionDao,
  TransactionNewDao,
  LinkMenuDao,
  UnitConversionDao,
  FaqDao,
  ProductRecomendationDao,
  LastTypeDao,
])
class AppDb extends _$AppDb {
  AppDb() : super(impl.connect());

  @override
  int get schemaVersion => 1;

  @override
  MigrationStrategy get migration {
    return MigrationStrategy(
      onCreate: (Migrator m) async {
        await m.createAll();
      },
      onUpgrade: (Migrator m, int from, int to) async {
        if (from < to) {
          await m.createAll();
        }
      },
    );
  }
}
