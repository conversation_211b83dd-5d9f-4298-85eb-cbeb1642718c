// ignore_for_file: non_constant_identifier_names, unnecessary_null_comparison

import 'dart:convert';

import 'package:drift/drift.dart';
import 'package:mobile_crm/data/models/available_model.dart';

List<AvailableModel> AvailableFromJson(String str) {
  if (str.isEmpty) {
    return <AvailableModel>[];
  }
  return List<AvailableModel>.from(
      json.decode(str).map((x) => AvailableModel.fromJson(x)));
}

String AvailableToJson(List<AvailableModel> data) {
  if (data == []) {
    return json.encode(data);
  }
  // return json.encode(List<AvailableModel>.from(data.map((x) => x.toJson())));
  return json.encode(data.map((element) => element.toJson()).toList());
}

class ListAvailableConverter
    extends TypeConverter<List<AvailableModel>, String> {
  const ListAvailableConverter();

  @override
  List<AvailableModel> fromSql(String fromDb) {
    return AvailableFromJson(fromDb);
  }

  @override
  String toSql(List<AvailableModel> value) {
    return AvailableToJson(value);
  }
}
