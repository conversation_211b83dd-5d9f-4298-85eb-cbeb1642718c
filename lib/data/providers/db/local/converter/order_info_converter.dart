// ignore_for_file: non_constant_identifier_names

import 'dart:convert';

import 'package:drift/drift.dart';
import 'package:mobile_crm/data/models/self_order_model.dart';

class OrderInfoConverter extends TypeConverter<SelfOrder, String> {
  const OrderInfoConverter();

  SelfOrder OrderInfoFromSql(String str) {
    return SelfOrder.fromJson(jsonDecode(str));
  }

  String OrderInfoToSql(SelfOrder data) {
    return json.encode(data.toJson());
  }

  @override
  SelfOrder fromSql(String fromDb) {
    return OrderInfoFromSql(fromDb);
  }

  @override
  String toSql(SelfOrder value) {
    return OrderInfoToSql(value);
  }
}
