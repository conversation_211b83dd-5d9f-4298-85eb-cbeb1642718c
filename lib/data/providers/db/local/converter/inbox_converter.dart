import 'dart:convert';

import 'package:drift/drift.dart';
import 'package:mobile_crm/data/models/inbox_model.dart';

class NotificationDataConverter
    extends TypeConverter<NotificationData, String> {
  const NotificationDataConverter();

  @override
  NotificationData fromSql(String fromDb) =>
      NotificationData.fromJson(jsonDecode(fromDb));

  @override
  String toSql(NotificationData value) => json.encode(value.toJson());
}
