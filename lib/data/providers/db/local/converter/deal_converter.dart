// ignore_for_file: non_constant_identifier_names

import 'dart:convert';

import 'package:drift/drift.dart';

import '../../../../models/deal_group_detail_model.dart';
import '../../../../models/deal_model.dart';

class DealGroupConverter extends TypeConverter<DealGroupDetailModel, String> {
  const DealGroupConverter();

  @override
  DealGroupDetailModel fromSql(String fromDb) {
    return DealGroupDetailModel.fromJson(jsonDecode(fromDb));
  }

  @override
  String toSql(DealGroupDetailModel value) {
    return json.encode(value.toJson());
  }
}

class ListDealGroupConverter
    extends TypeConverter<List<DealGroupDetailModel>, String> {
  const ListDealGroupConverter();

  @override
  List<DealGroupDetailModel> fromSql(String fromDb) {
    return List<DealGroupDetailModel>.from(
        json.decode(fromDb).map((x) => DealGroupDetailModel.fromJson(x)));
  }

  @override
  String toSql(List<DealGroupDetailModel> value) {
    return json.encode(List<dynamic>.from(value.map((e) => e.toJson())));
  }
}

class DealConverter extends TypeConverter<DealModel, String> {
  const DealConverter();

  DealModel DealModelFromSql(String str) {
    return DealModel.fromJson(jsonDecode(str));
  }

  String DealModelToSql(DealModel data) {
    return json.encode(data.toJson());
  }

  @override
  DealModel fromSql(String fromDb) {
    return DealModelFromSql(fromDb);
  }

  @override
  String toSql(DealModel value) {
    return DealModelToSql(value);
  }
}
