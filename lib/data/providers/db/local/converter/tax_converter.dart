// ignore_for_file: non_constant_identifier_names

import 'dart:convert';

import 'package:drift/drift.dart';
import 'package:mobile_crm/data/models/tax_group_model.dart';
import 'package:mobile_crm/data/models/tax_model.dart';

class ListTaxConverter extends TypeConverter<List<TaxModel>, String> {
  const ListTaxConverter();

  List<TaxModel> TaxModelFromJson(String str) {
    return List<TaxModel>.from(
        jsonDecode(str).map((x) => TaxModel.fromJson(x)));
  }

  String TaxModelToJson(List<TaxModel> data) {
    return json.encode(List<dynamic>.from(data.map((e) => e.toJson())));
  }

  @override
  List<TaxModel> fromSql(String fromDb) {
    return TaxModelFromJson(fromDb);
  }

  @override
  String toSql(List<TaxModel> value) {
    return TaxModelToJson(value);
  }
}

class TaxConverter extends TypeConverter<TaxGroupModel, String> {
  const TaxConverter();

  @override
  TaxGroupModel fromSql(String fromDb) =>
      TaxGroupModel.fromJson(jsonDecode(fromDb));

  @override
  String toSql(TaxGroupModel value) => json.encode(value.toJson());
}
