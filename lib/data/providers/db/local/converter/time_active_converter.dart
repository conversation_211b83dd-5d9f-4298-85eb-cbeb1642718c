// ignore_for_file: non_constant_identifier_names

import 'dart:convert';

import 'package:drift/drift.dart';
import 'package:mobile_crm/data/models/time_active_model.dart';

class ListTimeActiveConverter
    extends TypeConverter<List<TimeActiveModel>, String> {
  const ListTimeActiveConverter();

  List<TimeActiveModel> TimeActiveModelFromJson(String str) {
    return List<TimeActiveModel>.from(
        jsonDecode(str).map((x) => TimeActiveModel.fromJson(x)));
  }

  String TimeActiveModelToJson(List<TimeActiveModel> data) {
    return json.encode(List<dynamic>.from(data.map((e) => e.toJson())));
  }

  @override
  List<TimeActiveModel> fromSql(String fromDb) {
    return TimeActiveModelFromJson(fromDb);
  }

  @override
  String toSql(List<TimeActiveModel> value) {
    return TimeActiveModelToJson(value);
  }
}

class TimeActiveConverter extends TypeConverter<TimeActiveModel, String> {
  const TimeActiveConverter();

  @override
  TimeActiveModel fromSql(String fromDb) =>
      TimeActiveModel.fromJson(jsonDecode(fromDb));

  @override
  String toSql(TimeActiveModel value) => json.encode(value.toJson());
}
