import 'dart:convert';

import 'package:drift/drift.dart';
import 'package:mobile_crm/data/models/deal_payment_model.dart';
import 'package:mobile_crm/data/models/order_detail_model.dart';

class OrderDetailConverter extends TypeConverter<OrderDetailModel, String> {
  const OrderDetailConverter();

  @override
  OrderDetailModel fromSql(String fromDb) =>
      OrderDetailModel.fromJson(jsonDecode(fromDb));

  @override
  String toSql(OrderDetailModel value) => json.encode(value.toJson());
}

class DealPaymentConverter extends TypeConverter<DealPaymentModel, String> {
  const DealPaymentConverter();

  @override
  DealPaymentModel fromSql(String fromDb) =>
      DealPaymentModel.fromJson(jsonDecode(fromDb));

  @override
  String toSql(DealPaymentModel value) => json.encode(value.toJson());
}
