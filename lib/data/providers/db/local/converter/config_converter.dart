import 'dart:convert';

import 'package:drift/drift.dart';
import 'package:mobile_crm/data/models/config_model.dart';

class AppInfoConverter extends TypeConverter<AppInfo, String> {
  const AppInfoConverter();

  @override
  AppInfo fromSql(String fromDb) => AppInfo.fromJson(jsonDecode(fromDb));

  @override
  String toSql(AppInfo value) => json.encode(value.toJson());
}

class AssetConverter extends TypeConverter<Asset, String> {
  const AssetConverter();

  @override
  Asset fromSql(String fromDb) => Asset.fromJson(jsonDecode(fromDb));

  @override
  String toSql(Asset value) => json.encode(value.toJson());
}

class LanguageConverter extends TypeConverter<Language, String> {
  const LanguageConverter();

  @override
  Language fromSql(String fromDb) => Language.fromJson(jsonDecode(fromDb));

  @override
  String toSql(Language value) => json.encode(value.toJson());
}

class SocialMediaConverter extends TypeConverter<SocialMedia, String> {
  const SocialMediaConverter();

  @override
  SocialMedia fromSql(String fromDb) =>
      SocialMedia.fromJson(jsonDecode(fromDb));

  @override
  String toSql(SocialMedia value) => json.encode(value.toJson());
}

class ContactConverter extends TypeConverter<Contact, String> {
  const ContactConverter();

  @override
  Contact fromSql(String fromDb) => Contact.fromJson(jsonDecode(fromDb));

  @override
  String toSql(Contact value) => json.encode(value.toJson());
}
