import 'dart:convert';

import 'package:drift/drift.dart';
import 'package:mobile_crm/data/models/product_model.dart';

import '../../../../models/term_product_model.dart';

class ProductConverter extends TypeConverter<ProductModel, String> {
  const ProductConverter();

  @override
  ProductModel fromSql(String fromDb) {
    return ProductModel.fromJson(jsonDecode(fromDb));
  }

  @override
  String toSql(ProductModel value) {
    return json.encode(value.toJson());
  }
}

class ListProductConverter extends TypeConverter<List<ProductModel>, String> {
  const ListProductConverter();

  List<ProductModel> productsFromSql(String str) => List<ProductModel>.from(
      json.decode(str).map((x) => ProductModel.fromJson(x)));

  String productsToSql(List<ProductModel> products) =>
      json.encode(List<dynamic>.from(products.map((e) => e.toJson())));

  @override
  List<ProductModel> fromSql(String fromDb) {
    return productsFromSql(fromDb);
  }

  @override
  String toSql(List<ProductModel> value) {
    return productsToSql(value);
  }
}

class TermProductConverter extends TypeConverter<TermProductModel, String> {
  const TermProductConverter();

  @override
  TermProductModel fromSql(String fromDb) {
    return TermProductModel.fromJson(jsonDecode(fromDb));
  }

  @override
  String toSql(TermProductModel value) {
    return json.encode(value.toJson());
  }
}
