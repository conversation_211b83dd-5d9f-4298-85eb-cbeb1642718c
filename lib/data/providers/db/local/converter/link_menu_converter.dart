// ignore_for_file: non_constant_identifier_names

import 'dart:convert';

import 'package:drift/drift.dart';
import 'package:mobile_crm/data/models/product/link_menu_detail_model.dart';

import '../../../../models/product/link_menu_model.dart';

class ListLinkMenuConverter extends TypeConverter<List<LinkMenuModel>, String> {
  const ListLinkMenuConverter();

  List<LinkMenuModel> LinkMenuFromJson(String str) {
    return List<LinkMenuModel>.from(
        jsonDecode(str).map((x) => LinkMenuModel.fromJson(x)));
  }

  String LinkMenuToJson(List<LinkMenuModel> data) {
    return json.encode(List<dynamic>.from(data.map((e) => e.toJson())));
  }

  @override
  List<LinkMenuModel> fromSql(String fromDb) {
    return LinkMenuFromJson(fromDb);
  }

  @override
  String toSql(List<LinkMenuModel> value) {
    return LinkMenuToJson(value);
  }
}

class LinkMenuConverter extends TypeConverter<LinkMenuModel, String> {
  const LinkMenuConverter();

  @override
  LinkMenuModel fromSql(String fromDb) =>
      LinkMenuModel.fromJson(jsonDecode(fromDb));

  @override
  String toSql(LinkMenuModel value) => json.encode(value.toJson());
}

class ListLinkMenuDetailConverter
    extends TypeConverter<List<LinkMenuDetailModel>, String> {
  const ListLinkMenuDetailConverter();

  List<LinkMenuDetailModel> LinkMenuDetailModelFromJson(String str) {
    return List<LinkMenuDetailModel>.from(
        jsonDecode(str).map((x) => LinkMenuDetailModel.fromJson(x)));
  }

  String LinkMenuDetailModelToJson(List<LinkMenuDetailModel> data) {
    return json.encode(List<dynamic>.from(data.map((e) => e.toJson())));
  }

  @override
  List<LinkMenuDetailModel> fromSql(String fromDb) {
    return LinkMenuDetailModelFromJson(fromDb);
  }

  @override
  String toSql(List<LinkMenuDetailModel> value) {
    return LinkMenuDetailModelToJson(value);
  }
}

class LinkMenuDetailModelConverter
    extends TypeConverter<LinkMenuDetailModel, String> {
  const LinkMenuDetailModelConverter();

  @override
  LinkMenuDetailModel fromSql(String fromDb) =>
      LinkMenuDetailModel.fromJson(jsonDecode(fromDb));

  @override
  String toSql(LinkMenuDetailModel value) => json.encode(value.toJson());
}
