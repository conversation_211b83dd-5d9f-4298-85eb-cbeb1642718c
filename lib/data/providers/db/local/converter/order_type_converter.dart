// ignore_for_file: non_constant_identifier_names, unnecessary_null_comparison

import 'dart:convert';

import 'package:drift/drift.dart';
import 'package:mobile_crm/data/models/order_model.dart';

class OrderTypeConverter extends TypeConverter<OrderTypeModel, String> {
  const OrderTypeConverter();

  @override
  OrderTypeModel fromSql(String fromDb) {
    return OrderTypeModel.fromJson(jsonDecode(fromDb));
  }

  @override
  String toSql(OrderTypeModel value) {
    return json.encode(value.toJson());
  }
}
