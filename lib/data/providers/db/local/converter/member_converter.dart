// ignore_for_file: non_constant_identifier_names

import 'dart:convert';

import 'package:drift/drift.dart';

import '../../../../models/user_model.dart';

class MemberTypesConverter extends TypeConverter<MemberTypes, String> {
  const MemberTypesConverter();

  @override
  MemberTypes fromSql(String fromDb) {
    return MemberTypes.fromJson(jsonDecode(fromDb));
  }

  @override
  String toSql(MemberTypes value) {
    return json.encode(value.toJson());
  }
}

class ListMemberTypesConverter
    extends TypeConverter<List<MemberTypes>, String> {
  const ListMemberTypesConverter();

  @override
  List<MemberTypes> fromSql(String fromDb) {
    return List<MemberTypes>.from(
        json.decode(fromDb).map((x) => MemberTypes.fromJson(x)));
  }

  @override
  String toSql(List<MemberTypes> value) {
    return json.encode(List<dynamic>.from(value.map((e) => e.toJson())));
  }
}
