// ignore_for_file: non_constant_identifier_names

import 'dart:convert';

import 'package:drift/drift.dart';
import 'package:mobile_crm/data/models/business_hour_model.dart';
import 'package:mobile_crm/data/models/outlet_feature_model.dart';
import 'package:mobile_crm/data/models/outlet_model.dart';

class OutletConverter extends TypeConverter<OutletModel, String> {
  const OutletConverter();

  @override
  OutletModel fromSql(String fromDb) {
    return OutletModel.fromJson(jsonDecode(fromDb));
  }

  @override
  String toSql(OutletModel value) {
    return json.encode(value.toJson());
  }
}

class ListOutletConverter extends TypeConverter<List<OutletModel>, String> {
  const ListOutletConverter();

  @override
  List<OutletModel> fromSql(String fromDb) {
    return List<OutletModel>.from(
        json.decode(fromDb).map((x) => OutletModel.fromJson(x)));
  }

  @override
  String toSql(List<OutletModel> value) {
    return json.encode(List<dynamic>.from(value.map((e) => e.toJsonn())));
  }
}

List<BusinessHourModel> BusinessHourModelFromJson(String str) =>
    List<BusinessHourModel>.from(
        json.decode(str).map((x) => BusinessHourModel.fromJson(x)));

String BusinessHourModelToJson(List<BusinessHourModel> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class BusinessHourModelConverter
    extends TypeConverter<List<BusinessHourModel>, String> {
  const BusinessHourModelConverter();

  @override
  List<BusinessHourModel> fromSql(String fromDb) {
    return BusinessHourModelFromJson(fromDb);
  }

  @override
  String toSql(List<BusinessHourModel> value) {
    return BusinessHourModelToJson(value);
  }
}

class OutletFeatureConverter extends TypeConverter<OutletFeatureModel, String> {
  const OutletFeatureConverter();

  @override
  OutletFeatureModel fromSql(String fromDb) {
    return OutletFeatureModel.fromJson(jsonDecode(fromDb));
  }

  @override
  String toSql(OutletFeatureModel value) {
    return json.encode(value.toJson());
  }
}
