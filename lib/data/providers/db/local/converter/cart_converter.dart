import 'dart:convert';

import 'package:drift/drift.dart';

import '../../../../models/cart_model.dart';

class ListCartConverter extends TypeConverter<List<CartModel>, String> {
  const ListCartConverter();

  @override
  List<CartModel> fromSql(String fromDb) {
    return List<CartModel>.from(
        json.decode(fromDb).map((x) => CartModel.fromJson(x)));
  }

  @override
  String toSql(List<CartModel> value) {
    return json.encode(List<dynamic>.from(value.map((e) => e.toJson())));
  }
}

class CartConverter extends TypeConverter<CartModel, String> {
  const CartConverter();

  CartModel cartModelFromSql(String str) {
    return CartModel.fromJson(jsonDecode(str));
  }

  String cartModelToSql(CartModel data) {
    return json.encode(data.toJson());
  }

  @override
  CartModel fromSql(String fromDb) {
    return cartModelFromSql(fromDb);
  }

  @override
  String toSql(CartModel value) {
    return cartModelToSql(value);
  }
}
