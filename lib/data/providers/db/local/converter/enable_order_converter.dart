// ignore_for_file: non_constant_identifier_names, unnecessary_null_comparison

import 'dart:convert';

import 'package:drift/drift.dart';
import 'package:mobile_crm/data/models/available_model.dart';

class EnableOrderConverter extends TypeConverter<EnableOrder, String> {
  const EnableOrderConverter();

  @override
  EnableOrder fromSql(String fromDb) {
    return EnableOrder.fromJson(jsonDecode(fromDb));
  }

  @override
  String toSql(EnableOrder value) {
    return json.encode(value.toJson());
  }
}
