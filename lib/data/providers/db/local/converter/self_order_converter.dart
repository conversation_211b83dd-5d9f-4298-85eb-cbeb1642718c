
import 'dart:convert';

import 'package:drift/drift.dart';
import 'package:mobile_crm/data/models/order_model.dart';


class OrderConverter extends TypeConverter<OrderModel, String> {
  const OrderConverter();

  OrderModel orderModelFromSql(String str) {
    return OrderModel.fromJson(jsonDecode(str));
  }

  String orderModelToSql(OrderModel data) {
    return json.encode(data.toJson());
  }

  @override
  OrderModel fromSql(String fromDb) {
    return orderModelFromSql(fromDb);
  }

  @override
  String toSql(OrderModel value) {
    return orderModelToSql(value);
  }
}
