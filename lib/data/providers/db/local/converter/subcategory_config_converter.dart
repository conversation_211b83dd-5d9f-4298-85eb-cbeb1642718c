// ignore_for_file: non_constant_identifier_names, unnecessary_null_comparison

import 'dart:convert';

import 'package:drift/drift.dart';
import 'package:mobile_crm/data/models/product/subcategory_config_model.dart';

List<SubcategoryConfigModel> SubcategoryConfigFromJson(String str) {
  if (str.isEmpty) {
    return <SubcategoryConfigModel>[];
  }
  return List<SubcategoryConfigModel>.from(
      json.decode(str).map((x) => SubcategoryConfigModel.fromJson(x)));
}

String SubcategoryConfigToJson(List<SubcategoryConfigModel> data) {
  if (data == []) {
    return json.encode(data);
  }
  // return json.encode(List<AvailableModel>.from(data.map((x) => x.toJson())));
  return json.encode(data.map((element) => element.toJson()).toList());
}

class ListSubcategoryConfigConverter
    extends TypeConverter<List<SubcategoryConfigModel>, String> {
  const ListSubcategoryConfigConverter();

  @override
  List<SubcategoryConfigModel> fromSql(String fromDb) {
    return SubcategoryConfigFromJson(fromDb);
  }

  @override
  String toSql(List<SubcategoryConfigModel> value) {
    return SubcategoryConfigToJson(value);
  }
}

class SubcategoryConfigConverter
    extends TypeConverter<SubcategoryConfigModel, String> {
  const SubcategoryConfigConverter();
  @override
  SubcategoryConfigModel fromSql(String fromDb) =>
      SubcategoryConfigModel.fromJson(jsonDecode(fromDb));

  @override
  String toSql(SubcategoryConfigModel value) => jsonEncode(value.toJson());
}
