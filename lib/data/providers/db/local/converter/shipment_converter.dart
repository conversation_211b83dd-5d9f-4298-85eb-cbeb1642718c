// ignore_for_file: non_constant_identifier_names

import 'dart:convert';

import 'package:drift/drift.dart';
import 'package:mobile_crm/data/models/shipment_model.dart';

class ShipmentConverter extends TypeConverter<ShipmentModel, String> {
  const ShipmentConverter();

  ShipmentModel ShipmentModelFromSql(String str) {
    return ShipmentModel.fromJson(jsonDecode(str));
  }

  String ShipmentModelToSql(ShipmentModel data) {
    return json.encode(data.toJson());
  }

  @override
  ShipmentModel fromSql(String fromDb) {
    return ShipmentModelFromSql(fromDb);
  }

  @override
  String toSql(ShipmentModel value) {
    return ShipmentModelToSql(value);
  }
}
