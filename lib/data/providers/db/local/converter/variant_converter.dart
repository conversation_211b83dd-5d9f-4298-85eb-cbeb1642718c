import 'dart:convert';

import 'package:drift/drift.dart';
import 'package:mobile_crm/data/models/variant_model.dart';

List<VariantModel> variantFromJson(String str) {
  if (str.isEmpty) {
    return <VariantModel>[];
  }
  return List<VariantModel>.from(
      json.decode(str).map((x) => VariantModel.fromJson(x)));
}

String variantToJson(List<VariantModel> data) {
  if (data == []) {
    return json.encode(data);
  }
  // return json.encode(List<VariantModel>.from(data.map((x) => x.toJson())));
  return json.encode(data.map((element) => element.toJson()).toList());
}

class VariantConverter extends TypeConverter<List<VariantModel>, String> {
  const VariantConverter();

  @override
  List<VariantModel> fromSql(String fromDb) {
    return variantFrom<PERSON><PERSON>(fromDb);
  }

  @override
  String toSql(List<VariantModel> value) {
    return variantTo<PERSON><PERSON>(value);
  }
}
