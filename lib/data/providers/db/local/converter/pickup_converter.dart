// ignore_for_file: non_constant_identifier_names

import 'dart:convert';

import 'package:drift/drift.dart';
import 'package:mobile_crm/data/models/self_order_model.dart';

class PickupConverter extends TypeConverter<PickupModel, String> {
  const PickupConverter();

  PickupModel PickupModelFromSql(String str) {
    return PickupModel.fromJson(jsonDecode(str));
  }

  String PickupModelToSql(PickupModel data) {
    return json.encode(data.toJson());
  }

  @override
  PickupModel fromSql(String fromDb) {
    return PickupModelFromSql(fromDb);
  }

  @override
  String toSql(PickupModel value) {
    return PickupModelToSql(value);
  }
}
