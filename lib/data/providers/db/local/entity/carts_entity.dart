// ignore_for_file: non_constant_identifier_names

import 'package:drift/drift.dart';
import 'package:mobile_crm/data/providers/db/local/converter/converter.dart';

class Carts extends Table {
  IntColumn get crm_cart_id => integer().named("crmFKId").nullable()();
  IntColumn get cartId => integer().nullable()();

  IntColumn get admin_fkid => integer().named("adminFKId").nullable()();

  IntColumn get member_fkid => integer().named("memberFKId").nullable()();

  IntColumn get outlet_fkid => integer().named("outletFKId").nullable()();

  IntColumn get product_fkid => integer().named("productFKId").nullable()();

  IntColumn get product_detail_fkid =>
      integer().named("productDetailFKID").nullable()();

  IntColumn get product_detail_id => integer().nullable()();

  TextColumn get product => text().map(const ProductConverter()).nullable()();

  TextColumn get listProduct =>
      text().map(const ListProductConverter()).nullable()();

  TextColumn get outlet => text().map(const OutletConverter()).nullable()();

  TextColumn get tax => text().map(const ListTaxConverter()).nullable()();
  TextColumn get linkMenu =>
      text().map(const ListLinkMenuConverter()).nullable()();

  TextColumn get note => text().withDefault(const Constant("")).nullable()();

  IntColumn get qty => integer().nullable()();

  IntColumn get price => integer().nullable()();

  IntColumn get time_created => integer().named("timeCreated").nullable()();

  @override
  Set<Column> get primaryKey => {crm_cart_id};
}
