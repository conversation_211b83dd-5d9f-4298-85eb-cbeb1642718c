// ignore_for_file: non_constant_identifier_names

import 'package:drift/drift.dart';

class Faq extends Table {
  IntColumn get admin_fkid => integer().named("adminFkid").nullable()();
  TextColumn get answer => text().nullable()();
  TextColumn get category => text().nullable()();
  IntColumn get crm_faq_id => integer().named("crmFaqId").nullable()();
  IntColumn get data_created => integer().named("dataCreated").nullable()();
  IntColumn get data_modified => integer().named("dataModified").nullable()();
  TextColumn get status => text().nullable()();
  TextColumn get title => text().nullable()();

  @override
  Set<Column> get primaryKey => {crm_faq_id};
}
