// ignore_for_file: non_constant_identifier_names

import 'package:drift/drift.dart';

class WishlistCategory extends Table {
  IntColumn get id => integer().nullable()();

  IntColumn get dataCreated => integer().nullable()();

  IntColumn get dataModified => integer().nullable()();

  TextColumn get name => text().nullable()();

  BoolColumn get isChosen =>
      boolean().nullable().withDefault(const Constant(false))();

  @override
  Set<Column> get primaryKey => {id};
}
