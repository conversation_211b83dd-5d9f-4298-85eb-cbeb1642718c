// ignore_for_file: non_constant_identifier_names

import 'package:drift/drift.dart';

// @DataClassName('BannerApp')
class BannerApp extends Table {
  IntColumn get adminFkid => integer()();

  IntColumn get crmBannerId => integer()();

  IntColumn get status => integer()();

  TextColumn get actionDetail => text()();

  TextColumn get photo => text()();

  IntColumn get dataCreated => integer()();

  IntColumn get position => integer()();

  IntColumn get dataModified => integer()();

  TextColumn get dataStatus => text()();

  @override
  Set<Column> get primaryKey => {crmBannerId};
}
