import 'package:drift/drift.dart';
import 'package:mobile_crm/data/providers/db/local/converter/config_converter.dart';

class Config extends Table {
  TextColumn get appInfo => text().map(const AppInfoConverter()).nullable()();

  TextColumn get asset => text().map(const AssetConverter()).nullable()();

  TextColumn get language => text().map(const LanguageConverter()).nullable()();

  TextColumn get privacyPolicy => text().nullable()();
  TextColumn get termCondition => text().nullable()();

  TextColumn get socialMedia =>
      text().map(const SocialMediaConverter()).nullable()();

  TextColumn get contact => text().map(const ContactConverter()).nullable()();
}
