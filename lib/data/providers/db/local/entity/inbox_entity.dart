// ignore_for_file: non_constant_identifier_names

import 'package:drift/drift.dart';

import '../converter/inbox_converter.dart';

class Inbox extends Table {
  IntColumn get data_created => integer().nullable()();

  IntColumn get is_read => integer().nullable()();

  IntColumn get notification_id => integer().nullable()();

  TextColumn get message => text().nullable()();

  TextColumn get notification_type => text().nullable()();

  TextColumn get title => text().nullable()();

  BoolColumn get isHeader =>
      boolean().withDefault(const Constant(false)).nullable()();

  TextColumn get notification_data =>
      text().map(const NotificationDataConverter()).nullable()();

  @override
  Set<Column> get primaryKey => {notification_id};
}
