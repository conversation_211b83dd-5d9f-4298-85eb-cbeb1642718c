// ignore_for_file: non_constant_identifier_names

import 'package:drift/drift.dart';
import 'package:mobile_crm/data/providers/db/local/converter/enable_order_converter.dart';
import 'package:mobile_crm/data/providers/db/local/converter/order_type_converter.dart';
import 'package:mobile_crm/data/providers/db/local/converter/outlet_converter.dart';

class Outlets extends Table {
  IntColumn get outlet_id => integer().nullable()();

  IntColumn get promotion_id => integer().nullable()();

  IntColumn get distance_value => integer().nullable()();

  RealColumn get latitude => real().nullable()();

  RealColumn get longitude => real().nullable()();

  TextColumn get business_hour =>
      text().map(const BusinessHourModelConverter()).nullable()();

  TextColumn get workingHour =>
      text().map(const BusinessHourModelConverter()).nullable()();

  TextColumn get feature =>
      text().map(const OutletFeatureConverter()).nullable()();

  TextColumn get enable_order =>
      text().map(const EnableOrderConverter()).nullable()();

  TextColumn get order_type =>
      text().map(const OrderTypeConverter()).nullable()();

  TextColumn get outlet_logo => text().nullable()();

  TextColumn get address => text().nullable()();

  TextColumn get city => text().nullable()();

  TextColumn get distance => text().nullable()();

  TextColumn get name => text().nullable()();

  TextColumn get phone => text().nullable()();

  TextColumn get postal_code => text().nullable()();

  TextColumn get province => text().nullable()();

  TextColumn get receipt_logo => text().nullable()();

  @override
  Set<Column> get primaryKey => {outlet_id};
}
