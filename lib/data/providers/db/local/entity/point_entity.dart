// ignore_for_file: non_constant_identifier_names

import 'package:drift/drift.dart';

class Point extends Table {
  TextColumn get id => text().nullable()();

  TextColumn get source => text().nullable()();

  TextColumn get source_desc => text().named('sourceDesc').nullable()();

  TextColumn get type => text().nullable()();

  IntColumn get point => integer().nullable()();

  IntColumn get time_created => integer().named('timeCreated').nullable()();

  BoolColumn get isHeader =>
      boolean().withDefault(const Constant(false)).nullable()();
}
