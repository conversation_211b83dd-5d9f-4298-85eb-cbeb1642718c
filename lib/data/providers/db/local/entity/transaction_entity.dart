// ignore_for_file: non_constant_identifier_names

import 'package:drift/drift.dart';
import 'package:mobile_crm/data/providers/db/local/converter/converter.dart';

class TransactionData extends Table {
  IntColumn get id => integer().nullable()();

  IntColumn get promoId => integer().nullable()();

  IntColumn get orderId => integer().nullable()();

  TextColumn get type => text().nullable()();
  
  TextColumn get proofFilePath => text().nullable()();

  TextColumn get orderInfo =>
      text().map(const OrderInfoConverter()).nullable()();

  TextColumn get orderTax => text().map(const TaxConverter()).nullable()();

  TextColumn get orderDetail =>
      text().map(const OrderDetailConverter()).nullable()();

  TextColumn get dealPayment =>
      text().map(const DealPaymentConverter()).nullable()();

  @override
  Set<Column> get primaryKey => {id};
}
