// ignore_for_file: non_constant_identifier_names

import 'package:drift/drift.dart';
import 'package:mobile_crm/data/providers/db/local/converter/converter.dart';

class Deal extends Table {
  IntColumn get promotionId => integer().nullable()();

  IntColumn get promotionFkId => integer().nullable()();

  IntColumn get memberId => integer().nullable()();

  IntColumn get memberFkId => integer().nullable()();

  IntColumn get memberTypeId => integer().nullable()();

  IntColumn get memberTypeFkId => integer().nullable()();

  IntColumn get promotionBuyId => integer().nullable()();

  // ===================================================== //

  IntColumn get groupTotal => integer().nullable()();

  TextColumn get name => text().nullable()();

  TextColumn get voucherPriceType => text().nullable()();
  TextColumn get priceType => text().nullable()();

  TextColumn get voucherPriceValue => text().nullable()();
  IntColumn get price => integer().nullable()();

  TextColumn get groupDetail =>
      text().map(const ListDealGroupConverter()).nullable()();

  // ===================================================== //

  IntColumn get point => integer().nullable()();

  TextColumn get voucherCode => text().nullable()();

  TextColumn get outlet => text().map(const ListOutletConverter()).nullable()();

  TextColumn get photo => text().nullable()();

  // ===================================================== //

  IntColumn get dealsCanBuy => integer().nullable()();

  IntColumn get dealsValue => integer().nullable()();

  TextColumn get inputDeals => text().nullable()();

  TextColumn get notify => text().nullable()();

  // ===================================================== //

  IntColumn get memberMaximumRedeem => integer().nullable()();

  IntColumn get maximumDiscountNominal => integer().nullable()();

  IntColumn get maximumRedeem => integer().nullable()();

  IntColumn get maximumRedeemPeriod => integer().nullable()();

  IntColumn get maximumRedeemPeriodDays => integer().nullable()();

  IntColumn get maxQtyPromo => integer().nullable()();

  // ===================================================== //

  IntColumn get promoDiscountMaximum => integer().nullable()();

  TextColumn get promoDiscountType => text().nullable()();

  // ===================================================== //

  IntColumn get startPromotionDate => integer().nullable()();

  TextColumn get startPromotionTime => text().nullable()();

  IntColumn get endPromotionDate => integer().nullable()();

  TextColumn get endPromotionTime => text().nullable()();

  IntColumn get promoNominal => integer().nullable()();

  TextColumn get promotionTypeName => text().nullable()();

  TextColumn get promoType => text().nullable()();

  TextColumn get discountType => text().nullable()();

  TextColumn get promotionProduct =>
      text().map(const ListProductConverter()).nullable()();

  TextColumn get products =>
      text().map(const ListProductConverter()).nullable()();

  // ===================================================== //

  IntColumn get publishDate => integer().nullable()();

  IntColumn get isPercent => integer().nullable()();

  IntColumn get active => integer().nullable()();

  IntColumn get totalRedeem => integer().nullable()();

  IntColumn get minOrder => integer().nullable()();

  IntColumn get qty => integer().nullable()();

  IntColumn get sunday => integer().nullable()();

  IntColumn get monday => integer().nullable()();

  IntColumn get tuesday => integer().nullable()();

  IntColumn get wednesday => integer().nullable()();

  IntColumn get thursday => integer().nullable()();

  IntColumn get friday => integer().nullable()();

  IntColumn get saturday => integer().nullable()();

  BoolColumn get isHeader =>
      boolean().withDefault(const Constant(false)).nullable()();

  TextColumn get option => text().nullable()();

  TextColumn get redeemQrCodeUrl => text().nullable()();

  TextColumn get memberTypes =>
      text().map(const ListMemberTypesConverter()).nullable()();

  TextColumn get redeemQrCode => text().nullable()();

  TextColumn get source => text().nullable()();

  TextColumn get amount => text().nullable()();

  TextColumn get term => text().nullable()();

  TextColumn get startDateDb => text().nullable()();

  TextColumn get timeActive =>
      text().map(const TimeActiveConverter()).nullable()();

  TextColumn get termProduct =>
      text().map(const TermProductConverter()).nullable()();

  // ===================================================== //
  IntColumn get timeCreated => integer().nullable()();

  IntColumn get created => integer().nullable()();

  IntColumn get modified => integer().nullable()();

  IntColumn get timeModified => integer().nullable()();

  @override
  Set<Column> get primaryKey => {promotionId};
}
