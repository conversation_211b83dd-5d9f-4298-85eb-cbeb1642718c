// ignore_for_file: non_constant_identifier_names

import 'package:drift/drift.dart';
import 'package:mobile_crm/data/providers/db/local/converter/converter.dart';

class Wishlist extends Table {
  IntColumn get productFkId => integer().nullable()();

  IntColumn get crmProductWishlistId => integer().nullable()();

  IntColumn get adminFkId => integer().nullable()();

  IntColumn get memberFkId => integer().nullable()();

  IntColumn get timeCreated => integer().nullable()();

  IntColumn get wishlistCategoryFkId => integer().nullable()();

  IntColumn get total => integer().nullable()();

  TextColumn get product => text().map(const ProductConverter()).nullable()();

  @override
  Set<Column> get primaryKey => {productFkId};
}
