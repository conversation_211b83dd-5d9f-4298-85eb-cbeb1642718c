// ignore_for_file: non_constant_identifier_names

import 'package:drift/drift.dart';

class Membership extends Table {
  IntColumn get lifetime_day => integer().named("lifetimeDay").nullable()();

  IntColumn get point_target => integer().named("pointTarget").nullable()();

  IntColumn get spent_target => integer().named("spentTarget").nullable()();

  IntColumn get current => integer().nullable()();

  IntColumn get price => integer().nullable()();

  TextColumn get lifetime_type => text().named("lifetimeType").nullable()();

  TextColumn get name => text().nullable()();
}
