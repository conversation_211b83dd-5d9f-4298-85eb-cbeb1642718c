// ignore_for_file: non_constant_identifier_names

import 'package:drift/drift.dart';

class AddressTable extends Table {
  IntColumn get membersAddressId => integer().nullable()();

  TextColumn get address => text().nullable()();
  TextColumn get label => text().nullable()();
  TextColumn get city => text().nullable()();
  TextColumn get phone => text().nullable()();
  TextColumn get postalCode => text().nullable()();
  TextColumn get province => text().nullable()();
  TextColumn get district => text().nullable()();
  BoolColumn get isDefaultAddress => boolean().nullable()();
}
