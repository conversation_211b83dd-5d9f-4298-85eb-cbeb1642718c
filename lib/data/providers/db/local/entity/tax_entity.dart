// ignore_for_file: non_constant_identifier_names

import 'package:drift/drift.dart';
import 'package:mobile_crm/data/providers/db/local/converter/converter.dart';

class TaxEntity extends Table {
  IntColumn get id => integer().nullable()();
  IntColumn get gratuityId => integer().nullable()();
  IntColumn get jumlah => integer().nullable()();
  TextColumn get name => text().nullable()();
  TextColumn get taxCategory => text().nullable()();
  TextColumn get statusTax => text().nullable()();
  TextColumn get typeTax => text().nullable()();

  TextColumn get product =>
      text().map(const ListProductConverter()).nullable()();

  @override
  Set<Column<Object>>? get primaryKey => {gratuityId};
}
