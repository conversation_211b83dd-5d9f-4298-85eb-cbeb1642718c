// ignore_for_file: non_constant_identifier_names

import 'package:drift/drift.dart';

class NotificationMe extends Table {
  IntColumn get id => integer().nullable()();

  TextColumn get title => text().nullable()();

  TextColumn get body => text().nullable()();

  TextColumn get notificationType => text().nullable()();

  IntColumn get scheduleTime => integer().nullable()();

  TextColumn get payload => text().nullable()();

  BoolColumn get androidAllowWhileIdle =>
      boolean().nullable().withDefault(const Constant(false))();

  @override
  Set<Column> get primaryKey => {id};
}
