// ignore_for_file: non_constant_identifier_names

import 'package:drift/drift.dart';

class UnitConversionEntity extends Table {
  IntColumn get productsUnitConversionId => integer().nullable()();
  IntColumn get productFkId => integer().nullable()();
  IntColumn get unitFkId => integer().nullable()();
  IntColumn get qty => integer().nullable()();
  IntColumn get unitId => integer().nullable()();
  IntColumn get adminFkId => integer().nullable()();

  TextColumn get name => text().nullable()();
  TextColumn get description => text().nullable()();
  TextColumn get dataStatus => text().nullable()();

  @override
  Set<Column> get primaryKey => {productsUnitConversionId};
}
