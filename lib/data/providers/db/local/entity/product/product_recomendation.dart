import 'package:drift/drift.dart';

class ProductRecomendation extends Table {
  TextColumn get description => text().nullable()();
  IntColumn get minQtyOrder => integer().named("min_qty_order").nullable()();
  TextColumn get name => text().nullable()();
  IntColumn get outletFkid => integer().named("outlet_fkid").nullable()();
  TextColumn get outletName => text().named("outlet_name").nullable()();
  TextColumn get photo => text().nullable()();
  IntColumn get priceSell => integer().named("price_sell").nullable()();
  IntColumn get productDetailId =>
      integer().named("product_detail_id").nullable()();
  IntColumn get productId => integer().named("product_id").nullable()();
  IntColumn get productSubcategoryFkid =>
      integer().named("product_subcategory_fkid").nullable()();
  TextColumn get stock => text().nullable()();
  IntColumn get stockManagement =>
      integer().named("stock_management").nullable()();
  IntColumn get stockQty => integer().named("stock_qty").nullable()();
  IntColumn get unitFkid => integer().named("unit_fkid").nullable()();
  IntColumn get variantId => integer().named("variant_id").nullable()();
  TextColumn get variantName => text().named("variant_name").nullable()();

  @override
  Set<Column> get primaryKey => {productId};
}
