// ignore_for_file: non_constant_identifier_names

import 'package:drift/drift.dart';
import 'package:mobile_crm/data/providers/db/local/converter/converter.dart';


class LinkMenu extends Table {
  IntColumn get linkMenuId => integer().nullable()();
  TextColumn get name => text().nullable()();
  TextColumn get description => text().nullable()();
  IntColumn get orderNo => integer().nullable()();
  IntColumn get isMultipleChoice => integer().nullable()();
  IntColumn get outletFkId => integer().nullable()();
  IntColumn get productFkId => integer().nullable()();
  IntColumn get productDetailFkId => integer().nullable()();
  TextColumn get linkMenuDetail => text().map(const ListLinkMenuDetailConverter()).nullable()();

  @override
  Set<Column> get primaryKey => {linkMenuId};
}
