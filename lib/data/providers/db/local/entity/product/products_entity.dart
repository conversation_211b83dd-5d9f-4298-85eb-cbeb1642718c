// ignore_for_file: non_constant_identifier_names

import 'package:drift/drift.dart';
import 'package:mobile_crm/data/providers/db/local/converter/converter.dart';
import 'package:mobile_crm/data/providers/db/local/converter/subcategory_config_converter.dart';

class Products extends Table {
  IntColumn get adminFkId => integer().named("admin_fkid").nullable()();

  IntColumn get product_fkid => integer().nullable()();

  IntColumn get product_detail_id => integer().nullable()();

  IntColumn get productDetailFkId =>
      integer().named("product_detail_fkid").nullable()();

  IntColumn get productId => integer().named("product_id").nullable()();

  IntColumn get productSubcategoryId =>
      integer().named("product_subcategory_id").nullable()();

  IntColumn get outletId => integer().named("outletId").nullable()();

  IntColumn get price => integer().named("price").nullable()();

  IntColumn get priceSell => integer().nullable()();

  IntColumn get priceSellAvg => integer().nullable()();

  IntColumn get priceSellEnd => integer().nullable()();

  IntColumn get priceSellStart => integer().nullable()();

  IntColumn get priceSellPromo => integer().nullable()();
  IntColumn get priceSellDiscount => integer().nullable()();

  IntColumn get pricePromo => integer().named("price_promo").nullable()();

  IntColumn get totalAvailable =>
      integer().named("total_available").nullable()();

  IntColumn get min_qty_order => integer().nullable()();

  BoolColumn get is_in_wishlist => boolean()
      .named('isInWishlist')
      .withDefault(const Constant(false))
      .nullable()();

  BoolColumn get isHeader =>
      boolean().withDefault(const Constant(false)).nullable()();

  TextColumn get availability => text().named("availability").nullable()();

  TextColumn get barcode => text().named("barcode").nullable()();

  TextColumn get description =>
      text().named("description").nullable().withDefault(const Constant(''))();

  TextColumn get hourEnd => text().named("hour_end").nullable()();

  TextColumn get hourStart => text().named("hour_start").nullable()();

  TextColumn get name => text().named("name").nullable()();

  TextColumn get photo => text().named("photo").nullable()();

  TextColumn get subcategory => text().named("subcategory").nullable()();

  TextColumn get unit => text().named("unit").nullable()();

  TextColumn get unitDescription => text()
      .named("unit_description")
      .nullable()
      .withDefault(const Constant(''))();

  IntColumn get stockManagement =>
      integer().named('stock_management').nullable()();

  TextColumn get stock_status =>
      text().nullable().withDefault(const Constant(''))();

  TextColumn get stockStatusOld => text()
      .named('stock_status_old')
      .nullable()
      .withDefault(const Constant(''))();

  TextColumn get stockStatusesOld => text()
      .named('stock_statuses_old')
      .nullable()
      .withDefault(const Constant(''))();

  TextColumn get stockStatusesOld2 => text()
      .named('stock_statuses_old_2')
      .nullable()
      .withDefault(const Constant(''))();

  IntColumn get dataCreated => integer().named("data_created").nullable()();

  IntColumn get subcategoryPosition =>
      integer().named("subcategory_position").nullable()();

  IntColumn get dataModified => integer().named("data_modified").nullable()();

  IntColumn get dataStatus => integer().named("data_status").nullable()();

  TextColumn get available => text()
      .map(const ListAvailableConverter())
      .named("available")
      .nullable()();

  TextColumn get variant => text().map(const VariantConverter()).nullable()();
  TextColumn get tax => text().map(const ListTaxConverter()).nullable()();
  TextColumn get subcategoryConfig =>
      text().map(const SubcategoryConfigConverter()).nullable()();

  IntColumn get stockQty => integer().named("stock_qty").nullable()();

  @override
  Set<Column> get primaryKey => {productId};
}
