// ignore_for_file: non_constant_identifier_names

import 'package:drift/drift.dart';
import 'package:mobile_crm/data/providers/db/local/converter/converter.dart';

class TransactionNewTable extends Table {
  IntColumn get id => integer().nullable()();
  IntColumn get outletId => integer().nullable()();
  IntColumn get point => integer().nullable()();
  TextColumn get orderType => text().nullable()();
  TextColumn get orderSalesId => text().nullable()();
  TextColumn get message => text().nullable()();
  TextColumn get customerName => text().nullable()();
  TextColumn get receiptReceiver => text().nullable()();
  TextColumn get diningTable => text().nullable()();
  TextColumn get orderNote => text().nullable()();
  TextColumn get orderList =>
      text().map(const ListCartConverter()).nullable()();
  TextColumn get shipment => text().map(const ShipmentConverter()).nullable()();
  TextColumn get pickup => text().map(const PickupConverter()).nullable()();
  TextColumn get outlet => text().map(const OutletConverter()).nullable()();
  TextColumn get selfOrder => text().map(const OrderConverter()).nullable()();
  TextColumn get status => text().nullable()();
  TextColumn get path => text().nullable()();
  TextColumn get deal => text().map(const DealConverter()).nullable()();
  BoolColumn get isOnOrder => boolean().nullable()();
  IntColumn get timeOrder => integer().nullable()();
  IntColumn get subTotal => integer().nullable()();
  IntColumn get totalQTY => integer().nullable()();
  IntColumn get totalBill => integer().nullable()();
  IntColumn get totalDiscount => integer().nullable()();
  IntColumn get totalTax => integer().nullable()();
  IntColumn get paymentTimeout => integer().nullable()();

  @override
  Set<Column> get primaryKey => {id};
}
