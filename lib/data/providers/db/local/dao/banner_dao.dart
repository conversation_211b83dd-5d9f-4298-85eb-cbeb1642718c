import 'package:drift/drift.dart';
import 'package:mobile_crm/data/providers/db/local/entity/banner_entity.dart';

import '../../database.dart';

part 'banner_dao.g.dart';

@DriftAccessor(tables: [BannerApp])
class BannerDao extends DatabaseAccessor<AppDb> with _$BannerDaoMixin {
  BannerDao(AppDb db) : super(db);

  Future<List<BannerAppData>> getAllBanner() => select(db.bannerApp).get();

  Future insertAll(List<BannerAppData> banners) async =>
      await db.bannerApp.deleteAll().whenComplete(() async => await db.bannerApp
          .insertAll(banners.map((e) => e.toCompanion(true)),
              mode: InsertMode.insertOrReplace));

  Future deleteAllBanner() async => (delete(bannerApp).go());
}
