import 'package:drift/drift.dart';
import 'package:mobile_crm/data/providers/db/local/entity/product/products_entity.dart';

import '../../database.dart';

part 'product_dao.g.dart';

@DriftAccessor(
    tables: [Products],
    queries: {'totalProducts': 'SELECT COUNT(*) FROM products;'})
class ProductDao extends DatabaseAccessor<AppDb> with _$ProductDaoMixin {
  ProductDao(AppDb db) : super(db);

  Future<List<Product>> getAllProduct() => select(db.products).get();

  Stream<List<Product>> watchAllProduct() => select(db.products).watch();

  Future<List<Product>> getProductBySearchAndOutlet(String search, int id) {
    final query = select(products)
      ..where((tbl) => tbl.name.contains(search))
      ..where((tbl) => tbl.outletId.equals(id))
      ..orderBy([(p) => OrderingTerm(expression: p.name)]);
    return query.map((row) => row).get();
  }

  Stream<List<Product>> watchProductBySearchAndOutlet(String search, int id) {
    final query = select(products)
      ..where((tbl) => tbl.name.contains(search))
      ..where((tbl) => tbl.outletId.equals(id))
      ..orderBy([(p) => OrderingTerm(expression: p.name)]);
    return query.map((row) => row).watch();
  }

  Future<Product?> getProductById(int id) =>
      (select(products)..where((tbl) => tbl.productId.equals(id)))
          .getSingleOrNull();

  Future<Product?> getProductByDetailId(int id) async =>
      await select(db.products).get().then((value) {
        if(id == 0){
          return null;
        }
        for (var product in value) {
          if (product.available != null) {
            var searchP =
                (product.available?.where((el) => el.productDetailId == id));
            if (searchP != null && searchP.isNotEmpty) {
              return product;
            }
          } else {
            for (var variant in product.variant ?? []) {
              var searchP =
                  variant.available?.where((el) => el.productDetailId == id);
              if (searchP != null && searchP.isNotEmpty) {
                return product;
              }
            }
          }
        }
        return null;
      });

  Future<int> getTotalProduct() {
    return totalProducts().getSingle();
  }

  Future<int> insertProduct(ProductsCompanion entity) async =>
      await into(products).insert(entity, mode: InsertMode.insertOrReplace);

  Future insertAll(List<Product> products) async {
    await deleteAllProduct().whenComplete(() async => await db.products
        .insertAll(products, mode: InsertMode.insertOrReplace));
  }

  Future<int> deleteProduct(int id) async =>
      await (delete(products)..where((tbl) => tbl.productId.equals(id))).go();

  Future deleteAllProduct() async {
    return await (delete(products).go());
  }
}
