import 'package:drift/drift.dart';
import 'package:mobile_crm/data/providers/db/database.dart';
import 'package:mobile_crm/data/providers/db/local/entity/entity.dart';

part "faq_dao.g.dart";

@DriftAccessor(tables: [Faq])
class FaqDao extends DatabaseAccessor<AppDb> with _$FaqDaoMixin {
  FaqDao(AppDb db) : super(db);

  Future<List<FaqData>> getAll() => select(db.faq).get();

  Future<int> insertFaq(List<FaqData> entity) async =>
      await db.faq.deleteAll().whenComplete(
            () async => await db.faq
                .insertAll(entity, mode: InsertMode.insertOrReplace),
          );

  Future<int> deleteAllFaq() => (delete(faq).go());
}
