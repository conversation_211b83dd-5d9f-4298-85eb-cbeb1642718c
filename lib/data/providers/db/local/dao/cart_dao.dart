import 'package:drift/drift.dart';
import 'package:mobile_crm/data/providers/db/local/entity/carts_entity.dart';

import '../../database.dart';

part 'cart_dao.g.dart';

@DriftAccessor(
    tables: [Carts], queries: {'totalCarts': 'SELECT COUNT(*) FROM carts;'})
class CartDao extends DatabaseAccessor<AppDb> with _$CartDaoMixin {
  CartDao(AppDb db) : super(db);

  Future<List<Cart>> getAllCart() => select(db.carts).get();

  Stream<List<Cart>> watchAllCart() => select(db.carts).watch();

  Stream<List<Cart>> watchAllCartByOutletId(int outletId) =>
      (select(db.carts)..where((tbl) => tbl.outlet_fkid.equals(outletId)))
          .watch();

  Future<List<Cart>> getCartByOutletId(int id) =>
      (select(db.carts)..where((tbl) => tbl.outlet_fkid.equals(id))).get();

  Future<int> insertAll(List<Cart> carts) async =>
      await db.carts.deleteAll().whenComplete(() async => await db.carts
          .insertAll(carts.map((e) => e.toCompanion(true)),
              mode: InsertMode.insertOrReplace));

  Future<int> insert(Cart cart) async {
    Cart? find = await (select(db.carts)
          ..where((tbl) => (tbl.product_fkid.equals(cart.product_fkid ?? 0)))
          ..where((tbl) =>
              (tbl.product_detail_fkid.equals(cart.product_detail_fkid ?? 0))))
        .getSingleOrNull();
    if (find != null) {
      cart = cart.copyWith(qty: Value((find.qty ?? 0) + 1));
      return await updateCart(cart);
    }
    return await into(carts).insert(cart, mode: InsertMode.insertOrReplace);
  }

  Future<int> deleteCart(int id) async =>
      await (delete(carts)..where((tbl) => tbl.crm_cart_id.equals(id))).go();

  Future<int> deleteCartByProductId(int id) async =>
      await (delete(carts)..where((tbl) => tbl.product_detail_fkid.equals(id)))
          .go();

  Future<int> getTotalCart() {
    return totalCarts().getSingle();
  }

  Future<int> updateCart(Cart companion) async {
    return await (update(carts)
          ..where((tbl) => tbl.product_fkid.equals(companion.product_fkid ?? 0))
          ..where((tbl) => tbl.product_detail_fkid
              .equals(companion.product_detail_fkid ?? 0)))
        .write(companion);
  }

  Future<int> updateQtyCart(Cart cart) async {
    return (update(carts)
          ..where((t) =>
              t.product_detail_fkid.equals(cart.product_detail_fkid ?? 0))
          ..where((tbl) => tbl.product_fkid.equals(cart.product_fkid ?? 0)))
        .write(
      CartsCompanion(
        qty: Value(cart.qty),
      ),
    );
  }

  Stream<int> watchTotalCart() {
    return totalCarts().watchSingle();
  }

  Future deleteAllCart() async => (delete(carts).go());
}
