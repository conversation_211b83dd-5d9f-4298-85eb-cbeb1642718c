import 'package:drift/drift.dart';

import '../../database.dart';
import '../entity/deal_entity.dart';

part 'deal_dao.g.dart';

@DriftAccessor(tables: [Deal])
class DealDao extends DatabaseAccessor<AppDb> with _$DealDaoMixin {
  DealDao(AppDb db) : super(db);

  Future<List<DealData>> getAll() async => await (select(deal)
        ..orderBy([
          (tbl) =>
              OrderingTerm(expression: tbl.timeCreated, mode: OrderingMode.desc)
        ]))
      .get();

  Future<List<DealData>> insertAll(List<DealData> entity) async {
    return await deal
        .deleteAll()
        .whenComplete(() async =>
            await deal.insertAll(entity, mode: InsertMode.insertOrReplace))
        .then((value) async => await getAll());
  }
}
