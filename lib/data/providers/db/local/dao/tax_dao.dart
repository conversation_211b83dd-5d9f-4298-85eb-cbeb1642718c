
import 'package:drift/drift.dart';
import 'package:mobile_crm/app/utils/logger.dart';
import 'package:mobile_crm/data/models/tax_model.dart';
import 'package:mobile_crm/data/providers/db/local/entity/entity.dart';

import '../../database.dart';

part 'tax_dao.g.dart';

@DriftAccessor(tables: [TaxEntity])
class TaxDao extends DatabaseAccessor<AppDb> with _$TaxDaoMixin {
  TaxDao(AppDb db) : super(db);

  Future<List<TaxModel>> getAllTaxes() async {
    try {
      var result = await select(db.taxEntity).get();
      return List<TaxModel>.from(
        result.map(
          (e) => TaxModel(
            name: e.name,
            product: e.product,
            gratuityId: e.gratuityId,
            jumlah: e.jumlah,
            taxCategory: e.taxCategory,
            statusTax: e.statusTax,
            typeTax: e.typeTax,
            id: e.id,
          ),
        ),
      );
    } catch (e, s) {
      errorLogger(pos: "Get all taxes dao", error: e, stackTrace: s);
      return [];
    }
  }

  Future<List<TaxModel>> getProductDetailId(int? productDetailId) async {
    try {
      if (productDetailId == null) {
        return [];
      }
      var result = await select(taxEntity).get().then((value) =>
          value.where((element) => element.statusTax == 'permanent').toList());
      List<TaxModel> found = [];

      for (var e in result) {
        var search = e.product
            ?.where((element) => element.productDetailFkId == productDetailId)
            .toList();
        if (search?.isNotEmpty == true) {
          found.add(
            TaxModel(
              name: e.name,
              productDetailFkId: productDetailId,
              gratuityId: e.gratuityId,
              jumlah: e.jumlah,
              taxCategory: e.taxCategory,
              statusTax: e.statusTax,
              typeTax: e.typeTax,
              id: e.id,
            ),
          );
        }
      }
      infoLogger("Get Taxes By Id ",
          "${found.length} | ${result.length} | id $productDetailId");
      return found;
    } catch (e, s) {
      errorLogger(
          pos: "Get taxes by product detail id dao", error: e, stackTrace: s);
      return [];
    }
  }

  Future insertAllTaxes(List<TaxModel> taxes) async {
    await deleteAllTaxes().whenComplete(() async {
      await db.taxEntity.insertAll(taxes, mode: InsertMode.insertOrReplace);
    });
  }

  Future deleteAllTaxes() async {
    return await (delete(taxEntity).go());
  }
}
