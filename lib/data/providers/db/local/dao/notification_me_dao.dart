import 'package:drift/drift.dart';
import 'package:mobile_crm/data/providers/db/local/entity/entity.dart';

import '../../database.dart';

part 'notification_me_dao.g.dart';

@DriftAccessor(tables: [NotificationMe])
class NotificationMeDao extends DatabaseAccessor<AppDb>
    with _$NotificationMeDaoMixin {
  NotificationMeDao(AppDb db) : super(db);

  Future<List<NotificationMeData>> getAllNotification() async =>
      await select(notificationMe).get();

  Future<NotificationMeData?> getSingle(String id) async =>
      await (select(notificationMe)
            ..where((tbl) => tbl.id.equals(int.tryParse(id) ?? 0)))
          .getSingleOrNull();

  Future<int> insertNotification(NotificationMeCompanion entity) async =>
      await into(notificationMe)
          .insert(entity, mode: InsertMode.insertOrReplace);

  Future<int> deleteNotification(String id) async =>
      await (delete(notificationMe)
            ..where((tbl) => tbl.id.equals(int.tryParse(id) ?? 0)))
          .go();

  Future deleteAllNotification() async => (delete(notificationMe).go());
}
