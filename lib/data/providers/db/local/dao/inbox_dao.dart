import 'package:drift/drift.dart';

import '../../database.dart';
import '../entity/inbox_entity.dart';

part 'inbox_dao.g.dart';

@DriftAccessor(
    tables: [Inbox],
    queries: {'totalInbox': 'SELECT COUNT(*) FROM inbox WHERE is_read = 0;'})
class InboxDao extends DatabaseAccessor<AppDb> with _$InboxDaoMixin {
  InboxDao(AppDb db) : super(db);

  Future<List<InboxData>> getAllInbox() async => await (select(inbox)
        ..orderBy([
          (tbl) => OrderingTerm(
              expression: tbl.data_created, mode: OrderingMode.desc)
        ]))
      .get();

  Stream<List<InboxData>> watchAllInbox() => select(db.inbox).watch();

  Future insertAll(List<InboxData> inboxdata) async =>
      await (delete(inbox).go()).whenComplete(() async => await db.inbox
          .insertAll(inboxdata, mode: InsertMode.insertOrReplace));

  Future<InboxData?> getInbox(int id) =>
      (select(db.inbox)..where((tbl) => tbl.notification_id.equals(id)))
          .getSingleOrNull();

  Future<int> insertInbox(InboxCompanion entity) async =>
      await into(inbox).insert(entity, mode: InsertMode.insertOrReplace);

  Future<int> updateInbox(InboxData entity) async => await (update(inbox)
        ..where(
            (tbl) => tbl.notification_id.equals(entity.notification_id ?? 0)))
      .write(entity);

  Future deleteAll() => (delete(inbox).go());

  Future<int> getTotalInbox() => totalInbox().getSingle();

  Stream<int> watchTotalInbox() => totalInbox().watchSingle();

  Future<List<InboxData>> getInboxPaging(
      {required int page, int limit = 15}) async {
    if (page < 0) {
      page = 1;
    }
    return await (select(inbox)
          ..orderBy([
            (tbl) => OrderingTerm(
                expression: tbl.data_created, mode: OrderingMode.desc)
          ])
          ..limit(limit, offset: (page - 1) * limit))
        .get();
  }
}
