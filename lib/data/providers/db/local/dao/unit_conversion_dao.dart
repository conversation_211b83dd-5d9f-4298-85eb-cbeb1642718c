import 'package:drift/drift.dart';
import 'package:mobile_crm/data/models/unit_conversion_model.dart';
import 'package:mobile_crm/data/providers/db/local/entity/entity.dart';

import '../../database.dart';

part 'unit_conversion_dao.g.dart';

@DriftAccessor(tables: [UnitConversionEntity])
class UnitConversionDao extends DatabaseAccessor<AppDb>
    with _$UnitConversionDaoMixin {
  UnitConversionDao(AppDb db) : super(db);

  Future<List<UnitConversionModel>> getAllUnitConversion() async {
    var data = await select(db.unitConversionEntity).get();
    if (data.isEmpty) {
      return [];
    }

    List<UnitConversionModel> listUnitConversionModel = [];
    for (var unit in data) {
      var newUnit = UnitConversionModel(
          name: unit.name,
          qty: unit.qty,
          productFkId: unit.productFkId,
          description: unit.description,
          adminFkId: unit.adminFkId,
          dataStatus: unit.dataStatus,
          productsUnitConversionId: unit.productsUnitConversionId,
          unitFkId: unit.unitFkId,
          unitId: unit.unitId);
      listUnitConversionModel.add(newUnit);
    }

    return listUnitConversionModel;
  }

  Future<int> insertAllUnitConversion(List<UnitConversionModel> value) async {
    return await db.unitConversionEntity.deleteAll().whenComplete(() async =>
        await db.unitConversionEntity
            .insertAll(value, mode: InsertMode.insertOrReplace));
  }
}
