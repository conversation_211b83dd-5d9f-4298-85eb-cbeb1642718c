import 'package:drift/drift.dart';

import '../../database.dart';
import '../entity/wishlist_category_entity.dart';

part 'wishlist_category_dao.g.dart';

@DriftAccessor(tables: [WishlistCategory])
class WishlistCategoryDao extends DatabaseAccessor<AppDb>
    with _$WishlistCategoryDaoMixin {
  WishlistCategoryDao(AppDb db) : super(db);

  Future<List<WishlistCategoryData>> getAllWishlistCategory() async =>
      await (select(wishlistCategory).get());

  Future<WishlistCategoryData?> getSingleWishlistCategory(int id) async =>
      await (select(wishlistCategory)..where((tbl) => tbl.id.equals(id)))
          .getSingleOrNull();

  Future<WishlistCategoryData?> insertWishlistCategory(
          WishlistCategoryData wishlistCategoryData) async =>
      await into(wishlistCategory)
          .insert(wishlistCategoryData, mode: InsertMode.insertOrReplace)
          .then((value) async => value == 1
              ? await getSingleWishlistCategory(wishlistCategoryData.id ?? 0)
              : null);

  Future<int> insertAllWishlistCategory(
          List<WishlistCategoryData> wishlistCategoryData) async =>
      await (delete(wishlistCategory).go()).whenComplete(() async =>
          await wishlistCategory.insertAll(wishlistCategoryData,
              mode: InsertMode.insertOrReplace));

  Future<int> deleteSingleWishlistCategory(int id) async =>
      await (delete(wishlistCategory)..where((tbl) => tbl.id.equals(id))).go();

  Future<int> deleteAllWishlistCategory() async =>
      await delete(wishlistCategory).go();

  Future<int> countWishlistCategory() async =>
      await (select(wishlistCategory).get().then((value) => value.length));
}
