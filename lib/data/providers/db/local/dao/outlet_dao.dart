import 'package:drift/drift.dart';
import 'package:mobile_crm/data/providers/db/local/entity/outlets_entity.dart';

import '../../database.dart';

part 'outlet_dao.g.dart';

@DriftAccessor(tables: [
  Outlets
]) //, queries: {'totalOutlets': 'SELECT COUNT(*) FROM outlets;'}
class OutletDao extends DatabaseAccessor<AppDb> with _$OutletDaoMixin {
  OutletDao(AppDb db) : super(db);

  Future<List<Outlet>> getAllOutlet() => select(db.outlets).get();

  Future<Outlet?> getOutletById(int id) {
    final query = select(db.outlets)
      ..where((tbl) => tbl.outlet_id.equals(id))
      ..orderBy([(p) => OrderingTerm(expression: p.name)]);
    return query.getSingleOrNull();
  }

  Future insertAll(List<Outlet> outlets) async {
    await db.outlets.deleteAll().whenComplete(() async => await db.outlets
        .insertAll(outlets.map((e) => e.toCompanion(true)),
            mode: InsertMode.insertOrReplace));
  }
}
