import 'package:drift/drift.dart';
import 'package:mobile_crm/data/providers/db/local/entity/membership_entity.dart';

import '../../database.dart';

part 'membership_dao.g.dart';

@DriftAccessor(tables: [Membership])
class MembershipDao extends DatabaseAccessor<AppDb> with _$MembershipDaoMixin {
  MembershipDao(AppDb db) : super(db);

  Future<List<MembershipData>> getAllMembership() =>
      select(db.membership).get();

  Future insertAll(List<MembershipCompanion> member) async =>
      await db.membership.insertAll(member);

  Future deleteAll() => (delete(membership).go());
}
