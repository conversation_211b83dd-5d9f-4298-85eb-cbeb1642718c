import 'package:drift/drift.dart';

import '../../database.dart';
import '../entity/config_entity.dart';

part 'config_dao.g.dart';

@DriftAccessor(tables: [Config])
class ConfigDao extends DatabaseAccessor<AppDb> with _$ConfigDaoMixin {
  ConfigDao(AppDb db) : super(db);

  Future<List<ConfigData>> getALl() => select(db.config).get();

  Future<int> insertConfig(ConfigData entity) async =>
      await db.config.deleteAll().whenComplete(() async =>
          await into(config).insert(entity, mode: InsertMode.insertOrReplace));

  Future<int> deleteAllConfig() => (delete(config).go());
}
