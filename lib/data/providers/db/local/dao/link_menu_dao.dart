import 'package:drift/drift.dart';
import 'package:mobile_crm/data/models/product/link_menu_model.dart';
import 'package:mobile_crm/data/providers/db/local/entity/entity.dart';

import '../../database.dart';

part 'link_menu_dao.g.dart';

@DriftAccessor(tables: [LinkMenu])
class LinkMenuDao extends DatabaseAccessor<AppDb> with _$LinkMenuDaoMixin {
  LinkMenuDao(AppDb db) : super(db);

  List<LinkMenuModel> convertFromDataToModel(List<LinkMenuData> data) {
    List<LinkMenuModel> tmpListLink = [];
    for (var element in data) {
      tmpListLink.add(LinkMenuModel(
          name: element.name,
          description: element.description,
          isMultipleChoice: element.isMultipleChoice,
          linkMenuDetail: element.linkMenuDetail,
          linkMenuId: element.linkMenuId,
          orderNo: element.orderNo,
          outletFkId: element.outletFkId,
          productDetailFkId: element.productDetailFkId,
          productFkId: element.productFkId));
    }
    return tmpListLink;
  }

  Future<List<LinkMenuModel>> getAllLinkMenu() async {
    var data = await select(linkMenu).get();
    if (data.isEmpty) {
      return [];
    }
    return convertFromDataToModel(data);
  }

  Future<List<LinkMenuModel>> getLinkMenuBy(
      {int? outletId, int? productId, int? productDetailId}) async {
    List<LinkMenuData> data = [];

    if (outletId != null) {
      data = await (select(linkMenu)
            ..where((tbl) => tbl.outletFkId.equals(outletId)))
          .get();

      if (productId != null) {
        data =
            data.where((element) => element.productFkId == productId).toList();
      }

      if (productDetailId != null) {
        data = data
            .where((element) => element.productDetailFkId == productDetailId)
            .toList();
      }
      return convertFromDataToModel(data);
    }

    if (productId != null) {
      data = await (select(linkMenu)
            ..where((tbl) => tbl.productFkId.equals(productId)))
          .get();

      if (productDetailId != null) {
        data = data
            .where((element) => element.productDetailFkId == productDetailId)
            .toList();
      }
      return convertFromDataToModel(data);
    }

    if (productDetailId != null) {
      data = await (select(linkMenu)
            ..where((tbl) => tbl.productDetailFkId.equals(productDetailId)))
          .get();
      return convertFromDataToModel(data);
    }
    if (data.isEmpty) {
      return [];
    }
    return convertFromDataToModel(data);
  }

  Future<int> insertAllLinkMenu(List<LinkMenuModel>? newData) async {
    if (newData != null) {
      return await deleteAllLinkMenu().whenComplete(() async =>
          await linkMenu.insertAll(newData, mode: InsertMode.insertOrReplace));
    }
    return 0;
  }

  Future deleteAllLinkMenu() async {
    return await (delete(linkMenu).go());
  }
}
