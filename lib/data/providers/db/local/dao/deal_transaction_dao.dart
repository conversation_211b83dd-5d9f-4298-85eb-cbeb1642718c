import 'package:drift/drift.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/data/providers/db/local/entity/entity.dart';

import '../../database.dart';

part 'deal_transaction_dao.g.dart';

@DriftAccessor(tables: [TransactionData])
class DealTransactionDao extends DatabaseAccessor<AppDb>
    with _$DealTransactionDaoMixin {
  DealTransactionDao(AppDb db) : super(db);

  Future<List<TransactionDataData>> getAll() async =>
      await select(transactionData).get();

  Stream<List<TransactionDataData>> streamAll() =>
      select(transactionData).watch();

  Future<TransactionDataData?> getSingleDealPayment(int id) async =>
      await (select(transactionData)..where((tbl) => tbl.promoId.equals(id)))
          .get()
          .then((value) =>
              value.firstWhereOrNull((element) => element.promoId == id));

  Future<TransactionDataData?> getSingleOrder(int id) async =>
      await (select(transactionData)..where((tbl) => tbl.orderId.equals(id)))
          .getSingleOrNull();

  Future<int> deleteDeal(int id) async =>
      await (delete(transactionData)..where((tbl) => tbl.promoId.equals(id)))
          .go();

  Future<int> deleteOrder(int id) async =>
      await (delete(transactionData)..where((tbl) => tbl.promoId.equals(id)))
          .go();

  Future<TransactionDataData?> insertTransaction(
      TransactionDataData entity) async {
    var result = await into(transactionData)
        .insert(entity, mode: InsertMode.insertOrReplace);
    if (result == 0) {
      return null;
    }
    if (entity.dealPayment == null) {
      return getSingleOrder(entity.orderId ?? 0);
    } else {
      return getSingleDealPayment(entity.promoId ?? 0);
    }
  }

  Future<bool> updateTransaction(TransactionDataData entity) async =>
      await update(transactionData).replace(entity);

  Future<int> deleteTransaction(int id) async =>
      await (delete(transactionData)..where((tbl) => tbl.id.equals(id))).go();

  Future deleteAll() async => await (delete(transactionData).go());
}
