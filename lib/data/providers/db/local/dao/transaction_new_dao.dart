import 'package:drift/drift.dart';
import 'package:mobile_crm/data/providers/db/local/entity/entity.dart';

import '../../../../models/transaction_new_model.dart';
import '../../database.dart';

part 'transaction_new_dao.g.dart';

@DriftAccessor(tables: [TransactionNewTable])
class TransactionNewDao extends DatabaseAccessor<AppDb>
    with _$TransactionNewDaoMixin {
  TransactionNewDao(AppDb db) : super(db);

  Future<List<TransactionNewModel>> getAllTransactionNew(
      {bool? isOnOrder}) async {
    List<TransactionNewModel> result = [];
    var data = await select(db.transactionNewTable).get();
    for (var trans in data) {
      var i = TransactionNewModel(
        id: trans.id,
        outletId: trans.outletId,
        orderType: trans.orderType,
        customerName: trans.customerName,
        message: trans.message,
        receiptReceiver: trans.receiptReceiver,
        diningTable: trans.diningTable,
        orderNote: trans.orderNote,
        orderList: trans.orderList,
        shipment: trans.shipment,
        pickup: trans.pickup,
        selfOrder: trans.selfOrder,
        orderSalesId: trans.orderSalesId,
        status: trans.status,
        point: trans.point,
        deal: trans.deal,
        subTotal: trans.subTotal,
        totalQTY: trans.totalQTY,
        totalBill: trans.totalBill,
        totalDiscount: trans.totalDiscount,
        totalTax: trans.totalTax,
        outlet: trans.outlet,
        timeOrder: trans.timeOrder,
        isOnOrder: trans.isOnOrder,
        path: trans.path,
        paymentTimeout: trans.paymentTimeout,
      );
      result.add(i);
    }
    if (isOnOrder != null) {
      result =
          result.where((element) => element.isOnOrder == isOnOrder).toList();
    }
    return result;
  }

  Stream<List<TransactionNewTableData>> watchAllTransactionNew() =>
      select(db.transactionNewTable).watch();

  Future<TransactionNewModel?> getTransactionNewByOutletId(int id,
      {required bool isOnOrder,
      TransactionNewTableData? transactionNewModel}) async {
    if (isOnOrder && transactionNewModel?.selfOrder == null) {
      var trans = await (select(db.transactionNewTable)
            ..where((tbl) => tbl.orderSalesId
                .equals(transactionNewModel?.orderSalesId ?? ''))
            ..where((tbl) => tbl.isOnOrder.equals(isOnOrder)))
          .getSingleOrNull();
      if (trans != null) {
        return TransactionNewModel(
            id: trans.id,
            outletId: trans.outletId,
            orderType: trans.orderType,
            customerName: trans.customerName,
            message: trans.message,
            receiptReceiver: trans.receiptReceiver,
            diningTable: trans.diningTable,
            orderNote: trans.orderNote,
            outlet: trans.outlet,
            orderList: trans.orderList,
            shipment: trans.shipment,
            pickup: trans.pickup,
            selfOrder: trans.selfOrder,
            orderSalesId: trans.orderSalesId,
            status: trans.status,
            point: trans.point,
            deal: trans.deal,
            subTotal: trans.subTotal,
            totalQTY: trans.totalQTY,
            totalBill: trans.totalBill,
            totalDiscount: trans.totalDiscount,
            totalTax: trans.totalTax,
            isOnOrder: trans.isOnOrder,
            timeOrder: trans.timeOrder,
            path: trans.path,
            paymentTimeout: trans.paymentTimeout);
      }
    } else {
      var trans = await (select(db.transactionNewTable)
            ..where((tbl) => tbl.outletId.equals(id))
            ..where((tbl) => tbl.isOnOrder.equals(isOnOrder))
            ..where((tbl) {
              if (isOnOrder == true && transactionNewModel != null) {
                return tbl.orderSalesId
                    .equals(transactionNewModel.orderSalesId ?? '');
              } else {
                return tbl.isOnOrder.equals(isOnOrder);
              }
            }))
          .getSingleOrNull();
      if (trans != null) {
        return TransactionNewModel(
            id: trans.id,
            outletId: trans.outletId,
            orderType: trans.orderType,
            customerName: trans.customerName,
            message: trans.message,
            receiptReceiver: trans.receiptReceiver,
            diningTable: trans.diningTable,
            orderNote: trans.orderNote,
            outlet: trans.outlet,
            orderList: trans.orderList,
            shipment: trans.shipment,
            pickup: trans.pickup,
            selfOrder: trans.selfOrder,
            orderSalesId: trans.orderSalesId,
            status: trans.status,
            point: trans.point,
            deal: trans.deal,
            subTotal: trans.subTotal,
            totalQTY: trans.totalQTY,
            totalBill: trans.totalBill,
            totalDiscount: trans.totalDiscount,
            totalTax: trans.totalTax,
            isOnOrder: trans.isOnOrder,
            timeOrder: trans.timeOrder,
            path: trans.path,
            paymentTimeout: trans.paymentTimeout);
      }
    }
    return null;
  }

  Future<int> insertAll(
          List<TransactionNewTableData> transactionNewData) async =>
      await db.transactionNewTable.deleteAll().whenComplete(() async => await db
          .transactionNewTable
          .insertAll(transactionNewData.map((e) => e.toCompanion(true)),
              mode: InsertMode.insertOrReplace));

  Future<int> insert(TransactionNewTableData transactionNewModel) async =>
      await into(transactionNewTable)
          .insert(transactionNewModel, mode: InsertMode.insertOrReplace);

  Future<int> updateTrans(TransactionNewTableData transactionNewData,
      {required bool isOnOrder}) async {
    var check = await getTransactionNewByOutletId(
        transactionNewData.outletId ?? 0,
        isOnOrder: isOnOrder,
        transactionNewModel: transactionNewData);
    if (check == null) {
      return await insert(transactionNewData);
    }

    if (isOnOrder && transactionNewData.selfOrder == null) {
      return await (update(transactionNewTable)
            ..where((tbl) =>
                tbl.orderSalesId.equals(transactionNewData.orderSalesId ?? ''))
            ..where((tbl) => tbl.isOnOrder.equals(isOnOrder)))
          .write(transactionNewData);
    } else {
      return await (update(transactionNewTable)
            ..where(
                (tbl) => tbl.outletId.equals(transactionNewData.outletId ?? 0))
            ..where((tbl) => tbl.isOnOrder.equals(isOnOrder)))
          .write(transactionNewData);
    }
  }

  //
  Future<int> deleteTrans(TransactionNewTableData transactionNewData,
          {required bool isOnOrder}) async =>
      await (delete(transactionNewTable)
            ..where(
                (tbl) => tbl.outletId.equals(transactionNewData.outletId ?? 0))
            ..where((tbl) => tbl.isOnOrder.equals(isOnOrder))
            ..where((tbl) {
              if (isOnOrder == true) {
                if (transactionNewData.selfOrder != null) {
                  return tbl.selfOrder
                      .equalsValue(transactionNewData.selfOrder);
                }
                return tbl.orderSalesId
                    .equals(transactionNewData.orderSalesId ?? '');
              }
              return tbl.isOnOrder.equals(isOnOrder);
            }))
          .go();

  Future<int> deleteAllTrans() async => await delete(transactionNewTable).go();
}
