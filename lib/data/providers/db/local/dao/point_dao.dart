import 'package:drift/drift.dart';
import 'package:mobile_crm/data/providers/db/local/entity/point_entity.dart';

import '../../database.dart';

part 'point_dao.g.dart';

@DriftAccessor(tables: [Point])
class PointDao extends DatabaseAccessor<AppDb> with _$PointDaoMixin {
  PointDao(AppDb db) : super(db);

  Future<List<PointData>> getAll() => select(db.point).get();

  Future insertAll(List<PointData> points) async =>
      await db.point.deleteAll().whenComplete(() async =>
          await db.point.insertAll(points.map((e) => e.toCompanion(true))));
}
