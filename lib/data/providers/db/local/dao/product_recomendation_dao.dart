import 'package:drift/drift.dart';
import 'package:mobile_crm/data/providers/db/database.dart';
import 'package:mobile_crm/data/providers/db/local/entity/product/product_recomendation.dart';

part "product_recomendation_dao.g.dart";

@DriftAccessor(tables: [ProductRecomendation])
class ProductRecomendationDao extends DatabaseAccessor<AppDb>
    with _$ProductRecomendationDaoMixin {
  ProductRecomendationDao(AppDb db) : super(db);

  Future<List<ProductRecomendationData>> getAll() =>
      select(db.productRecomendation).get();

  Future<int> insertProductRecomendation(
      List<ProductRecomendationData> entity) async {
    return await db.productRecomendation.deleteAll().whenComplete(() async =>
        await db.productRecomendation
            .insertAll(entity, mode: InsertMode.insertOrReplace));
  }

  Future<int> deleteAllProductRecomendation() =>
      (delete(db.productRecomendation).go());
}
