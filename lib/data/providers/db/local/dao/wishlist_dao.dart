import 'package:drift/drift.dart';

import '../../database.dart';
import '../entity/wishlist_entity.dart';

part 'wishlist_dao.g.dart';

@DriftAccessor(tables: [Wishlist])
class WishlistDao extends DatabaseAccessor<AppDb> with _$WishlistDaoMixin {
  WishlistDao(AppDb db) : super(db);

  Future<List<WishlistData>> getAllWishlist() async =>
      await (select(wishlist).get());

  Future<List<WishlistData>> getAllWishlistByCategoryId(int categoryId) async =>
      await (select(wishlist)
            ..where((tbl) => tbl.wishlistCategoryFkId.equals(categoryId)))
          .get();

  Future<WishlistData?> getSingleWishlist(int wishlistId) async =>
      await (select(wishlist)
            ..where((tbl) => tbl.crmProductWishlistId.equals(wishlistId)))
          .getSingleOrNull();

  Future<WishlistData?> getSingleWishlistByProductId(int productFkId) async =>
      await (select(wishlist)
            ..where((tbl) => tbl.productFkId.equals(productFkId)))
          .getSingleOrNull();

  Future<WishlistData?> insertWishlist(
          WishlistCompanion wishlistCompanion) async =>
      await into(wishlist)
          .insert(wishlistCompanion, mode: InsertMode.insertOrReplace)
          .then((value) async => value == 1
              ? await getSingleWishlist(
                  wishlistCompanion.crmProductWishlistId.value ?? 0)
              : null);

  Future<int> insertAllWishlist(List<WishlistData> wishlistData) async =>
      await (delete(wishlist).go()).whenComplete(() async => await wishlist
          .insertAll(wishlistData, mode: InsertMode.insertOrReplace));

  Future<int> deleteSingleWishlist(int wishlistId) async =>
      await (delete(wishlist)
            ..where((tbl) => tbl.crmProductWishlistId.equals(wishlistId)))
          .go();

  Future<int> deleteAllWishlist() async => await delete(wishlist).go();

  Future<int> countWishlist() async =>
      await (select(wishlist).get().then((value) => value.length));
}
