import 'package:drift/drift.dart';
import 'package:mobile_crm/data/providers/db/database.dart';
import 'package:mobile_crm/data/providers/db/local/entity/last_type_entity.dart';

part "last_type_dao.g.dart";

@DriftAccessor(tables: [LastTypeEntity])
class LastTypeDao extends DatabaseAccessor<AppDb> with _$LastTypeDaoMixin {
  LastTypeDao(AppDb db) : super(db);

  Future insert(int outletId, String orderType) async {
    return await db.lastTypeEntity.insertOnConflictUpdate(
      LastTypeEntityCompanion.insert(
        outletId: Value(outletId),
        orderType: Value(orderType),
      ),
    );
  }

  Future<LastTypeEntityData?> getLastOrderType(int outletId) async =>
      (select(db.lastTypeEntity)..where((u) => u.outletId.equals(outletId)))
          .getSingleOrNull();
}
