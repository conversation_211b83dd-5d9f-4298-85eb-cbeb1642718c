// ignore_for_file: prefer_typing_uninitialized_variables

import 'package:mobile_crm/app/utils/logger.dart';
import 'package:mobile_crm/data/models/point_collection_model.dart';
import 'package:mobile_crm/data/models/server_response.dart';
import 'package:mobile_crm/domain/repository/point_collection_repository.dart';

import '../providers/remote/base_http/base_service.dart';

class PointCollectionRepositoryIml extends BaseService
    implements PointCollectionRepository {
  @override
  Future<ServerResponseList<PointCollectionModel>> getPointCollection(
      {bool? active,
      ParentPointType? parentPointType,
      int? memberTypeId}) async {
    try {
      Map<String, dynamic> queryParameters = {};

      if (active != null) {
        queryParameters.addAll({'active': '${active ? 1 : 0}'});
      }

      if (parentPointType != null) {
        queryParameters.addAll({'parent_point_type': parentPointType.name});
      }

      if (memberTypeId != null) {
        queryParameters.addAll({'member_type_id': "$memberTypeId"});
      }
      var response = await get("/v1/point-collection", query: queryParameters);
      // exceptionHandler(response);
      var data = ServerResponseList.fromJson(
          response.body, (json) => PointCollectionModel.fromJsonModel(json));
      queryParameters.clear();
      return data;
    } catch (e, s) {
      errorLogger(pos: "PCR", error: e, stackTrace: s);
      return Future.error("error");
    }
  }
}

enum ParentPointType { account, transaction }
