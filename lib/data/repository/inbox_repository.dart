// ignore_for_file: overridden_fields
import 'package:drift/drift.dart' as drift;
import 'package:mobile_crm/app/utils/logger.dart';
import 'package:mobile_crm/data/models/server_response.dart';
import 'package:mobile_crm/data/providers/db/database.dart';
import 'package:mobile_crm/data/providers/remote/services/inbox_service.dart';
import 'package:mobile_crm/domain/repository/inbox_repository.dart';

class InboxRepositoryIml extends InboxService implements InboxRepository {
  @override
  var className = "Inbox Repository Iml";

  @override
  Future<List<InboxData>> getInbox() async {
    if (store.token == null) {
      return [];
    }
    try {
      List<InboxData> inbox =
          await getAllInboxRemote().then((value) => value.data ?? []) ??
              <InboxData>[];
      await db.inboxDao.insertAll(inbox);
      return await db.inboxDao.getAllInbox();
    } catch (e, s) {
      errorLogger(pos: "$className ~ Get Inbox", error: e, stackTrace: s);
      return await db.inboxDao.getAllInbox();
    }
  }

  @override
  Future<int> updateInbox(InboxData value) async {
    final body = {'notif_id': value.notification_id};
    var response = await put('/v1/inbox', body,
        contentType: 'application/x-www-form-urlencoded');

    if (response.body == null) {
      return 0;
    }

    try {
      final values = value.copyWith(is_read: const drift.Value(1));
      return db.inboxDao.updateInbox(values);
    } catch (e, s) {
      errorLogger(pos: 'INBOX REPO _ UPDATE INBOX', error: e, stackTrace: s);
      return 0;
    }
  }

  @override
  Future<int> getTotalInbox() async {
    return await getTotalInboxRemote();
  }

  @override
  Future<List<InboxData>> getInboxPagination(
      {required int page, int limit = 15}) async {
    try {
      var result = await getInboxPagingRemote(page: page);
      if (result.status) {
        return result.data ?? [];
      }
      return await db.inboxDao.getInboxPaging(page: page, limit: limit);
    } catch (e, s) {
      errorLogger(
          pos: "$className get inbox pagination", error: e, stackTrace: s);
      return [];
    }
  }

  @override
  Future<ServerResponse<InboxData>> getDetailInbox(String id) async =>
      await getDetailInboxRemote(id);
}
