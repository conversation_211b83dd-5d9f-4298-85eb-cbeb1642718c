// ignore_for_file: overridden_fields

import 'package:get/get.dart';
import 'package:mobile_crm/core/env/env.dart';
import 'package:mobile_crm/data/models/auth_otp_model.dart';
import 'package:mobile_crm/data/models/auth_token_model.dart';
import 'package:mobile_crm/data/models/otp_model.dart';
import 'package:mobile_crm/data/models/server_response.dart';
import 'package:mobile_crm/data/providers/remote/services/auth_service.dart';
import 'package:mobile_crm/domain/repository/auth_repository.dart';

import '../../app/utils/logger.dart';
import '../../core/values/app_strings.dart';
import '../../routes/app_pages.dart';

class AuthRepositoryIml extends AuthService implements AuthRepository {
  @override
  var className = "Auth Repository Iml";

  @override
  Future<ServerResponse> login({required String token}) async =>
      await loginWithPhoneNumber(token: token);

  @override
  Future<ServerResponse<AuthTokenModel>> loginWithLink(
          {required String phone}) async =>
      sendAuthLink(phoneNumber: phone);

  @override
  Future<ServerResponse<AuthTokenModel>> getTokenPhoneAuth(String token) async {
    var result = await getToken(token: token);
    if (result.status) {
      store.token = result.data?.token ?? Env.authorizationKey;
    }
    return result;
  }

  @override
  Future<ServerResponse> getOobWhatsApp(String phone) async {
    return _getOobWhatsApp(phone);
  }

  @override
  Future<ServerResponse> checkOobWhatsApp(String token) async {
    return _checkOobWhatsApp(token);
  }

  Future<ServerResponse> _getOobWhatsApp(String phone) async {
    try {
      final resp = await get("/v1/auth/oob-whatsapp?phone=$phone");
      return ServerResponse.fromJson(resp.body, (json) => json);
    } catch (exception, stackTrace) {
      errorLogger(
          pos: "$className update token FCM",
          error: exception,
          stackTrace: stackTrace);
      return Future.error(exception);
    }
  }

  Future<ServerResponse> _checkOobWhatsApp(String token) async {
    try {
      final resp = await get("/v1/auth/oob-whatsapp/$token");
      return ServerResponse.fromJson(resp.body, (json) => json);
    } catch (exception, stackTrace) {
      errorLogger(
          pos: "$className update token FCM",
          error: exception,
          stackTrace: stackTrace);
      return Future.error(exception);
    }
  }

  /// update token FCM / Push Notification
  Future<ServerResponse> _updateTokenFCM({String? token}) async {
    try {
      final body = 'firebase_token=$token';
      var response = await post(
        '/v1/user/firebase_token',
        body,
        contentType: 'application/x-www-form-urlencoded',
      );
      return ServerResponse.fromJson(response.body, (json) => json);
    } catch (exception, stackTrace) {
      errorLogger(
          pos: "$className update token FCM",
          error: exception,
          stackTrace: stackTrace);
      return Future.error(exception);
    }
  }

  @override
  Future<void> logout() async {
    await _updateTokenFCM(token: 'null');
    box.remove('state');
    box.remove('phone');
    box.remove('email');
    box.remove('key');
    box.remove(Strings.tokenAuth);
    box.remove(Strings.tokenFirebase);
    store.user = null;
    store.token = null;
    store.clearApp();
    store.clearUser();
    db.cartDao.deleteAllCart();
    db.dealTransactionDao.deleteAll();
    db.inboxDao.deleteAll();
    await db.inboxDao.deleteAll();
    await db.dealTransactionDao.deleteAll();
    await db.wishlistDao.deleteAllWishlist();
    await db.wishlistCategoryDao.deleteAllWishlistCategory();
    await db.cartDao.deleteAllCart();
    await db.transactionNewDao.deleteAllTrans();
    auth.signOut();
    googleSignIn.signOut();
    Get.offAllNamed(Routes.HOME);
  }

  @override
  Future<ServerResponse<OtpModel>> requestOtp(String phone) async =>
      await sendOtp(phone: phone);

  @override
  Future<ServerResponse<AuthOtpModel>> validateOtp(
      String token, String code) async {
    try {
      final body = 'token=$token&code=$code';
      var response = await post(
        '/v1/auth/otp',
        body,
        contentType: 'application/x-www-form-urlencoded',
      );
      return ServerResponse<AuthOtpModel>.fromJson(
          response.body, (json) => AuthOtpModel.fromJsonModel(json));
    } catch (exception, stackTrace) {
      errorLogger(
          pos: "$className update token FCM",
          error: exception,
          stackTrace: stackTrace);
      return Future.error(exception);
    }
  }
}
