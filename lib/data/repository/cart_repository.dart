// ignore_for_file: prefer_typing_uninitialized_variables, overridden_fields

import 'dart:convert';

import 'package:mobile_crm/app/utils/dbhelper.dart';
import 'package:mobile_crm/app/utils/logger.dart';
import 'package:mobile_crm/data/models/server_response.dart';
import 'package:mobile_crm/data/models/transaction_new_model.dart';
import 'package:mobile_crm/data/providers/db/database.dart';
import 'package:mobile_crm/data/providers/remote/services/cart_service.dart';
import 'package:mobile_crm/domain/repository/cart_repository.dart';

import '../services/analytics_service.dart';

class CartRepositoryIml extends CartService implements CartRepository {
  final AppDb _db = DatabaseHelper.instance.database;
  @override
  var className = "Cart Repository Iml";

  @override
  Future<List<Cart>> getCart() async {
    try {
      var carts = await _db.cartDao.getAllCart();
      if (carts.isNotEmpty) {
        return carts;
      }
      var response = await getAllCartRemote();
      if (response.data == null) {
        return _db.cartDao.getAllCart();
      }

      await _db.cartDao.insertAll(response.data ?? <Cart>[]);
    } catch (e, s) {
      errorLogger(pos: "$className ~ Get Cart", error: e, stackTrace: s);
    }
    return _db.cartDao.getAllCart();
  }

  @override
  Future<List<Cart>> getCartLocal() {
    return _db.cartDao.getAllCart();
  }

  @override
  Future<List<Cart>> getCartByOutletId(int id) {
    return _db.cartDao.getCartByOutletId(id);
  }

  @override
  Future<ServerResponse> addCart(int productId) async {
    try {
      return await addCartRemote(productDetailId: productId)
          .whenComplete(() async => await getCart());
    } catch (e, s) {
      errorLogger(pos: "$className ~ add cart", error: e, stackTrace: s);
      return Future.error(e, s);
    }
  }

  @override
  Future<int> getTotalCart() async {
    return await _db.cartDao.getTotalCart();
  }

  @override
  Future<int> removeAllCartByCartIdLocal({required Cart cart}) async =>
      await _deleteCartById(cart.crm_cart_id ?? 0);

  @override
  Future<ServerResponse> removeSelfOrder(List<Cart> cart) async {
    final body = cart;
    var response = await request("/v1/cart", 'DELETE',
        body: body, contentType: 'application/json');
    final resp =
        ServerResponse.fromJson(response.body, (json) => jsonEncode(json));
    if (resp.status == true) {
      await _deleteCartById(cart.first.crm_cart_id ?? 0);
    }
    return resp;
  }

  Future<int> _deleteCartById(int id) async {
    return await _db.cartDao.deleteCart(id);
  }

  @override
  Future updateCartNote(Cart cart) async {
    await _db.cartDao.updateCart(cart);
  }

  @override
  Future<ServerResponse> updateAllCarts(List<Cart> value) async {
    var response = await put(
        '/v1/cart', value.map((element) => element.toJson()),
        contentType: 'application/x-www-form-urlencoded');
    // return ServerResponse();
    return ServerResponse.fromJson(response.body, (json) => jsonEncode(json));
  }

  // Local
  @override
  Stream<List<Cart>> watchCartLocal() => _db.cartDao.watchAllCart();

  @override
  Stream<List<Cart>> watchCartLocalByOutlet({required int outletId}) =>
      _db.cartDao.watchAllCartByOutletId(outletId);

  @override
  Future<int> addProductToCartLocal({required Cart cart}) async {
    AnalyticsService.observer.analytics
        .logEvent(name: "add_product_to_cart", parameters: {
      "product_name": cart.product?.name,
      "outlet_name": cart.outlet?.name,
    });
    return await _db.cartDao.insert(cart);
  }

  @override
  Future<int> removeProductToCartLocal({required Cart cart}) async {
    AnalyticsService.observer.analytics
        .logEvent(name: "remove_product_to_cart", parameters: {
      "product_name": cart.product?.name,
      "outlet_name": cart.outlet?.name,
    });
    return await _db.cartDao.deleteCart(cart.crm_cart_id ?? 0);
  }

  @override
  Future<int> updateProductToCartLocal({required Cart cart}) async {
    AnalyticsService.observer.analytics
        .logEvent(name: "update_cart", parameters: {
      "product_name": cart.product?.name,
      "outlet_name": cart.outlet?.name,
      "product_qty": cart.qty
    });
    return await _db.cartDao.updateCart(cart);
  }

  @override
  Future<ServerResponse> updateBatchCarts({required int outletId}) async {
    var result = await db.cartDao.getCartByOutletId(outletId);
    return await updateBatchCartRemote(cart: result);
  }

  @override
  Future<int> removeAllCart() async => await db.cartDao.deleteAllCart();

  @override
  Future<int> deleteTransactionOrder(TransactionNewModel transactionNewModel,
          {bool isOnOrder = false}) async =>
      _db.transactionNewDao
          .deleteTrans(transactionNewModel, isOnOrder: isOnOrder);

  @override
  Future<List<TransactionNewModel>> getAllTransactionOrder(
          {bool? isOnOrder}) async =>
      await _db.transactionNewDao.getAllTransactionNew(isOnOrder: isOnOrder);

  @override
  Future<TransactionNewModel?> getTransactionOrderByOutletId(int outletId,
          {bool isOnOrder = false,
          TransactionNewModel? transactionNewModel}) async =>
      await _db.transactionNewDao.getTransactionNewByOutletId(outletId,
          isOnOrder: isOnOrder, transactionNewModel: transactionNewModel);

  @override
  Future<int> insertTransactionOrder(
          TransactionNewModel transactionNewModel) async =>
      await _db.transactionNewDao.insert(transactionNewModel);

  @override
  Future<int> updateTransactionOrder(TransactionNewModel transactionNewModel,
          {bool isOnOrder = false}) async =>
      await _db.transactionNewDao
          .updateTrans(transactionNewModel, isOnOrder: isOnOrder);
}
