// ignore_for_file: overridden_fields

import 'package:mobile_crm/app/enum/wishlist_enum.dart';
import 'package:mobile_crm/app/utils/dbhelper.dart';
import 'package:mobile_crm/app/utils/logger.dart';
import 'package:mobile_crm/data/models/server_response.dart';
import 'package:mobile_crm/data/models/wishlist_model.dart';
import 'package:mobile_crm/data/providers/db/database.dart';
import 'package:mobile_crm/data/providers/remote/services/wishlist_service.dart';
import 'package:mobile_crm/domain/repository/wishlist_repository.dart';

class WishlistRepositoryIml extends WishlistService
    implements WishlistRepository {
  final AppDb _db = DatabaseHelper.instance.database;
  @override
  var className = "Wishlist Repository Iml";

  @override
  Future<ServerResponse<WishlistData>> addWishlist(
      {required Product product}) async {
    try {
      var result = await addWishListRemote(
          productId: (product.productId ?? 0).toString());
      return result;
    } catch (e, s) {
      errorLogger(pos: "$className Add Wishlist", error: e, stackTrace: s);
      return Future.error(e);
    }
  }

  @override
  Future<ServerResponse<WishlistCategoryData>> addWishlistCategory(
      {required WishlistCategoryModel wishlistCategoryModel,
      required String wishlistId}) async {
    var result = await addWishListCategoryRemote(
        categoryName: wishlistCategoryModel.name ?? '');
    if (result.status) {
      await setWishlistCategory(
          categoryId: "${result.data?.id ?? 0}", wishlistId: wishlistId);
    }
    return result;
  }

  @override
  Future<int> countWishlist() async {
    try {
      return await countWishListRemote().then(
          (value) async => value.data ?? await _db.wishlistDao.countWishlist());
    } catch (e, s) {
      errorLogger(pos: "$className count Wishlist", error: e, stackTrace: s);
      return await _db.wishlistDao.countWishlist();
    }
  }

  @override
  Future<ServerResponse> deleteWishlist(String crmProductWishlistId) async {
    try {
      var result = await deleteWishListRemote(
          crmProductWishlistId: crmProductWishlistId);
      if (result.status) {
        await _db.wishlistDao
            .deleteSingleWishlist(int.tryParse('crmProductWishlistId') ?? 0);
      }
      return result;
    } catch (e, s) {
      errorLogger(pos: "$className delete Wishlist", error: e, stackTrace: s);
      return Future.error(e);
    }
  }

  @override
  Future<List<WishlistData>> getAllWishlist({WishlistEnum? format}) async {
    try {
      var data = await _db.wishlistDao.getAllWishlist();
      if (data.isNotEmpty) {
        insertAllWishlistFromRemote();
        return data;
      }

      return await insertAllWishlistFromRemote();
    } catch (e, s) {
      errorLogger(pos: "$className get all Wishlist", error: e, stackTrace: s);
      return await _db.wishlistDao.getAllWishlist();
    }
  }

  Future<List<WishlistData>> insertAllWishlistFromRemote(
      {WishlistEnum? format}) async {
    try {
      var respond = await getWishListRemote();
      if (respond.status) {
        await _db.wishlistDao.insertAllWishlist(respond.data ?? []);
        return await _db.wishlistDao.getAllWishlist();
      }
      return await _db.wishlistDao.getAllWishlist();
    } catch (e, s) {
      errorLogger(
          pos: "$className insert Wishlist from remote to db",
          error: e,
          stackTrace: s);
      return await _db.wishlistDao.getAllWishlist();
    }
  }

  @override
  Future<List<WishlistCategoryData>> getWishlistCategory() async {
    try {
      var data = await _db.wishlistCategoryDao.getAllWishlistCategory();
      if (data.isNotEmpty) {
        insertAllWishlistCategoryFromRemote();
        return data;
      }

      return await insertAllWishlistCategoryFromRemote();
    } catch (e, s) {
      errorLogger(pos: "$className get all Wishlist", error: e, stackTrace: s);
      return await _db.wishlistCategoryDao.getAllWishlistCategory();
    }
  }

  Future<List<WishlistCategoryData>>
      insertAllWishlistCategoryFromRemote() async {
    try {
      var respond = await getWishListCategoryRemote();
      if (respond.status) {
        await _db.wishlistCategoryDao
            .insertAllWishlistCategory(respond.data ?? []);
        return await _db.wishlistCategoryDao.getAllWishlistCategory();
      }
      return await _db.wishlistCategoryDao.getAllWishlistCategory();
    } catch (e, s) {
      errorLogger(
          pos: "$className insert Wishlist Category from remote to db",
          error: e,
          stackTrace: s);
      return _db.wishlistCategoryDao.getAllWishlistCategory();
    }
  }

  @override
  Future<ServerResponse> setWishlistCategory(
      {required String categoryId, required String wishlistId}) async {
    try {
      var respond = await setWishListCategoryRemote(
          categoryId: categoryId, wishlistId: wishlistId);
      return respond;
    } catch (e, s) {
      errorLogger(
          pos: "$className insert Wishlist Category from remote to db",
          error: e,
          stackTrace: s);
      return Future.error(e);
    }
  }

  @override
  Future<List<WishlistData>> getAllWishlistByCategoryId(int id) async {
    return await _db.wishlistDao.getAllWishlistByCategoryId(id);
  }

  @override
  Future<WishlistData?> getSingleWishlistByProductFkId(int productFkId) async =>
      await _db.wishlistDao.getSingleWishlistByProductId(productFkId);
}
