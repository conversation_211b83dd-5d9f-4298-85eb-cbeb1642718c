// ignore_for_file: overridden_fields

import 'dart:convert';

import 'package:get/get.dart';
import 'package:mobile_crm/app/utils/dbhelper.dart';
import 'package:mobile_crm/app/utils/logger.dart';
import 'package:mobile_crm/app/utils/toast.dart';
import 'package:mobile_crm/core/extensions/future_extensions.dart';
import 'package:mobile_crm/core/values/app_strings.dart';
import 'package:mobile_crm/data/models/address_model.dart';
import 'package:mobile_crm/data/models/secret_id_model.dart';
import 'package:mobile_crm/data/models/server_response.dart';
import 'package:mobile_crm/data/models/user_code_model.dart';
import 'package:mobile_crm/data/models/user_model.dart';
import 'package:mobile_crm/data/providers/db/database.dart';
import 'package:mobile_crm/data/providers/remote/services/user_service.dart';
import 'package:mobile_crm/domain/repository/user_repository.dart';

class UserRepositoryIml extends UserService implements UserRepository {
  final AppDb _db = DatabaseHelper.instance.database;
  @override
  var className = "User Repository Iml";

  @override
  Future<ServerResponse<SecretIdModel>> getVoucherSecretId(
      String promotionId) async {
    try {
      var response = await get('/v1/promotion-deals/$promotionId/secret-code');
      if (response.body == null) {
        return Future.error(0);
      }
      var data = ServerResponse<SecretIdModel>.fromJson(
          response.body, (json) => SecretIdModel.fromJsonModel(json));
      return data;
    } catch (e, s) {
      errorLogger(
          pos: "$className ~ Get Voucher Secret Id", error: e, stackTrace: s);
      return Future.error(e, s);
    }
  }

  @override
  Future<UserModel?> getUser() async {
    UserModel? user = store.user;
    if (user != null) {
      _getUser.withRetries(3);
      return user;
    }

    return await _getUser.withRetries(3);
  }

  Future<UserModel?> _getUser() async {
    var result = await getUserDetailRemote();
    if (result.status) {
      store.user = result.data;
      return result.data;
    }
    return store.user;
  }

  @override
  Future<List<DealData>> getUserVoucher() async {
    var result = await getAllVoucher(group: false);
    if (result.status) {
      return result.data ?? [];
    }

    return [];
  }

  @override
  Future<List<DealData>> getUserVoucherGroup() async {
    var result = await getAllVoucher(group: true);
    if (result.status) {
      return result.data ?? [];
    }

    return [];
  }

  @override
  Future<ServerResponse> updateUserProfile(
          {String? token, required UserModel user}) async =>
      await updateUserProfileRemote(token: token, user: user);

  @override
  Future<ServerResponse> buyDeal(String dealId, transactionId) async {
    try {
      final body = 'deals_id=$dealId&transaction_id=$transactionId';
      Response response = await post('/v1/promo/deals/buy', body,
          contentType: 'application/x-www-form-urlencoded');
      return ServerResponse.fromJson(response.body, (json) => jsonEncode(json));
    } catch (e, s) {
      errorLogger(pos: "$className ~ Buy Deal", error: e, stackTrace: s);
      return Future.error(e, s);
    }
  }

  @override
  Future<ServerResponse> removeAccount(UserCodeModel user) async =>
      await validateRemoveAccountRemote(user: user);

  @override
  Future<ServerResponse> requestRemoveAccount() async =>
      await requestRemoveAccountRemote();

  @override
  Future<ServerResponse<UserCodeModel>> requestEmailVerify() async =>
      await requestCodeEmailVerification();

  @override
  Future<ServerResponse> validateCodeVerification(UserCodeModel user) async =>
      await validateCodeEmailVerification(
          key: user.key ?? '', code: user.code ?? '');

  @override
  Future<ServerResponse<DealData>> getDetailVoucher(String promotionId) async =>
      await getDetailVoucherRemote(promotionId: promotionId);

  @override
  Future<List<PointData>> getPointHistory() async {
    List<PointData> points = await _db.pointDao.getAll();
    if (points.isNotEmpty) {
      _getPointHistoryRemote();
      return points;
    }
    return await _getPointHistoryRemote();
  }

  Future<List<PointData>> _getPointHistoryRemote() async {
    var result = await getUserPointHistoryRemote();
    if (result.status && result.data?.isNotEmpty == true) {
      await db.pointDao.insertAll(result.data ?? []);
    }
    return await db.pointDao.getAll();
  }

  @override
  Future<ServerResponse> addAddress(AddressModel address) async =>
      await addAddressRemote(address: address);

  @override
  Future<ServerResponse> deleteAddress(String id) async =>
      await deleteAddressRemote(addressId: id);

  @override
  Future<List<AddressModel>> getAddress() async {
    var result = await getAddressRemote();
    if (result.status) {
      return result.data ?? [];
    }
    return [];
  }

  @override
  Future<ServerResponse> updateAddress(AddressModel address) async =>
      await updateAddressRemote(address: address);

  @override
  Future<ServerResponse> registerMember(UserModel user, String token) async {
    var result = await registerNewMember(token: token, user: user);
    return result;
  }

  @override
  Future<ServerResponse> registerMemberV2(UserModel user) async {
    var result = await registerNewMemberV2(user: user);
    return result;
  }

  @override
  Future<ServerResponse<UserModel>> checkExistingUserByNumberOrEmail(
      {String? phoneNumber, String? email, bool? showToast = false}) async {
    var result =
        await checkExistingUser(phoneNumber: phoneNumber, email: email);
    if (showToast ?? false) {
      if (result.code == 1) {
        if (result.data?.email == "used") {
          Toast.show(
              Strings.valueAlreadyUsePleaseUseAnotherValue
                  .trParams({'value': Strings.email.tr}),
              type: ToastType.error,
              duration: 3);
        }

        if (result.data?.phone == "used") {
          Toast.show(
              Strings.valueAlreadyUsePleaseUseAnotherValue
                  .trParams({'value': Strings.phoneNumber.tr}),
              type: ToastType.error,
              duration: 3);
        }
      }
    }
    return result;
  }

  @override
  Future<ServerResponse<UserCodeModel>> verifyChangePhoneNumber(
          {required String key, required String code}) async =>
      await validateChangePhoneNumber(key: key, code: code);

  @override
  Future<ServerResponse> removeFCMToken() async {
    const tokenMessaging = "null";
    return updateTokenFCM(token: tokenMessaging);
  }

  @override
  Future<ServerResponse> updateFCMToken() async {
    final tokenMessaging = await firebaseMessaging.getToken();
    return updateTokenFCM(token: tokenMessaging);
  }

  @override
  Future<ServerResponse> joinOtherBusiness(String token) async =>
      await joinOtherBusinessRemote(token: token);

  @override
  Future<ServerResponse> resendEmailConfirmation(String email) async =>
      await resendEmailConfirmationRemote(email: email);

  @override
  Future<ServerResponse> lostPhoneNumber(String email, String phone) async =>
      await lostPhoneNumberRemote(email: email, phoneNumber: phone);
}
