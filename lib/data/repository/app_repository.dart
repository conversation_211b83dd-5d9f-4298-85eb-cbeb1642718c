import 'package:mobile_crm/app/utils/dbhelper.dart';
import 'package:mobile_crm/app/utils/logger.dart';
import 'package:mobile_crm/data/models/banner_model.dart';
import 'package:mobile_crm/data/models/server_response.dart';
import 'package:mobile_crm/data/models/share_model.dart';
import 'package:mobile_crm/data/providers/db/database.dart';
import 'package:mobile_crm/domain/repository/app_repository.dart';

import '../providers/remote/base_http/base_service.dart';

class AppRepositoryIml extends BaseService implements AppRepository {
  final AppDb _db = DatabaseHelper.instance.database;

  @override
  Future<ServerResponse<ShareModel>> getShareLink(
      String type, String id) async {
    var response = await get("/v1/share?data_type=$type&data_id=$id");
    try {
      return ServerResponse<ShareModel>.fromJson(
          response.body, (json) => ShareModel.fromJsonModel(json));
    } catch (e, s) {
      errorLogger(pos: 'APP REPO GET SHARE', error: e, stackTrace: s);
      return ServerResponse();
    }
  }

  @override
  Future<List<BannerAppData>> getBannerApp() async {
    List<BannerAppData> data = await _db.bannerDao.getAllBanner();
    if (data.isNotEmpty) {
      _getBannerApp();
      return data;
    }
    return await _getBannerApp();
  }

  Future<List<BannerAppData>> _getBannerApp() async {
    try {
      var response = await get("/v1/app/banner");
      if (response.body.toString() == 'null') {
        return await _db.bannerDao.getAllBanner();
      }
      var data = ServerResponseList<BannerAppData>.fromJson(
          response.body, (json) => BannerModel.fromJsonModel(json));
      await _db.bannerDao.insertAll(data.data ?? []);
      return await _db.bannerDao.getAllBanner();
    } catch (e, s) {
      errorLogger(pos: 'APP REPO _ GET BANNER APP', error: e, stackTrace: s);
      return [];
    }
  }
}
