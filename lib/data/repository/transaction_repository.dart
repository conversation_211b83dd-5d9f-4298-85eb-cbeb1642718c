// ignore_for_file: overridden_fields

import 'dart:convert';

import 'package:image_picker/image_picker.dart';
import 'package:mobile_crm/core/extensions/datetime_extensions.dart';
import 'package:mobile_crm/data/models/feedback_model.dart';
import 'package:mobile_crm/data/models/order_detail_model.dart';
import 'package:mobile_crm/data/models/order_model.dart';
import 'package:mobile_crm/data/models/payment_method_model.dart';
import 'package:mobile_crm/data/models/payments_model.dart';
import 'package:mobile_crm/data/models/self_order_model.dart';
import 'package:mobile_crm/data/models/server_response.dart';
import 'package:mobile_crm/data/models/transaction/feedback/rating_review_model.dart';
import 'package:mobile_crm/data/models/transaction/feedback/rating_summary_model.dart';
import 'package:mobile_crm/data/models/transaction_history_model.dart';
import 'package:mobile_crm/data/models/transaction_new_model.dart';
import 'package:mobile_crm/data/providers/db/database.dart';
import 'package:mobile_crm/data/providers/remote/services/transaction_service.dart';
import 'package:mobile_crm/domain/repository/deal_repository.dart';
import 'package:mobile_crm/domain/repository/transaction_repository.dart';

import '../../app/enum/order_status_enum.dart';
import '../../app/helper/transaction_helper.dart';
import '../../app/utils/logger.dart';
import '../models/transaction_data_model.dart';

class TransactionRepositoryIml extends TransactionService
    implements TransactionRepository {
  @override
  var className = "Transaction Repository Iml";

  @override
  Future<ServerResponse<OrderModel>> addTransactionSelfOrder(
          SelfOrder selfOrder) async =>
      await createTransactionSelfOrder(selfOrder: selfOrder);

  @override
  Future<ServerResponse<PaymentMethodDetail>> createTransactionPayment(
          TransactionNewModel newModel,
          PaymentMethodDetail paymentMethodModel) async =>
      await createTransactionPaymentRemote(
          newModel: newModel, paymentMethodDetail: paymentMethodModel);

  @override
  Future<TransactionHistoryModel> getTransactionHistoryDetail(
          String id) async =>
      await getTransactionHistory(id: id)
          .then((value) => value.data ?? TransactionHistoryModel());

  @override
  Future<ServerResponse> postGiveFeedback(FeedbackModel feedback) async {
    var body = feedback.toJson();
    var response = await post('/v1/transaction/feedback', body,
        contentType: 'application/x-www-form-urlencoded');
    try {
      return ServerResponse.fromJson(response.body, (json) => jsonEncode(json));
    } catch (e, s) {
      errorLogger(pos: "$className Give Feedback", error: e, stackTrace: s);
      return Future.error(e, s);
    }
  }

  @override
  Future<ServerResponse<OrderDetailModel>> createTransaction(
          SelfOrder selfOrder) async =>
      await createTransactionDeliveryPickupDineIn(selfOrder: selfOrder);

  @override
  Future<ServerResponse<OrderDetailModel>> getOrderDetail(String id) async =>
      getStatusTransactionOrderDetail(id: id);

  @override
  Future<ServerResponse<PaymentDataModel>> getPaymentDetailOnline(
          String invoice, PaymentDataModel paymentData) async =>
      await getDetailPayment(invoice: invoice, paymentDataModel: paymentData);

  @override
  Future<ServerResponse<OrderDetailModel>> updateOrderDetail(
          String orderSalesId, String status, XFile? file) async =>
      updateTransactionOrder(
          orderSalesId: orderSalesId, status: status, file: file);

  @override
  Future<int> deleteTransactionRecord(int id) async =>
      await db.dealTransactionDao.deleteTransaction(id);

  @override
  Future<int> deleteDealRecord(int id) async =>
      await db.dealTransactionDao.deleteDeal(id);

  @override
  Future<List<TransactionDataData>> getAllRecord() async =>
      await db.dealTransactionDao.getAll();

  @override
  Stream<List<TransactionDataData>> streamAllRecord() =>
      db.dealTransactionDao.streamAll();

  @override
  Stream<List<TransactionNewTableData>> streamAllNewTransaction() {
    if (store.token != null) {
      return db.transactionNewDao.watchAllTransactionNew();
    } else {
      return const Stream.empty();
    }
  }

  @override
  Future<TransactionDataData?> insertRecord(TransactionDataModel entity) async {
    try {
      return await db.dealTransactionDao.insertTransaction(entity);
    } catch (e, s) {
      errorLogger(pos: "$className Insert Record", error: e, stackTrace: s);
      return Future.error(e);
    }
  }

  @override
  Future<ServerResponse> getStatusSelfOrder(
          {required TransactionNewModel transactionNewModel}) async =>
      await getStatusTransactionSelfOrder(
          transactionNewModel: transactionNewModel);

  @override
  Future<bool> updateTransactionRecord(TransactionDataData entity) async {
    return await db.dealTransactionDao.updateTransaction(entity);
  }

  @override
  Future<List<TransactionDataData>> getAllTransaction(
      {DealRepository? dealRepo}) async {
    var data = await getAllRecord();
    if (data.isEmpty) {
      return [];
    }
    for (var tr in data) {
      if (tr.dealPayment != null) {
        var milisDate = DateTime.fromMillisecondsSinceEpoch(
                tr.dealPayment?.payment?.expiredAt ?? 0)
            .add(const Duration(minutes: 3));
        if (milisDate.isTimeExpired) {
          await deleteTransactionRecord(tr.id ?? 0);
        }
        continue;
      }

      if (tr.dealPayment == null) {
        if (tr.orderDetail != null) {
          var milisDate = DateTime.fromMillisecondsSinceEpoch(
                  tr.orderDetail?.timeOrder ?? 0)
              .add(const Duration(hours: 12));
          if (milisDate.isTimeExpired ||
              (tr.orderDetail?.status?.toLowerCase() ==
                  OrderStatusEnum.received.name.toLowerCase()) ||
              (tr.orderDetail?.status?.toLowerCase() ==
                  OrderStatusEnum.cancel.name.toLowerCase())) {
            await deleteTransactionRecord(tr.id ?? 0);
          }
          continue;
        }

        if (tr.orderInfo != null) {
          var milisDate = DateTime.fromMillisecondsSinceEpoch(
                  tr.orderInfo?.orderExpired?.expired ?? 0)
              .add(const Duration(minutes: 3));
          if (milisDate.isTimeExpired) {
            await deleteTransactionRecord(tr.id ?? 0);
          }
          continue;
        }
      }
    }

    data = await TransactionHelper.filterTransaction(
        listTrans: data, transactionRepo: this, dealRepository: dealRepo);
    data.sort((a, b) => (b.dealPayment?.payment?.expiredAt ??
            (a.orderDetail?.timeOrder ??
                (b.orderInfo?.orderExpired?.expired ?? 0)))
        .compareTo(a.dealPayment?.payment?.expiredAt ??
            (b.orderDetail?.timeOrder ??
                (a.orderInfo?.orderExpired?.expired ?? 0))));
    return data;
  }

  @override
  Future<Outlet?> getOrderTypeSetting({required String outletId}) async {
    try {
      var result = await getOrderTransactionSetting(outletId: outletId);
      return result.data ??
          await db.outletDao.getOutletById(int.tryParse(outletId) ?? 0);
    } catch (e, s) {
      errorLogger(
          pos: "$className ~ Get Order Type Setting", error: e, stackTrace: s);
      return await db.outletDao.getOutletById(int.tryParse(outletId) ?? 0);
    }
  }

  @override
  Future<ServerResponse> postGiveFeedbackV2(FeedbackModel feedback) async =>
      await createFeedBackV2(feedback: feedback);

  @override
  Future<List<PaymentMethodModel>?> getPaymentMethod(
      {String? orderType, String? outletId}) async {
    var result =
        await getPaymentMethodRemote(outletId: outletId, orderType: orderType);
    return result.data;
  }

  @override
  Future<ServerResponse<TransactionNewModel>> createTransaction2(
          TransactionNewModel transactionNewModel) async =>
      await createTransactionDeliveryPickupDineIn2(
          transactionNewModel: transactionNewModel);

  @override
  Future<ServerResponse<TransactionNewModel>> createTransactionSelfOrderV2(
          TransactionNewModel transactionNewModel) async =>
      await createTransactionSelfOrder2(
          transactionNewModel: transactionNewModel);

  @override
  Future<List<TransactionNewModel>> getAllTransactionOrder(
      {bool? isOnOrder}) async {
    var data =
        await db.transactionNewDao.getAllTransactionNew(isOnOrder: isOnOrder);
    for (var el in data) {
      if (el.status == OrderStatusEnum.received.name ||
          el.status == OrderStatusEnum.arrived.name) {
        await db.transactionNewDao.deleteTrans(el, isOnOrder: true);
      }
    }
    return data;
  }

  @override
  Future<ServerResponse<TransactionNewModel>> getOrderDetail2(
          String id) async =>
      await getStatusTransactionOrderDetailV2(id: id);

  @override
  Future<ServerResponse<RatingSummaryModel>> getRatingSummary(
          {required int outletId}) async =>
      await getRatingSummaryRemote(outletId: outletId);

  @override
  Future<ServerResponseList<RatingReviewModel>> getReview(
          {required int outletId}) async =>
      await getReviewRemote(outletId: outletId);
}
