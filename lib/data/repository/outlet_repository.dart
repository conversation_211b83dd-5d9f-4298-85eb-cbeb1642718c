// ignore_for_file: prefer_typing_uninitialized_variables

import 'package:flutter/foundation.dart';
import 'package:geolocator/geolocator.dart';
import 'package:mobile_crm/app/enum/permission_type_enum.dart';
import 'package:mobile_crm/app/utils/location_helper.dart';
import 'package:mobile_crm/app/utils/logger.dart';
import 'package:mobile_crm/app/utils/permission_handler_helper.dart';
import 'package:mobile_crm/data/models/delivery_price_model.dart';
import 'package:mobile_crm/data/providers/db/database.dart';
import 'package:mobile_crm/data/providers/remote/services/outlet_service.dart';
import 'package:mobile_crm/domain/repository/outlet_repository.dart';

class OutletRepositoryIml extends OutletService implements OutletRepository {
  var response;
  Future<List<Outlet>> _refreshOutlet() async {
    Map<String, dynamic>? query;
    if (!kIsWeb) {
      bool locationPermissionIsPermitted =
          await PermissionHandlerHelper.requestPermission(
        PermissionTypeEnum.location,
      );
      if (locationPermissionIsPermitted) {
        try {
          Position? position = await LocationHelper.instance.getPosition();
          query = {"latlng": "${position?.latitude},${position?.longitude}"};
        } catch (e, s) {
          errorLogger(pos: "Outlet Repository", error: e, stackTrace: s);
        }
      }
    }

    var remote = await getAllOutlet(query: query);
    if (remote.status || (remote.data?.isNotEmpty == true)) {
      if (remote.data != null) {
        await db.outletDao.insertAll(remote.data ?? []);
      }
    }
    return await db.outletDao.getAllOutlet();
  }

  @override
  void insertLastOrderType(int outletId, String orderType) async {
    await db.lastTypeDao.insert(outletId, orderType);
  }

  @override
  Future<LastTypeEntityData?> getLastOrderType(int outletId) async {
    return await db.lastTypeDao.getLastOrderType(outletId);
  }

  @override
  Future<DeliveryPrice?> getOutletDeliveryPrice(
      Map<String, dynamic> latlng, int id) async {
    var data = await getDeliveryPrice(query: latlng, outletId: id);
    return data.data;
  }

  @override
  Future<List<Outlet>> getOutlet() async {
    List<Outlet> outlets = await db.outletDao.getAllOutlet();
    if (outlets.isNotEmpty || outlets.isNotEmpty) {
      _refreshOutlet();
      return outlets;
    }
    return await _refreshOutlet();
  }

  @override
  Future<Outlet?> getOutletById(int id) async {
    Outlet? result = await db.outletDao.getOutletById(id);
    if (result != null) {
      return result;
    }
    _refreshOutlet();
    return getOutletDetailById(id);
  }

  @override
  Future<Outlet?> getOutletDetailById(int id) async =>
      await getDetailOutlet(outletId: id.toString())
          .then((value) => value.data);

  @override
  Future<List<Product>> getOutletProductByOutletId(String id) async {
    List<Product> products = await db.productDao.getAllProduct();
    if (products.isEmpty) {
      return [];
    }
    Map map = {'products': products, 'outlet': id};
    return _filterTest(map);
  }

  List<Product> _filterTest(Map map) {
    List<Product> temporaryProducts = <Product>[];

    for (var product in map['products'] as List<Product>) {
      if (product.variant?.isNotEmpty == true && product.variant != null) {
        for (var variant in product.variant!) {
          for (var available in variant.available!) {
            if (available.outletId.toString() == map['outlet'] as String) {
              temporaryProducts.add(product);
            }
          }
        }
      } else if (product.available != null) {
        for (var available in product.available!) {
          if (available.outletId.toString() == map['outlet'] as String) {
            temporaryProducts.add(product);
          }
        }
      }
    }
    temporaryProducts = temporaryProducts.toSet().toList();
    temporaryProducts.sort((a, b) => a.subcategory!.compareTo(b.subcategory!));
    return temporaryProducts;
  }
}
