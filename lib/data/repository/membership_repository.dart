// ignore_for_file: prefer_typing_uninitialized_variables

import 'package:mobile_crm/app/utils/dbhelper.dart';
import 'package:mobile_crm/app/utils/logger.dart';
import 'package:mobile_crm/data/models/membership_model.dart';
import 'package:mobile_crm/data/models/server_response.dart';
import 'package:mobile_crm/data/providers/db/database.dart';
import 'package:mobile_crm/domain/repository/membership_repository.dart';

import '../providers/remote/base_http/base_service.dart';

class MembershipRepositoryIml extends BaseService
    implements MembershipRepository {
  final AppDb _db = DatabaseHelper.instance.database;

  @override
  Future<ServerResponseList<MembershipData>> getMembership() async {
    try {
      var response = await get("/v1/membership/type");
      ServerResponseList<MembershipData> serverResponseList =
          ServerResponseList<MembershipData>.fromJson(
              response.body, (json) => MembershipModel.fromJsonModel(json));

      if (serverResponseList.status) {
        await _db.membershipDao.deleteAll();
        List<MembershipData> membershipData = serverResponseList.data ?? [];
        await _db.membershipDao
            .insertAll(membershipData.map((e) => e.toCompanion(true)).toList());
      }
      return serverResponseList;
    } catch (e, s) {
      errorLogger(pos: 'Membership Repo', error: e, stackTrace: s);
      throw Exception(e);
    }
  }

  @override
  Future<List<MembershipData>> getMembershipLocal() async {
    List<MembershipData> membership = <MembershipData>[];
    try {
      membership = await _db.membershipDao.getAllMembership();
      if (membership.isNotEmpty) {
        getMembership();
        return membership;
      }

      await getMembership();
    } catch (e, s) {
      errorLogger(pos: "MR", error: e, stackTrace: s);
      throw Exception(e);
    }

    return await _db.membershipDao.getAllMembership();
  }
}
