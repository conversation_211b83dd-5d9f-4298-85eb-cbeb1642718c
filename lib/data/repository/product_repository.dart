// ignore_for_file: prefer_typing_uninitialized_variables

import 'dart:async';

import 'package:get/get.dart';
import 'package:mobile_crm/app/helper/product_helper.dart';
import 'package:mobile_crm/app/utils/logger.dart';
import 'package:mobile_crm/app/utils/toast.dart';
import 'package:mobile_crm/core/extensions/future_extensions.dart';
import 'package:mobile_crm/core/values/app_strings.dart';
import 'package:mobile_crm/data/models/product/link_menu_model.dart';
import 'package:mobile_crm/data/models/product_group_model.dart';
import 'package:mobile_crm/data/models/search_model.dart';
import 'package:mobile_crm/data/models/server_response.dart';
import 'package:mobile_crm/data/models/tax_model.dart';
import 'package:mobile_crm/data/models/unit_conversion_model.dart';
import 'package:mobile_crm/data/providers/db/database.dart';
import 'package:mobile_crm/data/providers/remote/services/product_service.dart';
import 'package:mobile_crm/domain/repository/product_repository.dart';

class ProductRepositoryIml extends ProductService implements ProductRepository {
  @override
  Future<List<Product>> getProduct() async {
    List<Product> products = await db.productDao.getAllProduct();
    if (products.isNotEmpty) {
      _refreshProduct();
      return products;
    }
    return await _refreshProduct();
  }

  @override
  Future<Product?> getProductById(String id) async =>
      await db.productDao.getProductById(int.tryParse(id) ?? 0);

  /// version 2 using [getAllProductAvailability]
  /// version 3 using [getAllProductAvailabilityV3]
  Future<List<Product>> _refreshProduct() async {
    var remote = await getAllProductAvailabilityV3.withRetries(3);
    if (remote.status || (remote.data?.isNotEmpty == true)) {
      if (remote.data != null) {
        // infoLogger('_refreshProduct', jsonEncode(remote.data));
        await db.productDao.insertAll(remote.data ?? []);
      }
    }
    return await db.productDao.getAllProduct();
  }

  Future<List<ProductRecomendationData>> _refreshProductRecomendation(
      int outletId) async {
    var data = await getAllProductRecomendation(outletId);
    if (data.status || (data.data?.isNotEmpty == true)) {
      if (data.data != null) {
        await db.productRecomendationDao.deleteAllProductRecomendation();
        await db.productRecomendationDao
            .insertProductRecomendation(data.data ?? []);
      }
      return await db.productRecomendationDao.getAll();
    } else {
      return [];
    }
  }

  @override
  Future<List<ProductGroupModel>> getFilteredProduct(
          {String? outletId}) async =>
      await getProduct().then((value) async {
        // infoLogger('>>> allProducts', jsonEncode(value));
        for (var product in value) {
          product =
              await ProductHelper.sortAvailableAtDistance(product: product);
        }
        return ProductHelper.filterBySubCategory(
            products: value, outletDao: db.outletDao);
      });

  @override
  Future<List<SearchModel>> getSearchHistoryProduct() async {
    if (store.token == null) {
      return [];
    }
    var result = await getSearchProductHistory();
    if (result.status) {
      return result.data ?? [];
    }
    return [];
  }

  @override
  Future<bool> saveSearchHistoryProduct({String? search}) async {
    if (store.token == null) {
      return false;
    }
    if (search != '' || (search?.isNotEmpty == true)) {
      var result = await saveSearchProductHistory(search: search);
      return result.status;
    }
    return false;
  }

  @override
  Future<List<Product>> getProductLocal() async =>
      await db.productDao.getAllProduct();

  @override
  Future<List<LinkMenuModel>> getAllLinkMenu() async {
    var data = await db.linkMenuDao.getAllLinkMenu();
    if (data.isNotEmpty) {
      _getAllLinkMenu();
      return data;
    }
    return await _getAllLinkMenu();
  }

  Future<List<LinkMenuModel>> _getAllLinkMenu() async {
    var data = await getAllLinkMenuRemote();
    if (data.status) {
      await db.linkMenuDao.insertAllLinkMenu(data.data);
    }
    return await db.linkMenuDao.getAllLinkMenu();
  }

  @override
  Future<List<LinkMenuModel>> getLinkMenuBy(
          {int? outletId, int? productId, int? productDetailId}) async =>
      await db.linkMenuDao.getLinkMenuBy(
          outletId: outletId,
          productDetailId: productDetailId,
          productId: productId);

  @override
  Future<List<UnitConversionModel>> getUnitConversion() async {
    var data = await db.unitConversionDao.getAllUnitConversion();
    if (data.isNotEmpty) {
      _getAllUnitConversion();
      return data;
    }

    return await _getAllUnitConversion();
  }

  Future<List<UnitConversionModel>> _getAllUnitConversion() async {
    var data = await getUnitConversionRemote();
    if (data.status) {
      await db.unitConversionDao.insertAllUnitConversion(data.data ?? []);
    }
    return await db.unitConversionDao.getAllUnitConversion();
  }

  @override
  Future<ServerResponse> addNotifyProduct(
      {required int productDetailId, required int qty}) async {
    if (store.token == null) {
      Toast.show(Strings.pleaseLoginToContinue.tr, type: ToastType.dark);
      return ServerResponse(status: false);
    }
    var result = await addNotifyProductRemote(
        productDetailId: productDetailId, qty: qty);
    Toast.show(result.message,
        type: result.status ? ToastType.success : ToastType.error,
        duration: 3,
        context: Get.context);

    return result;
  }

  @override
  Future<Product?> getProductByDetailId(String id) async =>
      await db.productDao.getProductByDetailId(int.tryParse(id) ?? 0);

  @override
  Future<List<TaxModel>> getAllTaxes() async {
    try {
      List<TaxModel> taxes = await db.taxDao.getAllTaxes();
      if (taxes.isNotEmpty) {
        _getAllTaxes();
        return taxes;
      }
      return await _getAllTaxes();
    } catch (e, s) {
      errorLogger(pos: "Get All Taxes", error: e, stackTrace: s);
      return [];
    }
  }

  Future<List<TaxModel>> _getAllTaxes() async {
    var remote = await getAllTaxesRemote();
    if (remote.status || (remote.data?.isNotEmpty == true)) {
      if (remote.data != null) {
        await db.taxDao.insertAllTaxes(remote.data ?? []);
      }
    }
    return await db.taxDao.getAllTaxes();
  }

  @override
  Future<List<TaxModel>> getTaxByProductDetailId(int? productDetailId) async =>
      await db.taxDao.getProductDetailId(productDetailId);

  @override
  Future<List<ProductRecomendationData>> getProductRecomendation(
      int outletId) async {
    // _refreshProductRecomendation(outletId);
    // List<ProductRecomendationData> products =
    //     await db.productRecomendationDao.getAll();
    // if (products.isNotEmpty) {
    //   return products;
    // }
    return await _refreshProductRecomendation(outletId);
  }
}
