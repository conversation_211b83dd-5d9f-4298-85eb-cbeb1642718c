import 'package:get/get.dart';
import 'package:mobile_crm/app/utils/dbhelper.dart';
import 'package:mobile_crm/data/models/config_model.dart';
import 'package:mobile_crm/data/models/faq_model.dart';
import 'package:mobile_crm/data/models/server_response.dart';
import 'package:mobile_crm/data/providers/db/database.dart';
import 'package:mobile_crm/domain/repository/config_repository.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

import '../providers/remote/base_http/base_service.dart';

class ConfigRepositoryIml extends BaseService implements ConfigRepository {
  final AppDb _db = DatabaseHelper.instance.database;
  var response = const Response();

  Future<ConfigData?> _getConfig() async {
    try {
      response = await get("/v1/app-config");
      final data = ServerResponse<ConfigData>.fromJson(
          response.body, (json) => ConfigModel.fromJsonModel(json));
      return data.data;
    } catch (e) {
      Sentry.captureException(e);
      return null;
    }
  }

  Future<List<FaqData>?> _getFaq() async {
    try {
      Response res = await get("/v1/app/faqs");
      final data = ServerResponseList<FaqData>.fromJson(
          res.body, (json) => FaqModel.fromJsonModel(json));

      return data.data;
    } catch (e) {
      Sentry.captureException(e);
      return [];
    }
  }

  @override
  Future<List<FaqData>> getFaq() async {
    List<FaqData>? faq = await _getFaq();
    if (faq != null) {
      await _db.faqDao.insertFaq(faq);
    }

    var faqs = await _db.faqDao.getAll();
    return faqs;
  }

  @override
  Future<ConfigData> getConfigLocal() async {
    ConfigData? configData = await _db.configDao.getALl().then((value) =>
        value.firstWhereOrNull((element) => element.appInfo != null));
    if (configData != null) {
      getConfig();
      return configData;
    }

    return await getConfig();
  }

  @override
  Future<ConfigData> getConfig() async {
    ConfigData? cfg = await _getConfig();
    if (cfg != null) {
      await _db.configDao.insertConfig(cfg);
    }
    var allConfig = await _db.configDao.getALl().then((value) =>
        value.firstWhereOrNull((element) => element.appInfo != null));
    return allConfig ?? ConfigData();
  }

  @override
  Stream<ConfigData> watchConfig() async* {
    // yield ConcatStream([_db.configDao.watchAllConfig()]);
  }
}
