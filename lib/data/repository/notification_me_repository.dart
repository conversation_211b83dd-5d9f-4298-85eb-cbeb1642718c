import 'package:mobile_crm/app/utils/dbhelper.dart';
import 'package:mobile_crm/app/utils/logger.dart';
import 'package:mobile_crm/data/providers/db/database.dart';
import 'package:mobile_crm/domain/repository/notification_me_repository.dart';

class NotificationMeRepositoryIml implements NotificationMeRepository {
  var nameClass = "NotificationMeRepositoryIml";
  final _db = DatabaseHelper.instance.database;

  @override
  Future<bool> deleteSingle(String id) async {
    try {
      return await _db.notificationMeDao
          .deleteNotification(id)
          .then((value) => value == 1 ? true : false);
    } catch (e, s) {
      errorLogger(pos: nameClass, error: e, stackTrace: s);
      return false;
    }
  }

  @override
  Future<List<NotificationMeData>> getAll() async {
    try {
      return await _db.notificationMeDao.getAllNotification();
    } catch (e, s) {
      errorLogger(pos: nameClass, error: e, stackTrace: s);
      return [];
    }
  }

  @override
  Future<NotificationMeData?> getSingle(String id) async {
    try {
      return await _db.notificationMeDao.getSingle(id);
    } catch (e, s) {
      errorLogger(pos: nameClass, error: e, stackTrace: s);
      return null;
    }
  }

  @override
  Future<bool> insert(NotificationMeData notificationMeData) async {
    try {
      return await _db.notificationMeDao
          .insertNotification(notificationMeData.toCompanion(true))
          .then((value) => value == 1 ? true : false);
    } catch (e, s) {
      errorLogger(pos: nameClass, error: e, stackTrace: s);
      return false;
    }
  }
}
