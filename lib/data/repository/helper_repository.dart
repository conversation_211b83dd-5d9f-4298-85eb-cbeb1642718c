// ignore_for_file: prefer_typing_uninitialized_variables

import 'package:flutter/foundation.dart';
import 'package:geolocator/geolocator.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/enum/permission_type_enum.dart';
import 'package:mobile_crm/app/helper/sentry_helper.dart';
import 'package:mobile_crm/app/utils/location_helper.dart';
import 'package:mobile_crm/app/utils/logger.dart';
import 'package:mobile_crm/app/utils/permission_handler_helper.dart';
import 'package:mobile_crm/data/models/address_model.dart';
import 'package:mobile_crm/data/models/province_model.dart';
import 'package:mobile_crm/data/models/server_response.dart';
import 'package:mobile_crm/domain/repository/helper_repository.dart';

import '../providers/remote/base_http/base_service.dart';

class HelperRepositoryIml extends BaseService implements HelperRepository {
  var className = "Helper Repository Iml";
  List<ProvinceModel> cachedProvinces = [];
  Map<String, List<ProvinceModel>> cachedRegency = Map();

  @override
  Future<List<ProvinceModel>> getProvince() async {
    try {
      if (cachedProvinces.isNotEmpty) {
        infoLogger('getProvince', 'get from cache..');
        return cachedProvinces;
      }
      var res = await get('/v1/region/indonesia');
      var data = ServerResponseList<ProvinceModel>.fromJson(
          res.body, (json) => ProvinceModel.fromJsonModel(json));
      cachedProvinces = data.data ?? [];
      return data.data ?? [];
    } catch (e, s) {
      errorLogger(pos: "$className ~ Get Province", error: e, stackTrace: s);
      await SentryHelper.logException(e, s);
      return Future.error(e, s);
    }
  }

  @override
  Future<List<ProvinceModel>> getRegency({required String provinceId}) async {
    try {
      if (cachedRegency.containsKey(provinceId) &&
          cachedRegency[provinceId]?.isNotEmpty == true) {
        infoLogger('getRegency', 'get from cache..');
        return cachedRegency[provinceId]!;
      }
      var res = await get('/v1/region/indonesia/$provinceId');
      infoLogger('getRegency', 'statusCode: ${res.statusCode}');
      if (res.statusCode != 200) {
        return Future.error('error status code ${res.statusCode}');
      }
      var data = ServerResponseList<ProvinceModel>.fromJson(
          res.body, (json) => ProvinceModel.fromJsonModel(json));
      cachedRegency[provinceId] = data.data ?? [];
      infoLogger('getRegency', 'success');
      return data.data ?? [];
    } catch (e, s) {
      errorLogger(pos: "$className ~ Get Regency", error: e, stackTrace: s);
      infoLogger('getRegency', 'failed... $e');
      SentryHelper.logException(e, s);
      return Future.error(e, s);
    }
  }

  @override
  Future<AddressModel> getAddress() async {
    try {
      Map<String, dynamic>? query;

      if (!kIsWeb || GetPlatform.isMobile) {
        bool locationPermissionIsPermitted =
            await PermissionHandlerHelper.requestPermission(
                PermissionTypeEnum.location);
        if (locationPermissionIsPermitted) {
          Position? position = await LocationHelper.instance.getPosition();
          query = {"latlng": "${position?.latitude},${position?.longitude}"};
        }
      }

      if (query == null) {
        return AddressModel();
      }

      var res = await get('/v1/geocoding', query: query);
      return ServerResponse.fromJson(
              res.body, (json) => AddressModel.fromJsonModel(json)).data ??
          AddressModel();
    } catch (e, s) {
      errorLogger(pos: "$className ~ Get Regency", error: e, stackTrace: s);
      return AddressModel();
    }
  }
}
