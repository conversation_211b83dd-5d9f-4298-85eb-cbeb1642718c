// ignore_for_file: overridden_fields

import 'package:mobile_crm/app/enum/promotion_type_enum.dart';
import 'package:mobile_crm/app/utils/logger.dart';
import 'package:mobile_crm/data/models/deal_payment_model.dart';
import 'package:mobile_crm/data/models/server_response.dart';
import 'package:mobile_crm/data/providers/db/database.dart';
import 'package:mobile_crm/data/providers/remote/services/deal_service.dart';
import 'package:mobile_crm/domain/repository/deal_repository.dart';

import '../models/deals_payment_model.dart';
import '../models/secret_id_model.dart';

class DealRepositoryIml extends DealService implements DealRepository {
  @override
  var className = "Deal Repository Iml";

  @override
  Future<List<DealData>> getAll() async {
    var result = await db.dealDao.getAll();
    if (result.isNotEmpty) {
      _getAll();
      return result;
    }
    return _getAll();
  }

  Future<List<DealData>> _getAll() async {
    try {
      var result = await getAllPromotion(
          query: {'types': PromotionTypeEnum.voucherpromo_deals.name});
      if (result.status) {
        return db.dealDao.insertAll(result.data ?? []);
      }
      var data = await db.dealDao.getAll();
      return data;
    } catch (e, s) {
      errorLogger(pos: className, error: e, stackTrace: s);
      return await db.dealDao.getAll();
      // .then((value) => DealHelper.dealSortFilter(value));
    }
  }

  @override
  Future<List<DealData>> getOutletPromotion(
      {List<String>? type, required String outletId}) async {
    try {
      Map<String, dynamic>? query = {};
      if (type == null) {
        query = {
          "types":
              "${PromotionTypeEnum.specialpromo_all.name},${PromotionTypeEnum.memberpromo_all.name}",
          "outlet_ids": outletId
        };
      } else {
        query = {
          "types": type
              .toString()
              .replaceAll("[", "")
              .replaceAll("]", "")
              .replaceAll(RegExp(r"\s+"), ""),
          "outlet_ids": outletId
        };
      }
      var result = await getAllPromotion(query: query);

      var user = store.user;
      List<DealData> listPromos = result.data ?? [];
      List<DealData> promos = [];

      for (var element in listPromos) {
        element.outlet?.forEach((outlet) {
          if (outlet.outlet_id.toString() == outletId) {
            if (element.memberTypes != null && user != null) {
              element.memberTypes?.forEach((type) {
                if (type.memberTypeId == user.typefkid) {
                  promos.add(element);
                }
              });
            } else {
              promos.add(element);
            }
          }
        });
      }
      return listPromos;
    } catch (e, s) {
      errorLogger(pos: className, error: e, stackTrace: s);
      return [];
    }
  }

  @override
  Future<ServerResponse<DealData>> getOutletDetailPromotion(
      {required String promotionId}) async {
    try {
      var result = await getDetailPromotion(promotionId: promotionId);
      return result;
    } catch (e, s) {
      errorLogger(pos: className, error: e, stackTrace: s);
      return Future.error(e);
    }
  }

  @override
  Future<ServerResponse<DealData>> getDealDetail(
      {required String dealId}) async {
    try {
      var result = await getDetail(id: dealId);
      return result;
    } catch (e, s) {
      errorLogger(pos: className, error: e, stackTrace: s);
      return Future.error(e, s);
    }
  }

  @override
  Future<ServerResponse<DealPaymentModel>> buyDeals(
          {required String dealId}) async =>
      await buy(id: dealId);

  @override
  Future<bool> notifyMe({required String dealId}) async {
    if (store.token == null) {
      return false;
    }
    try {
      var result = await sendMeReminder(id: dealId);
      return result.status;
    } catch (e, s) {
      errorLogger(pos: className, error: e, stackTrace: s);
      return false;
    }
  }

  @override
  Future<List<DealData>> getMyVoucher() async {
    if (store.token == null) {
      return [];
    }
    var result = await getMyVoucherRemote();
    if (result.status) {
      return result.data ?? [];
    }
    return [];
  }

  @override
  Future<DealData?> getDetailMyVoucher({required String promotionId}) async {
    if (store.token == null) {
      return null;
    }
    var result = await getDetailMyVouherRemote(promotionId: promotionId);
    if (result.status) {
      return result.data;
    }
    return null;
  }

  @override
  Future<SecretIdModel?> getMyVoucherSecretId(String promotionId) async {
    try {
      var result = await getVoucherSecretId(promotionId);
      return result.data;
    } catch (e, s) {
      errorLogger(
          pos: "$className ~ Get Voucher Secret Id", error: e, stackTrace: s);
      return Future.error(e, s);
    }
  }

  @override
  Future<DealsPaymentModel?> getDealsPayment(
      {required String promotionId,
      required String paymentType,
      String? phoneNumber}) async {
    var result = await getDealsPaymentRemote(
        promotionId: promotionId,
        paymentType: paymentType,
        phoneNumber: phoneNumber);
    return result.data;
  }

  @override
  Future<ServerResponse> refundVoucher(String promotionBuyId) async =>
      await refundVoucherRemote(promotionBuyId);
}
