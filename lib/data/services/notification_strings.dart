import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:mobile_crm/app/utils/logger.dart';

//* Channel PUSH notifications
const channelId = 'urgent';
const channelKey = 'urgent';
const channelName = 'urgent';
const channelDescription = 'urgent';

//* IOS Settings
DarwinInitializationSettings iosInitializationSettings() {
  return DarwinInitializationSettings(notificationCategories: [
    DarwinNotificationCategory(
      channelId,
      actions: <DarwinNotificationAction>[
        DarwinNotificationAction.plain(
          channelKey,
          'Tienes una nueva notificacion',
          options: {
            DarwinNotificationActionOption.foreground,
          },
        ),
      ],
    ),
  ], onDidReceiveLocalNotification: onDidReceiveLocalNotification);
}

void onDidReceiveLocalNotification(
    int id, String? title, String? body, String? payload) async {
  // display a dialog with the notification details, tap ok to go to another page
  infoLogger("On DId receive local notif ios", "id $id");
}

//* Android Settings
const androidInitializationSettings =
    AndroidInitializationSettings('@drawable/ic_notification');

const androidChannel = AndroidNotificationChannel(
  channelId,
  channelName,
  description: channelDescription,
  importance: Importance.max,
  showBadge: true,
  enableVibration: true,
  playSound: true,
  enableLights: true,
  ledColor: Colors.red,
);

NotificationDetails notificationDetailsWithImage(String uri) {
  return NotificationDetails(
    android: AndroidNotificationDetails(
        icon: "@drawable/ic_notification",
        "100",
        "File Saved",
        channelDescription: channelDescription,
        visibility: NotificationVisibility.public,
        importance: Importance.max,
        actions: [
          const AndroidNotificationAction("open_image", "Open"),
        ],
        priority: Priority.high,
        ledOnMs: 100,
        ledOffMs: 1000,
        category: AndroidNotificationCategory.message,
        styleInformation:
            BigPictureStyleInformation(FilePathAndroidBitmap(uri))),
    iOS: DarwinNotificationDetails(
      attachments: [DarwinNotificationAttachment(uri)],
      presentSound: true,
      presentAlert: true,
      presentBadge: true,
    ),
  );
}

NotificationDetails notifyWithImage(String uri) {
  const dealChannelId = 'Promo';
  const dealChannelName = 'Deals';
  const dealChannelDescription = 'Don\'t miss the deals';
  return NotificationDetails(
    android: AndroidNotificationDetails(
        // icon: "@drawable/ic_notification",
        dealChannelId,
        dealChannelName,
        channelDescription: dealChannelDescription,
        // visibility: NotificationVisibility.public,
        importance: Importance.max,
        priority: Priority.high,
        ledOnMs: 100,
        ledOffMs: 1000,
        category: AndroidNotificationCategory.promo,
        // actions: [
        //   const AndroidNotificationAction(
        //     "open_deals",
        //     "Open",
        //     showsUserInterface: true
        //   ),
        // ],
        styleInformation:
            BigPictureStyleInformation(FilePathAndroidBitmap(uri))),
    iOS: DarwinNotificationDetails(
      attachments: [DarwinNotificationAttachment(uri)],
      presentSound: true,
      presentAlert: true,
      presentBadge: true,
    ),
  );
}

const pushNotificationDetails = NotificationDetails(
  android: AndroidNotificationDetails(
      icon: "@drawable/ic_notification",
      channelId,
      channelName,
      channelDescription: channelDescription,
      color: Colors.red,
      ledColor: Colors.red,
      visibility: NotificationVisibility.public,
      importance: Importance.max,
      priority: Priority.high,
      ledOnMs: 100,
      ledOffMs: 1000,
      category: AndroidNotificationCategory.message,
      styleInformation: DefaultStyleInformation(true, true)),
  iOS: DarwinNotificationDetails(
    presentSound: true,
    presentAlert: true,
    presentBadge: true,
  ),
);
