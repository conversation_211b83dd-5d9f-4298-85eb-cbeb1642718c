import 'package:firebase_dynamic_links/firebase_dynamic_links.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/home/<USER>';
import 'package:mobile_crm/app/modules/login/controller/login_controller.dart';
import 'package:mobile_crm/app/modules/screen_wrapper/controller/wrapper_controller.dart';
import 'package:mobile_crm/app/utils/logger.dart';
import 'package:mobile_crm/app/utils/toast.dart';
import 'package:mobile_crm/data/models/config_model.dart';
import 'package:mobile_crm/data/services/local_storage.dart';
import 'package:mobile_crm/routes/app_pages.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

import '../../app/helper/sentry_helper.dart';

class DynamicLinksService {
  static Future<String> createDynamicLink(String type, String parameter,
      {String? tableInput}) async {
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    var c = Get.find<WrapperController>();

    AppInfo appInfo = c.configApp.value.appInfo ??= AppInfo(
        android: Android(package_name: packageInfo.packageName),
        ios: Ios(bundle_id: packageInfo.packageName),
        web: Web(url: 'https://uniq-crm-dev.web.app/#'));

    String dynamicLink = '${appInfo.web?.url}/$type/$parameter';
    if (tableInput != null) {
      dynamicLink = '${appInfo.web?.url}/$type/$parameter?table=$tableInput';
    }

    String uriPrefix = appInfo.dynamic_link.isEmpty
        ? 'https://uniqcrm.page.link'
        : appInfo.dynamic_link;

    final DynamicLinkParameters parameters = DynamicLinkParameters(
        uriPrefix: uriPrefix,
        link: Uri.parse(dynamicLink),
        androidParameters: AndroidParameters(
          packageName: packageInfo.packageName,
        ),
        iosParameters: IOSParameters(
            bundleId: packageInfo.packageName,
            minimumVersion: packageInfo.version));

    Uri dynamicUrl;
    try {
      dynamicUrl = await FirebaseDynamicLinks.instance
          .buildShortLink(parameters)
          .then((value) => value.shortUrl);
    } catch (e, s) {
      dynamicUrl = await FirebaseDynamicLinks.instance.buildLink(parameters);
      errorLogger(pos: 'Dynamic Links Service', error: e, stackTrace: s);
      // AppAlert.showDebug(e.toString());
    }
    Sentry.addBreadcrumb(Breadcrumb(
        type: 'debug',
        category: 'user.services.dynamic_links.create',
        data: {"url_dynamic": dynamicUrl},
        level: SentryLevel.debug));
    return dynamicUrl.toString();
  }

  static void initDynamicLinks() async {
    final PendingDynamicLinkData? data =
        await FirebaseDynamicLinks.instance.getInitialLink();

    _handleDynamicLink(data);

    FirebaseDynamicLinks.instance.onLink.listen((dynamicLinkData) {
      _handleDynamicLink(dynamicLinkData);
    }).onError((error, stackTrace) async {
      await SentryHelper.logException(error, stackTrace);
      errorLogger(
          pos: 'Dynamic Link Service', error: error, stackTrace: stackTrace);
    });
  }

  static _handleDynamicLink(PendingDynamicLinkData? data) async {
    final Uri? deepLink = data?.link;

    Sentry.addBreadcrumb(Breadcrumb(
        type: 'debug',
        category: 'user.services.dynamic_links.handle',
        data: {"deeplink": deepLink},
        level: SentryLevel.debug));

    if (deepLink == null) {
      return;
    }

    if (deepLink.pathSegments.isEmpty) {
      return await _handleUrlWithRegEx(deepLink);
    }

    await _handleUrlWithPathSegments(deepLink);
  }

  static _handleUrlWithRegEx(Uri deepLink) async {
    final store = LocalStorageService();
    RegExp exp =
        RegExp('https://[a-z0-9\-]+\.[a-z0-9\.]+/(#/|)([a-z]+)/([0-9]+)');

    if (exp.allMatches(deepLink.toString()).isEmpty) {
      exp = RegExp(
          'https://[a-z0-9\-]+\.[a-z0-9\.]+/(#/|)([a-z]+)/([a-z0-9]+)/([A-Z0-9]+)');
    }

    RegExpMatch? match = exp.firstMatch(deepLink.toString());
    match = exp.firstMatch(deepLink.toString());

    String type = match?.group(2) ?? '';
    String id = match?.group(3) ?? '';

    store.deepType = type;

    infoLogger('Dynamic Links Service',
        "Using RegEx \nLink $deepLink\ntype $type\nid $id");

    infoLogger("name route", "name route");

    switch (type) {
      case 'auth':
        if (store.token != null) {
          return Toast.show('You have logged in before', type: ToastType.dark);
        }

        String nameRoute = Get.currentRoute;

        if (nameRoute.contains('login')) {
          final loginController = Get.find<LoginController>();
          await loginController.singInWithEmailLink(deepLink.toString());
        } else {
          Get.toNamed(Routes.LOGIN);
          Future.delayed(const Duration(seconds: 1), () async {
            final loginController = Get.find<LoginController>();
            await loginController.singInWithEmailLink(deepLink.toString());
          });
        }
        break;
      case 'deal':
        Get.toNamed(Routes.DEALDETAIL(id));
        break;
      case 'voucher':
        Get.toNamed(Routes.VOUCHERDETAIL(id));
        break;
      case 'product':
        store.deepProduct = id;
        String nameRoute = Get.currentRoute;
        if (nameRoute == Routes.HOME) {
          final controller = Get.find<HomeController>();
          controller.checkDynamicLink();
        }
        Get.offAllNamed(Routes.HOME);
        break;
      case 'outlet':
        Uri uriParse = Uri.parse(deepLink.toString().replaceFirst("#/", ""));
        if (uriParse.hasQuery) {
          String? tableNumber = uriParse.queryParameters['table'];
          Get.offAndToNamed(Routes.OUTLET(id),
              arguments: {'tableNumber': tableNumber});
        } else {
          Get.offAndToNamed(Routes.OUTLET(id));
        }
        break;
      case 'transaction':
        Get.toNamed(Routes.TRANSACTION(id));
        break;
      case 'inbox':
        {
          if (match?.group(3) == 'sales') {
            id = match?.group(4) ?? '';
            Get.toNamed(Routes.NOTIFICATIONSALES(id));
            break;
          }
          Get.toNamed(Routes.NOTIFICATION);
        }
        break;
      case 'profile':
        Get.toNamed(Routes.PROFILE);
        Toast.show('Email has been verified', type: ToastType.success);
        break;
    }
  }

  static _handleUrlWithPathSegments(Uri deepLink) async {
    final store = LocalStorageService();
    infoLogger('Dynamic Link Service',
        "Using Path Segments \nLink $deepLink\ntype ${deepLink.pathSegments[0]}\nid ${deepLink.pathSegments[1]}\n${deepLink.pathSegments}");

    var parameterID =
        deepLink.pathSegments.length > 1 ? deepLink.pathSegments[1] : null;

    if (deepLink.pathSegments.contains('auth')) {
      store.deepType = 'auth';
      if (store.token != null) {
        return Toast.show('You have logged in before', type: ToastType.dark);
      }

      String nameRoute = Get.currentRoute;

      if (nameRoute.contains('login')) {
        Get.toNamed(Routes.LOGIN);
        Future.delayed(const Duration(seconds: 1), () async {
          final loginController = Get.find<LoginController>();
          await loginController.singInWithEmailLink(deepLink.toString());
        });
      } else {
        Get.toNamed(Routes.LOGIN);
        Future.delayed(const Duration(seconds: 1), () async {
          final loginController = Get.find<LoginController>();
          await loginController.singInWithEmailLink(deepLink.toString());
        });
      }
    }

    if (deepLink.pathSegments.contains('product')) {
      if (parameterID != null) {
        store.deepType = 'product';
        store.deepProduct = parameterID;
        Get.offAllNamed(Routes.HOME);
      }
    }

    if (deepLink.pathSegments.contains('outlet')) {
      if (parameterID != null) {
        store.deepType = 'outlet';
        Get.offAndToNamed(Routes.OUTLET(parameterID));
      }
    }

    if (deepLink.pathSegments.contains('voucher')) {
      if (parameterID != null) {
        store.deepType = 'voucher';
        Get.toNamed(Routes.VOUCHERDETAIL(parameterID));
      }
    }

    if (deepLink.pathSegments.contains('deal')) {
      if (parameterID != null) {
        store.deepType = 'deal';
        Get.toNamed(Routes.DEALDETAIL(parameterID));
      }
    }
  }
}
