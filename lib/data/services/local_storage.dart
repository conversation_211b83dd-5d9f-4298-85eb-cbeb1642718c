import 'dart:convert';

import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:mobile_crm/core/values/app_strings.dart';
import 'package:mobile_crm/data/models/address_model.dart';
import 'package:mobile_crm/data/models/order_model.dart';
import 'package:mobile_crm/data/models/outlet_model.dart';
import 'package:mobile_crm/data/models/self_order_model.dart';
import 'package:mobile_crm/data/models/tax_model.dart';
import 'package:mobile_crm/data/models/transaction_data_model.dart';
import 'package:mobile_crm/data/models/user_code_model.dart';
import 'package:mobile_crm/data/models/user_model.dart';
import 'package:mobile_crm/data/providers/db/database.dart';

import '../models/payment_method_model.dart';

class LocalStorageService extends GetxService {
  final boxApp = GetStorage('app');
  final boxUser = GetStorage('user');
  final boxLink = GetStorage('deeplink');
  final boxTrans = GetStorage('transaction');

  // ---------------- Model ---------------- //
  PaymentMethodDetail? get lastTransaction {
    final rawJson = boxTrans.read(Strings.lastTransaction);
    if (rawJson == null) {
      return null;
    }
    Map<String, dynamic> map = jsonDecode(rawJson);
    return PaymentMethodDetail.fromJson(map);
  }

  set lastTransaction(PaymentMethodDetail? value) {
    if (value != null) {
      boxTrans.write(Strings.lastTransaction, json.encode(value.toJson()));
    } else {
      boxTrans.remove(Strings.lastTransaction);
    }
  }

  TransactionDataData? get orderTransaction {
    final rawJson = boxTrans.read(Strings.orderTransaction);
    if (rawJson == null) {
      return null;
    }
    Map<String, dynamic> map = jsonDecode(rawJson);
    return TransactionDataModel.fromJson(map);
  }

  set orderTransaction(TransactionDataData? value) {
    if (value != null) {
      boxTrans.write(Strings.orderTransaction, json.encode(value.toJson()));
    } else {
      boxTrans.remove(Strings.orderTransaction);
    }
  }

  UserModel? get user {
    final rawJson = boxUser.read(Strings.user);
    if (rawJson == null) {
      return null;
    }
    Map<String, dynamic> map = jsonDecode(rawJson);
    return UserModel.fromJson(map);
  }

  set user(UserModel? value) {
    if (value != null) {
      boxUser.write(Strings.user, json.encode(value.toJson()));
    } else {
      boxUser.remove(Strings.user);
    }
  }

  SelfOrder? get order {
    final rawJson = boxApp.read(Strings.listOrder);
    if (rawJson == null) {
      return null;
    }
    Map<String, dynamic> map = jsonDecode(rawJson);
    return SelfOrder.fromJson(map);
  }

  set order(SelfOrder? value) {
    if (value != null) {
      boxApp.write(Strings.listOrder, json.encode(value.toJson()));
    } else {
      boxApp.remove(Strings.listOrder);
    }
  }

  OrderModel? get orderTime {
    final rawJson = boxApp.read(Strings.timeOrder);
    if (rawJson == null) {
      return null;
    }
    Map<String, dynamic> map = jsonDecode(rawJson);
    return OrderModel.fromJson(map);
  }

  set orderTime(OrderModel? value) {
    if (value != null) {
      boxApp.write(Strings.timeOrder, json.encode(value.toJson()));
    } else {
      boxApp.remove(Strings.timeOrder);
    }
  }

  Outlet? get outlet {
    final rawJson = boxApp.read(Strings.outlet);
    if (rawJson == null) {
      return null;
    }
    Map<String, dynamic> map = jsonDecode(rawJson);
    return OutletModel.fromJson(map);
  }

  set outlet(Outlet? value) {
    if (value != null) {
      boxApp.write(Strings.outlet, json.encode(value.toJson()));
    } else {
      boxApp.remove(Strings.outlet);
    }
  }

  UserCodeModel? get keyDeleteAccount {
    final rawJson = boxApp.read(Strings.keyDeleteAccount);
    if (rawJson == null) {
      return null;
    }
    Map<String, dynamic> map = jsonDecode(rawJson);
    return UserCodeModel.fromJson(map);
  }

  set keyDeleteAccount(UserCodeModel? value) {
    if (value != null) {
      boxApp.write(Strings.keyDeleteAccount, json.encode(value.toJson()));
    } else {
      boxApp.remove(Strings.keyDeleteAccount);
    }
  }

  AddressModel? get mainAddress {
    final rawJson = boxApp.read(Strings.mainAddress);
    if (rawJson == null) {
      return null;
    }
    Map<String, dynamic> map = jsonDecode(rawJson);
    return AddressModel.fromJson(map);
  }

  set mainAddress(AddressModel? value) {
    if (value != null) {
      boxApp.write(Strings.mainAddress, json.encode(value.toJson()));
    } else {
      boxApp.remove(Strings.mainAddress);
    }
  }

  // ---------------- Model List ---------------- //
  List<TaxModel>? get taxModel {
    final rawJson = boxApp.read(Strings.tax);
    if (rawJson == null) {
      return null;
    }
    return List<TaxModel>.from(
        json.decode(rawJson).map((x) => TaxModel.fromJson(x)));
  }

  set taxModel(List<TaxModel>? value) {
    if (value != null) {
      boxApp.write(Strings.tax,
          json.encode(value.map((element) => element.toJson()).toList()));
    } else {
      boxApp.remove(Strings.tax);
    }
  }

  // ---------------- Variable ---------------- //
  String? get token => boxUser.read(Strings.tokenAuth);

  set token(String? value) => boxUser.write(Strings.tokenAuth, value);

  String? get tokenMessaging => boxApp.read(Strings.tokenMessage);

  set tokenMessaging(String? value) =>
      boxApp.write(Strings.tokenMessage, value);

  int? get productDetail => boxApp.read(Strings.productDetail);

  set productDetail(int? value) => boxApp.write(Strings.productDetail, value);

  String? get authState => boxApp.read(Strings.authState);

  set authState(String? value) => boxApp.write(Strings.authState, value);

  String? get authPhone => boxApp.read(Strings.authPhone);

  set authPhone(String? value) => boxApp.write(Strings.authPhone, value);

  String? get authEmail => boxApp.read(Strings.authEmail);

  set authEmail(String? value) => boxApp.write(Strings.authEmail, value);

  String? get authName => boxApp.read(Strings.authName);

  set authName(String? value) => boxApp.write(Strings.authName, value);

  String? get authKey => boxApp.read(Strings.authKey);

  set authKey(String? value) => boxApp.write(Strings.authKey, value);

  String? get orderType => boxApp.read(Strings.orderTypeNext);

  set orderType(String? value) => boxApp.write(Strings.orderTypeNext, value);

  // ---------------- DeepLink ------------------ //
  String? get deepProduct => boxLink.read(Strings.deepProduct);

  set deepProduct(String? value) => boxLink.write(Strings.deepProduct, value);

  String? get deepType => boxLink.read(Strings.deepType);

  set deepType(String? value) => boxLink.write(Strings.deepType, value);

  clearUser() {
    boxUser.erase();
    boxTrans.erase();
  }

  clearApp() {
    boxApp.erase();
    boxLink.erase();
  }
}
