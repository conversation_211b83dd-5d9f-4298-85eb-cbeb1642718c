import 'package:in_app_review/in_app_review.dart';
import 'package:mobile_crm/data/services/analytics_service.dart';

class InAppReviewService {
  final InAppReview _inAppReview = InAppReview.instance;

  InAppReviewService._();

  static final _service = InAppReviewService._();

  factory InAppReviewService() => _service;

  static InAppReview get instance => _service._inAppReview;

  static Future<void> requestReview() async {
    if (await _service._inAppReview.isAvailable()) {
      AnalyticsService.instance.logEvent(name: 'IN_APP_REVIEW');
      await _service._inAppReview.requestReview();
    }
  }

  static void openStore() => _service._inAppReview
      .openStoreListing(appStoreId: "", microsoftStoreId: "");
}
