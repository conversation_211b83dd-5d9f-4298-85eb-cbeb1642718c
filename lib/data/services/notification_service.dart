import 'dart:convert';

import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/utils/logger.dart';
import 'package:mobile_crm/app/utils/storage_helper.dart';
import 'package:mobile_crm/data/models/fcm_model.dart';
import 'package:mobile_crm/data/providers/db/database.dart';
import 'package:mobile_crm/data/services/local_storage.dart';
import 'package:mobile_crm/data/services/notification_strings.dart';
import 'package:mobile_crm/routes/app_pages.dart';
import 'package:rxdart/rxdart.dart';
import 'package:sentry_flutter/sentry_flutter.dart';
import 'package:timezone/data/latest_all.dart' as tz;
import 'package:timezone/timezone.dart' as tz;

import '../../app/helper/sentry_helper.dart';
import '../../main.dart';

class NotificationService {
  factory NotificationService() => _instance;

  NotificationService._internal();

  static final store = LocalStorageService();
  static final NotificationService _instance = NotificationService._internal();

  static get _firebaseMessaging => FirebaseMessaging.instance;

  static FlutterLocalNotificationsPlugin get _flutterLocalNotificationsPlugin =>
      FlutterLocalNotificationsPlugin();

  static final BehaviorSubject<NotificationResponse?>
      _notificationActionStream =
      BehaviorSubject<NotificationResponse?>.seeded(null);

  //
  static Stream<NotificationResponse?> get notificationActionStream =>
      _notificationActionStream.stream;

  static Stream<String> get onTokenRefresh => _firebaseMessaging.onTokenRefresh;
  static bool _isFlutterLocalNotificationsInitialized = false;

  static Future<void> initializeNotificationService(
    Function(NotificationResponse)? onDidReceiveNotificationResponse,
  ) async {
    tz.initializeTimeZones();
    tz.setLocalLocation(tz.getLocation("Asia/Jakarta"));
    if (_isFlutterLocalNotificationsInitialized) return;
    var token = "";

    try {
      token = await _firebaseMessaging.getToken();
      store.tokenMessaging = token;
    } catch (e,s) {
      token = store.tokenMessaging ?? '';
      errorLogger(pos: "Get token fcm", error: e, stackTrace: s);
    }
    // ignore: avoid_print
    infoLogger("FCM TOKEN", token);
    await _initFirebaseMessaging();
    await _flutterLocalNotificationsPlugin.initialize(
      InitializationSettings(
        android: androidInitializationSettings,
        iOS: iosInitializationSettings(),
      ),
      onDidReceiveNotificationResponse: onDidReceiveNotificationResponse,
      onDidReceiveBackgroundNotificationResponse: notificationTapBackground,
    );

    await _requestPermissions();
    await _createNotificationChannels();
    await _listenNotificationActionStream();
    _isFlutterLocalNotificationsInitialized = true;
  }

  static Future<NotificationAppLaunchDetails?>
      getNotificationOnAppLaunchDetails() async =>
          await _flutterLocalNotificationsPlugin
              .getNotificationAppLaunchDetails();

  static Future<void> pushNotificationWithSchedule(
      NotificationMeData notifData) async {
    try {
      infoLogger("Display Push Notification Schedule",
          "Notification $notifData \n${notifData.toJson()}");
      var publishTime =
          DateTime.fromMillisecondsSinceEpoch(notifData.scheduleTime ?? 0);
      var tzNow = tz.TZDateTime.now(tz.local);
      var timeToShow = tz.TZDateTime(
          tz.local,
          publishTime.year,
          publishTime.month,
          publishTime.day,
          publishTime.hour,
          publishTime.minute - 5,
          publishTime.second,
          publishTime.millisecond,
          publishTime.microsecond);

      if (!tzNow.isBefore(timeToShow)) {
        timeToShow = tzNow.add(const Duration(seconds: 1));
      }

      await _flutterLocalNotificationsPlugin.zonedSchedule(
          notifData.id ?? createUniqueId,
          notifData.title,
          notifData.body,
          timeToShow,
          pushNotificationDetails,
          payload: json.encode(notifData.toJson()),
          uiLocalNotificationDateInterpretation:
              UILocalNotificationDateInterpretation.absoluteTime);
    } catch (e, s) {
      errorLogger(pos: "NS", error: e, stackTrace: s);
    }
  }

  static Future<void> displayPushNotification(
      RemoteMessage notification, String? cek) async {
    FcmModel fcmModel = FcmModel.fromJson(notification.data);
    if (fcmModel.imageUrl != null) {
      var file =
          await StorageHelper.downloadFile(fcmModel.imageUrl ?? '', 'notif');
      await _flutterLocalNotificationsPlugin.show(
          int.tryParse("${fcmModel.id}") ?? createUniqueId,
          fcmModel.title,
          fcmModel.message,
          notifyWithImage(file.path),
          payload: jsonEncode(notification.data));
    } else {
      await _flutterLocalNotificationsPlugin.show(createUniqueId,
          fcmModel.title, fcmModel.message, pushNotificationDetails,
          payload: jsonEncode(notification.data));
    }
  }

  static Future<void> sentOrderNotification(RemoteMessage notification) async {
    FcmModel fcmModel = FcmModel.fromJson(notification.data);
    const customPushNotificationDetails = NotificationDetails(
      android: AndroidNotificationDetails(
          icon: "@drawable/ic_notification",
          "Order",
          "Order",
          channelDescription: "Monitoring order status",
          visibility: NotificationVisibility.public,
          importance: Importance.high,
          priority: Priority.high,
          ledOnMs: 100,
          ledOffMs: 1000,
          category: AndroidNotificationCategory.service,
          styleInformation: DefaultStyleInformation(true, true),
          channelAction: AndroidNotificationChannelAction.createIfNotExists,
          actions: [AndroidNotificationAction('54', 'Detail')]),
      iOS: DarwinNotificationDetails(
        presentSound: true,
        presentAlert: true,
        presentBadge: true,
        interruptionLevel: InterruptionLevel.critical,
      ),
    );
    await _flutterLocalNotificationsPlugin.show(
        createUniqueId,
        notification.notification?.title,
        fcmModel.message,
        customPushNotificationDetails);
  }

  static Future<void> cancelAllNotifications() async {
    await _flutterLocalNotificationsPlugin.cancelAll();
  }

  static Future<void> _initFirebaseMessaging() async {
    FirebaseMessaging.onMessage.listen(_handleIncomingForegroundNotification);
    FirebaseMessaging.onMessageOpenedApp.listen(_handleOpenedAppNotification);
  }

  static Future<void> _handleIncomingForegroundNotification(
    RemoteMessage remoteMessage,
  ) async {
    try {
      printLongString(
          "============= REMOTE MESSAGE ==============\n${jsonEncode(remoteMessage.toMap())}\n============= END OF REMOTE MESSAGE ==============");
      await displayPushNotification(
          remoteMessage, "Handle Incoming Foreground Notification");
    } catch (e, s) {
      await SentryHelper.logException(e, s);
    }
  }

  static Future<void> _handleOpenedAppNotification(
      RemoteMessage remoteMessage) async {
    try {
      Sentry.captureMessage(
          "_handleOpenedAppNotification\n${remoteMessage.toMap().toString()}");
      infoLogger("_handleOpenedAppNotification",
          "Remote message ${remoteMessage.toMap().toString()}");
      Get.toNamed(Routes.NOTIFICATION);
    } catch (e, s) {
      await SentryHelper.logException(e, s);
    }
  }

  static Future<void> _requestPermissions() async {
    NotificationSettings settings = await _firebaseMessaging.requestPermission(
      alert: true,
      badge: true,
      provisional: false,
      sound: true,
    );

    if (settings.authorizationStatus == AuthorizationStatus.authorized) {
    } else {}
    await _flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<
            IOSFlutterLocalNotificationsPlugin>()
        ?.requestPermissions(
          alert: true,
          badge: true,
          sound: true,
        );
  }

  static Future<void> _listenNotificationActionStream() async {
    selectNotificationStream.stream
        .listen((event) => event)
        .onData((data) async {
      _notificationActionStream.add(data);
      switch (data?.notificationResponseType) {
        case NotificationResponseType.selectedNotification:
          break;
        case NotificationResponseType.selectedNotificationAction:
          break;
        default:
      }
    });
  }

  static Future<void> _createNotificationChannels() async {
    await _flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(androidChannel);
  }

  static Future<void> deleteToken() async => _firebaseMessaging.deleteToken();
}

int get createUniqueId =>
    DateTime.now().millisecondsSinceEpoch.remainder(100000);
