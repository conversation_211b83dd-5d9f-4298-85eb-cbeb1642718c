import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:mobile_crm/app/utils/logger.dart';

class AnalyticsService {
  final _analytics = FirebaseAnalytics.instance;

  AnalyticsService._();

  static final _service = AnalyticsService._();

  factory AnalyticsService() => _service;

  static FirebaseAnalytics get instance => _service._analytics;

  static FirebaseAnalyticsObserver get observer =>
      FirebaseAnalyticsObserver(analytics: _service._analytics);

  static loginMethod({required String method}) {
    try {
      method = method.replaceAll(' ', '_');
      _service._analytics.logLogin(loginMethod: method);
    } catch (e, s) {
      errorLogger(pos: "Analytics Services", error: e, stackTrace: s);
    }
  }

  static void setUserProperty({
    String? uId,
    String? loginType,
    String? name,
    String? email,
    String? phone,
  }) {
    _service._analytics.setUserId(id: uId);
    _service._analytics.setUserProperty(name: "login_method", value: loginType);
    _service._analytics.setUserProperty(name: "name", value: name);
    _service._analytics.setUserProperty(name: "email", value: email);
    _service._analytics.setUserProperty(name: "phone", value: phone);
  }

  static void logAddCart(
      {required String currency,
      double? value,
      required List<AnalyticsEventItem> items}) {
    _service._analytics
        .logAddToCart(items: items, value: value, currency: currency);
  }

  static void logBeginCheckout(
      {required String currency,
      double? value,
      String? coupon,
      required List<AnalyticsEventItem> items}) {
    _service._analytics.logBeginCheckout(
        items: items, currency: currency, value: value, coupon: coupon);
  }
}
