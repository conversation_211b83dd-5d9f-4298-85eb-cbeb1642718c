import 'package:json_annotation/json_annotation.dart';

part 'auth_token_model.g.dart';

@JsonSerializable()
class AuthTokenModel {
  AuthTokenModel({this.type, this.token, this.expired, this.waSender});

  String? type;
  String? token;
  int? expired;
  @Json<PERSON>ey(name: 'wa_sender')
  String? waSender;
  String? phone;

  static AuthTokenModel fromJsonModel(Object? json) =>
      AuthTokenModel.fromJson(json as Map<String, dynamic>);

  static Map<String, dynamic> toJsonModel(AuthTokenModel authTokenModel) =>
      authTokenModel.toJson();

  factory AuthTokenModel.fromJson(Map<String, dynamic> json) =>
      _$AuthTokenModelFromJson(json);

  Map<String, dynamic> toJson() => _$AuthTokenModelToJson(this);
}
