import 'package:json_annotation/json_annotation.dart';

part 'deals_payment_model.g.dart';

@JsonSerializable()
class DealsPaymentModel {
  PaymentDeal? payment;
  @JsonKey(name: 'payment_type')
  String? paymentType;
  @JsonKey(name: 'expired_at')
  int? expiredAt;

  DealsPaymentModel({this.payment, this.paymentType, this.expiredAt});

  static DealsPaymentModel fromJsonModel(Object? json) =>
      DealsPaymentModel.fromJson(json as Map<String, dynamic>);

  factory DealsPaymentModel.fromJson(Map<String, dynamic> json) =>
      _$DealsPaymentModelFromJson(json);

  Map<String, dynamic> toJson() => _$DealsPaymentModelToJson(this);
}

@JsonSerializable()
class PaymentDeal {
  String? url;

  PaymentDeal({this.url});

  static PaymentDeal fromJsonModel(Object? json) =>
      PaymentDeal.fromJson(json as Map<String, dynamic>);

  static Map<String, dynamic> toJsonModel(PaymentDeal paymentDeal) =>
      paymentDeal.toJson();

  factory PaymentDeal.fromJson(Map<String, dynamic> json) =>
      _$PaymentDealFromJson(json);

  Map<String, dynamic> toJson() => _$PaymentDealToJson(this);
}
