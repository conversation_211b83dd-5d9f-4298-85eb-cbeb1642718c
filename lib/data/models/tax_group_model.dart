import 'package:json_annotation/json_annotation.dart';
import 'package:mobile_crm/data/models/tax_model.dart';

part 'tax_group_model.g.dart';

@JsonSerializable()
class TaxGroupModel {
  int? outletFkId;
  List<TaxModel>? tax;

  TaxGroupModel({this.outletFkId, this.tax});

  factory TaxGroupModel.fromJson(Map<String, dynamic> json) =>
      _$TaxGroupModelFromJson(json);

  Map<String, dynamic> toJson() => <String, dynamic>{
        'outletFkId': outletFkId,
        'tax': tax,
      };
}
