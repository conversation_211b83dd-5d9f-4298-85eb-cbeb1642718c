import 'package:json_annotation/json_annotation.dart';
import 'package:mobile_crm/data/models/payments_model.dart';

import '../providers/db/database.dart';
import 'deal_model.dart';

part 'deal_payment_model.g.dart';

@JsonSerializable()
class DealPaymentModel {
  PaymentModel? payment;
  DealModel? dealDetail;
  @JsonKey(name: 'promotion_buy_id')
  int? promotionBuyId;

  DealPaymentModel({this.payment, this.promotionBuyId, this.dealDetail});

  factory DealPaymentModel.fromJson(Map<String, dynamic> json) =>
      DealPaymentModel(
        payment: json['payment'] == null
            ? null
            : PaymentModel.fromJson(json['payment'] as Map<String, dynamic>),
        promotionBuyId: json['promotion_buy_id'] as int?,
        dealDetail: json['dealDetail'] == null
            ? null
            : DealModel.fromJson(json['dealDetail'] as Map<String, dynamic>),
      );

  Map<String, dynamic> toJson() => _$DealPaymentModelToJson(this);

  void setDealDetail(DealData d) {
    dealDetail = DealModel(
        name: d.name,
        voucherPriceType: d.voucherPriceType,
        publishDate: d.publishDate,
        promotionId: d.promotionId,
        products: d.products,
        timeActive: d.timeActive,
        dealsValue: d.dealsValue,
        outlet: d.outlet,
        discountType: d.discountType,
        promoDiscountType: d.promoDiscountType,
        promoType: d.promoType,
        memberTypes: d.memberTypes);
  }

  static DealPaymentModel fromJsonModel(Object? json) =>
      DealPaymentModel.fromJson(json as Map<String, dynamic>);

  static Map<String, dynamic> toJsonModel(DealPaymentModel dealPaymentModel) =>
      dealPaymentModel.toJson();
}
