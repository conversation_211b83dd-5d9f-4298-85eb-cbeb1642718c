import 'package:json_annotation/json_annotation.dart';
import 'package:mobile_crm/data/providers/db/database.dart';

part "product_recomendation_model.g.dart";

@JsonSerializable()
class ProductRecomendationModel extends ProductRecomendationData {
  String? description;
  @J<PERSON><PERSON><PERSON>(name: "min_qty_order")
  int? minQtyOrder;
  String? name;
  @<PERSON>son<PERSON><PERSON>(name: "outlet_fkid")
  int? outletFkid;
  @<PERSON>son<PERSON><PERSON>(name: "outlet_name")
  String? outletName;
  String? photo;
  @<PERSON>son<PERSON>ey(name: "price_sell")
  int? priceSell;
  @JsonKey(name: "product_detail_id")
  int? productDetailId;
  @Json<PERSON>ey(name: "product_id")
  int? productId;
  @<PERSON>son<PERSON>ey(name: "product_subcatogory_fkid")
  int? productSubcategoryFkid;
  String? stock;
  @<PERSON><PERSON><PERSON><PERSON>(name: "stock_management")
  int? stockManagement;
  @<PERSON><PERSON><PERSON><PERSON>(name: "stock_qty")
  int? stockQty;
  @<PERSON><PERSON><PERSON><PERSON>(name: "unit_fkid")
  int? unitFkid;
  @<PERSON><PERSON><PERSON><PERSON>(name: "variant_id")
  int? variantId;
  @<PERSON><PERSON><PERSON><PERSON>(name: "variant_name")
  String? variantName;

  ProductRecomendationModel(
      {this.description,
      this.minQtyOrder,
      this.name,
      this.outletFkid,
      this.outletName,
      this.photo,
      this.priceSell,
      this.productDetailId,
      this.productId,
      this.productSubcategoryFkid,
      this.stock,
      this.stockManagement,
      this.stockQty,
      this.unitFkid,
      this.variantId,
      this.variantName})
      : super(
            description: description,
            minQtyOrder: minQtyOrder,
            name: name,
            outletFkid: outletFkid,
            outletName: outletName,
            photo: photo,
            priceSell: priceSell,
            productDetailId: productDetailId,
            productId: productId,
            productSubcategoryFkid: productSubcategoryFkid,
            stock: stock,
            stockManagement: stockManagement,
            stockQty: stockQty,
            unitFkid: unitFkid,
            variantId: variantId,
            variantName: variantName);

  factory ProductRecomendationModel.fromJsonModel(Object? json) =>
      ProductRecomendationModel.fromJson(json as Map<String, dynamic>);

  factory ProductRecomendationModel.fromJson(Map<String, dynamic> json) =>
      _$ProductRecomendationModelFromJson(json);

  Map<String, dynamic> toJsonn() => _$ProductRecomendationModelToJson(this);
}
