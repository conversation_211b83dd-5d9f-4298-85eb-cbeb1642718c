// ignore_for_file: overridden_fields
import 'package:json_annotation/json_annotation.dart';

part 'subcategory_config_model.g.dart';

@JsonSerializable()
class SubcategoryConfigModel {
  SubcategoryConfigModel({
    this.viewType,
  });

  @J<PERSON><PERSON><PERSON>(name: 'view_type')
  String? viewType;

  static SubcategoryConfigModel fromJsonModel(Object? json) =>
      SubcategoryConfigModel.fromJson(json as Map<String, dynamic>);
  factory SubcategoryConfigModel.fromJson(Map<String, dynamic> json) =>
      _$SubcategoryConfigModelFromJson(json);
  Map<String, dynamic> toJson() =>
      _$SubcategoryConfigModelToJson(this);
}
