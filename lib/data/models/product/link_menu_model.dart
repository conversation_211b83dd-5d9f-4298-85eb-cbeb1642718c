// ignore_for_file: overridden_fields

import 'package:drift/drift.dart' as dr;
import 'package:mobile_crm/data/providers/db/database.dart';

import 'link_menu_detail_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'link_menu_model.g.dart';

@JsonSerializable()
class LinkMenuModel extends LinkMenuData {
  LinkMenuModel({
    this.linkMenuId,
    this.name,
    this.description,
    this.orderNo,
    this.isMultipleChoice,
    this.outletFkId,
    this.productFkId,
    this.productDetailFkId,
    this.linkMenuDetail,
  });

  @override
  @Json<PERSON>ey(name: 'linkmenu_id')
  int? linkMenuId;
  @override
  String? name;
  @override
  String? description;
  @override
  @JsonKey(name: 'order_no')
  int? orderNo;
  @override
  @JsonKey(name: 'is_multiplechoice')
  int? isMultipleChoice;
  @override
  @JsonKey(name: 'outlet_fkid')
  int? outletFkId;
  @override
  @JsonKey(name: 'product_fkid')
  int? productFkId;
  @override
  @JsonKey(name: 'product_detail_fkid')
  int? productDetailFkId;
  @override
  @JsonKey(name: 'link_menu_detail')
  List<LinkMenuDetailModel>? linkMenuDetail;

  static LinkMenuModel fromJsonModel(Object? json) =>
      LinkMenuModel.fromJson(json as Map<String, dynamic>);
  factory LinkMenuModel.fromJson(Map<String, dynamic> json) =>
      _$LinkMenuModelFromJson(json);
  @override
  Map<String, dynamic> toJson({dr.ValueSerializer? serializer}) =>
      _$LinkMenuModelToJson(this);

  void setChosenMenuDetail(bool value, int linkMenuDetailId,
      {required String productName}) {
    if (isMultipleChoice == 0) {
      linkMenuDetail?.forEach((element) {
        if (element.linkMenuDetailId == linkMenuDetailId) {
          element.setChosen(value);
          element.setName(productName);
        } else {
          element.setChosen(false);
        }
      });
    } else {
      linkMenuDetail?.forEach((element) {
        if (element.linkMenuDetailId == linkMenuDetailId) {
          element.setChosen(value);
          element.setName(productName);
        }
      });
    }
  }

  int sumAdditionalPrice(int qty) {
    int i = 0;
    linkMenuDetail
        ?.where((element) => element.isChosen == true)
        .forEach((element) {
      i += ((element.priceAdd ?? 0) * qty);
    });
    return i;
  }
}
