import 'package:json_annotation/json_annotation.dart';

import '../tax_model.dart';

part 'link_menu_detail_model.g.dart';

@JsonSerializable()
class LinkMenuDetailModel {
  LinkMenuDetailModel(
      {this.linkMenuDetailId,
      this.linkMenuFkId,
      this.productDetailFkId,
      this.priceAdd,
      this.isChosen,
      this.name,
      this.tax});

  @JsonKey(name: 'linkmenu_detail_id')
  int? linkMenuDetailId;
  @JsonKey(name: 'linkmenu_fkid')
  int? linkMenuFkId;
  @JsonKey(name: 'product_detail_fkid')
  int? productDetailFkId;
  @Json<PERSON>ey(name: 'price_add')
  int? priceAdd;
  bool? isChosen;
  String? name;
  List<TaxModel>? tax;

  factory LinkMenuDetailModel.fromJson(Map<String, dynamic> json) =>
      _$LinkMenuDetailModelFromJson(json);
  Map<String, dynamic> toJson() => _$LinkMenuDetailModelToJson(this);

  setChosen(bool value) => isChosen = value;
  setName(String value) => name = value;

  int calculateTax(int qty) {
    int totTax = 0;
    if (tax != null && (isChosen == true)) {
      tax?.forEach((tx) {
        int eachTax = 0;
        if (tx.statusTax == 'permanent') {
          if (tx.typeTax == 'percentage') {
            double valTax = (tx.jumlah ?? 0) / 100;
            eachTax += (((priceAdd ?? 0) * valTax) * qty).toInt();
          } else if (tx.typeTax == 'nominal') {
            int valTax = tx.jumlah ?? 0;
            eachTax += (((priceAdd ?? 0) + valTax) * qty).toInt();
          }
        }
        tx.updateTotalTax(eachTax);
        totTax += eachTax;
      });
    }
    return totTax;
  }
}
