import 'package:json_annotation/json_annotation.dart';

part 'payments_model.g.dart';

@JsonSerializable()
class PaymentsModel {
  String? method = '';
  String? name = '';
  int? pay = 0;
  int? total = 0;

  PaymentsModel({this.method, this.name, this.pay, this.total});

  factory PaymentsModel.fromJson(Map<String, dynamic> json) =>
      _$PaymentsModelFromJson(json);

  Map<String, dynamic> toJson() => _$PaymentsModelToJson(this);
}

@JsonSerializable()
class PaymentModel {
  String? title;
  String? type;
  String? method;
  String? name;
  int? pay = 0;
  int? total = 0;
  @JsonKey(name: 'payment_type')
  String? paymentType;
  @JsonKey(name: 'require_detail')
  bool? requireDetail;
  @JsonKey(name: 'expired_at')
  int? expiredAt;
  QrisPaymentModel? qris;
  List<PaymentDataModel>? data;

  PaymentModel(
      {this.title,
      this.type,
      this.requireDetail,
      this.data,
      this.name,
      this.method,
      this.pay,
      this.paymentType,
      this.total,
      this.qris,
      this.expiredAt});

  factory PaymentModel.fromJson(Map<String, dynamic> json) =>
      _$PaymentModelFromJson(json);

  Map<String, dynamic> toJson() => _$PaymentModelToJson(this);
}

@JsonSerializable()
class PaymentDataModel {
  dynamic id;
  String? info;
  String? name;
  String? type;
  @JsonKey(name: 'payment_info')
  PaymentInfoModel? paymentInfo;

  PaymentDataModel(
      {this.id, this.info, this.name, this.paymentInfo, this.type});

  static PaymentDataModel fromJsonModel(Object? json) =>
      PaymentDataModel.fromJson(json as Map<String, dynamic>);

  static Map<String, dynamic> toJsonModel(PaymentDataModel paymentDataModel) =>
      paymentDataModel.toJson();

  factory PaymentDataModel.fromJson(Map<String, dynamic> json) =>
      _$PaymentDataModelFromJson(json);

  Map<String, dynamic> toJson() => _$PaymentDataModelToJson(this);
}

@JsonSerializable()
class PaymentMediaModel {
  @JsonKey(name: 'bank_id')
  int? bankId;
  String? name;
  @JsonKey(name: 'no_rekening')
  String? noRekening;
  String? owner;

  PaymentMediaModel({this.bankId, this.name, this.noRekening, this.owner});

  factory PaymentMediaModel.fromJson(Map<String, dynamic> json) =>
      _$PaymentMediaModelFromJson(json);

  Map<String, dynamic> toJson() => _$PaymentMediaModelToJson(this);
}

@JsonSerializable()
class PaymentInfoModel {
  String? type;
  String? value;
  @JsonKey(name: 'expired_at')
  int? expiredAt;

  PaymentInfoModel({this.type, this.value, this.expiredAt});

  factory PaymentInfoModel.fromJson(Map<String, dynamic> json) =>
      _$PaymentInfoModelFromJson(json);

  Map<String, dynamic> toJson() => _$PaymentInfoModelToJson(this);
}

@JsonSerializable()
class QrisPaymentModel {
  String? url;

  QrisPaymentModel({this.url});

  factory QrisPaymentModel.fromJson(Map<String, dynamic> json) =>
      _$QrisPaymentModelFromJson(json);

  Map<String, dynamic> toJson() => _$QrisPaymentModelToJson(this);
}
