import 'package:json_annotation/json_annotation.dart';
import 'package:mobile_crm/data/models/available_model.dart';

part 'variant_model.g.dart';

@JsonSerializable()
class VariantModel {
  List<AvailableModel>? available;
  String? name;
  @J<PERSON><PERSON><PERSON>(name: 'stock_status')
  String? stockStatus;
  @Json<PERSON>ey(name: 'outlet_id')
  int? outletId;
  @JsonKey(name: 'price_sell')
  int? priceSell;
  @Json<PERSON>ey(name: 'product_detail_id')
  int? productDetailId;
  @Json<PERSON>ey(name: 'variant_id')
  int? variantId;

  VariantModel(
      {this.available,
      this.name,
      this.stockStatus,
      this.outletId,
      this.productDetailId,
      this.priceSell,
      this.variantId});

  factory VariantModel.fromJson(Map<String, dynamic> json) =>
      _$VariantModelFromJson(json);

  Map<String, dynamic> toJson() => _$VariantModelToJson(this);
}
