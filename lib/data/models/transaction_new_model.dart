// ignore_for_file: non_constant_identifier_names, overridden_fields

import 'package:drift/drift.dart' as dr;
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:logger/logger.dart';
import 'package:mobile_crm/app/helper/deal_helper.dart';
import 'package:mobile_crm/app/helper/product_helper.dart';
import 'package:mobile_crm/app/utils/logger.dart';
import 'package:mobile_crm/app/utils/utils.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/data/models/tax_model.dart';
import 'package:mobile_crm/data/providers/db/database.dart';
import 'package:mobile_crm/domain/repository/product_repository.dart';

import '../../app/modules/outlet/views/widget/dialog/remove_order_list_dialog.dart';
import 'cart_model.dart';
import 'deal_model.dart';
import 'order_model.dart';
import 'outlet_model.dart';
import 'product_model.dart';
import 'self_order_model.dart';
import 'shipment_model.dart';

part 'transaction_new_model.g.dart';

var logger = Logger(printer: PrettyPrinter(methodCount: 0));

@JsonSerializable(createFactory: false)
class TransactionNewModel extends TransactionNewTableData {
  @override
  int? id;
  @override
  @JsonKey(name: 'outlet_id')
  int? outletId;
  @override
  @JsonKey(name: 'order_type')
  String? orderType;
  @override
  @JsonKey(name: 'customer_name')
  String? customerName;
  @override
  @JsonKey(name: 'receipt_receiver')
  String? receiptReceiver;
  @override
  @JsonKey(name: 'dining_table')
  String? diningTable;
  @override
  @JsonKey(name: 'order_note')
  String? orderNote;
  @override
  @JsonKey(name: 'order_list', toJson: _listCartToJson)
  List<CartModel>? orderList;
  @override
  ShipmentModel? shipment;
  @override
  PickupModel? pickup;
  @override
  @JsonKey(name: 'self_order')
  OrderModel? selfOrder;
  @override
  String? status;
  @override
  @JsonKey(name: 'order_sales_id')
  String? orderSalesId;
  @override
  String? message;
  @override
  int? point;
  @override
  @JsonKey(toJson: _dealToJson)
  DealModel? deal;
  @override
  @JsonKey(name: 'total_qty')
  int? totalQTY;
  @override
  @JsonKey(name: 'sub_total')
  int? subTotal;
  @override
  @JsonKey(toJson: _toJsonOutlet)
  OutletModel? outlet;
  @override
  String? path;
  @override
  @JsonKey(name: 'total_tax')
  int? totalTax;
  @override
  @JsonKey(name: 'total_discount')
  int? totalDiscount;
  @override
  @JsonKey(name: 'grand_total')
  int? totalBill;
  @override
  @JsonKey(name: 'time_order')
  int? timeOrder;
  @override
  bool? isOnOrder;
  @JsonKey(name: 'payment_timeout')
  int? paymentTimeout;
  TransactionNewModel(
      {this.id,
      this.outletId,
      this.orderType,
      this.customerName,
      this.message,
      this.receiptReceiver,
      this.diningTable,
      this.path,
      this.orderNote,
      this.orderList,
      this.shipment,
      this.pickup,
      this.selfOrder,
      this.orderSalesId,
      this.status,
      this.point,
      this.outlet,
      this.deal,
      this.timeOrder,
      this.isOnOrder = false,
      this.subTotal = 0,
      this.totalQTY = 0,
      this.totalBill = 0,
      this.totalDiscount = 0,
      this.totalTax = 0,
      this.paymentTimeout = 0});

  static TransactionNewModel fromJsonModel(Object? json) =>
      TransactionNewModel.fromJson(json as Map<String, dynamic>);

  static Map<String, dynamic> toJsonModel(
          TransactionNewModel transactionHistoryModel) =>
      transactionHistoryModel.toJson();

  static Map<String, dynamic>? _dealToJson(DealModel? dM) => dM?.toJson();
  static Map<String, dynamic>? _toJsonOutlet(OutletModel? om) => om?.toJson();

  factory TransactionNewModel.fromJson(Map<String, dynamic> json) =>
      TransactionNewModel(
        id: json['id'] as int?,
        outletId: json['outlet_id'] as int?,
        orderType: json['order_type'] as String?,
        customerName: json['customer_name'] as String?,
        message: json['message'] as String?,
        receiptReceiver: json['receipt_receiver'] as String?,
        diningTable: json['dining_table'] as String?,
        orderNote: json['order_note'] as String?,
        orderList: (json['order_list'] as List<dynamic>?)
            ?.map((e) => CartModel.fromJson(e as Map<String, dynamic>))
            .toList(),
        outlet: json['outlet'] == null
            ? null
            : OutletModel.fromJson(json['outlet'] as Map<String, dynamic>),
        shipment: json['shipment'] == null
            ? null
            : ShipmentModel.fromJson(json['shipment'] as Map<String, dynamic>),
        pickup: json['pickup'] == null
            ? null
            : PickupModel.fromJson(json['pickup'] as Map<String, dynamic>),
        selfOrder: json['self_order'] == null
            ? null
            : OrderModel.fromJson(json['self_order'] as Map<String, dynamic>),
        orderSalesId: json['order_sales_id'] as String?,
        status: json['status'] as String?,
        path: json['path'] as String?,
        point: json['point'] as int?,
        timeOrder: json['time_order'] as int?,
        deal: json['deal'] == null
            ? null
            : DealModel.fromJson(json['deal'] as Map<String, dynamic>),
        totalQTY: json['total_qty'] as int?,
        subTotal: json['sub_total'] as int?,
        totalTax: json['total_tax'] as int?,
        totalDiscount: json['total_discount'] as int?,
        totalBill: json['grand_total'] as int?,
        paymentTimeout: json['payment_timeout'] as int?,
      );

  @override
  Map<String, dynamic> toJson({dr.ValueSerializer? serializer}) =>
      _$TransactionNewModelToJson(this);

  static List<Map<String, dynamic>> _listCartToJson(List<CartModel>? cm) =>
      (cm ?? []).map((e) => e.toJson()).toList();

  void addToOrder(Cart value) {
    List<CartModel> _orderList = orderList ?? [];
    var filterTax = value.product?.tax
        ?.where((element) => (element.statusTax == 'permanent'))
        .toList();
    value.product?.tax = filterTax;
    CartModel newCart = CartModel(
        cartId: Utils.generateUniqueNumber(orderList?.fold(
                [],
                (previousValue, element) =>
                    previousValue?..add(element.cartId ?? 0)) ??
            []),
        product_detail_id: value.product_detail_fkid,
        product_fkid: value.product_fkid,
        product_detail_fkid: value.product_detail_fkid,
        outlet_fkid: value.outlet_fkid,
        qty: value.qty,
        price: value.price,
        linkMenu: value.linkMenu,
        note: value.note,
        product: value.product,
        tax: filterTax);
    _orderList.add(newCart);
    _orderList.sort(
      (a, b) => (a.product?.name ?? '').compareTo(b.product?.name ?? ''),
    );
    orderList = _orderList;
    _applyDeals();
    sumTotalBill();
    infoLogger("Add To Order",
        "Add Product ${newCart.product?.name} | cart id ${value.cartId} , ${newCart.cartId} | ${newCart.product?.priceSell} | Have Tax ${newCart.tax != null}");
  }

  Future<void> checkProductList({required ProductRepository repo}) async {
    List<CartModel> tmpListOrder = [];
    var orderListCopy = List.from(orderList ?? []);

    await Future.forEach(orderListCopy, (element) async {
      var res = await repo
          .getProductByDetailId(element.product_detail_fkid.toString());
      element.product = ProductModel(
        description: element.product?.description ?? '',
        name: element.product?.name ?? '',
        minQtyOrder: element.product?.min_qty_order ?? 1,
        photo: element.product?.photo,
        priceSell: element.product?.priceSell,
        product_detail_id: element.product_detail_fkid,
        product_fkid: element.product?.product_fkid,
        availability: element.product?.availability,
        available: element.product?.available,
        hourStart: res?.hourStart ?? element.product?.hourStart,
        hourEnd: res?.hourEnd ?? element.product?.hourEnd,
        stockStatus: element.product?.stockStatus,
        tax: element.product?.tax,
        unit: element.product?.unit,
      );

      if (!ProductHelper.isProductAvailableOnHours(element.product)) {
        tmpListOrder.add(element);
      }
    });

    for (var ele in tmpListOrder) {
      orderList?.removeWhere((el) => el.cartId == ele.cartId);
    }
    BuildContext? context = Get.context;

    if (context != null && tmpListOrder.isNotEmpty) {
      RemoveOrderListDialog(nonActive: tmpListOrder)
          .toDialog(barrierDismissible: false)
          .of(context);
    }

    _applyDeals();
    sumTotalBill();
  }

  bool? updateOrder(Cart? value) {
    if (value != null) {
      var findCart = orderList
          ?.firstWhereOrNull((element) => element.cartId == value.cartId);
      if (findCart != null) {
        findCart.qty = value.qty;
        findCart.note = value.note;
        findCart.product = value.product;
        findCart.tax = value.tax;
        _applyDeals();
      }
      sumTotalBill();
      return findCart != null;
    }
    return false;
  }

  List<TaxModel> getTaxDetail() {
    List<TaxModel> i = [];
    List<TaxModel> j = [];
    orderList?.forEach((element) {
      element.tax?.forEach((element) {
        i.add(element);
      });
    });

    i.sort(
      (a, b) => (a.gratuityId ?? 0).compareTo(b.gratuityId ?? 0),
    );
    List<int> lints = [];
    for (var element in i) {
      lints.add(element.gratuityId ?? 0);
    }

    lints = lints.toSet().toList();
    for (var n in lints) {
      var o = i.where((element) => element.gratuityId == n).toList();
      if (o.isNotEmpty) {
        var u = o.firstOrNull;
        j.add(TaxModel(
          gratuityId: u?.gratuityId,
          name: u?.name,
          totalTax: o.fold(
              0,
              (previousValue, element) =>
                  (previousValue ?? 0) + (element.totalTax ?? 0)),
        ));
      }
    }

    for (var tM in j) {
      orderList?.forEach((oL) {
        oL.linkMenu?.forEach((element) {
          element.linkMenuDetail?.forEach((lmd) {
            lmd.tax?.forEach((el) {
              if (tM.gratuityId == el.gratuityId) {
                tM.totalTax = (tM.totalTax ?? 0) + (el.totalTax ?? 0);
              }
            });
          });
        });
      });
    }

    return j;
  }

  bool? removeOrder(Cart? value) {
    if (value != null) {
      var findCart = orderList
          ?.firstWhereOrNull((element) => element.cartId == value.cartId);
      var isRemove = orderList?.remove(findCart);
      _applyDeals();
      sumTotalBill();
      infoLogger("Remove To Order",
          "Remove Product ${value.product?.name} | QTY ${value.qty} | ${value.product?.priceSell} | Have Tax ${value.tax != null} | Have Link Menu ${value.linkMenu != null}");
      return isRemove;
    }
    return false;
  }

  int sumTax() {
    var taxTot = 0;
    orderList?.forEach((element) {
      logger.i("Element ${element.tax}");
      taxTot += element.calculateTax(deal);
      element.linkMenu?.forEach((linkMe) {
        linkMe.linkMenuDetail?.forEach((linkMenuDe) {
          taxTot += linkMenuDe.calculateTax(element.qty ?? 0);
        });
      });
    });
    totalTax = taxTot;
    return taxTot;
  }

  int sumQTY() {
    int qtyTotal = 0;
    orderList?.forEach((element) {
      qtyTotal += element.qty ?? 0;
    });
    totalQTY = qtyTotal;
    return qtyTotal;
  }

  int sumPrice() {
    int subTot = 0;
    orderList?.forEach((element) {
      subTot += ((element.qty ?? 0) * (element.product?.priceSell ?? 0));
      subTot += (element.linkMenu?.fold(
              0,
              (previousValue, el) =>
                  (previousValue ?? 0) +
                  el.sumAdditionalPrice(element.qty ?? 0)) ??
          0);
    });
    subTotal = subTot;
    return subTot;
  }

  int sumTotalDiscount() => totalDiscount ?? 0;

  int sumTotalBill() {
    int totBill = (sumPrice() + sumTax()) - sumTotalDiscount();
    int rounded = totBill +
        ((100 - totBill % 100) != 100 ? (100 - totBill % 100) : 0) +
        (shipment?.shippingCharge ?? 0);
    totalBill = rounded;
    return rounded;
  }

  // Deal

  DealModel? setDeals(DealData? value) {
    if (value != null) {
      var i = DealModel(
        promotionBuyId: value.promotionBuyId,
        publishDate: value.publishDate,
        promotionTypeName: value.promotionTypeName,
        promotionId: value.promotionId,
        memberTypes: value.memberTypes,
        active: value.active,
        name: value.name,
        products: value.products,
        termProduct: value.termProduct,
        endPromotionDate: value.endPromotionDate,
        promotionProduct: value.promotionProduct,
        maximumDiscountNominal: value.maximumDiscountNominal,
        minOrder: value.minOrder,
        maxQtyPromo: value.maxQtyPromo,
        endPromotionTime: value.endPromotionTime,
        startPromotionTime: value.startPromotionTime,
        discountType: value.discountType,
        amount: value.amount,
        promoDiscountMaximum: value.promoDiscountMaximum,
        voucherPriceType: value.voucherPriceType,
        promoType: value.promoType,
        promotionFkId: value.promotionFkId,
        dealsValue: value.dealsValue,
        promoNominal: value.promoNominal,
        promoDiscountType: value.promoDiscountType,
        photo: value.photo,
        isPercent: value.isPercent,
        point: value.point,
        source: value.source,
        sunday: value.sunday,
        monday: value.monday,
        tuesday: value.tuesday,
        wednesday: value.wednesday,
        thursday: value.thursday,
        friday: value.friday,
        saturday: value.saturday,
      );
      deal = i;
    } else {
      deal = null;
      totalDiscount = 0;
    }
    _applyDeals();
    return deal;
  }

  void _removePricePromo() {
    orderList?.forEach((element) {
      element.product?.updatePricePromo(null);
      element.updateQtyPromo(null);
      element.product?.updatePriceDiscount(null);
    });
  }

  void _applyDeals() {
    _removePricePromo();
    if (deal == null) {
      _removePricePromo();
    } else {
      bool? isCartQualifiedBasedOnTermProductDeal = true;
      if ((!DealHelper.isVoucherCanBeUserBasedOnDate(deal ?? DealModel())) ||
          (sumPrice() < (deal?.minOrder ?? 0))) {
        isCartQualifiedBasedOnTermProductDeal =
            _isCartQualifiedBasedOnTermProduct(deal ?? DealModel());
        totalDiscount = 0;
        _removePricePromo();
        return;
      }
      int discount = 0;
      if (deal?.promoType?.toLowerCase() == DealPromoType.discount.name) {
        _removePricePromo();
        // infoLogger("Calculate Deal", "Type ${deal?.promoDiscountType}");
        if (deal?.promoDiscountType?.toLowerCase() ==
            DealDiscountType.nominal.name) {
          if (deal?.termProduct != null) {
            if (isCartQualifiedBasedOnTermProductDeal == true) {
              double applyPriceForAll = (deal?.promoNominal ?? 0) / sumQTY();
              orderList?.forEach((element) {
                element.product?.updatePriceDiscount(applyPriceForAll.isInfinite
                    ? 0
                    : (applyPriceForAll.toInt()));
              });
            }
            discount = (isCartQualifiedBasedOnTermProductDeal == true)
                ? (deal?.promoNominal ?? 0)
                : 0;
          } else {
            double applyPriceForAll = (deal?.promoNominal ?? 0) / sumQTY();
            orderList?.forEach((element) {
              element.product?.updatePriceDiscount(
                  applyPriceForAll.isInfinite ? 0 : (applyPriceForAll.toInt()));
            });
            discount = (deal?.promoNominal ?? 0);
          }
        } else if (deal?.promoDiscountType?.toLowerCase() ==
            DealDiscountType.percent.name) {
          bool isDiscountUnlimited =
              deal?.promoDiscountMaximum == null ? true : false;

          if (deal?.termProduct != null) {
            if (isCartQualifiedBasedOnTermProductDeal == true) {
              if (isDiscountUnlimited) {
                orderList?.forEach((element) {
                  infoLogger("price 1",
                      "Product name ${element.product?.name} | ${element.product?.priceSell}");
                  element.product?.updatePriceDiscount(
                      ((element.product?.priceSell ?? 0) *
                              ((deal?.promoNominal ?? 0) / 100))
                          .toInt());
                  infoLogger("price 2",
                      "Product name ${element.product?.name} | ${element.product?.priceSellDiscount}");
                });
              } else {
                int totCurrentPrice = 0;
                orderList?.forEach((element) {
                  totCurrentPrice += ((element.qty ?? 0) *
                          ((element.product?.priceSell ?? 0) *
                              ((deal?.promoNominal ?? 0) / 100)))
                      .toInt();
                });

                if (totCurrentPrice > (deal?.promoDiscountMaximum ?? 0)) {
                  double applyPriceForAll =
                      (deal?.promoDiscountMaximum ?? 0) / sumQTY();
                  orderList?.forEach((element) {
                    infoLogger("price 1",
                        "Product name ${element.product?.name} | ${element.product?.priceSell}");
                    element.product?.updatePriceDiscount(
                        applyPriceForAll.isInfinite
                            ? 0
                            : applyPriceForAll.toInt());
                    infoLogger("price 2",
                        "Product name ${element.product?.name} | ${element.product?.priceSellDiscount}");
                  });
                } else {
                  orderList?.forEach((element) {
                    infoLogger("price 1",
                        "Product name ${element.product?.name} | ${element.product?.priceSell}");
                    element.product?.updatePriceDiscount(
                        ((element.product?.priceSell ?? 0) *
                                ((deal?.promoNominal ?? 0) / 100))
                            .toInt());
                    infoLogger("price 2",
                        "Product name ${element.product?.name} | ${element.product?.priceSellDiscount}");
                  });
                }
              }
            }
          } else {
            if (isDiscountUnlimited) {
              orderList?.forEach((element) {
                infoLogger("price 1",
                    "Product name ${element.product?.name} | ${element.product?.priceSellDiscount}");
                element.product?.updatePriceDiscount(
                    ((element.product?.priceSell ?? 0) *
                            ((deal?.promoNominal ?? 0) / 100))
                        .toInt());
                infoLogger("price 2",
                    "Product name ${element.product?.name} | ${element.product?.priceSellDiscount}");
              });
            } else {
              int totCurrentPrice = 0;
              orderList?.forEach((element) {
                totCurrentPrice += ((element.qty ?? 0) *
                        ((element.product?.priceSell ?? 0) *
                            ((deal?.promoNominal ?? 0) / 100)))
                    .toInt();
              });

              if (totCurrentPrice > (deal?.promoDiscountMaximum ?? 0)) {
                double applyPriceForAll =
                    (deal?.promoDiscountMaximum ?? 0) / sumQTY();
                orderList?.forEach((element) {
                  infoLogger("price 1",
                      "Product name ${element.product?.name} | ${element.product?.priceSellDiscount}");
                  element.product?.updatePriceDiscount(
                      applyPriceForAll.isInfinite
                          ? 0
                          : applyPriceForAll.toInt());
                  infoLogger("price 2",
                      "Product name ${element.product?.name} | ${element.product?.priceSellDiscount}");
                });
              } else {
                orderList?.forEach((element) {
                  infoLogger("price 1",
                      "Product name ${element.product?.name} | ${element.product?.priceSellDiscount}");
                  element.product?.updatePriceDiscount(
                      ((element.product?.priceSell ?? 0) *
                              ((deal?.promoNominal ?? 0) / 100))
                          .toInt());
                  infoLogger("price 2",
                      "Product name ${element.product?.name} | ${element.product?.priceSellDiscount}");
                });
              }
            }
          }

          bool isBillMoreThanMaxDiscountNom = isDiscountUnlimited
              ? false
              : (sumPrice() * ((deal?.promoNominal ?? 0) / 100)) >
                  (deal?.promoDiscountMaximum ?? 0);
          int discountSum =
              (sumPrice() * ((deal?.promoNominal ?? 0) / 100)).toInt();
          int maxDiscount = (deal?.promoDiscountMaximum ?? 0);
          if (deal?.termProduct != null) {
            // infoLogger("Calculate Deal",
            //     "Term Product Exist ~ is Cart Qualified $isCartQualifiedBasedOnTermProductDeal | Is Bill more than max discount $isBillMoreThanMaxDiscountNom ~ Is Unlimited $isDiscountUnlimited | Promo Nominal ${deal?.promoNominal} | Promo Discount Maximum ${deal?.promoDiscountMaximum} | Type ${deal?.promoDiscountType}");
            discount = (isCartQualifiedBasedOnTermProductDeal == true)
                ? isBillMoreThanMaxDiscountNom
                    ? maxDiscount
                    : discountSum
                : 0;
          } else {
            // infoLogger("Calculate Deal",
            //     "Term Product Not Exist ~ is Cart Qualified $isCartQualifiedBasedOnTermProductDeal | Is Bill more than max discount $isBillMoreThanMaxDiscountNom ~ Is Unlimited $isDiscountUnlimited | Promo Nominal ${deal?.promoNominal} | Promo Discount Maximum ${deal?.promoDiscountMaximum} | Type ${deal?.promoDiscountType}");

            discount = isBillMoreThanMaxDiscountNom ? maxDiscount : discountSum;
          }
        }
      } else if (deal?.promoType?.toLowerCase() ==
          DealPromoType.special_price.name) {
        _removePricePromo();
        // infoLogger("Calculate Deal",
        //     "Type ${deal?.promoType} | Term Product exist ${deal?.termProduct != null}");
        if (deal?.termProduct != null) {
          discount = _getDiscountProductByTerm(appliedVoucher: deal);
        } else {
          discount = _getProductPriceAfterDiscount(appliedVoucher: deal);
        }
      }
      // infoLogger("Apply deals", discount);
      totalDiscount = discount;
    }
    // printLongString(jsonEncode(deal?.toJson()));
  }

  bool _isCartQualifiedBasedOnTermProduct(DealData dealData) {
    int totalQtyBuyTermProduct = 0;
    if (dealData.termProduct != null) {
      if (dealData.termProduct?.qty == 0) {
        return true;
      } else {
        for (var cart in (orderList ?? <CartModel>[])) {
          for (var term in dealData.termProduct?.products ?? []) {
            if (term.productDetailFkId == cart.product_detail_id) {
              totalQtyBuyTermProduct += cart.qty ?? 0;
              break;
            }
          }
        }
      }
    }
    return totalQtyBuyTermProduct >= (dealData.termProduct?.qty ?? 0);
  }

  int _getDiscountProductByTerm({required DealData? appliedVoucher}) {
    double sum = 0;
    int currentQtyBuyPromo = 0;
    int qtyBuyPromoRemaining = appliedVoucher?.maxQtyPromo ?? 0;
    if (appliedVoucher != null && appliedVoucher.termProduct != null) {
      appliedVoucher.promotionProduct
          ?.sort((a, b) => (a.name ?? '').compareTo(b.name ?? ''));
      for (var cart in (orderList ?? <CartModel>[])) {
        var product = appliedVoucher.promotionProduct?.firstWhereOrNull(
            (element) => element.productDetailFkId == cart.product_detail_fkid);
        if (product != null &&
            _isCartQualifiedBasedOnTermProduct(appliedVoucher)) {
          if (appliedVoucher.maxQtyPromo != null) {
            if ((cart.qty ?? 0) <= (appliedVoucher.maxQtyPromo ?? 0)) {
              if (currentQtyBuyPromo > (appliedVoucher.maxQtyPromo ?? 0)) {
                sum += 0;
              } else {
                if (currentQtyBuyPromo < (appliedVoucher.maxQtyPromo ?? 0)) {
                  cart.product?.updatePricePromo(product.priceSellPromo);
                  if (((cart.qty ?? 0) + currentQtyBuyPromo) >
                      (appliedVoucher.maxQtyPromo ?? 0)) {
                    if ((cart.qty ?? 0) > qtyBuyPromoRemaining) {
                      cart.updateQtyPromo(qtyBuyPromoRemaining);
                      qtyBuyPromoRemaining = 0;
                    } else {
                      cart.updateQtyPromo(
                          qtyBuyPromoRemaining - (cart.qty ?? 0));
                      qtyBuyPromoRemaining =
                          qtyBuyPromoRemaining - (cart.qty ?? 0);
                    }

                    sum += ((cart.product?.priceSell ?? 0) -
                            (product.priceSellPromo ?? 0)) *
                        (cart.qtyPromo ?? 0);
                    currentQtyBuyPromo += qtyBuyPromoRemaining;
                  } else {
                    sum += ((cart.product?.priceSell ?? 0) -
                            (product.priceSellPromo ?? 0)) *
                        (cart.qty ?? 0);
                    currentQtyBuyPromo += (cart.qty ?? 0);
                    cart.updateQtyPromo(cart.qty);
                    qtyBuyPromoRemaining =
                        qtyBuyPromoRemaining - (cart.qty ?? 0);
                  }
                } else {
                  sum += 0;
                }
              }
            } else {
              if (currentQtyBuyPromo > (appliedVoucher.maxQtyPromo ?? 0)) {
                sum += 0;
              } else {
                if (currentQtyBuyPromo < (appliedVoucher.maxQtyPromo ?? 0)) {
                  cart.product?.updatePricePromo(product.priceSellPromo);
                  if (((cart.qty ?? 0) + currentQtyBuyPromo) >
                      (appliedVoucher.maxQtyPromo ?? 0)) {
                    sum += ((cart.product?.priceSell ?? 0) -
                            (product.priceSellPromo ?? 0)) *
                        ((appliedVoucher.maxQtyPromo ?? 0) -
                            currentQtyBuyPromo);
                    cart.updateQtyPromo(
                        (appliedVoucher.maxQtyPromo ?? 0) - currentQtyBuyPromo);
                    currentQtyBuyPromo +=
                        (appliedVoucher.maxQtyPromo ?? 0) - currentQtyBuyPromo;
                  } else {
                    sum += ((cart.product?.priceSell ?? 0) -
                            (product.priceSellPromo ?? 0)) *
                        (cart.qty ?? 0);
                    currentQtyBuyPromo += (cart.qty ?? 0);
                    cart.updateQtyPromo(cart.qty);
                  }
                } else {
                  sum += 0;
                }
              }
              sum += 0;
            }
          } else {
            cart.product?.updatePricePromo(product.priceSellPromo);
            sum += ((cart.product?.priceSell ?? 0) -
                    (product.priceSellPromo ?? 0)) *
                (cart.qty ?? 0);
          }
        }
      }
      // infoLogger("Get discount product by term", logString);
    }
    sumTotalDiscount();
    return sum.toInt();
  }

  int _getProductPriceAfterDiscount({required DealData? appliedVoucher}) {
    double sum = 0;
    int currentQtyBuyPromo = 0;
    for (var product in (orderList ?? <CartModel>[])) {
      appliedVoucher?.promotionProduct?.forEach((element) {
        if (element.productDetailFkId == product.product_detail_id &&
            (currentQtyBuyPromo <= (appliedVoucher.maxQtyPromo ?? 0))) {
          if (appliedVoucher.maxQtyPromo != null) {
            currentQtyBuyPromo += 1;
          }
          product.product?.updatePricePromo(element.priceSellPromo);
          sum += ((element.priceSell ?? 0) - (element.priceSellPromo ?? 0)) *
              (product.qty ?? 0);
        }
      });
    }
    // infoLogger("Get product price after discount", logString);
    return sum.toInt();
  }

  void setOutlet(Outlet value) {
    outlet = OutletModel(
        outlet_id: value.outlet_id,
        province: value.province,
        phone: value.phone,
        longitude: value.longitude,
        order_type: value.order_type,
        enable_order: value.enable_order,
        latitude: value.latitude,
        distance_value: value.distance_value,
        distance: value.distance,
        city: value.city,
        address: value.address,
        name: value.name);
  }

  int sumQtyWhereProductId(int productId) {
    var i = orderList
        ?.where((element) => element.product_fkid == productId)
        .toList();
    return i?.fold(
            0,
            (previousValue, element) =>
                (previousValue ?? 0) + (element.qty ?? 0)) ??
        0;
  }

  int sumQtyWhereProductDetailId(int productDetailId) {
    var i = orderList
        ?.where((element) => element.product_detail_id == productDetailId)
        .toList();
    return i?.fold(
            0,
            (previousValue, element) =>
                (previousValue ?? 0) + (element.qty ?? 0)) ??
        0;
  }
}
