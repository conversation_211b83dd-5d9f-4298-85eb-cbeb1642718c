// ignore_for_file: non_constant_identifier_names

class ShareModel {
  ShareModel({
    this.shortLink = '',
  });

  String shortLink;

  static ShareModel fromJsonModel(Object? json) =>
      ShareModel.fromJson(json as Map<String, dynamic>);

  static Map<String, dynamic> toJsonModel(ShareModel userCodeModel) =>
      userCodeModel.toJson();

  factory ShareModel.fromJson(Map<String, dynamic> json) => ShareModel(
        shortLink: json["shortLink"],
      );

  Map<String, dynamic> toJson() => {
        "shortLink": shortLink,
      };
}
