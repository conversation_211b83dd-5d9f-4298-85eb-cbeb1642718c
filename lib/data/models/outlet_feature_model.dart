// ignore_for_file: non_constant_identifier_names

import 'package:json_annotation/json_annotation.dart';

part 'outlet_feature_model.g.dart';

@JsonSerializable()
class OutletFeatureModel {
  @Json<PERSON>ey(name: 'tableinput')
  bool? tableInput;

  OutletFeatureModel({this.tableInput});

  factory OutletFeatureModel.fromJson(Map<String, dynamic> json) =>
      _$OutletFeatureModelFromJson(json);

  Map<String, dynamic> toJson() => _$OutletFeatureModelToJson(this);
}
