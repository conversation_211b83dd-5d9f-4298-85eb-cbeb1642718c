import 'package:image_picker/image_picker.dart';
import 'package:json_annotation/json_annotation.dart';

part 'feedback_model.g.dart';

@JsonSerializable()
class FeedbackModel {
  String? comment = '';
  @J<PERSON><PERSON>ey(name: 'sales_fkid')
  String? salesFkId = '';
  @J<PERSON><PERSON><PERSON>(name: 'sales_id')
  String? salesId = '';
  String? opinion = '';
  @JsonKey(name: 'opinion_code')
  String? opinionCode = '';
  @<PERSON><PERSON><PERSON><PERSON>(name: 'sales_feedback_id')
  int? salesFeedbackId = 0;
  int? stars = 0;
  @Json<PERSON>ey(fromJson: ignore, toJson: ignore)
  XFile? file;
  @Json<PERSON>ey(name: 'date_created')
  int? dateCreated = 0;

  FeedbackModel(
      {this.comment,
      this.salesFeedbackId,
      this.salesFkId,
      this.salesId,
      this.opinion,
      this.opinionCode,
      this.stars,
      this.file,
      this.dateCreated});

  factory FeedbackModel.fromJson(Map<String, dynamic> json) =>
      _$FeedbackModelFromJson(json);

  Map<String, dynamic> toJson() => _$FeedbackModelToJson(this);
}

T? ignore<T>(dynamic _) => null;
