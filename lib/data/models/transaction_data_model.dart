// ignore_for_file: non_constant_identifier_names, overridden_fields

import 'package:drift/drift.dart' as dr;
import 'package:json_annotation/json_annotation.dart';
import 'package:mobile_crm/data/providers/db/database.dart';

import 'deal_payment_model.dart';
import 'order_detail_model.dart';
import 'self_order_model.dart';
import 'tax_group_model.dart';

part 'transaction_data_model.g.dart';

@JsonSerializable()
class TransactionDataModel extends TransactionDataData {
  @override
  int? id;
  @override
  int? promoId;
  @override
  int? orderId;
  @override
  String? type;
  @override
  SelfOrder? orderInfo;
  @override
  String? proofFilePath;
  @override
  TaxGroupModel? orderTax;
  @override
  OrderDetailModel? orderDetail;
  @override
  DealPaymentModel? dealPayment;

  TransactionDataModel(
      {this.id,
      this.promoId,
      this.orderId,
      this.type,
      this.orderInfo,
      this.orderTax,
      this.orderDetail,
      this.dealPayment,
      this.proofFilePath});

  static TransactionDataModel fromJsonModel(Object? json) =>
      TransactionDataModel.fromJson(json as Map<String, dynamic>);

  static Map<String, dynamic> toJsonModel(
          TransactionDataModel transactionHistoryModel) =>
      transactionHistoryModel.toJson();

  factory TransactionDataModel.fromJson(Map<String, dynamic> json) =>
      _$TransactionDataModelFromJson(json);

  @override
  Map<String, dynamic> toJson({dr.ValueSerializer? serializer}) => _$TransactionDataModelToJson(this);
}
