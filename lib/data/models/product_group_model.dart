// ignore_for_file: non_constant_identifier_names, overridden_fields, annotate_overrides

import 'package:mobile_crm/data/providers/db/database.dart';

class ProductGroupModel {
  String categoryName = '';
  int categoryPosition = 999;
  List<Product>? products;

  ProductGroupModel(
      {this.categoryName = "", this.categoryPosition = 999, this.products});

  Map toJson() {
    List<Map>? productList = products?.map((i) => i.toJson()).toList();
    return {
    'category_name': categoryName,
    'category_position': categoryPosition,
    'products': productList,
    };
  }
}
