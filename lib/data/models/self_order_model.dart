// ignore_for_file: non_constant_identifier_names
import 'package:json_annotation/json_annotation.dart';
import 'package:mobile_crm/data/models/cart_model.dart';
import 'package:mobile_crm/data/models/deal_model.dart';
import 'package:mobile_crm/data/providers/db/database.dart';

import 'order_model.dart';
import 'shipment_model.dart';

part 'self_order_model.g.dart';

class SelfOrder {
  SelfOrder(
      {this.outlet_id,
      this.customer_name,
      this.orderType,
      this.order_list,
      this.orderNote,
      this.deal,
      this.outletName,
      this.totalQTY,
      this.shipment,
      this.pickup,
      this.totalPrice,
      this.point = 0,
      this.totalDiscount,
      this.totalSaving,
      this.address,
      this.orderExpired,
      this.receiptReceiver,
      this.diningTable,
      this.viewMore = false});

  int? outlet_id;
  String? customer_name;
  String? orderType;
  String? receiptReceiver;
  String? diningTable;
  String? orderNote;
  List<Cart>? order_list;
  String? outletName;
  OrderModel? orderExpired;
  int? point;
  DealData? deal;
  String? address;
  int? totalQTY;
  int? totalPrice;
  int? totalDiscount;
  int? totalSaving;
  ShipmentModel? shipment;
  PickupModel? pickup;
  bool viewMore;

  factory SelfOrder.fromJson(Map<String, dynamic> json) => SelfOrder(
        outlet_id: json['outlet_id'] as int?,
        customer_name: json['customer_name'] as String?,
        orderType: json['order_type'] as String?,
        receiptReceiver: json['receipt_receiver'] as String?,
        diningTable: json['dining_table'] as String?,
        address: json['address'] as String?,
        orderNote: json['order_note'] as String?,
        shipment: json['shipment'] == null
            ? null
            : ShipmentModel.fromJson(json['shipment'] as Map<String, dynamic>),
        pickup: json['pickup'] == null
            ? null
            : PickupModel.fromJson(json['pickup'] as Map<String, dynamic>),
        orderExpired: json['order_expired'] == null
            ? null
            : OrderModel.fromJson(
                json['order_expired'] as Map<String, dynamic>),
        outletName: json['outletName'] as String?,
        totalQTY: json['totalQTY'] as int?,
        totalPrice: json['totalPrice'] as int?,
        point: json['point'] as int?,
        deal: json['deal'] == null
            ? null
            : DealModel.fromJson(json['deal'] as Map<String, dynamic>),
        totalSaving: json['totalSaving'] as int?,
        totalDiscount: json['totalDiscount'] as int?,
        viewMore: json['viewMore'] as bool? ?? false,
        order_list: (json['order_list'] as List<dynamic>?)
            ?.map((e) => CartModel.fromJson(e as Map<String, dynamic>))
            .toList(),
      );

  Map<String, dynamic> toJson() {
    Map<String, dynamic> jsonMap = <String, dynamic>{
      'outlet_id': outlet_id,
      'customer_name': customer_name,
      'outletName': outletName,
      'receipt_receiver': receiptReceiver,
      'dining_table': diningTable,
      'order_type': orderType,
      'order_note': orderNote,
      'shipment': shipment?.toJson(),
      'pickup': pickup?.toJson(),
      'address': address,
      'deal': deal?.toJson(),
      'totalQTY': totalQTY,
      'totalPrice': totalPrice,
      'order_expired': orderExpired,
      'point': point,
      'totalSaving': totalSaving,
      'totalDiscount': totalDiscount,
      'viewMore': viewMore,
      'order_list': order_list,
    };
    jsonMap.removeWhere((key, value) => value == null);
    return jsonMap;
  }

  void setPoint(int value) {
    point = value;
  }

  void setDinningTable(String table) {
    diningTable = table;
  }

  void setReceiveReceipts(String phoneNumber) {
    receiptReceiver = phoneNumber;
  }
}

@JsonSerializable()
class PickupModel {
  PickupModel({this.pickupTime});

  @JsonKey(name: 'pickup_time')
  String? pickupTime;

  static PickupModel fromJsonModel(Object? json) =>
      PickupModel.fromJson(json as Map<String, dynamic>);

  static Map<String, dynamic> toJsonModel(PickupModel pickupModel) =>
      pickupModel.toJson();

  factory PickupModel.fromJson(Map<String, dynamic> json) =>
      _$PickupModelFromJson(json);

  Map<String, dynamic> toJson() => _$PickupModelToJson(this);
}
