import 'package:json_annotation/json_annotation.dart';

import 'cart_model.dart';
import 'feedback_model.dart';
import 'payments_model.dart';
import 'shipment_model.dart';

part 'order_detail_model.g.dart';

@JsonSerializable()
class OrderDetailModel {
  OrderDetailModel(
      {this.paymentMedia,
      this.payment,
      this.feedBack,
      this.orderNote,
      this.orderType,
      this.pickupTime,
      this.status,
      this.message,
      this.memberFkId,
      this.salesFkId,
      this.id,
      this.outletId,
      this.grandTotal,
      this.grandTotalPayment,
      this.orderSalesId,
      this.outletFkId,
      this.paymentInfo,
      this.rejectReason,
      this.timeAcceptReject,
      this.timeModified,
      this.timeOrder,
      this.timeOrderExpired,
      this.timeReady,
      this.timeTaken,
      this.orderList,
      this.shipment});

  final FeedbackModel? feedBack;
  @JsonKey(name: 'grand_total')
  final int? grandTotal;
  @JsonKey(name: 'grand_total_payment')
  final int? grandTotalPayment;
  final int? id;
  @Json<PERSON><PERSON>(name: 'member_fkid')
  final int? memberFkId;
  final String? message;
  @Json<PERSON>ey(name: 'order_note')
  final String? orderNote;
  @JsonKey(name: 'order_sales_id')
  final String? orderSalesId;
  @JsonKey(name: 'order_type')
  final String? orderType;
  @JsonKey(name: 'outlet_fkid')
  final int? outletFkId;
  @JsonKey(name: 'outlet_id')
  final int? outletId;
  @JsonKey(name: 'payment_info')
  final PaymentInfoModel? paymentInfo;
  @JsonKey(name: 'pickup_time')
  final String? pickupTime;
  @JsonKey(name: 'reject_reason')
  final String? rejectReason;
  @JsonKey(name: 'sales_fkid')
  final int? salesFkId;
  final String? status;
  @JsonKey(name: 'time_accept_reject')
  final int? timeAcceptReject;
  @JsonKey(name: 'time_modified')
  final int? timeModified;
  @JsonKey(name: 'time_order')
  final int? timeOrder;
  @JsonKey(name: 'time_order_expired')
  final int? timeOrderExpired;
  @JsonKey(name: 'time_ready')
  final int? timeReady;
  @JsonKey(name: 'time_taken')
  final int? timeTaken;
  final ShipmentModel? shipment;
  @JsonKey(name: 'order_list')
  final List<CartModel>? orderList;
  @JsonKey(name: 'payment_media')
  final List<PaymentMediaModel>? paymentMedia;
  final List<PaymentModel>? payment;

  static OrderDetailModel fromJsonModel(Object? json) =>
      OrderDetailModel.fromJson(json as Map<String, dynamic>);

  static Map<String, dynamic> toJsonModel(OrderDetailModel orderDetailModel) =>
      orderDetailModel.toJson();

  factory OrderDetailModel.fromJson(Map<String, dynamic> json) =>
      _$OrderDetailModelFromJson(json);

  Map<String, dynamic> toJson() {
    Map<String, dynamic> jsonMap = _$OrderDetailModelToJson(this);
    jsonMap.removeWhere((key, value) => value == null);
    return jsonMap;
  }
}
