import 'package:json_annotation/json_annotation.dart';
part "delivery_price_model.g.dart";

@JsonSerializable()
class DeliveryPrice {
  Breakdown? breakdown;
  String? currency;
  Distance? distance;
  int? price;

  DeliveryPrice({this.breakdown, this.currency, this.distance, this.price});

  static DeliveryPrice fromJsonModel(Object? json) =>
      DeliveryPrice.fromJson(json as Map<String, dynamic>);

  static Map<String, dynamic> toJsonModel(DeliveryPrice deliveryPrice) =>
      deliveryPrice.toJson();

  factory DeliveryPrice.fromJson(Object? json) =>
      _$DeliveryPriceFromJson(json as Map<String, dynamic>);

  Map<String, dynamic> toJson() => _$DeliveryPriceToJson(this);
}

@JsonSerializable()
class Distance {
  double? distance;
  String? unit;

  Distance({this.distance, this.unit});

  static Distance fromJsonModel(Object? json) =>
      Distance.fromJson(json as Map<String, dynamic>);

  static Map<String, dynamic> toJsonModel(Distance distance) =>
      distance.toJson();

  factory Distance.fromJson(Map<String, dynamic> json) =>
      _$DistanceFromJson(json);

  Map<String, dynamic> toJson() => _$DistanceToJson(this);
}

@JsonSerializable()
class Breakdown {
  String? price;
  String? threshold;

  Breakdown({this.price, this.threshold});

  static Breakdown fromJsonModel(Object? json) =>
      Breakdown.fromJson(json as Map<String, dynamic>);

  static Map<String, dynamic> toJsonModel(Breakdown breakdown) =>
      breakdown.toJson();

  factory Breakdown.fromJson(Map<String, dynamic> json) =>
      _$BreakdownFromJson(json);

  Map<String, dynamic> toJson() => _$BreakdownToJson(this);
}
