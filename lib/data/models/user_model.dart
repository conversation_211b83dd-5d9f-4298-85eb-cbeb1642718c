import 'package:json_annotation/json_annotation.dart';

part 'user_model.g.dart';

@JsonSerializable()
class UserModel {
  UserModel({
    this.additionalData,
    this.address,
    this.barcode,
    this.barcodeUrl,
    this.city,
    this.dataCreated,
    this.dateOfBirth,
    this.email,
    this.emailVerificationStatus,
    this.emailVerified,
    this.gender,
    this.memberId,
    this.memberType,
    this.label,
    this.typefkid,
    this.membersDetailId,
    this.name,
    this.phone,
    this.postalCode,
    this.province,
    this.registerDate,
    this.totalPoint,
  });

  @<PERSON>son<PERSON>ey(name: 'additional_data')
  AdditionalData? additionalData;
  String? address;
  String? barcode;
  @J<PERSON><PERSON><PERSON>(name: 'barcode_url')
  String? barcodeUrl;
  String? city;
  @JsonKey(name: 'data_created')
  int? dataCreated;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'date_of_birth')
  int? dateOfBirth;
  String? email;
  String? label;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'email_verification_status')
  String? emailVerificationStatus;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'email_verified')
  int? emailVerified;
  int? gender;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'member_id')
  int? memberId;
  @J<PERSON><PERSON><PERSON>(name: 'member_type')
  String? memberType;
  @JsonKey(name: 'type_fkid')
  int? typefkid;
  @JsonKey(name: 'members_detail_id')
  int? membersDetailId;
  String? name;
  String? phone;
  @JsonKey(name: 'postal_code')
  String? postalCode;
  String? province;
  @JsonKey(name: 'register_date')
  int? registerDate;
  @JsonKey(name: 'total_point')
  int? totalPoint;
  @JsonKey(name: 'total_spent')
  int? totalSpent;
  @JsonKey(name: 'total_point_lost')
  int? totalPointLost;
  @JsonKey(name: 'total_spend_lost')
  int? totalSpendLost;

  int getCurrentTotalSpent() => (totalSpent ?? 0) - (totalSpendLost ?? 0);
  int getCurrentTotalPoint() => (totalPoint ?? 1) - (totalPointLost ?? 0);

  static UserModel fromJsonModel(Object? json) =>
      UserModel.fromJson(json as Map<String, dynamic>);

  static Map<String, dynamic> toJsonModel(UserModel userModel) =>
      userModel.toJson();

  factory UserModel.fromJson(Map<String, dynamic> json) =>
      _$UserModelFromJson(json);

  Map<String, dynamic> toJson() => _$UserModelToJson(this);
}

class AdditionalData {
  AdditionalData({
    this.industry,
  });

  String? industry;

  factory AdditionalData.fromJson(Map<String, dynamic> json) => AdditionalData(
        industry: json["industry"],
      );

  Map<String, dynamic> toJson() => {
        "industry": industry,
      };
}

@JsonSerializable()
class MemberTypes {
  MemberTypes({
    this.memberTypeId,
    this.memberTypeName,
    this.promotionId,
  });

  @JsonKey(name: 'member_type_id')
  int? memberTypeId;
  @JsonKey(name: 'member_type_name')
  String? memberTypeName;
  @JsonKey(name: 'promotion_id')
  int? promotionId;

  factory MemberTypes.fromJson(Map<String, dynamic> json) => MemberTypes(
        memberTypeId: json["member_type_id"],
        memberTypeName: json["member_type_name"],
        promotionId: json["promotion_id"],
      );

  Map<String, dynamic> toJson() => _$MemberTypesToJson(this);

// Map<String, dynamic> toJson() => {
//   "industry": industry,
// };
}
