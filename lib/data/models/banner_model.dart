// ignore_for_file: overridden_fields

import 'package:json_annotation/json_annotation.dart';
import 'package:mobile_crm/data/providers/db/database.dart';

part 'banner_model.g.dart';

@JsonSerializable()
class BannerModel extends BannerAppData {
  @override
  @Json<PERSON><PERSON>(name: "action_detail")
  String actionDetail;
  @override
  @Json<PERSON>ey(name: "admin_fkid")
  int adminFkid;
  @override
  @JsonKey(name: "crm_banner_id")
  int crmBannerId;
  @override
  @JsonKey(name: "data_created")
  int dataCreated;
  @override
  @JsonKey(name: "data_modified")
  int dataModified;
  @override
  @JsonKey(name: "data_status")
  String dataStatus;
  @override
  String photo;
  @override
  int status;
  @override
  int position;

  BannerModel(
      {this.actionDetail = '',
      this.adminFkid = 0,
      this.crmBannerId = 0,
      this.dataCreated = 0,
      this.dataModified = 0,
      this.dataStatus = '',
      this.photo = '',
      this.position = 9999,
      this.status = 0})
      : super(
            actionDetail: actionDetail,
            adminFkid: adminFkid,
            crmBannerId: crmBannerId,
            dataCreated: dataCreated,
            dataModified: dataModified,
            dataStatus: dataStatus,
            photo: photo,
            position: position,
            status: status);

  factory BannerModel.fromJson(Map<String, dynamic> json) =>
      _$BannerModelFromJson(json);

  static BannerModel fromJsonModel(Object? json) =>
      BannerModel.fromJson(json as Map<String, dynamic>);

  static Map<String, dynamic> toJsonModel(BannerModel bannerModel) =>
      bannerModel.toJson();
}
