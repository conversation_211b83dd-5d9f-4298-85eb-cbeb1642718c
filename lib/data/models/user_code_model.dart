// ignore_for_file: non_constant_identifier_names

class UserCodeModel {
  UserCodeModel({this.expired_at, this.key, this.code});

  int? expired_at;
  String? key;
  String? code;

  static UserCodeModel fromJsonModel(Object? json) =>
      UserCodeModel.fromJson(json as Map<String, dynamic>);

  static Map<String, dynamic> toJsonModel(UserCodeModel userCodeModel) =>
      userCodeModel.toJson();

  factory UserCodeModel.fromJson(Map<String, dynamic> json) => UserCodeModel(
        expired_at: json["expired_at"],
        key: json["key"],
        code: json["code"],
      );

  Map<String, dynamic> toJson() => {
        "expired_at": expired_at,
        "key": key,
        "code": code,
      };
}
