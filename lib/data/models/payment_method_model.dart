import 'package:json_annotation/json_annotation.dart';
import 'package:mobile_crm/data/models/payments_model.dart';

part 'payment_method_model.g.dart';

@JsonSerializable()
class PaymentMethodModel {
  String? type;
  String? name;
  List<PaymentMethodDetail>? detail;

  PaymentMethodModel({this.type, this.name, this.detail});

  static PaymentMethodModel fromJsonModel(Object? json) =>
      PaymentMethodModel.fromJson(json as Map<String, dynamic>);

  factory PaymentMethodModel.fromJson(Map<String, dynamic> json) =>
      _$PaymentMethodModelFromJson(json);

  Map<String, dynamic> toJson() => _$PaymentMethodModelToJson(this);
}

@JsonSerializable()
class PaymentMethodDetail {
  String? id;
  String? name;
  PaymentMethodDetailIcon? icon;
  String? type;
  @JsonKey(name: 'account_name')
  String? accountName;
  @JsonKey(name: 'account_number')
  String? accountNumber;
  @JsonKey(name: 'payment_info')
  PaymentInfoModel? paymentInfo;

  PaymentMethodDetail(
      {this.id, this.name, this.icon, this.type, this.paymentInfo, this.accountName, this.accountNumber});

  static PaymentMethodDetail fromJsonModel(Object? json) =>
      PaymentMethodDetail.fromJson(json as Map<String, dynamic>);

  static Map<String, dynamic> toJsonModel(
          PaymentMethodDetail paymentMethodDetail) =>
      paymentMethodDetail.toJson();

  factory PaymentMethodDetail.fromJson(Map<String, dynamic> json) =>
      _$PaymentMethodDetailFromJson(json);

  Map<String, dynamic> toJson() => _$PaymentMethodDetailToJson(this);

  set methodDetailType(String? type) => this.type = type;
  String? get methodDetailType => type;
}

@JsonSerializable()
class PaymentMethodDetailIcon {
  String? small;
  String? medium;

  PaymentMethodDetailIcon({this.small, this.medium});

  static PaymentMethodDetailIcon fromJsonModel(Object? json) =>
      PaymentMethodDetailIcon.fromJson(json as Map<String, dynamic>);

  static Map<String, dynamic> toJsonModel(
          PaymentMethodDetailIcon paymentMethodDetailIcon) =>
      paymentMethodDetailIcon.toJson();

  factory PaymentMethodDetailIcon.fromJson(Map<String, dynamic> json) =>
      _$PaymentMethodDetailIconFromJson(json);

  Map<String, dynamic> toJson() => _$PaymentMethodDetailIconToJson(this);
}
