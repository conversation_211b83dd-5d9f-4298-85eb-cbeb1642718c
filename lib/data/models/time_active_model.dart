// ignore_for_file: overridden_fields, non_constant_identifier_names

import 'package:json_annotation/json_annotation.dart';

part 'time_active_model.g.dart';

@JsonSerializable()
class TimeActiveModel {
  @J<PERSON><PERSON>ey(name: 'date_end')
  int? dateEnd;
  @J<PERSON><PERSON><PERSON>(name: 'date_start')
  int? dateStart;
  @Json<PERSON><PERSON>(name: 'day_active')
  List<String>? dayActive;
  @Json<PERSON>ey(name: 'time_end')
  String? timeEnd;
  @J<PERSON><PERSON><PERSON>(name: 'time_start')
  String? timeStart;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'end_promotion_date')
  int? endPromotionDate;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'start_promotion_date')
  int? startPromotionDate;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'end_promotion_time')
  String? endPromotionTime;
  @J<PERSON><PERSON><PERSON>(name: 'start_promotion_time')
  String? startPromotionTime;

  TimeActiveModel(
      {this.dateEnd,
      this.dateStart,
      this.dayActive,
      this.timeEnd,
      this.timeStart,
      this.endPromotionDate,
      this.endPromotionTime,
      this.startPromotionDate,
      this.startPromotionTime});

  factory TimeActiveModel.fromJson(Map<String, dynamic> json) =>
      _$TimeActiveModelFromJson(json);

  Map<String, dynamic> toJson() => _$TimeActiveModelToJson(this);
}
