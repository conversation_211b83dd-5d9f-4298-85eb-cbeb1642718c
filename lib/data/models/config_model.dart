// ignore_for_file: non_constant_identifier_names, overridden_fields

import 'dart:ui';

import 'package:json_annotation/json_annotation.dart';
import 'package:mobile_crm/core/extensions/color_extensions.dart';
import 'package:mobile_crm/data/providers/db/database.dart';

part 'config_model.g.dart';

@JsonSerializable()
class ConfigModel extends ConfigData {
  ConfigModel({this.appInfo, this.asset, this.language})
      : super(appInfo: appInfo, asset: asset, language: language);

  @override
  @J<PERSON><PERSON>ey(name: 'app_info')
  AppInfo? appInfo;
  @override
  Asset? asset;
  @override
  Language? language;
  @override
  @JsonKey(name: 'social_media')
  SocialMedia? socialMedia;
  @override
  Contact? contact;
  @JsonKey(name: "privacy_policy")
  String? privacyPolicy;
  @JsonKey(name: "term_condition")
  String? termCondition;
  @override
  Contact? product;

  static ConfigModel fromJsonModel(Object? json) =>
      ConfigModel.fromJson(json as Map<String, dynamic>);

  factory ConfigModel.fromJson(Map<String, dynamic> json) =>
      _$ConfigModelFromJson(json);

  Map<String, dynamic> toJsonn() => _$ConfigModelToJson(this);
}

@JsonSerializable()
class Asset {
  String? app_background;
  String? app_icon;
  String? toolbar_background;
  String? profile_card_background;
  ColorConfig? color;

  Asset(
      {this.app_background,
      this.app_icon,
      this.toolbar_background,
      this.profile_card_background,
      this.color});

  factory Asset.fromJson(Map<String, dynamic> json) => _$AssetFromJson(json);

  Map<String, dynamic> toJson() => _$AssetToJson(this);
}

@JsonSerializable()
class Language {
  String? point;
  String outlet;
  String menu;
  String deals;

  Language(
      {this.point,
      this.outlet = "Outlet",
      this.menu = "Menu",
      this.deals = "Deals"});

  factory Language.fromJson(Map<String, dynamic> json) =>
      _$LanguageFromJson(json);

  Map<String, dynamic> toJson() => _$LanguageToJson(this);

  String get getOutletLanguage => outlet;
  String get getMenuLanguage => menu;
  String get getDealsLanguage => deals;
  String get getPointLanguage => (point ?? 'Point');
}

@JsonSerializable()
class ColorConfig {
  String? accent;
  String? primary;
  String? primary_dark;
  String? secondary;

  ColorConfig({this.accent, this.primary, this.primary_dark, this.secondary});

  factory ColorConfig.fromJson(Map<String, dynamic> json) =>
      _$ColorConfigFromJson(json);

  Map<String, dynamic> toJson() => _$ColorConfigToJson(this);

  Color get getPrimaryColor => HexColor(primary ?? '');
  Color get getSecondaryColor => HexColor(secondary ?? '');
  Color get getPrimaryDarkColor => HexColor(primary_dark ?? '');
  Color get getAccentColor => HexColor(accent ?? '');
}

@JsonSerializable()
class AppInfo {
  Android? android;
  Ios? ios;
  Web? web;
  String dynamic_link;

  AppInfo({this.android, this.ios, this.web, this.dynamic_link = ''});

  factory AppInfo.fromJson(Map<String, dynamic> json) =>
      _$AppInfoFromJson(json);

  Map<String, dynamic> toJson() => _$AppInfoToJson(this);
}

@JsonSerializable()
class Android {
  String package_name;

  Android({this.package_name = ''});

  factory Android.fromJson(Map<String, dynamic> json) =>
      _$AndroidFromJson(json);

  Map<String, dynamic> toJson() => _$AndroidToJson(this);
}

@JsonSerializable()
class Ios {
  String bundle_id;

  Ios({this.bundle_id = ''});

  factory Ios.fromJson(Map<String, dynamic> json) => _$IosFromJson(json);

  Map<String, dynamic> toJson() => _$IosToJson(this);
}

@JsonSerializable()
class Web {
  String url;

  Web({this.url = ''});

  factory Web.fromJson(Map<String, dynamic> json) => _$WebFromJson(json);

  Map<String, dynamic> toJson() => _$WebToJson(this);
}

@JsonSerializable()
class SocialMedia {
  String? instagram;
  String? tiktok;
  String? twitter;

  SocialMedia({this.instagram, this.tiktok, this.twitter});

  factory SocialMedia.fromJson(Map<String, dynamic> json) =>
      _$SocialMediaFromJson(json);

  Map<String, dynamic> toJson() => _$SocialMediaToJson(this);
}

@JsonSerializable()
class Contact {
  String? whatsapp;

  Contact({this.whatsapp});

  factory Contact.fromJson(Map<String, dynamic> json) =>
      _$ContactFromJson(json);

  Map<String, dynamic> toJson() => _$ContactToJson(this);
}

@JsonSerializable()
class ProductConfig {
  @JsonKey(name: 'show_stock')
  bool? showStock = false;

  ProductConfig({this.showStock});

  factory ProductConfig.fromJson(Map<String, dynamic> json) =>
      _$ProductConfigFromJson(json);

  Map<String, dynamic> toJson() => _$ProductConfigToJson(this);
}
