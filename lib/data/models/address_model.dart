import 'package:json_annotation/json_annotation.dart';

part 'address_model.g.dart';

@JsonSerializable()
class AddressModel {
  AddressModel({
    this.city = '',
    this.phone,
    this.postalCode = '',
    this.province = '',
    this.district = '',
    this.latitude,
    this.longitude,
    this.address,
    this.membersAddressId,
    this.label = '',
    this.memberFkId,
    this.isDefaultAddress,
  });

  String? city;
  String? label;
  double? latitude;
  double? longitude;
  String? address;
  @JsonKey(name: 'member_fkid')
  int? memberFkId;
  String? phone;
  @JsonKey(name: 'members_address_id')
  int? membersAddressId;
  @Json<PERSON>ey(name: 'postal_code')
  String? postalCode;
  String? province;
  String? district;
  bool? isDefaultAddress;

  static AddressModel fromJsonModel(Object? json) =>
      AddressModel.fromJson(json as Map<String, dynamic>);

  static Map<String, dynamic> toJsonModel(AddressModel addressModel) =>
      addressModel.toJson();

  factory AddressModel.fromJson(Map<String, dynamic> json) =>
      _$AddressModelFromJson(json);

  Map<String, dynamic> toJson() => _$AddressModelToJson(this);
}
