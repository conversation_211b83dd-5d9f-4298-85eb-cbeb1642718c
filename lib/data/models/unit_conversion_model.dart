// ignore_for_file: overridden_fields

import 'package:json_annotation/json_annotation.dart';
import 'package:mobile_crm/data/providers/db/database.dart';
import 'package:drift/drift.dart' as dr;

part 'unit_conversion_model.g.dart';

@JsonSerializable()
class UnitConversionModel extends UnitConversionEntityData {
  @override
  @JsonKey(name: 'products_unit_convertion_id')
  int? productsUnitConversionId;
  @override
  @JsonKey(name: 'product_fkid')
  int? productFkId;
  @override
  @JsonKey(name: 'unit_fkid')
  int? unitFkId;
  @override
  int? qty;
  @override
  @JsonKey(name: 'unit_id')
  int? unitId;
  @override
  String? name = '';
  @override
  String? description = '';
  @override
  String? dataStatus = '';
  @override
  @JsonKey(name: 'admin_fkid')
  int? adminFkId;

  UnitConversionModel(
      {this.name,
      this.qty,
      this.productFkId,
      this.description,
      this.adminFkId,
      this.dataStatus,
      this.productsUnitConversionId,
      this.unitFkId,
      this.unitId});

  factory UnitConversionModel.fromJson(Map<String, dynamic> json) =>
      _$UnitConversionModelFromJson(json);

  @override
  Map<String, dynamic> toJson({dr.ValueSerializer? serializer}) => _$UnitConversionModelToJson(this);

  static UnitConversionModel fromJsonModel(Object? json) =>
      UnitConversionModel.fromJson(json as Map<String, dynamic>);
}
