import 'package:json_annotation/json_annotation.dart';

part 'search_model.g.dart';

@JsonSerializable()
class SearchModel {
  int? count;
  List<DetailModel>? detail;
  @JsonKey(name: 'search_text')
  String? searchText;

  SearchModel({this.count, this.detail, this.searchText});

  static SearchModel fromJsonModel(Object? json) =>
      SearchModel.fromJson(json as Map<String, dynamic>);

  static Map<String, dynamic> toJsonModel(SearchModel searchModel) =>
      searchModel.toJson();

  factory SearchModel.fromJson(Map<String, dynamic> json) =>
      _$SearchModelFromJson(json);

  Map<String, dynamic> toJson() => _$SearchModelToJson(this);
}

@JsonSerializable()
class DetailModel {
  String? crmSearchHistoryId;
  String? dateCreated;

  DetailModel({this.crmSearchHistoryId, this.dateCreated});

  factory DetailModel.fromJson(Map<String, dynamic> json) =>
      _$DetailModelFromJson(json);

  Map<String, dynamic> toJson() => _$DetailModelToJson(this);
}
