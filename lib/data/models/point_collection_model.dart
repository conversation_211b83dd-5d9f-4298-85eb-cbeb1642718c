// ignore_for_file: overridden_fields, non_constant_identifier_names

import 'package:json_annotation/json_annotation.dart';
import 'package:mobile_crm/data/models/product_model.dart';

import 'time_active_model.dart';

part 'point_collection_model.g.dart';

@JsonSerializable()
class PointCollectionModel {
  @JsonKey(name: 'member_type_id')
  int? memberTypeId;
  @JsonKey(name: 'min_transaction')
  int? minTransaction;
  String? name;
  int? point;
  @Json<PERSON>ey(name: 'point_type')
  String? pointType;
  @JsonKey(name: 'point_type_nominal')
  PointTypeNominal? pointTypeNominal;
  @JsonKey(name: 'point_type_product')
  List<ProductModel>? pointTypeProduct;
  @JsonKey(name: 'time_active')
  TimeActiveModel? timeActive;

  PointCollectionModel(
      {this.name,
      this.point,
      this.memberTypeId,
      this.minTransaction,
      this.pointType,
      this.pointTypeNominal,
      this.pointTypeProduct,
      this.timeActive});

  factory PointCollectionModel.fromJson(Map<String, dynamic> json) =>
      _$PointCollectionModelFromJson(json);

  Map<String, dynamic> toJsonn() => _$PointCollectionModelToJson(this);

  static PointCollectionModel fromJsonModel(Object? json) =>
      PointCollectionModel.fromJson(json as Map<String, dynamic>);

  static Map<String, dynamic> toJsonModel(
          PointCollectionModel pointCollectionModel) =>
      pointCollectionModel.toJsonn();
}

@JsonSerializable()
class PointTypeNominal {
  @JsonKey(name: 'outlet_id')
  int? outletId;
  @JsonKey(name: 'repeat_point')
  int? repeatPoint;
  @JsonKey(name: 'product_id')
  int? productId;
  @JsonKey(name: 'variant_id')
  int? variantId;
  int? transaction;

  PointTypeNominal({this.outletId, this.repeatPoint, this.transaction});

  factory PointTypeNominal.fromJson(Map<String, dynamic> json) =>
      _$PointTypeNominalFromJson(json);

  Map<String, dynamic> toJsonn() => _$PointTypeNominalToJson(this);
}

enum PointCollectionType { nominal, product }
