import 'package:json_annotation/json_annotation.dart';
import 'package:drift/drift.dart' as dr;
import 'rating_count_detail_model.dart';

part 'rating_summary_model.g.dart';

@JsonSerializable()
class RatingSummaryModel {
  double? rating;
  int? count;
  @Json<PERSON>ey(name: 'count_detail')
  List<RatingCountDetailModel>? countDetail;

  RatingSummaryModel({this.rating, this.count});

  static RatingSummaryModel fromJsonModel(Object? json) =>
      RatingSummaryModel.fromJson(json as Map<String, dynamic>);

  static Map<String, dynamic> toJsonModel(RatingSummaryModel productModel) =>
      productModel.toJson();

  factory RatingSummaryModel.fromJson(Map<String, dynamic> json) =>
      _$RatingSummaryModelFromJson(json);

  Map<String, dynamic> toJson({dr.ValueSerializer? serializer}) =>
      _$RatingSummaryModelToJson(this);
}
