import 'package:json_annotation/json_annotation.dart';
import 'package:drift/drift.dart' as dr;

part 'rating_review_model.g.dart';

@JsonSerializable()
class RatingReviewModel {
  String? customer;
  @JsonKey(name: 'date_millis')
  int? dateMillis;
  double? rating;
  String? comment;
  @JsonKey(name: 'order_item')
  List<String>? orderItem;
  List<String>? attachment;

  RatingReviewModel(
      {this.customer,
      this.dateMillis,
      this.rating,
      this.comment,
      this.orderItem,
      this.attachment});

  static RatingReviewModel fromJsonModel(Object? json) =>
      RatingReviewModel.fromJson(json as Map<String, dynamic>);

  static Map<String, dynamic> toJsonModel(RatingReviewModel productModel) =>
      productModel.toJson();

  factory RatingReviewModel.fromJson(Map<String, dynamic> json) =>
      _$RatingReviewModelFromJson(json);

  Map<String, dynamic> toJson({dr.ValueSerializer? serializer}) =>
      _$RatingReviewModelToJson(this);
}
