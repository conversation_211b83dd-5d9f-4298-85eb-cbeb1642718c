import 'package:json_annotation/json_annotation.dart';
import 'package:drift/drift.dart' as dr;
part 'rating_count_detail_model.g.dart';

@JsonSerializable()
class RatingCountDetailModel {
  double? rating;
  int? count;

  RatingCountDetailModel({this.rating, this.count});

  static RatingCountDetailModel fromJsonModel(Object? json) =>
      RatingCountDetailModel.fromJson(json as Map<String, dynamic>);

  static Map<String, dynamic> toJsonModel(
          RatingCountDetailModel productModel) =>
      productModel.toJson();

  factory RatingCountDetailModel.fromJson(Map<String, dynamic> json) =>
      _$RatingCountDetailModelFromJson(json);

  Map<String, dynamic> toJson({dr.ValueSerializer? serializer}) =>
      _$RatingCountDetailModelToJson(this);
}
