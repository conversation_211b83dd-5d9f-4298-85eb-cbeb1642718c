import 'package:mobile_crm/data/models/auth_token_model.dart';
import 'package:mobile_crm/data/models/user_model.dart';

class AuthDummyModel {
  AuthDummyModel({this.user, this.token});
  AuthTokenModel? token;
  UserModel? user;

  factory AuthDummyModel.fromJson(Map<String, dynamic> json) => AuthDummyModel(
        user: json['user'] == null
            ? null
            : UserModel.fromJson(json['user'] as Map<String, dynamic>),
        token: json['token'] == null
            ? null
            : AuthTokenModel.fromJson(json['token'] as Map<String, dynamic>),
      );
}
