import 'package:json_annotation/json_annotation.dart';

part 'otp_model.g.dart';

@JsonSerializable()
class OtpModel {
  OtpModel({this.token, this.expiredAt});

  String? token;
  @JsonKey(name: 'expired_at')
  int? expiredAt;

  static OtpModel fromJsonModel(Object? json) =>
      OtpModel.fromJson(json as Map<String, dynamic>);

  static Map<String, dynamic> toJsonModel(OtpModel authTokenModel) =>
      authTokenModel.toJson();

  factory OtpModel.fromJson(Map<String, dynamic> json) =>
      _$OtpModelFromJson(json);

  Map<String, dynamic> toJson() => _$OtpModelToJson(this);
}
