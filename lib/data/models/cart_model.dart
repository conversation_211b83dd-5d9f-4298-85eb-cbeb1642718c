// ignore_for_file: overridden_fields, non_constant_identifier_names
import 'package:drift/drift.dart' as dr;
import 'package:json_annotation/json_annotation.dart';
import 'package:mobile_crm/data/models/outlet_model.dart';
import 'package:mobile_crm/data/models/product_model.dart';
import 'package:mobile_crm/data/models/tax_model.dart';
import 'package:mobile_crm/data/providers/db/database.dart';

import 'product/link_menu_model.dart';

part 'cart_model.g.dart';

@JsonSerializable()
class CartModel extends Cart {
  @override
  int? crm_cart_id;
  @override
  int? cartId;
  @override
  int? admin_fkid;
  @override
  int? member_fkid;
  @override
  int? outlet_fkid;
  @override
  int? product_fkid;
  @override
  int? product_detail_fkid;
  @override
  int? product_detail_id;
  @override
  List<TaxModel>? tax;
  @override
  @JsonKey(toJson: _toJsonProduct)
  ProductModel? product;
  @override
  @JsonKey(toJson: _toJsonOutlet)
  OutletModel? outlet;
  @override
  String? note;
  @override
  int? qty;
  @JsonKey(name: 'qty_promo')
  int? qtyPromo;
  @override
  int? price;
  @override
  int? time_created;
  @override
  @JsonKey(name: 'link_menu')
  List<LinkMenuModel>? linkMenu;

  CartModel(
      {this.crm_cart_id,
      this.cartId,
      this.admin_fkid,
      this.member_fkid,
      this.outlet_fkid,
      this.product_fkid,
      this.product_detail_fkid,
      this.product_detail_id,
      this.product,
      this.tax,
      this.price,
      this.outlet,
      this.note,
      this.qty,
      this.qtyPromo,
      this.linkMenu,
      this.time_created})
      : super(
            cartId: cartId,
            admin_fkid: admin_fkid,
            product_detail_id: product_detail_id,
            product_fkid: product_fkid,
            product_detail_fkid: product_detail_fkid,
            outlet_fkid: outlet_fkid,
            crm_cart_id: crm_cart_id,
            qty: qty,
            price: price,
            note: note,
            linkMenu: linkMenu,
            time_created: time_created,
            member_fkid: member_fkid,
            product: product,
            outlet: outlet,
            tax: tax);

  static CartModel fromJsonModel(Object? json) =>
      CartModel.fromJson(json as Map<String, dynamic>);

  static Map<String, dynamic> toJsonModel(CartModel cartModel) =>
      cartModel.toJson();

  factory CartModel.fromJson(Map<String, dynamic> json) =>
      _$CartModelFromJson(json);

  static Map<String, dynamic>? _toJsonProduct(ProductModel? pm) => pm?.toJson();
  static Map<String, dynamic>? _toJsonOutlet(OutletModel? om) => om?.toJson();

  @override
  Map<String, dynamic> toJson({dr.ValueSerializer? serializer}) =>
      _$CartModelToJson(this);

  (List<TaxModel>?, String?) checkTax() => (product?.tax, product?.name);

  void setOutlet(Outlet value) {
    outlet = OutletModel(
        outlet_id: value.outlet_id,
        business_hour: value.business_hour,
        receipt_logo: value.receipt_logo,
        province: value.province,
        postal_code: value.postal_code,
        phone: value.phone,
        outlet_logo: value.outlet_logo,
        longitude: value.longitude,
        order_type: value.order_type,
        enable_order: value.enable_order,
        latitude: value.latitude,
        distance_value: value.distance_value,
        distance: value.distance,
        city: value.city,
        address: value.address,
        name: value.name,
        promotion_id: value.promotion_id);
  }

  void updateQtyPromo(int? value) => qtyPromo = value;

  void filterLinkMenu() {
    linkMenu?.forEach((element) {
      var k = element;
      k.linkMenuDetail = k.linkMenuDetail
          ?.where((element) => element.isChosen == true)
          .toList();
    });
  }

  int sumTax() {
    var taxTot = 0;
    tax?.forEach((element) {
      taxTot += (element.totalTax ?? 0);
    });
    return taxTot;
  }

  int calculateTax(DealData? deal) {
    int totTax = 0;
    if (tax != null) {
      tax?.forEach((tx) {
        int eachTax = 0;
        if (tx.statusTax == 'permanent') {
          if (tx.typeTax == 'percentage') {
            if (qtyPromo != null) {
              double valTax = (tx.jumlah ?? 0) / 100;
              eachTax +=
                  (((product?.priceSellPromo ?? 0) * valTax) * (qtyPromo ?? 0))
                      .toInt();
              eachTax += (((product?.priceSell ?? 0) * valTax) *
                      ((qty ?? 0) - (qtyPromo ?? 0)))
                  .toInt();
            } else {
              eachTax += (((product?.priceSellDiscount ??
                              (product?.priceSellPromo ??
                                  (product?.priceSell ?? 0))) *
                          (qty ?? 0)) *
                      ((tx.jumlah ?? 0) / 100))
                  .toInt();
            }
          } else if (tx.typeTax == 'nominal') {
            int valTax = tx.jumlah ?? 0;
            if (qtyPromo != null) {
              eachTax +=
                  (((product?.priceSellPromo ?? 0) + valTax) * (qtyPromo ?? 0))
                      .toInt();
              eachTax += (((product?.priceSell ?? 0) + valTax) *
                      ((qty ?? 0) - (qtyPromo ?? 0)))
                  .toInt();
            } else {
              eachTax += ((product?.priceSellDiscount ??
                          (product?.priceSellPromo ??
                              (product?.priceSell ?? 0))) *
                      (qty ?? 0)) +
                  valTax.toInt();
            }
          }
        }
        tx.updateTotalTax(eachTax);
        totTax += eachTax;
        // infoLogger("Tax", logString);
      });
    }
    return totTax;
  }
}
