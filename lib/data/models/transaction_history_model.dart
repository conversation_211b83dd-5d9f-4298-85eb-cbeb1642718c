// ignore_for_file: non_constant_identifier_names
import 'package:json_annotation/json_annotation.dart';
import 'package:mobile_crm/data/models/tax_model.dart';

import 'feedback_model.dart';
import 'payments_model.dart';

part 'transaction_history_model.g.dart';

@JsonSerializable()
class TransactionHistoryModel {
  TransactionHistoryModel(
      {this.dataHtml, this.feedback, this.payments, this.taxes});

  @JsonKey(name: 'data_html')
  String? dataHtml;
  FeedbackModel? feedback;
  List<PaymentsModel>? payments;
  List<TaxModel>? taxes;

  static TransactionHistoryModel fromJsonModel(Object? json) =>
      TransactionHistoryModel.fromJson(json as Map<String, dynamic>);

  static Map<String, dynamic> toJsonModel(
          TransactionHistoryModel transactionHistoryModel) =>
      transactionHistoryModel.toJsonn();

  factory TransactionHistoryModel.fromJson(Map<String, dynamic> json) =>
      _$TransactionHistoryModelFromJson(json);

  Map<String, dynamic> toJsonn() => _$TransactionHistoryModelToJson(this);
}
