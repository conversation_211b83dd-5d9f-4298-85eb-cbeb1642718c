// ignore_for_file: overridden_fields

import 'package:json_annotation/json_annotation.dart';
import 'package:mobile_crm/data/providers/db/database.dart';

part 'notification_model.g.dart';

@JsonSerializable()
class NotificationModel extends NotificationMeData {
  @override
  int? id;
  @override
  String? title;
  @override
  String? notificationType;
  @override
  String? body;
  @override
  bool? androidAllowWhileIdle;
  @override
  String? payload;
  @override
  int? scheduleTime;

  NotificationModel(
      {this.id = 0,
      this.title,
      this.payload,
      this.androidAllowWhileIdle,
      this.notificationType,
      this.body,
      this.scheduleTime})
      : super(
            id: id,
            body: body,
            payload: payload,
            notificationType: notificationType,
            scheduleTime: scheduleTime,
            title: title,
            androidAllowWhileIdle: androidAllowWhileIdle);

  static NotificationModel fromJsonModel(Object? json) =>
      NotificationModel.fromJson(json as Map<String, dynamic>);

  static Map<String, dynamic> toJsonModel(NotificationModel productModel) =>
      productModel.toJsonn();

  factory NotificationModel.fromJson(Map<String, dynamic> json) =>
      _$NotificationModelFromJson(json);

  Map<String, dynamic> toJsonn() {
    Map<String, dynamic> jsonMap = _$NotificationModelToJson(this);
    jsonMap.removeWhere((key, value) => value == null);
    return jsonMap;
  }
}

enum NotificationMeType {
  deals,
}
