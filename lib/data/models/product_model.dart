// ignore_for_file: non_constant_identifier_names, overridden_fields, annotate_overrides

import 'package:drift/drift.dart' as dr;
import 'package:json_annotation/json_annotation.dart';
import 'package:mobile_crm/data/models/available_model.dart';
import 'package:mobile_crm/data/models/tax_model.dart';
import 'package:mobile_crm/data/models/variant_model.dart';
import 'package:mobile_crm/data/providers/db/database.dart';

import 'product/subcategory_config_model.dart';

part 'product_model.g.dart';

@JsonSerializable()
class ProductModel extends Product {
  @JsonKey(name: 'admin_fkid')
  int? adminFkId;
  int? product_fkid;
  @Json<PERSON>ey(name: 'product_detail_id')
  int? product_detail_id;
  @JsonKey(name: 'product_detail_fkid')
  int? productDetailFkId;
  @JsonKey(name: 'product_id')
  int? productId;
  @JsonKey(name: 'product_subcategory_id')
  int? productSubcategoryId;
  @JsonKey(name: 'outlet_id')
  int? outletId;
  @JsonKey(name: 'variant_id')
  int? variantId;
  int? price;
  @JsonKey(name: 'price_sell')
  int? priceSell;
  @JsonKey(name: 'price_sell_avg')
  int? priceSellAvg;
  @JsonKey(name: 'price_sell_end')
  int? priceSellEnd;
  @JsonKey(name: 'price_sell_start')
  int? priceSellStart;
  @JsonKey(name: 'price_sell_promo')
  int? priceSellPromo;
  @JsonKey(name: 'price_sell_discount')
  int? priceSellDiscount;
  @JsonKey(name: 'price_promo')
  int? pricePromo;
  @JsonKey(name: 'total_available')
  int? totalAvailable;
  @JsonKey(name: 'min_qty_order')
  int? minQtyOrder;
  bool? is_in_wishlist;
  bool? isHeader;
  String? availability;
  String? barcode;
  String? description;
  @JsonKey(name: 'repeat_point')
  int? repeatPoint;
  @JsonKey(name: 'hour_end')
  String? hourEnd;
  @JsonKey(name: 'hour_start')
  String? hourStart;
  String? name;
  String? photo;
  int? point;
  String? subcategory;
  @JsonKey(name: 'subcategory_position')
  int? subcategoryPosition;
  String? unit;
  @JsonKey(name: 'unit_description')
  String? unitDescription;
  @JsonKey(name: 'stock_management')
  int? stockManagement;
  @JsonKey(name: 'stock_status')
  String? stockStatus;
  @JsonKey(name: 'stock_status_old')
  String? stockStatusOld;
  @JsonKey(name: 'stock_statuses_old')
  String? stockStatusesOld;
  @JsonKey(name: 'stock_statuses_old_2')
  String? stockStatusesOld2;
  @JsonKey(name: 'data_created')
  int? dataCreated;
  @JsonKey(name: 'data_modified')
  int? dataModified;
  @JsonKey(name: 'data_status')
  int? dataStatus;
  List<AvailableModel>? available;
  @JsonKey(name: 'subcategory_config')
  SubcategoryConfigModel? subcategoryConfig;
  List<VariantModel>? variant;
  @JsonKey(toJson: _listTaxToJson)
  List<TaxModel>? tax;
  @JsonKey(name: 'stock_qty')
  int? stockQty;

  ProductModel(
      {this.adminFkId,
      this.product_fkid,
      this.subcategoryConfig,
      this.product_detail_id,
      this.productDetailFkId,
      this.productId,
      this.productSubcategoryId,
      this.outletId,
      this.price,
      this.priceSell,
      this.pricePromo,
      this.totalAvailable,
      this.minQtyOrder,
      this.is_in_wishlist,
      this.isHeader,
      this.availability,
      this.subcategoryPosition,
      this.barcode,
      this.description,
      this.hourEnd,
      this.hourStart,
      this.name,
      this.photo,
      this.subcategory,
      this.unit,
      this.unitDescription,
      this.stockManagement,
      this.priceSellPromo,
      this.stockStatus,
      this.stockStatusOld,
      this.stockStatusesOld,
      this.stockStatusesOld2,
      this.dataCreated,
      this.dataModified,
      this.dataStatus,
      this.available,
      this.variant,
      this.repeatPoint,
      this.point,
      this.variantId,
      this.tax,
      this.priceSellStart,
      this.priceSellEnd,
      this.priceSellDiscount,
      this.priceSellAvg,
      this.stockQty});

  static List<Map<String, dynamic>> _listTaxToJson(List<TaxModel>? tM) =>
      (tM ?? []).map((e) => e.toJson()).toList();
  static ProductModel fromJsonModel(Object? json) =>
      ProductModel.fromJson(json as Map<String, dynamic>);

  static Map<String, dynamic> toJsonModel(ProductModel productModel) =>
      productModel.toJson();

  factory ProductModel.fromJson(Map<String, dynamic> json) =>
      _$ProductModelFromJson(json);

  @override
  Map<String, dynamic> toJson({dr.ValueSerializer? serializer}) =>
      _$ProductModelToJson(this);

  void updatePricePromo(int? newPrice) {
    priceSellPromo = newPrice;
  }

  void updatePriceDiscount(int? newPrice) => priceSellDiscount =
      newPrice == null ? null : (priceSell ?? 0) - (newPrice);
}
