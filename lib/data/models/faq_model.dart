import 'package:json_annotation/json_annotation.dart';
import 'package:mobile_crm/data/providers/db/database.dart';

part "faq_model.g.dart";

@JsonSerializable()
class FaqModel extends FaqData {
  @Json<PERSON>ey(name: "admin_fkid")
  int? adminFkid;
  String? answer;
  String? category;
  @JsonKey(name: "crm_faq_id")
  int? crmFaqId;
  @JsonKey(name: "data_created")
  int? dataCreated;
  @Json<PERSON>ey(name: "data_modified")
  int? dataModified;
  String? status;
  String? title;
  FaqModel(
      {this.adminFkid,
      this.answer,
      this.category,
      this.crmFaqId,
      this.dataCreated,
      this.dataModified,
      this.status,
      this.title})
      : super(
            admin_fkid: adminFkid,
            answer: answer,
            category: category,
            crm_faq_id: crmFaqId,
            data_created: dataCreated,
            data_modified: dataModified,
            status: status,
            title: title);

  factory FaqModel.fromJsonModel(Object? json) =>
      FaqModel.fromJson(json as Map<String, dynamic>);

  factory FaqModel.fromJson(Map<String, dynamic> json) =>
      _$FaqModelFromJson(json);

  Map<String, dynamic> toJsonn() => _$FaqModelToJson(this);
}
