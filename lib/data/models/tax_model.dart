import 'package:drift/drift.dart' as dr;
import 'package:json_annotation/json_annotation.dart';
import 'package:mobile_crm/data/models/product_model.dart';

import '../providers/db/database.dart';

part 'tax_model.g.dart';

@JsonSerializable()
class TaxModel extends TaxEntityData {
  @override
  int? id;
  @override
  @JsonKey(name: 'gratuity_id')
  int? gratuityId = 0;
  @override
  int? jumlah = 0;
  int? disTax = 0;
  int? currentTax = 0;
  int? outletFkId;
  @override
  String? name = '';
  // @override
  // List<ProductModel>? products;
  int? productDetailFkId;
  @override
  @JsonKey(name: 'tax_category')
  String? taxCategory;
  int? total = 0;
  @override
  @JsonKey(name: 'tax_status')
  String? statusTax;
  @override
  @JsonKey(name: 'tax_type')
  String? typeTax;
  int? totalTax = 0;
  @override
  @JsonKey(toJson: _listProductToJson)
  List<ProductModel>? product;
  @JsonKey(name: 'product_detail_fkid')
  TaxModel(
      {this.name,
      this.jumlah,
      this.id,
      this.disTax,
      this.currentTax,
      this.outletFkId,
      this.taxCategory,
      this.gratuityId,
      this.total,
      this.productDetailFkId,
      this.statusTax,
      this.typeTax,
      this.product,
      this.totalTax});

  factory TaxModel.fromJson(Map<String, dynamic> json) =>
      _$TaxModelFromJson(json);

  @override
  Map<String, dynamic> toJson({dr.ValueSerializer? serializer}) =>
      _$TaxModelToJson(this);

  static List<Map<String, dynamic>> _listProductToJson(
          List<ProductModel>? cm) =>
      (cm ?? []).map((e) => e.toJson()).toList();

  static TaxModel fromJsonModel(Object? json) =>
      TaxModel.fromJson(json as Map<String, dynamic>);

  int? updateTotalTax(int? value) => totalTax = value;
}
