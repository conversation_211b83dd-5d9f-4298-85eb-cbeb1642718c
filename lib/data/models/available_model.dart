import 'package:json_annotation/json_annotation.dart';

import '../providers/db/database.dart';

part 'available_model.g.dart';

@JsonSerializable()
class AvailableModel {
  String? name;
  @Json<PERSON>ey(name: 'outlet_id')
  int? outletId;
  @Json<PERSON>ey(name: 'price_sell')
  int? priceSell;
  @JsonKey(name: 'stock_status')
  String? stockStatus;
  @JsonKey(name: 'product_detail_id')
  int? productDetailId;
  @<PERSON>son<PERSON>ey(name: 'variant_id')
  int? variantId;
  @<PERSON><PERSON><PERSON>ey(name: 'enable_order')
  EnableOrder? enableOrder;
  @<PERSON><PERSON><PERSON><PERSON>(fromJson: ignore, toJson: ignore)
  Outlet? outlet;
  @JsonKey(name: 'stock_qty')
  int? stockQty;

  AvailableModel(
      {this.name,
      this.outletId,
      this.priceSell,
      this.stockStatus,
      this.productDetailId,
      this.variantId,
      this.outlet,
      this.enableOrder, 
      this.stockQty});

  factory AvailableModel.fromJson(Map<String, dynamic> json) =>
      _$AvailableModelFromJson(json);

  Map<String, dynamic> toJson() => _$AvailableModelToJson(this);

  factory AvailableModel.fromMap(Map<String, dynamic> json) =>
      _$AvailableModelFromJson(json);

  Map<String, dynamic> toMap() => {
        "name": name,
        "outlet_id": outletId,
        "price_sell": priceSell,
        "product_detail_id": productDetailId,
        "stock_status": stockStatus,
        "variant_id": variantId,
        "enable_order": enableOrder,
        "stock_qty": stockQty,
      };
}

class EnableOrder {
  String? status;

  EnableOrder({this.status});

  EnableOrder.fromJson(Map<String, dynamic> json) {
    status = json['status'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['status'] = status;
    return data;
  }

  factory EnableOrder.fromMap(Map<String, dynamic> json) =>
      EnableOrder(status: json["status"]);

  Map<String, dynamic> toMap() => {"status": status};
}

T? ignore<T>(dynamic _) => null;
