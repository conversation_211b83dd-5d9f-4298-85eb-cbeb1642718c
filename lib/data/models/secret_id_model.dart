import 'package:json_annotation/json_annotation.dart';

part 'secret_id_model.g.dart';

@JsonSerializable()
class SecretIdModel {
  @J<PERSON><PERSON>ey(name: 'secret_id')
  String? secretId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'secret_code')
  String? secretCode;
  int? expired;
  @Json<PERSON>ey(name: 'expired_value')
  int? expiredValue;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'expired_value_duration')
  String? expiredValueDuration;
  @J<PERSON><PERSON><PERSON>(name: 'time_millis')
  int? timeMillis;

  SecretIdModel(
      {this.expired,
      this.expiredValue,
      this.expiredValueDuration,
      this.secretId,
      this.secretCode,
      this.timeMillis});

  static SecretIdModel fromJsonModel(Object? json) =>
      SecretIdModel.fromJson(json as Map<String, dynamic>);

  static Map<String, dynamic> toJsonModel(SecretIdModel secretIdModel) =>
      secretIdModel.toJson();

  factory SecretIdModel.fromJson(Map<String, dynamic> json) =>
      _$SecretIdModelFromJson(json);

  Map<String, dynamic> toJson() => _$SecretIdModelToJson(this);
}
