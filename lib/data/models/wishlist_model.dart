// ignore_for_file: non_constant_identifier_names, overridden_fields, annotate_overrides

import 'package:json_annotation/json_annotation.dart';
import 'package:mobile_crm/data/models/product_model.dart';
import 'package:mobile_crm/data/providers/db/database.dart';

part 'wishlist_model.g.dart';

@JsonSerializable()
class WishlistModel extends WishlistData {
  @JsonKey(name: 'product_fkid')
  int? productFkId;
  @<PERSON><PERSON><PERSON>ey(name: 'crm_product_wishlist_id')
  int? crmProductWishlistId;
  @Json<PERSON>ey(name: 'admin_fkid')
  int? adminFkId;
  @Json<PERSON>ey(name: 'time_created')
  int? timeCreated;
  @J<PERSON><PERSON><PERSON>(name: 'wishlist_category_fkid')
  int? wishlistCategoryFkId;
  @Json<PERSON>ey(name: 'member_fkid')
  int? memberFkId;
  int? total;
  ProductModel? product;

  WishlistModel(
      {this.productFkId,
      this.total,
      this.product,
      this.adminFkId,
      this.crmProductWishlistId,
      this.wishlistCategoryFkId,
      this.memberFkId,
      this.timeCreated});

  static WishlistModel fromJsonModel(Object? json) =>
      WishlistModel.fromJson(json as Map<String, dynamic>);

  static Map<String, dynamic> toJsonModel(WishlistModel wishlistModel) =>
      wishlistModel.toJsonn();

  factory WishlistModel.fromJson(Map<String, dynamic> json) =>
      _$WishlistModelFromJson(json);

  Map<String, dynamic> toJsonn() {
    Map<String, dynamic> jsonMap = _$WishlistModelToJson(this);
    jsonMap.removeWhere((key, value) => value == null);
    return jsonMap;
  }
}

@JsonSerializable()
class WishlistCategoryModel extends WishlistCategoryData {
  String? name;
  @JsonKey(name: 'date_created')
  int? dataCreated;
  @JsonKey(name: 'data_modified')
  int? dataModified;
  bool? isChose;
  int? id;

  WishlistCategoryModel(
      {this.id,
      this.name,
      this.dataCreated,
      this.dataModified,
      this.isChose = false});

  static WishlistCategoryModel fromJsonModel(Object? json) =>
      WishlistCategoryModel.fromJson(json as Map<String, dynamic>);

  static Map<String, dynamic> toJsonModel(
          WishlistCategoryModel wishlistCategoryModel) =>
      wishlistCategoryModel.toJsonn();

  factory WishlistCategoryModel.fromJson(Map<String, dynamic> json) =>
      _$WishlistCategoryModelFromJson(json);

  Map<String, dynamic> toJsonn() {
    Map<String, dynamic> jsonMap = _$WishlistCategoryModelToJson(this);
    jsonMap.removeWhere((key, value) => value == null);
    return jsonMap;
  }
}
