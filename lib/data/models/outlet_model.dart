// ignore_for_file: non_constant_identifier_names, overridden_fields, annotate_overrides

import 'package:json_annotation/json_annotation.dart';
import 'package:mobile_crm/data/models/available_model.dart';
import 'package:mobile_crm/data/models/business_hour_model.dart';
import 'package:mobile_crm/data/models/order_model.dart';
import 'package:mobile_crm/data/models/outlet_feature_model.dart';
import 'package:mobile_crm/data/providers/db/database.dart';

part 'outlet_model.g.dart';

@JsonSerializable()
class OutletModel extends Outlet {
  @override
  int? outlet_id;
  @override
  int? distance_value;
  @override
  double? latitude;
  @override
  double? longitude;
  @override
  List<BusinessHourModel>? business_hour;
  @JsonKey(name: 'working_hour')
  List<BusinessHourModel>? workingHour;
  OrderTypeModel? order_type;
  @override
  String? outlet_logo;
  @override
  String? address;
  @override
  String? city;
  @override
  String? distance;
  @override
  String? name;
  @override
  String? phone;
  @override
  String? postal_code;
  @override
  String? province;
  OutletFeatureModel? feature;
  @override
  String? receipt_logo;
  EnableOrder? enable_order;
  int? promotion_id;

  OutletModel(
      {this.outlet_id,
      this.distance_value,
      this.latitude,
      this.longitude,
      this.business_hour,
      this.outlet_logo,
      this.address,
      this.city,
      this.distance,
      this.order_type,
      this.name,
      this.phone,
      this.postal_code,
      this.province,
      this.workingHour,
      this.receipt_logo,
      this.enable_order,
      this.promotion_id})
      : super(
            outlet_id: outlet_id,
            business_hour: business_hour,
            receipt_logo: receipt_logo,
            province: province,
            postal_code: postal_code,
            phone: phone,
            outlet_logo: outlet_logo,
            longitude: longitude,
            order_type: order_type,
            enable_order: enable_order,
            latitude: latitude,
            distance_value: distance_value,
            distance: distance,
            city: city,
            address: address,
            name: name,
            promotion_id: promotion_id);

  static OutletModel fromJsonModel(Object? json) =>
      OutletModel.fromJson(json as Map<String, dynamic>);

  static Map<String, dynamic> toJsonModel(OutletModel outletModel) =>
      outletModel.toJson();

  factory OutletModel.fromJson(Map<String, dynamic> json) =>
      _$OutletModelFromJson(json);

  Map<String, dynamic> toJsonn() => _$OutletModelToJson(this);
}
