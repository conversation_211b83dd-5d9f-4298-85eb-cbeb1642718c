class ProvinceModel {
  ProvinceModel({this.id, this.name, this.match});

  String? id;
  String? name;
  int? match;

  static ProvinceModel fromJsonModel(Object? json) =>
      ProvinceModel.fromJson(json as Map<String, dynamic>);

  static Map<String, dynamic> toJsonModel(ProvinceModel provinceModel) =>
      provinceModel.toJson();

  factory ProvinceModel.fromJson(Map<String, dynamic> json) =>
      ProvinceModel(id: json['id'] as String?, name: json['nama'] as String?);

  Map<String, dynamic> toJson() => <String, dynamic>{
        'id': id,
        'nama': name,
      };
}
