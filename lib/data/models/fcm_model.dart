import 'package:json_annotation/json_annotation.dart';

part 'fcm_model.g.dart';

@JsonSerializable()
class FcmModel {
  String? id;
  String? title;
  String? message;
  @JsonKey(name: 'image_url')
  String? imageUrl;
  String? type;
  FcmModel({this.id, this.title, this.message, this.imageUrl, this.type});

  factory FcmModel.fromJson(Map<String, dynamic> json) =>
      _$FcmModelFromJson(json);

  Map<String, dynamic> toJson() => _$FcmModelToJson(this);
}
