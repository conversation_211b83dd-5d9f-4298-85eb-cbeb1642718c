// ignore_for_file: overridden_fields, non_constant_identifier_names

import 'package:json_annotation/json_annotation.dart';
import 'package:mobile_crm/data/providers/db/database.dart';

part 'point_model.g.dart';

@JsonSerializable()
class PointModel extends PointData {
  @override
  String? id;
  @override
  int? point;
  @override
  String? type;
  @override
  String? source;
  @override
  String? source_desc;
  @override
  int? time_created;
  @override
  bool? isHeader;

  PointModel(
      {this.id,
      this.point,
      this.type,
      this.source,
      this.source_desc,
      this.isHeader,
      this.time_created})
      : super(
            id: id,
            type: type,
            point: point,
            source: source,
            source_desc: source_desc,
            time_created: time_created,
            isHeader: isHeader);

  factory PointModel.fromJson(Map<String, dynamic> json) =>
      _$PointModelFromJson(json);

  Map<String, dynamic> toJsonn() => _$PointModelToJson(this);

  static PointModel fromJsonModel(Object? json) =>
      PointModel.fromJson(json as Map<String, dynamic>);

  static Map<String, dynamic> toJsonModel(PointModel pointModel) =>
      pointModel.toJson();
}
