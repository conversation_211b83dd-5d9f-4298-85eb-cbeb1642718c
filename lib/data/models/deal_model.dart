// ignore_for_file: invalid_annotation_target, must_be_immutable, overridden_fields, constant_identifier_names

import 'package:drift/drift.dart' as dr;
import 'package:json_annotation/json_annotation.dart';
import 'package:mobile_crm/data/models/deal_group_detail_model.dart';
import 'package:mobile_crm/data/models/outlet_model.dart';
import 'package:mobile_crm/data/models/product_model.dart';
import 'package:mobile_crm/data/providers/db/database.dart';

import 'term_product_model.dart';
import 'time_active_model.dart';
import 'user_model.dart';

part 'deal_model.g.dart';

@JsonSerializable()
class DealModel extends DealData {
  @override
  int? active;
  @JsonKey(name: 'admin_fkid')
  int? adminFkId;
  @override
  @JsonKey(name: 'member_type_id')
  int? memberTypeId;
  @override
  @JsonKey(name: 'member_types')
  List<MemberTypes>? memberTypes;
  @override
  @JsonKey(name: 'promotion_id')
  int? promotionId;
  @override
  @JsonKey(name: 'promotion_type_name')
  String? promotionTypeName;
  @override
  @JsonKey(name: 'publish_date')
  int? publishDate;
  @JsonKey(name: 'promotion_type_id')
  int? promotionTypeId;
  @override
  @JsonKey(name: 'max_qty_promo')
  int? maxQtyPromo;
  @override
  @JsonKey(name: 'promotion_buy_id')
  int? promotionBuyId;
  @override
  @JsonKey(name: 'promotion_fkid')
  int? promotionFkId;
  @override
  @JsonKey(name: 'promotion_product')
  List<ProductModel>? promotionProduct;
  @override
  bool? isHeader;
  @override
  @JsonKey(name: 'deals_can_buy')
  int? dealsCanBuy;
  @override
  @JsonKey(name: 'min_order')
  int? minOrder;
  @override
  @JsonKey(name: 'start_promotion_date')
  int? startPromotionDate;
  @override
  @JsonKey(name: 'end_promotion_date')
  int? endPromotionDate;
  @override
  @JsonKey(name: 'start_promotion_time')
  String? startPromotionTime;
  @override
  @JsonKey(name: 'end_promotion_time')
  String? endPromotionTime;
  @override
  @JsonKey(name: 'ammount')
  String? amount;
  @override
  @JsonKey(name: 'deals_value')
  int? dealsValue;
  @override
  @JsonKey(name: 'discount_type')
  String? discountType;
  @override
  @JsonKey(name: 'promo_type')
  String? promoType;
  @override
  @JsonKey(name: 'input_deals')
  String? inputDeals;
  @override
  @JsonKey(name: 'is_percent')
  int? isPercent;
  @override
  @JsonKey(name: 'maximum_discount_nominal')
  int? maximumDiscountNominal;
  @override
  @JsonKey(name: 'maximum_redeem')
  int? maximumRedeem;
  @override
  @JsonKey(name: 'maximum_redeem_period')
  int? maximumRedeemPeriod;
  @override
  @JsonKey(name: 'maximum_redeem_period_days')
  int? maximumRedeemPeriodDays;
  @override
  @JsonKey(name: 'member_maximum_redeem')
  int? memberMaximumRedeem;
  @override
  String? notify;
  @override
  String? name;
  @override
  String? option;
  @override
  List<OutletModel>? outlet;
  @override
  String? photo;
  @override
  int? point;
  @override
  List<ProductModel>? products;
  @override
  @JsonKey(name: 'redeem_qr_code')
  String? redeemQrCode;
  @override
  @JsonKey(name: 'redeem_qr_code_url')
  String? redeemQrCodeUrl;
  @override
  String? source;
  @override
  @JsonKey(name: 'promo_discount_maximum')
  int? promoDiscountMaximum;
  @override
  @JsonKey(name: 'promo_discount_type')
  String? promoDiscountType;
  @override
  @JsonKey(name: 'promo_nominal')
  int? promoNominal;
  @override
  @JsonKey(name: 'group_detail')
  List<DealGroupDetailModel>? groupDetail;
  @override
  @JsonKey(name: 'group_total')
  int? groupTotal;
  String? prove;
  @override
  @JsonKey(name: 'start_date_db')
  String? startDateDb;
  @override
  String? term;
  @override
  @JsonKey(name: 'term_product')
  TermProductModel? termProduct;
  @override
  @JsonKey(name: 'total_reedem')
  int? totalRedeem;
  @override
  @JsonKey(name: 'voucher_code')
  String? voucherCode;
  @override
  @JsonKey(name: 'voucher_price_type')
  String? voucherPriceType;
  @override
  @JsonKey(name: 'price_type')
  String? priceType;
  @override
  @JsonKey(name: 'voucher_price_value')
  String? voucherPriceValue;
  @override
  int? price;
  @override
  @JsonKey(name: 'time_active')
  TimeActiveModel? timeActive;
  @override
  int? created;
  @override
  int? modified;
  @override
  @JsonKey(name: 'time_created')
  int? timeCreated;
  @override
  @JsonKey(name: 'time_modified')
  int? timeModified;
  @override
  int? sunday;
  @override
  int? monday;
  @override
  int? tuesday;
  @override
  int? wednesday;
  @override
  int? thursday;
  @override
  int? friday;
  @override
  int? saturday;

  DealModel(
      {this.promotionBuyId,
      this.maxQtyPromo,
      this.promotionTypeId,
      this.publishDate,
      this.promotionTypeName,
      this.promotionId,
      this.memberTypes,
      this.adminFkId,
      this.active,
      this.name,
      this.timeCreated,
      this.timeActive,
      this.totalRedeem,
      this.isHeader,
      this.groupDetail,
      this.outlet,
      this.products,
      this.termProduct,
      this.endPromotionDate,
      this.notify,
      this.term,
      this.maximumRedeem,
      this.startPromotionDate,
      this.memberTypeId,
      this.promotionProduct,
      this.maximumDiscountNominal,
      this.minOrder,
      this.endPromotionTime,
      this.startPromotionTime,
      this.discountType,
      this.amount,
      this.promoDiscountMaximum,
      this.dealsCanBuy,
      this.voucherPriceType,
      this.promoType,
      this.promotionFkId,
      this.dealsValue,
      this.promoNominal,
      this.promoDiscountType,
      this.photo,
      this.created,
      this.groupTotal,
      this.friday,
      this.inputDeals,
      this.isPercent,
      this.maximumRedeemPeriod,
      this.maximumRedeemPeriodDays,
      this.memberMaximumRedeem,
      this.modified,
      this.monday,
      this.option,
      this.point,
      this.prove,
      this.redeemQrCode,
      this.redeemQrCodeUrl,
      this.saturday,
      this.source,
      this.startDateDb,
      this.sunday,
      this.thursday,
      this.timeModified,
      this.tuesday,
      this.voucherCode,
      this.voucherPriceValue,
      this.wednesday});

  static DealModel fromJsonModel(Object? json) =>
      DealModel.fromJson(json as Map<String, dynamic>);

  static Map<String, dynamic> toJsonModel(DealModel dealModel) =>
      dealModel.toJson();

  factory DealModel.fromJson(Map<String, dynamic> json) =>
      _$DealModelFromJson(json);

  @override
  Map<String, dynamic> toJson({dr.ValueSerializer? serializer}) => _$DealModelToJson(this);
}

enum DealPromoType { discount, special_price }

enum DealDiscountType { nominal, percent }
