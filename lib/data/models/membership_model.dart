// ignore_for_file: overridden_fields

import 'package:json_annotation/json_annotation.dart';
import 'package:mobile_crm/data/providers/db/database.dart';

part 'membership_model.g.dart';

@JsonSerializable()
class MembershipModel extends MembershipData {
  @Json<PERSON>ey(name: 'lifetime_day')
  int? lifetimeDay;
  @JsonKey(name: 'point_target')
  int? pointTarget;
  @Json<PERSON>ey(name: 'spent_target')
  int? spentTarget;
  @override
  int? price;
  @JsonKey(name: 'lifetime_type')
  String? lifetimeType;
  @override
  String? name;

  MembershipModel(
      {this.name,
      this.price,
      this.lifetimeDay,
      this.lifetimeType,
      this.pointTarget,
      this.spentTarget})
      : super(
            name: name,
            lifetime_day: lifetimeDay,
            lifetime_type: lifetimeType,
            point_target: pointTarget,
            price: price,
            spent_target: spentTarget);

  static MembershipModel fromJsonModel(Object? json) =>
      MembershipModel.fromJson(json as Map<String, dynamic>);

  static Map<String, dynamic> toJsonModel(MembershipModel membershipModel) =>
      membershipModel.toJson();

  factory MembershipModel.fromJson(Map<String, dynamic> json) =>
      _$MembershipModelFromJson(json);

  Map<String, dynamic> toJsonn() => _$MembershipModelToJson(this);
}
