// ignore_for_file: non_constant_identifier_names

import 'package:json_annotation/json_annotation.dart';

part 'business_hour_model.g.dart';

@JsonSerializable()
class BusinessHourModel {
  String? day;
  int? outlet_fkid;
  String? time_close;
  String? time_open;

  BusinessHourModel(
      {this.day, this.outlet_fkid, this.time_close, this.time_open});

  factory BusinessHourModel.fromJson(Map<String, dynamic> json) =>
      _$BusinessHourModelFromJson(json);

  Map<String, dynamic> toJson() => _$BusinessHourModelToJson(this);
}
