import 'package:json_annotation/json_annotation.dart';

part 'shipment_model.g.dart';

@JsonSerializable()
class ShipmentModel {
  ShipmentModel({
    this.memberAddressId,
    this.timeModified,
    this.timeCreated,
    this.orderSalesID,
    this.shippingAddress,
    this.shippingCharge,
    this.shippingCourier,
    this.shippingId,
    this.shippingReceipt,
    this.deliveryTime,
  });

  @JsonKey(name: 'member_address_id')
  int? memberAddressId;
  @<PERSON>son<PERSON>ey(name: 'order_sales_id')
  String? orderSalesID;
  @J<PERSON><PERSON>ey(name: 'shipping_address')
  String? shippingAddress;
  @J<PERSON><PERSON><PERSON>(name: 'shipping_charge')
  int? shippingCharge;
  @Json<PERSON>ey(name: 'shipping_id')
  String? shippingId;
  @JsonKey(name: 'shipping_courier')
  String? shippingCourier;
  @JsonKey(name: 'shipping_receipt')
  String? shippingReceipt;
  @JsonKey(name: 'time_created')
  int? timeCreated;
  @Json<PERSON>ey(name: 'time_modified')
  int? timeModified;
  @JsonKey(name: 'delivery_time')
  int? deliveryTime;

  static ShipmentModel fromJsonModel(Object? json) =>
      ShipmentModel.fromJson(json as Map<String, dynamic>);

  static Map<String, dynamic> toJsonModel(ShipmentModel shipmentModel) =>
      shipmentModel.toJson();

  factory ShipmentModel.fromJson(Map<String, dynamic> json) =>
      _$ShipmentModelFromJson(json);

  Map<String, dynamic> toJson() => _$ShipmentModelToJson(this);
}
