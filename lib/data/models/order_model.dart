import 'package:json_annotation/json_annotation.dart';

part 'order_model.g.dart';

@JsonSerializable()
class OrderModel {
  OrderModel({this.expired, this.orderCode, this.qrCode});

  final int? expired;
  @JsonKey(name: 'order_code')
  final int? orderCode;
  @J<PERSON><PERSON>ey(name: 'qrcode')
  final String? qrCode;

  factory OrderModel.fromJson(Map<String, dynamic> json) =>
      _$OrderModelFromJson(json);

  Map<String, dynamic> toJson() => _$OrderModelToJson(this);

  static OrderModel fromJsonModel(Object? json) =>
      OrderModel.fromJson(json as Map<String, dynamic>);

  static Map<String, dynamic> toJsonModel(OrderModel orderModel) =>
      orderModel.toJson();
}

@JsonSerializable()
class OrderTypeModel {
  OrderTypeModel(
      {this.delivery,
      this.pickup,
      this.selfOrder,
      this.dinein,
      this.internalDelivery});

  final String? delivery;
  final String? pickup;
  final String? dinein;
  @JsonKey(name: 'self_order')
  final String? selfOrder;
  @JsonKey(name: 'internal_delivery')
  final String? internalDelivery;

  factory OrderTypeModel.fromJson(Map<String, dynamic> json) =>
      _$OrderTypeModelFromJson(json);

  Map<String, dynamic> toJson() => _$OrderTypeModelToJson(this);
}
