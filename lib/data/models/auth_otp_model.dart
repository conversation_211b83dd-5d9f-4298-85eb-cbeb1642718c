import 'package:json_annotation/json_annotation.dart';

part 'auth_otp_model.g.dart';

@JsonSerializable()
class AuthOtpModel {
  AuthOtpModel({
    this.type,
    this.token,
    this.expired,
  });

  String? type;
  String? token;
  int? expired;

  static AuthOtpModel fromJsonModel(Object? json) =>
      AuthOtpModel.fromJson(json as Map<String, dynamic>);

  static Map<String, dynamic> toJsonModel(AuthOtpModel authTokenModel) =>
      authTokenModel.toJson();

  factory AuthOtpModel.fromJson(Map<String, dynamic> json) =>
      _$AuthOtpModelFromJson(json);

  Map<String, dynamic> toJson() => _$AuthOtpModelToJson(this);
}
