import 'package:json_annotation/json_annotation.dart';

part 'server_response.g.dart';

@JsonSerializable(explicitToJson: true, genericArgumentFactories: true)
class ServerResponse<T> {
  int code;
  bool status;
  String message;
  T? data;

  ServerResponse(
      {this.code = 0, this.status = false, this.message = '', this.data});

  factory ServerResponse.fromJson(
          Map<String, dynamic>? json, T Function(Object? json) fromJsonT) =>
      json == null
          ? ServerResponse<T>()
          : _$ServerResponseFromJson<T>(json, fromJsonT);

  Map<String, dynamic> toJson(Object Function(T) toJsonT) =>
      _$ServerResponseToJson<T>(this, toJsonT);
}

@JsonSerializable(explicitToJson: true, genericArgumentFactories: true)
class ServerResponseList<T> {
  int code;
  bool status;
  String message;
  List<T>? data;

  ServerResponseList(
      {this.code = 0, this.status = false, this.message = '', this.data});

  factory ServerResponseList.fromJson(
          Map<String, dynamic>? json, T Function(Object? json) fromJsonT) =>
      json == null
          ? ServerResponseList<T>()
          : _$ServerResponseListFromJson<T>(json, fromJsonT);

  Map<String, dynamic> toJson(Object Function(T) toJsonT) =>
      _$ServerResponseListToJson<T>(this, toJsonT);
}
