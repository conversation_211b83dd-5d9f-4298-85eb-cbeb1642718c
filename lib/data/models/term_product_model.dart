import 'package:json_annotation/json_annotation.dart';
import 'package:mobile_crm/data/models/product_model.dart';

part 'term_product_model.g.dart';

@JsonSerializable()
class TermProductModel {
  List<ProductModel>? products;
  int? qty;

  TermProductModel({this.products, this.qty});

  static TermProductModel fromJsonModel(Object? json) =>
      TermProductModel.fromJson(json as Map<String, dynamic>);

  static Map<String, dynamic> toJsonModel(TermProductModel termProductModel) =>
      termProductModel.toJson();

  factory TermProductModel.fromJson(Map<String, dynamic> json) =>
      _$TermProductModelFromJson(json);

  Map<String, dynamic> toJson() => _$TermProductModelToJson(this);
}
