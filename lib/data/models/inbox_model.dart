// ignore_for_file: overridden_fields, non_constant_identifier_names

import 'package:json_annotation/json_annotation.dart';
import 'package:mobile_crm/data/providers/db/database.dart';

part 'inbox_model.g.dart';

@JsonSerializable()
class InboxModel extends InboxData {
  @override
  int? data_created;
  @override
  int? is_read;
  @override
  String? message;
  @override
  NotificationData? notification_data;
  @override
  int? notification_id;
  @override
  String? notification_type;
  @override
  String? title;

  InboxModel(
      {this.data_created,
      this.is_read,
      this.message,
      this.notification_data,
      this.notification_id,
      this.notification_type,
      this.title})
      : super(
            message: message,
            title: title,
            is_read: is_read,
            data_created: data_created,
            notification_data: notification_data,
            notification_id: notification_id,
            notification_type: notification_type);

  factory InboxModel.fromJson(Map<String, dynamic> json) =>
      _$InboxModelFromJson(json);

  Map<String, dynamic> toJsonn() => _$InboxModelToJson(this);

  static InboxModel fromJsonModel(Object? json) =>
      InboxModel.fromJson(json as Map<String, dynamic>);

  static Map<String, dynamic> toJsonModel(InboxModel productModel) =>
      productModel.toJson();
}

@JsonSerializable()
class NotificationData {
  String? sales_id;
  dynamic? id;

  NotificationData({this.sales_id, this.id});

  factory NotificationData.fromJson(Map<String, dynamic> json) =>
      _$NotificationDataFromJson(json);

  Map<String, dynamic> toJson() => _$NotificationDataToJson(this);
}
