import 'package:flutter/material.dart';

class Responsive extends StatelessWidget {
  final Widget mobile;
  final Widget tablet;
  final Widget desktop;
  final Widget desktop4K;

  const Responsive({
    Key? key,
    required this.mobile,
    required this.tablet,
    required this.desktop,
    required this.desktop4K,
  }) : super(key: key);

  static bool isMobileSmall(BuildContext context) =>
      MediaQuery.of(context).size.width <= 320;

  static bool isMobileMedium(BuildContext context) =>
      MediaQuery.of(context).size.width < 425 &&
      MediaQuery.of(context).size.width >= 375;

  static bool isMobileLarge(BuildContext context) =>
      MediaQuery.of(context).size.width < 768 &&
      MediaQuery.of(context).size.width >= 425;

  static bool isMobile(BuildContext context) =>
      MediaQuery.of(context).size.width < 768;

  static bool isTablet(BuildContext context) =>
      MediaQuery.of(context).size.width < 1024 &&
      MediaQuery.of(context).size.width >= 768;

  static bool isDesktop(BuildContext context) =>
      MediaQuery.of(context).size.width < 2560 &&
      MediaQuery.of(context).size.width >= 1024;

  static bool isDesktop4K(BuildContext context) =>
      MediaQuery.of(context).size.width >= 2560;

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        if (constraints.maxWidth >= 2560) {
          return desktop4K;
        } else if (constraints.maxWidth >= 1024) {
          return desktop;
        } else if (constraints.maxWidth >= 768) {
          return tablet;
        } else {
          return mobile;
        }
      },
    );
  }
}
