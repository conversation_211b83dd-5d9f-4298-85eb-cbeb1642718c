// ignore_for_file: non_constant_identifier_names, constant_identifier_names

part of 'app_pages.dart';
// DO NOT EDIT. This is code generated via package:get_cli/get_cli.dart

abstract class Routes {
  Routes._();

  static const HOME = _Paths.HOME;
  static const LOGIN = _Paths.LOGIN;
  static const REGISTER = _Paths.REGISTER;
  static const LOSTPHONENUMBER = _Paths.LOSTPHONENUMBER;
  static const OTP = _Paths.OTP;
  static const NOTIFICATION = _Paths.NOTIFICATION;
  static const _NOTIFICATIONDETAIL = _Paths.NOTIFICATIONDETAIL;
  static const _NOTIFICATIONSALES = _Paths.NOTIFICATIONSALES;
  static const PROFILE = _Paths.PROFILE;
  static const CART = _Paths.CART;
  static const _CARTDETAIL = _Paths.CARTDETAIL;
  static const VOUCHER = _Paths.VOUCHER;
  static const DEALS = _Paths.DEALS;
  static const _DEALDETAIL = _Paths.DEALDETAIL;
  static const _VOUCHERDETAIL = _Paths.VOUCHERDETAIL;
  static const _OUTLET = _Paths.OUTLET;
  static const OUTLETLIST = _Paths.OUTLETLIST;
  static const _TRANSACTION = _Paths.TRANSACTION;
  static const ORDER = _Paths.ORDER;
  static const POINT = _Paths.POINT;
  static const POINTHISTORY = _Paths.POINTHISTORY;
  static const ADDRESS = _Paths.ADDRESS;
  static const MAPVIEW = _Paths.MAPVIEW;
  static const ADDRESSEDIT = _Paths.ADDRESSEDIT;
  static const WISHLIST = _Paths.WISHLIST;
  static const _WISHLISTDETAIL = _Paths.WISHLISTDETAIL;
  static const MYQRCODE = _Paths.MYQRCODE;

  static const TERMS = _Paths.TERMS;
  static const PRIVACY = _Paths.PRIVACY;
  static const FAQ = _Paths.FAQ;

  static DEALDETAIL(String id) => _DEALDETAIL.replaceFirst(':id', id);

  static CARTDETAIL(String id) => _CARTDETAIL.replaceFirst(':id', id);

  static WISHLISTDETAIL(String id) => _WISHLISTDETAIL.replaceFirst(':id', id);

  static NOTIFICATIONDETAIL(String id) =>
      _NOTIFICATIONDETAIL.replaceFirst(':id', id);
  static TRANSACTION(String id) => _TRANSACTION.replaceFirst(':id', id);

  static NOTIFICATIONSALES(String id) =>
      _NOTIFICATIONSALES.replaceFirst(':id', id);

  static VOUCHERDETAIL(String id) => _VOUCHERDETAIL.replaceFirst(':id', id);

  static OUTLET(String id) => _OUTLET.replaceFirst(':id', id);
  static const SEARCH = _Paths.SEARCH;
}

abstract class _Paths {
  _Paths._();

  static const HOME = '/';
  static const LOGIN = '/login';
  static const REGISTER = '/register';
  static const LOSTPHONENUMBER = '/lostphonenumber';
  static const OTP = '/otp';
  static const NOTIFICATION = '/notification';
  static const NOTIFICATIONDETAIL = '/inbox/:id';
  static const NOTIFICATIONSALES = '/inbox/sales/:id';
  static const PROFILE = '/profile';
  static const MYQRCODE = '/myqr';
  static const VOUCHER = '/voucher';
  static const DEALS = '/deals';
  static const DEALDETAIL = '/deal/:id';
  static const VOUCHERDETAIL = '/voucher/:id';
  static const OUTLET = '/outlet/:id';
  static const OUTLETLIST = '/listoulet';
  static const SEARCH = '/search';
  static const CART = '/cart';
  static const CARTDETAIL = '/cartdetail/:id';
  static const ORDER = '/order';
  static const TRANSACTION = '/transaction/:id';
  static const POINT = '/point';
  static const POINTHISTORY = '/point/history';
  static const ADDRESS = '/address';
  static const ADDRESSEDIT = '/address/update';
  static const MAPVIEW = '/address/map';
  static const WISHLIST = '/wishlist';
  static const WISHLISTDETAIL = '/wishlist/:id';

  static const TERMS = "/terms";
  static const PRIVACY = "/privacy";
  static const FAQ = "/faq";
}
