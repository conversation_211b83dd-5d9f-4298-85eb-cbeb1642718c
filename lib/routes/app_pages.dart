// ignore_for_file: constant_identifier_names

import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/address/address_export.dart';
import 'package:mobile_crm/app/modules/address/views/map_view.dart';
import 'package:mobile_crm/app/modules/cart/cart_export.dart';
import 'package:mobile_crm/app/modules/deal_detail/deal_detail_export.dart';
import 'package:mobile_crm/app/modules/home/<USER>';
import 'package:mobile_crm/app/modules/inbox/inbox_export.dart';
import 'package:mobile_crm/app/modules/login/login_export.dart';
import 'package:mobile_crm/app/modules/my_qrcode/export.dart';
import 'package:mobile_crm/app/modules/order/order_export.dart';
import 'package:mobile_crm/app/modules/outlet/outlet_export.dart';
import 'package:mobile_crm/app/modules/point/point_export.dart';
import 'package:mobile_crm/app/modules/profile/profile_export.dart';
import 'package:mobile_crm/app/modules/terms_privacy/bindings/terms_privacy_binding.dart';
import 'package:mobile_crm/app/modules/terms_privacy/views/faq_page.dart';
import 'package:mobile_crm/app/modules/voucher/voucher_export.dart';
import 'package:mobile_crm/app/modules/wishlist/export.dart';
import 'package:mobile_crm/data/services/local_storage.dart';

import '../app/modules/terms_privacy/views/privacy.dart';
import '../app/modules/terms_privacy/views/terms.dart';
import '../middleware/auth_middleware.dart';

part 'app_routes.dart';

class AppPages {
  AppPages._();

  static final store = LocalStorageService();
  static String initialRoute = Routes.HOME;

  static final routes = [
    GetPage(
        name: _Paths.HOME,
        page: () => const HomeScreen(),
        binding: HomeBinding()),
    GetPage(
        name: _Paths.LOGIN,
        page: () => const LoginScreen(),
        bindings: [LoginBinding(), LostPhoneNumberBinding()],
        transition: Transition.downToUp),
    GetPage(
        name: Routes.REGISTER,
        page: () => const RegisterScreen(),
        binding: RegisterBinding()),
    GetPage(
      name: Routes.LOSTPHONENUMBER,
      page: () => const LostPhoneNumberScreen(),
      binding: LostPhoneNumberBinding(),
    ),
    GetPage(
        name: Routes.OTP, page: () => const OtpScreen(), binding: OtpBinding()),
    GetPage(
        name: Routes.PROFILE,
        page: () => const ProfileScreen(),
        binding: ProfileBinding(),
        middlewares: [AuthMiddleware()]),
    GetPage(
        name: Routes.NOTIFICATION,
        page: () => const InboxScreen(),
        binding: NotificationBinding(),
        middlewares: [AuthMiddleware()]),
    GetPage(
        name: Routes._NOTIFICATIONDETAIL,
        page: () => const InboxDetailScreen(),
        binding: InboxDetailBinding(),
        middlewares: [AuthMiddleware()]),
    GetPage(
        name: Routes._NOTIFICATIONSALES,
        page: () => const InboxSalesDetailScreen(),
        binding: InboxSalesDetailBinding(),
        middlewares: [AuthMiddleware()]),
    GetPage(
        name: Routes._OUTLET,
        page: () => const OutletScreen(),
        binding: OutletBinding()),
    GetPage(
        name: Routes.VOUCHER,
        page: () => const VoucherScreen(),
        binding: VoucherBinding(),
        middlewares: [AuthMiddleware()]),
    GetPage(
        name: Routes._VOUCHERDETAIL,
        page: () => const VoucherDetailScreen(),
        binding: VoucherDetailBinding(),
        middlewares: [AuthMiddleware()]),
    GetPage(
        name: Routes._DEALDETAIL,
        page: () => const DealDetailScreen(),
        binding: DealDetailBinding()),
    GetPage(
      name: Routes.CART,
      page: () => const CartScreen(),
      binding: CartBinding(),
    ),
    GetPage(
      name: Routes._CARTDETAIL,
      page: () => const CartDetailScreen(),
      binding: CartDetailBinding(),
    ),
    GetPage(
      name: Routes._TRANSACTION,
      page: () => const OrderScreen(),
      binding: OrderBinding(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
        name: Routes.ORDER,
        page: () => const OrderScreen(),
        binding: OrderBinding()),
    GetPage(
      name: Routes.POINT,
      page: () => const PointScreen(),
      binding: PointBinding(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: Routes.POINTHISTORY,
      page: () => const PointHistoryScreen(),
      binding: PointHistoryBinding(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: Routes.ADDRESS,
      page: () => const AddressScreen(),
      binding: AddressBinding(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: Routes.ADDRESSEDIT,
      page: () => const EditAddressScreen(),
      binding: EditAddressBinding(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: Routes.MAPVIEW,
      page: () => const MapScreen(),
      binding: EditAddressBinding(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: Routes.WISHLIST,
      page: () => WishlistPage(),
      binding: WishlistBinding(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: Routes._WISHLISTDETAIL,
      page: () => WishlistDetailPage(),
      binding: WishlistDetailBinding(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: Routes.MYQRCODE,
      page: () => const MyQRPage(),
      binding: MyQRBinding(),
      fullscreenDialog: true,
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: Routes.TERMS,
      page: () => const TermsPage(),
      binding: TermsPrivacyBinding(),
    ),
    GetPage(
      name: Routes.PRIVACY,
      page: () => const PrivacyPage(),
      binding: TermsPrivacyBinding(),
    ),
    GetPage(
      name: Routes.FAQ,
      page: () => const FaqPage(),
      binding: TermsPrivacyBinding(),
    ),
  ];
}
