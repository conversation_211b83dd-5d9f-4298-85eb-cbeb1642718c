import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../core/theme/app_dimen.dart';
import '../modules/screen_wrapper/controller/wrapper_controller.dart';

class ProgressBarAnimation extends StatefulWidget {
  final double currentValue;
  final double maxValue;
  final double? width;
  final Duration duration;
  const ProgressBarAnimation(
      {super.key,
      required this.currentValue,
      required this.maxValue,
      this.width,
      this.duration = const Duration(seconds: 2)});

  @override
  State<StatefulWidget> createState() => _ProgressBarState();
}

class _ProgressBarState extends State<ProgressBarAnimation>
    with SingleTickerProviderStateMixin {
  late WrapperController wrapperController;
  late Animation<double> animation;
  late AnimationController controller;

  @override
  void initState() {
    super.initState();
    wrapperController = Get.find<WrapperController>();
    controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );

    animation = Tween<double>(
            begin: 0, end: (widget.currentValue / widget.maxValue).toDouble())
        .animate(
            CurvedAnimation(parent: controller, curve: Curves.easeOutQuint));

    controller.forward();
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: animation,
      builder: (context, child) {
        var progress = animation.value;
        return Container(
          width: (((progress *
                      (widget.width ?? MediaQuery.of(context).size.width))
                  .isNaN)
              ? AppDimen.h24
              : (progress * (widget.width ?? MediaQuery.of(context).size.width))
                  .clamp(
                      0, (widget.width ?? MediaQuery.of(context).size.width))),
          height: AppDimen.h6,
          decoration: BoxDecoration(
              color: wrapperController.getSecondaryColor(),
              borderRadius: BorderRadius.all(Radius.circular(AppDimen.h10))),
        );
      },
    );
  }
}
