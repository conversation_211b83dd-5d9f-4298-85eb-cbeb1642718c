import 'package:get/get.dart';
import 'package:mobile_crm/core/values/app_strings.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

import '../enum/firebase_auth_result_enum.dart';

class FirebaseAuthExceptionHandler {
  static handleException(e) {
    FirebaseAuthResultStatus status;
    switch (e.code) {
      case "invalid-email":
      case "ERROR_INVALID_EMAIL":
        status = FirebaseAuthResultStatus.invalidEmail;
        break;
      case "wrong-password":
      case "ERROR_WRONG_PASSWORD":
        status = FirebaseAuthResultStatus.wrongPassword;
        break;
      case "user-not-found":
      case "ERROR_USER_NOT_FOUND":
        status = FirebaseAuthResultStatus.userNotFound;
        break;
      case "error-user-disabled":
      case "user-disabled":
      case "ERROR_USER_DISABLED":
        status = FirebaseAuthResultStatus.userDisabled;
        break;
      case "too-many-requests":
      case "ERROR_TOO_MANY_REQUESTS":
        status = FirebaseAuthResultStatus.tooManyRequests;
        break;
      case "operation-not-allowed":
      case "ERROR_OPERATION_NOT_ALLOWED":
        status = FirebaseAuthResultStatus.operationNotAllowed;
        break;
      case "ERROR_EMAIL_ALREADY_IN_USE":
      case "account-exists-with-different-credential":
      case "email-already-in-use":
        status = FirebaseAuthResultStatus.emailAlreadyExists;
        break;
      case "invalid-cert-hash":
        status = FirebaseAuthResultStatus.invalidCertHash;
        break;
      case "invalid-phone-number":
        status = FirebaseAuthResultStatus.invalidPhoneNumber;
        break;
      case "invalid-action-code":
        status = FirebaseAuthResultStatus.invalidActionCode;
        break;
      case "app-not-authorized":
        status = FirebaseAuthResultStatus.appNotAuthorized;
        break;
      case "invalid-verification-id":
        status = FirebaseAuthResultStatus.invalidVerificationId;
        break;
      case "invalid-verification-code":
        status = FirebaseAuthResultStatus.invalidVerificationCode;
        break;
      case "session-expired":
        status = FirebaseAuthResultStatus.sessionExpired;
        break;
      case "requires-recent-login":
        status = FirebaseAuthResultStatus.requiresRecentLogin;
        break;
      case "network-request-failed":
        status = FirebaseAuthResultStatus
            .timeoutInterruptedConnectionUnreachableHost;
        break;
      case "user-token-expired":
        status = FirebaseAuthResultStatus.userTokenExpired;
        break;
      default:
        status = FirebaseAuthResultStatus.undefined;
        Sentry.captureMessage("Unknown Error [$e]", level: SentryLevel.error);
    }
    return generateExceptionMessage(status, e);
  }

  ///
  /// Accepts AuthExceptionHandler.errorType
  ///
  static generateExceptionMessage(FirebaseAuthResultStatus exceptionCode, e) {
    String errorMessage;
    switch (exceptionCode) {
      case FirebaseAuthResultStatus.invalidEmail:
        errorMessage = Strings.yourEmailAddressIncorrect.tr;
        break;
      case FirebaseAuthResultStatus.wrongPassword:
        errorMessage = Strings.thePasswordIncorrect.tr;
        break;
      case FirebaseAuthResultStatus.userNotFound:
        errorMessage = Strings.theUserDoesNotExist.tr;
        break;
      case FirebaseAuthResultStatus.userDisabled:
        errorMessage = Strings.theUserHasBeenDisabled.tr;
        break;
      case FirebaseAuthResultStatus.tooManyRequests:
        errorMessage = Strings.thereHaveBeenTooManyAttempts.tr;
        break;
      case FirebaseAuthResultStatus.operationNotAllowed:
        errorMessage = Strings.theOptionToSignInNotAvailable.tr;
        break;
      case FirebaseAuthResultStatus.emailAlreadyExists:
        errorMessage = Strings.thisEmailAlreadyBeenRegistered.tr;
        break;
      case FirebaseAuthResultStatus.invalidCertHash:
        errorMessage = Strings.invalidCertHash.tr;
        break;
      case FirebaseAuthResultStatus.invalidPhoneNumber:
        if (e.message.toString().contains("TOO_LONG")) {
          errorMessage = Strings.thePhoneNumberInvalidFormatTooLong.tr;
        } else {
          errorMessage = Strings.thePhoneNumberInvalidFormat.tr;
        }
        break;
      case FirebaseAuthResultStatus.invalidActionCode:
        errorMessage = Strings.theLinkIsInvalidExpiredOrAlreadyUsed.tr;
        break;
      case FirebaseAuthResultStatus.appNotAuthorized:
        errorMessage = Strings.theVersionAppNotAuthorized.tr;
        break;
      case FirebaseAuthResultStatus.invalidVerificationId:
        errorMessage = Strings.weAreUnableToPerformRequest.tr;
        break;
      case FirebaseAuthResultStatus.invalidVerificationCode:
        errorMessage = Strings.theOTPCodeIsInvalid.tr;
        break;
      case FirebaseAuthResultStatus.sessionExpired:
        errorMessage = Strings.theSmsCodeYouEnteredHasExpired.tr;
        break;
      case FirebaseAuthResultStatus.requiresRecentLogin:
        errorMessage = Strings.pleaseLogInAgain.tr;
        break;
      case FirebaseAuthResultStatus.timeoutInterruptedConnectionUnreachableHost:
        errorMessage = Strings.networkErrorOccurred.tr;
        break;
      case FirebaseAuthResultStatus.userTokenExpired:
        errorMessage = Strings.yourSessionHasExpired.tr;
        break;
      default:
        errorMessage = "Unknown error, please try again later";
    }

    return errorMessage;
  }
}
