class NetworkException implements Exception {
  String? code;
  String? message;
  String? details;

  NetworkException({this.code, this.message, this.details});

  @override
  String toString() {
    return "$message \n $details";
  }
}

class FetchDataException extends NetworkException {
  FetchDataException(String? details)
      : super(
          code: "fetch-data",
          message: "Error During Communication",
          details: details,
        );
}

class OkRequest extends NetworkException {
  OkRequest(int? code, String? details)
      : super(
          code: "$code",
          message: "Invalid Request",
          details: details,
        );
}

class BadRequestException extends NetworkException {
  BadRequestException(String? details)
      : super(
          code: "invalid-request",
          message: "Invalid Request",
          details: details,
        );
}

class OnConflictException extends NetworkException {
  OnConflictException(String? details)
      : super(
          code: "0",
          message: "Already Created",
          details: details,
        );
}

class UnauthorisedException extends NetworkException {
  UnauthorisedException(String? details)
      : super(
          code: "unauthorised",
          message: "Unauthorised",
          details: details,
        );
}

class InvalidInputException extends NetworkException {
  InvalidInputException(String? details)
      : super(
          code: "invalid-input",
          message: "Invalid Input",
          details: details,
        );
}

class AuthenticationException extends NetworkException {
  AuthenticationException(String? details)
      : super(
          code: "authentication-failed",
          message: "Authentication Failed",
          details: details,
        );
}

class TimeOutException extends NetworkException {
  TimeOutException(String? details)
      : super(
          code: "request-timeout",
          message: "Request TimeOut",
          details: details,
        );
}
