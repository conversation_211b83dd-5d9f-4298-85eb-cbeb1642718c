import 'package:get/get.dart';
import 'package:mobile_crm/core/values/app_strings.dart';

class InAppUpdateExceptionHandler {
  static handleException(e) {
    String errorMessage = "${e.message}";
    RegExp regExp = RegExp(r'^(-?\d+):');
    RegExpMatch? match = regExp.firstMatch(errorMessage);
    int code = 0;
    if (match != null) {
      String errorCode = match.group(1) ?? '0';
      code = int.parse(errorCode);
    }
    return generateExceptionMessage(code);
  }

  static generateExceptionMessage(int code) {
    String errorMessage;
    switch (code) {
      case 0:
        errorMessage = '';
        break;
      case -2:
        errorMessage = Strings.anUnknownErrorOccurred.tr;
        break;
      case -3:
        errorMessage = Strings.theAPIisNotAvailableOnThisDevice;
        break;
      case -4:
        errorMessage = Strings.theRequestThatWasSentByTheAppIsMalformed.tr;
        break;
      case -5:
        errorMessage = Strings.theInstallIsUnavailableToThisUserOrDevice.tr;
        break;
      case -6:
        errorMessage = Strings.theDownloadInstallIsNotAllowed.tr;
        break;
      case -7:
        errorMessage = Strings.theInstallUpdateHasNotBeenDownloaded.tr;
        break;
      case -8:
        errorMessage = Strings.theInstallIsAlreadyInProgress.tr;
        break;
      case -9:
        errorMessage = Strings.thePlayStoreAppIsEitherNotInstalled.tr;
        break;
      case -10:
        errorMessage = Strings.theAppIsNotOwnedByAnyUserOnThisDevice.tr;
        break;
      case -100:
        errorMessage = Strings.anInternalErrorHappenedInThePlayStore.tr;
        break;
      default:
        errorMessage = Strings.anUnknownErrorOccurred.tr;
    }

    return errorMessage;
  }
}
