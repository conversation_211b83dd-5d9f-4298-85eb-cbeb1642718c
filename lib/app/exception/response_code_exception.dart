import 'package:get/get.dart';
import 'package:mobile_crm/app/utils/logger.dart';
import 'package:mobile_crm/app/utils/toast.dart';

import 'network_exception.dart';

dynamic responseCodeExceptionHandler(Response<dynamic> response) {
  if (response.statusCode != 200) {
    infoLogger("Exception Handler", "${response.statusCode}");
  }
  switch (response.statusCode) {
    case 200:
      return response.body;
    case 400:
      // Toast.show("[400] : Bad Request", type: ToastType.error, duration: 5);
      throw BadRequestException(response.body.toString());
    case 401:
    case 403:
      throw UnauthorisedException(response.body.toString());
    case 404:
      // Toast.show("[404] : Not Found", type: ToastType.error, duration: 3);
      throw BadRequestException('Not found');
    case 409:
      // Toast.show("Already Created", type: ToastType.error, duration: 3);
      return response.body;
    // throw OnConflictException('Already Created');
    case 429:
      throw FetchDataException('Too many request');
    case 500:
      Toast.show("Internal Server Error", type: ToastType.error, duration: 5);
      throw FetchDataException('Internal Server Error');
    default:
      // Toast.show(
          // "Error occurred while Communication with Server, please check your internet connection",
          // type: ToastType.error,
          // duration: 8);
      throw FetchDataException(
          'Error occurred while Communication with Server with StatusCode : ${response.statusCode}');
  }
}
