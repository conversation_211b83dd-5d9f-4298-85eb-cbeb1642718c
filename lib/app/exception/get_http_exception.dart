import 'package:get/get.dart';
import 'package:get/get_connect/http/src/exceptions/exceptions.dart';
import 'package:mobile_crm/app/utils/toast.dart';
import 'package:mobile_crm/core/values/values.dart';

void getHttpExceptionHandler(Object exception) {
  if (exception is GetHttpException) {
    String message = exception.message;
    if (message.contains("timed out") ||
        message.contains("timed out") ||
        message.contains("TimeoutException") ||
        message.contains("Timeout")) {
      Toast.show(Strings.connectionTimeOut.tr,
          type: ToastType.error, duration: 3);
    } else if (message.contains("SocketException") ||
        message.contains("Failed host lookup")) {
      Toast.show(Strings.noInternetConnection.tr,
          type: ToastType.error, duration: 3);
    } else if (message.contains("Unauthorised")) {}
  }
}
