import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/widget/button_widget/button_widget.dart';
import 'package:mobile_crm/core/extensions/double_extensions.dart';
import 'package:mobile_crm/core/extensions/widget_extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/values.dart';

class FillProvinceBottomSheet {
  FillProvinceBottomSheet._();

  static void show(
          {required BuildContext context,
          required VoidCallback onPressed,
          Key? key}) =>
      WillPopScope(
        key: key,
        onWillPop: () => Future.value(false),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(
                Icons.drag_handle,
                color: AppColor.black70,
              ),
              10.0.height,
              Text(
                Strings.provinceFieldEmpty.tr,
                style: AppFont.paragraphSmallBold,
              ),
              10.0.height,
              SizedBox(
                  height: AppDimen.h128,
                  child: const Image(image: AssetImage(ImgStrings.noData))),
              10.0.height,
              PrimaryButton(
                onPressed: onPressed,
                width: MediaQuery.of(context).size.width,
                text: Strings.next.tr,
                type: PrimaryButtonType.type3,
              ),
              15.0.height,
            ],
          ),
        ),
      ).toModalBottomSheetNoMinHeight.of(context);
}
