import 'package:flutter/material.dart';
import 'package:mobile_crm/app/modules/login/view/widget/login_form_dummy.dart';
import 'package:mobile_crm/app/modules/login/view/widget/login_verify_by.dart';
import 'package:mobile_crm/app/modules/login/view/widget/login_widget.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/app_constants.dart';

import '../../modules/login/lost_phone_number/views/widget/lost_phone_number_widget.dart';

class LoginBottomSheet extends StatelessWidget {
  const LoginBottomSheet({Key? key, this.type = BottomSheetWidgetType.login})
      : super(key: key);
  final BottomSheetWidgetType type;

  @override
  Widget build(BuildContext context) {
    return type == BottomSheetWidgetType.login
        ? _login(context)
        : type == BottomSheetWidgetType.lostPhone
            ? _lostPhone(context)
            : type == BottomSheetWidgetType.verifyBy
                ? _verifyBy(context)
                : _loginDummy(context);
  }

  Widget _login(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(8),
      constraints: BoxConstraints(maxHeight: Constants.defaultMaxWidth),
      decoration: const BoxDecoration(
          color: AppColor.black10,
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(25), topRight: Radius.circular(25))),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          10.0.height,
          const LoginForm(),
          AppDimen.h32.height,
          const LoginFooter(),
          AppDimen.h8.height
        ],
      ),
    );
  }

  Widget _loginDummy(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(8),
      constraints: BoxConstraints(maxWidth: Constants.defaultMaxWidth),
      decoration: const BoxDecoration(
          color: AppColor.black10,
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(25), topRight: Radius.circular(25))),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [10.0.height, const LoginFormDummy(), AppDimen.h24.height],
      ),
    );
  }

  Widget _lostPhone(BuildContext context) {
    return Container(
      padding:
          EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
      constraints: BoxConstraints(maxWidth: Constants.defaultMaxWidth),
      decoration: const BoxDecoration(
          color: AppColor.black10,
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(10), topRight: Radius.circular(10))),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          10.0.height,
          const LostPhoneNumberTitle(),
          25.0.height,
          const LostPhoneNumberForm(),
          32.0.height,
          const LostPhoneNumberFooter()
        ],
      ),
    );
  }

  Widget _verifyBy(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(8),
      constraints: BoxConstraints(
          minWidth: Constants.defaultMaxWidth,
          maxWidth: Constants.defaultMaxWidth),
      decoration: const BoxDecoration(
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(25), topRight: Radius.circular(25))),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [const LoginVerifyBy(), AppDimen.h24.height],
      ),
    );
  }
}

enum BottomSheetWidgetType { login, lostPhone, loginDummy, verifyBy }
