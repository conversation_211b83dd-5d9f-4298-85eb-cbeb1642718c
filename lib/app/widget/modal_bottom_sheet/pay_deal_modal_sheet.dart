import 'package:drift/drift.dart' as drift;
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/deal_detail/controllers/deal_detail_controller.dart';
import 'package:mobile_crm/app/modules/order/controllers/order_controller.dart';
import 'package:mobile_crm/app/widget/button_widget/button_widget.dart';
import 'package:mobile_crm/app/widget/custom_circular_progress_widget.dart';
import 'package:mobile_crm/app/widget/full_screen_dialog/ovo_deal_payment/ovo_deal_payment_fdialog.dart';
import 'package:mobile_crm/app/widget/full_screen_dialog/payment_method/payment_method_fdialog.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/values.dart';
import 'package:mobile_crm/data/models/deals_payment_model.dart';
import 'package:mobile_crm/data/services/analytics_service.dart';
import 'package:mobile_crm/domain/repository/deal_repository.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

import '../../../data/models/deal_payment_model.dart';
import '../../../data/models/order_detail_model.dart';
import '../../../data/models/payment_method_model.dart';
import '../../../data/providers/db/database.dart';
import '../../modules/order/view/components/order_modal_bottom_sheet.dart';
import '../../modules/screen_wrapper/controller/wrapper_controller.dart';
import '../cached_image_widget.dart';
import '../full_screen_dialog/payment_method/next_payment_fdialog.dart';
import '../full_screen_dialog/payment_method/payment_method_with_required_phone_number_fdialog.dart';
import '../full_screen_dialog/qris_deal_payment/qris_deal_payment_fdialog.dart';

class PayBottomSheet {
  PayBottomSheet._();

  static void show(
      {required BuildContext context,
      required DealData deal,
      required DealPaymentModel dealPost,
      required List<PaymentMethodModel> paymentMethods,
      required DealRepository dealRepo}) {
    final wrapperController = Get.find<WrapperController>();
    final controller = Get.find<DealDetailController>();
    var chooseType = PaymentMethodDetail().obs;
    var show = false.obs;
    var loading = false.obs;
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      enableDrag: false,
      isDismissible: false,
      constraints: BoxConstraints(
          minWidth: MediaQuery.of(context).size.width,
          maxWidth: MediaQuery.of(context).size.width),
      builder: (context) {
        chooseType.value =
            wrapperController.store.lastTransaction ?? PaymentMethodDetail();
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  IconButton(
                    onPressed: () {},
                    icon: const Icon(
                      Icons.close,
                      color: Colors.transparent,
                    ),
                  ),
                  Text(
                    Strings.buyDeal.tr,
                    style: AppFont.componentSmallBold,
                  ),
                  IconButton(
                    onPressed: () => Get.back(),
                    icon: const Icon(
                      Icons.close,
                      color: AppColor.black90,
                    ),
                  ),
                ],
              ),
              15.0.height,
              Container(
                width: double.infinity,
                padding: EdgeInsets.all(8.h),
                decoration: BoxDecoration(
                    color: AppColor.black5,
                    borderRadius: BorderRadius.circular(10.r)),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      "RP",
                      style: AppFont.componentSmallBold
                          .copyWith(color: AppColor.black70),
                    ),
                    Text(
                      deal.dealsValue.toCurrency,
                      style: AppFont.componentSmallBold
                          .copyWith(color: AppColor.black90),
                    ),
                  ],
                ),
              ),
              10.0.height,
              InkWell(
                onTapDown: (details) => showPaymentMethod(
                  context: context,
                  paymentMethods: paymentMethods,
                  onChoose: (value) {
                    chooseType.value = value;
                  },
                ),
                child: Container(
                  width: double.infinity,
                  padding: EdgeInsets.all(8.h),
                  decoration: BoxDecoration(
                      color: AppColor.black5,
                      borderRadius: BorderRadius.circular(10.r)),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Obx(() {
                        return Visibility(
                          visible: chooseType.value.icon != null,
                          child: SizedBox(
                            width: AppDimen.h32,
                            height: AppDimen.h32,
                            child: CachedImageWidget(
                              imageUrl: chooseType.value.icon?.small ?? '',
                              alignment: Alignment.centerLeft,
                              fit: BoxFit.contain,
                            ),
                          ),
                        );
                      }),
                      20.0.width,
                      Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Obx(() {
                            return Text(
                              chooseType.value.name == null
                                  ? Strings.selectPaymentMethod.tr
                                  : Strings.paymentMethod.tr,
                              style: AppFont.componentSmall,
                            );
                          }),
                          Obx(() {
                            return Visibility(
                              visible: chooseType.value.name != null,
                              child: Text(
                                chooseType.value.name ?? '',
                                style: AppFont.componentSmallBold
                                    .copyWith(color: AppColor.black90),
                              ),
                            );
                          }),
                        ],
                      ),
                      const Spacer(),
                      const Icon(
                        Icons.unfold_more,
                        color: AppColor.black50,
                      )
                    ],
                  ),
                ),
              ),
              10.0.height,
              Obx(() {
                return Visibility(
                    visible: show.value,
                    child: Text(
                      Strings.selectPaymentMethod.tr,
                      style: AppFont.componentSmall
                          .copyWith(color: AppColor.utilityDanger),
                    ));
              }),
              10.0.height,
              PrimaryButton(
                onPressed: () async {
                  if (chooseType.value.id == null) {
                    showPaymentMethod(
                      context: context,
                      paymentMethods: paymentMethods,
                      onChoose: (value) {
                        chooseType.value = value;
                      },
                    );
                    show.value = true;
                  } else {
                    AnalyticsService.observer.analytics
                        .logEvent(name: "payment_chosen", parameters: {
                      "name": chooseType.value.name,
                      "id": chooseType.value.id,
                    });
                    if (chooseType.value.id?.toLowerCase() == 'ovo') {
                      wrapperController.store.lastTransaction =
                          chooseType.value;
                      PaymentMethodWithPhoneNumberFullScreenDialog.show(
                          context: context,
                          controller: controller.phoneNumberController,
                          onPay: () async {
                            loading.value = true;
                            var phoneNumber = controller
                                .phoneNumberController.text.toLocal
                                .replaceRange(0, 1, '62');
                            Get.back();
                            var result = await dealRepo
                                .getDealsPayment(
                                    promotionId: "${dealPost.promotionBuyId}",
                                    paymentType: chooseType.value.id ?? 'ovo',
                                    phoneNumber: phoneNumber)
                                .whenComplete(() => loading.value = false);
                            if (result != null) {
                              Get.back();
                              deal = deal.copyWith(
                                  promotionBuyId:
                                      drift.Value(dealPost.promotionBuyId));
                              OvoDealPaymentFullScreenDialog.show(
                                  context: Get.context!,
                                  paymentMethodDetail: chooseType.value,
                                  deal: deal,
                                  dealPaymentModel: result);
                            }
                          },
                          paymentMethodDetail: chooseType.value);
                    } else if (chooseType.value.id?.toLowerCase() == 'qris') {
                      loading.value = true;
                      wrapperController.store.lastTransaction =
                          chooseType.value;
                      var result = await dealRepo
                          .getDealsPayment(
                              promotionId: "${dealPost.promotionBuyId}",
                              paymentType: chooseType.value.id ?? '')
                          .whenComplete(() => loading.value = false);
                      if (result != null) {
                        Get.back();
                      }
                      QrisDealPaymentFullScreenDialog.show(
                          context: Get.context!,
                          deal: deal,
                          dealPaymentModel: result ?? DealsPaymentModel());
                    } else {
                      loading.value = true;
                      var result = await dealRepo
                          .getDealsPayment(
                              promotionId: "${dealPost.promotionBuyId}",
                              paymentType: chooseType.value.id ?? '')
                          .whenComplete(() => loading.value = false);
                      Get.back();
                      wrapperController.store.lastTransaction =
                          chooseType.value;
                      if (result != null) {
                        deal = deal.copyWith(
                            promotionBuyId:
                                drift.Value(dealPost.promotionBuyId));
                        NextPaymentFullScreenDialog.show(
                            context: Get.context!,
                            dealsPaymentModel: result,
                            deal: deal);
                      }
                    }
                  }
                },
                text: "",
                type: PrimaryButtonType.type4,
                child: SizedBox(
                  width: double.infinity,
                  child: Align(
                    alignment: Alignment.center,
                    child: Obx(() {
                      return loading.value
                          ? const CustomCircularProgressIndicator()
                          : Text(
                              Strings.payBtn.tr,
                              style: AppFont.componentSmallBold
                                  .copyWith(color: AppColor.white),
                            );
                    }),
                  ),
                ),
              ),
              10.0.height
            ],
          ),
        );
      },
    );
  }

  static Future showOrder(
      {required BuildContext context,
      required List<PaymentMethodModel> paymentMethods}) {
    final wrapperController = Get.find<WrapperController>();
    final controller = Get.find<OrderController>();
    var chooseType = PaymentMethodDetail().obs;
    var show = false.obs;
    var loading = false.obs;
    chooseType.value =
        wrapperController.store.lastTransaction ?? PaymentMethodDetail();
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text(
                Strings.choosePayment.tr,
                style: AppFont.componentSmallBold,
              ),
            ],
          ),
          15.0.height,
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(8.h),
            decoration: BoxDecoration(
                color: AppColor.black5,
                borderRadius: BorderRadius.circular(10.r)),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  "Rp${controller.newOrder.value.sumTotalBill().toCurrency}",
                  style: AppFont.componentSmallBold
                      .copyWith(color: AppColor.black70),
                ),
              ],
            ),
          ),
          10.0.height,
          InkWell(
            onTapDown: (details) => showPaymentMethod(
              context: context,
              paymentMethods: paymentMethods,
              onChoose: (value) {
                chooseType.value = value;
                wrapperController.store.lastTransaction = value;
              },
            ),
            child: Container(
              width: double.infinity,
              padding: EdgeInsets.all(8.h),
              decoration: BoxDecoration(
                  color: AppColor.black5,
                  borderRadius: BorderRadius.circular(10.r)),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Obx(() {
                    return Visibility(
                      visible: chooseType.value.icon != null,
                      child: SizedBox(
                        width: AppDimen.h32,
                        height: AppDimen.h32,
                        child: CachedImageWidget(
                          imageUrl: chooseType.value.icon?.small ?? '',
                          alignment: Alignment.centerLeft,
                          fit: BoxFit.contain,
                          errorWidget: (context, url, error) =>
                              SizedBox(height: AppDimen.h32),
                        ),
                      ),
                    );
                  }),
                  20.0.width,
                  Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Obx(() {
                        return Text(
                          chooseType.value.name == null
                              ? Strings.selectPaymentMethod.tr
                              : Strings.paymentMethod.tr,
                          style: AppFont.componentSmall,
                        );
                      }),
                      Obx(() {
                        return Visibility(
                          visible: chooseType.value.name != null,
                          child: Text(
                            chooseType.value.name ?? '',
                            style: AppFont.componentSmallBold
                                .copyWith(color: AppColor.black90),
                          ),
                        );
                      }),
                    ],
                  ),
                  const Spacer(),
                  const Icon(
                    Icons.unfold_more,
                    color: AppColor.black50,
                  )
                ],
              ),
            ),
          ),
          10.0.height,
          Obx(() {
            return Visibility(
                visible: show.value,
                child: Text(
                  Strings.selectPaymentMethod.tr,
                  style: AppFont.componentSmall
                      .copyWith(color: AppColor.utilityDanger),
                ));
          }),
          10.0.height,
          PrimaryButton(
            onPressed: () async {
              if (chooseType.value.id == null) {
                showPaymentMethod(
                  context: context,
                  paymentMethods: paymentMethods,
                  onChoose: (value) {
                    chooseType.value = value;
                  },
                );
                show.value = true;
              } else {
                AnalyticsService.observer.analytics
                    .logEvent(name: "payment_chosen", parameters: {
                  "name": chooseType.value.name,
                  "id": chooseType.value.id,
                });
                if (chooseType.value.methodDetailType == 'bank_transfer') {
                  Get.back();
                  return OrderModalBottomSheet.payBankTransfer(
                      context: Get.context ?? context,
                      controller: controller,
                      paymentMethodDetail: chooseType.value);
                }
                loading.value = true;
                var result = await controller.repoTransaction
                    .createTransactionPayment(
                        controller.newOrder.value, chooseType.value)
                    .whenComplete(() => loading.value = false);

                if (result.status) {
                  if (result.data != null) {
                    if (chooseType.value.methodDetailType == 'ewallet') {
                      if (result.data?.paymentInfo?.type == "link") {
                        Get.back();
                        NextOrderPaymentFullScreenDialog.show(
                            context: Get.context ?? context,
                            paymentMethodDetail:
                                result.data ?? PaymentMethodDetail(),
                            controller: controller);
                      }
                    }

                    if (chooseType.value.methodDetailType == 'qris') {
                      if (result.data?.paymentInfo?.type == "image") {
                        if(result.data?.paymentInfo?.value == ""){
                          //log to sentry
                          Sentry.captureException(throw("payment qris error, no qris value"));
                        }
                        Get.back();                        
                        QrisOrderPaymentFullScreenDialog.show(
                            context: Get.context ?? context,
                            controller: controller,
                            orderDetailModel: OrderDetailModel(),
                            paymentMethodDetail:
                                result.data ?? PaymentMethodDetail());
                      }
                    }
                  }
                } else {
                  Get.back();
                }
              }
            },
            text: "",
            type: PrimaryButtonType.type4,
            child: SizedBox(
              width: double.infinity,
              child: Align(
                alignment: Alignment.center,
                child: Obx(() {
                  return loading.value
                      ? const CustomCircularProgressIndicator()
                      : Text(
                          Strings.payBtn.tr,
                          style: AppFont.componentSmallBold
                              .copyWith(color: AppColor.white),
                        );
                }),
              ),
            ),
          ),
          10.0.height
        ],
      ),
    ).toModalBottomSheetNoMinHeight.of(context);
  }

  static void showPaymentMethod(
          {required BuildContext context,
          required List<PaymentMethodModel> paymentMethods,
          required void Function(PaymentMethodDetail) onChoose}) =>
      PaymentMethodFullScreenDialog.show(
          context: context, paymentMethods: paymentMethods, onChoose: onChoose);
}
