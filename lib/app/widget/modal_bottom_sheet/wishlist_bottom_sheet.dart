import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/utils/animations.dart';
import 'package:mobile_crm/app/utils/toast.dart';
import 'package:mobile_crm/app/widget/cached_image_widget.dart';
import 'package:mobile_crm/app/widget/input_widget/input_form_widget.dart';
import 'package:mobile_crm/core/extensions/double_extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/values.dart';
import 'package:mobile_crm/data/models/wishlist_model.dart';
import 'package:mobile_crm/data/providers/db/database.dart';
import 'package:mobile_crm/domain/repository/wishlist_repository.dart';

import '../../../data/services/analytics_service.dart';
import '../app_dotted_separator.dart';
import '../custom_circular_progress_widget.dart';
import '../html_widget.dart';

class WishlistBottomSheet {
  WishlistBottomSheet._();

  static void showWishlistOption(
      {required BuildContext context,
      required WishlistRepository repo,
      required Product product,
      WishlistData? wishlist}) {
    const double padding = 10;
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      useSafeArea: true,
      constraints: BoxConstraints(
          maxWidth: Constants.defaultMaxWidth,
          minHeight: MediaQuery.of(context).size.height * 0.4,
          maxHeight: MediaQuery.of(context).size.height * 0.8),
      builder: (context) => Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            decoration: const BoxDecoration(
                color: AppColor.disable,
                borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(padding),
                    topRight: Radius.circular(padding))),
            child: Column(
              children: [
                const Align(
                    alignment: Alignment.center,
                    child: Icon(
                      Icons.drag_handle,
                      color: AppColor.black70,
                    )),
                Container(
                  margin: const EdgeInsets.symmetric(vertical: padding),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          AppDimen.h8.width,
                          ClipRRect(
                            borderRadius: BorderRadius.circular(padding),
                            child: CachedImageWidget(
                              imageUrl: product.photo ?? '',
                              width: AppDimen.h40,
                              height: AppDimen.h40,
                            ),
                          ),
                          AppDimen.h6.width,
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                Strings.saved.tr,
                                style: AppFont.componentSmallBold,
                              ),
                              Text(
                                product.name ?? '',
                                style: AppFont.componentSmall,
                              ),
                            ],
                          )
                        ],
                      ),
                      IconButton(
                          onPressed: () async {
                            var result = await repo.deleteWishlist(
                                "${wishlist?.crmProductWishlistId ?? '0'}");

                            Toast.show(
                                result.status
                                    ? Strings.valueRemovedFromWishlist
                                        .trParams({'value': product.name ?? ''})
                                    : result.message,
                                type: result.status
                                    ? ToastType.success
                                    : ToastType.error);
                          },
                          icon: const Icon(
                            Icons.bookmark_added_sharp,
                            color: AppColor.black70,
                          ))
                    ],
                  ),
                )
              ],
            ),
          ),
          const DottedDivider(
            width: 5,
            height: 2,
            color: AppColor.black5,
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                5.0.height,
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      Strings.category.tr,
                      style: AppFont.componentSmallBold,
                    ),
                    InkWell(
                      onTapDown: (details) {
                        AnalyticsService.observer.analytics.logEvent(
                            name: "btn_add_category_wishlist",
                            parameters: {"product_name": product.name});
                        addNewCategory(
                            context: context,
                            repo: repo,
                            product: product,
                            wishlist: wishlist);
                      },
                      child: Text(
                        Strings.addCategory.tr,
                        style: AppFont.componentSmallBold,
                      ),
                    ),
                  ],
                ),
                5.0.height,
                Flexible(
                  child: FutureBuilder(
                    future: repo.getWishlistCategory(),
                    builder: (BuildContext context, snapshot) {
                      if (snapshot.hasData) {
                        var category = snapshot.data.obs;
                        return ListView.builder(
                          shrinkWrap: true,
                          itemCount: category.value?.length ?? 0,
                          itemBuilder: (context, index) {
                            var item = category.value![index];
                            return Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  "${item.name}",
                                  style: AppFont.componentSmall,
                                ),
                                Obx(
                                  () {
                                    if ((category.value![index].isChosen ??
                                            false) ==
                                        true) {
                                      return IconButton(
                                          onPressed: () {},
                                          icon: const Icon(
                                            Icons.done,
                                            color: AppColor.black70,
                                          ));
                                    }
                                    return IconButton(
                                        onPressed: () async => await repo
                                                .setWishlistCategory(
                                                    categoryId: (item.id ?? 0)
                                                        .toString(),
                                                    wishlistId: wishlist
                                                            ?.crmProductWishlistId
                                                            .toString() ??
                                                        '0')
                                                .then((value) {
                                              AnalyticsService
                                                  .observer.analytics
                                                  .logEvent(
                                                      name:
                                                          "set_category_wishlist",
                                                      parameters: {
                                                    "product_name":
                                                        product.name,
                                                    'category_name': item.name
                                                  });
                                              category.update((val) {
                                                val
                                                    ?.where((element) =>
                                                        element.isChosen ==
                                                        true)
                                                    .forEach((element) {
                                                  element.isChosen = false;
                                                });
                                                val![index].isChosen = true;
                                              });
                                            }),
                                        icon: const Icon(
                                          Icons.add_circle_outline_rounded,
                                          color: AppColor.black70,
                                        ));
                                  },
                                )
                              ],
                            );
                          },
                        );
                      }

                      return const CustomCircularProgressIndicator(
                        valueColor: AppColor.black90,
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  static void addNewCategory(
      {required BuildContext context,
      required WishlistRepository repo,
      required Product product,
      WishlistData? wishlist}) {
    showGeneralDialog(
      context: context,
      pageBuilder: (context, animation, secondaryAnimation) {
        return Animations.fromBottom(animation, secondaryAnimation,
            newCategoryWidget(product, repo, wishlist, context));
      },
    );
  }

  static Dialog newCategoryWidget(Product product, WishlistRepository repo,
      WishlistData? wishlistData, BuildContext context) {
    TextEditingController controller = TextEditingController();
    var isLoading = false.obs;
    return Dialog.fullscreen(
      child: WillPopScope(
        onWillPop: () => Future.value(false),
        child: Column(
          children: [
            AppBar(
              leadingWidth: AppDimen.h64,
              leading: TextButton(
                style: const ButtonStyle(alignment: Alignment.centerLeft, splashFactory: NoSplash.splashFactory),
                  onPressed: () {
                    AnalyticsService.observer.analytics.logEvent(
                        name: "cancel_add_new_category_wishlist",
                        parameters: {
                          "product_name": product.name,
                        });
                    Get.back();
                  },
                  child: Text(
                    Strings.cancel.tr,
                    style: AppFont.componentSmall,
                  )),
              automaticallyImplyLeading: false,
              title: Text(
                Strings.newValue.trParams({'value': Strings.category.tr}),
                style: AppFont.componentSmall,
              ),
              centerTitle: true,
              backgroundColor: AppColor.white,
              actions: [
                Obx(
                  () => isLoading.value
                      ? TextButton(
                          onPressed: () {},
                          child: const CustomCircularProgressIndicator(
                            valueColor: AppColor.black90,
                            backgroundColor: Colors.transparent,
                          ),
                        )
                      : TextButton(
                          onPressed: () async {
                            isLoading.value = true;
                            AnalyticsService.observer.analytics.logEvent(
                                name: "add_new_category_wishlist",
                                parameters: {
                                  "product_name": product.name,
                                  'category_name': controller.text
                                });
                            var result = await repo
                                .addWishlistCategory(
                                    wishlistCategoryModel:
                                        WishlistCategoryModel(
                                            name: controller.text),
                                    wishlistId:
                                        "${wishlistData?.crmProductWishlistId ?? 0}")
                                .whenComplete(() => isLoading.value = false);
                            if (result.status) {
                              Get.back();
                              Get.back();
                            }
                            Toast.show(
                                result.status
                                    ? Strings
                                        .valueAddedToYourNewWishlistCategory
                                        .trParams({'value': product.name ?? ''})
                                    : result.message,
                                type: result.status
                                    ? ToastType.success
                                    : ToastType.error);
                          },
                          child: Text(
                            Strings.save.tr,
                            style: AppFont.componentSmall,
                          )),
                )
              ],
            ),
            Expanded(
              child: SingleChildScrollView(
                child: Column(children: [
                  Padding(
                    padding: EdgeInsets.all(AppDimen.h10),
                    child: SizedBox(
                      height: 200.h,
                      child: ClipRRect(
                          borderRadius: BorderRadius.circular(AppDimen.h10),
                          child: CachedImageWidget(
                              width: 300,
                              height: 200,
                              fit: BoxFit.cover,
                              imageUrl: product.photo.toString())),
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: AppDimen.h10),
                    child: SizedBox(
                      width: double.infinity,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Text(
                            product.name ?? '',
                            style: AppFont.componentSmallBold,
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                          Text(
                            product.subcategory ?? '',
                            style: AppFont.componentSmall
                                .copyWith(fontSize: 10.sp),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                          HtmlWidget(
                            data: product.description ?? '',
                            fontSize: 12,
                          ),
                        ],
                      ),
                    ),
                  ),
                  const DottedDivider(
                    height: 2,
                    width: 3,
                    color: AppColor.disable,
                  ),
                  Padding(
                    padding: EdgeInsets.all(AppDimen.h10),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          Strings.categoryName.tr,
                          style:
                              AppFont.componentSmall.copyWith(fontSize: 10.sp),
                        ),
                        AppDimen.h2.height,
                        AppInputText(
                            label: Strings.nameYourCategory.tr,
                            keyboardType: TextInputType.name,
                            controller: controller),
                      ],
                    ),
                  )
                ]),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
