// ignore_for_file: invalid_use_of_protected_member, unnecessary_null_comparison

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/helper/deal_helper.dart';
import 'package:mobile_crm/app/modules/home/<USER>/home_controller.dart';
import 'package:mobile_crm/app/modules/screen_wrapper/controller/wrapper_controller.dart';
import 'package:mobile_crm/app/utils/share_helper.dart';
import 'package:mobile_crm/app/widget/app_dotted_separator.dart';
import 'package:mobile_crm/app/widget/button_widget/button_widget.dart';
import 'package:mobile_crm/app/widget/cached_image_widget.dart';
import 'package:mobile_crm/app/widget/custom_circular_progress_widget.dart';
import 'package:mobile_crm/app/widget/html_widget.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/app_constants.dart';
import 'package:mobile_crm/core/values/app_strings.dart';
import 'package:mobile_crm/data/models/available_model.dart';
import 'package:mobile_crm/data/models/variant_model.dart';
import 'package:mobile_crm/data/providers/db/database.dart';
import 'package:mobile_crm/data/services/analytics_service.dart';
import 'package:mobile_crm/routes/app_pages.dart';
import 'package:sentry_flutter/sentry_flutter.dart';
import 'package:mobile_crm/app/utils/logger.dart';
import '../button_widget/add_order_button_widget.dart';
import 'wishlist_bottom_sheet.dart';

class ShowCustomModalBottom {
  ShowCustomModalBottom._();

  static void showModalProductHome(  
      BuildContext context, Product product, HomeController controller) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.white,
      constraints: BoxConstraints(maxWidth: Constants.defaultMaxWidth),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
            topLeft: Radius.circular(15.0), topRight: Radius.circular(15.0)),
      ),
      builder: (context) {
        AnalyticsService.observer.analytics.logEvent(
            name: "select_product_menu",
            parameters: {"product_name": product.name});
        Sentry.addBreadcrumb(Breadcrumb(
            type: 'debug',
            category: 'user.activity.home',
            data: {"product_name": product.name},
            level: SentryLevel.debug));
        final wrapperC = Get.find<WrapperController>();
        var tabIndex = 0.obs;
        return Padding(
          padding: EdgeInsets.only(
              left: Constants.defaultPadding,
              right: Constants.defaultPadding,
              bottom: Constants.defaultPadding),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Center(
                  child: Icon(
                Icons.drag_handle,
                color: AppColor.black70,
              )),
              5.0.height,
              Center(
                child: SizedBox(
                  height: 200.h,
                  child: ClipRRect(
                      borderRadius: BorderRadius.circular(10),
                      child: CachedImageWidget(
                          disable: product.availability != "available",
                          width: 300,
                          height: 200,
                          fit: BoxFit.cover,
                          imageUrl: product.photo.toString())),
                ),
              ),
              15.0.height,
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Text(product.name.toString().toUpperCase(),
                          style: AppFont.componentMediumBold),
                      Text(product.subcategory.toString(),
                          style: AppFont.componentSmallBold),
                      Text(Strings.unit.trParams({'unit': product.unit ?? ''}),
                          style: AppFont.componentSmall)
                    ],
                  ),
                  Row(
                    children: [
                      IconButton(
                        onPressed: () {
                          shareProduct(product);
                        },
                        icon: const Icon(
                          Icons.share,
                          color: AppColor.black70,
                        ),
                        padding: const EdgeInsets.all(0),
                      ),
                      Obx(
                        () => controller.isLoadingAddRemoveWishlist.value
                            ? IconButton(
                                onPressed: () {},
                                icon: const CustomCircularProgressIndicator(
                                  backgroundColor: Colors.transparent,
                                  valueColor: AppColor.black90,
                                ))
                            : product.is_in_wishlist ?? false
                                ? IconButton(
                                    onPressed: () async {
                                      await controller.removeWishlist(product);
                                    },
                                    icon: const Icon(
                                      Icons.bookmark_added_rounded,
                                      color: AppColor.black70,
                                    ),
                                    padding: const EdgeInsets.all(0),
                                  )
                                : IconButton(
                                    onPressed: () async {
                                      var result =
                                          await controller.addWishlist(product);
                                      if (result.status) {
                                        Get.back();
                                        WishlistBottomSheet.showWishlistOption(
                                            context: context,
                                            repo: controller.repoWishlist,
                                            wishlist: result.data,
                                            product: product);
                                      } else {
                                        Get.back();
                                      }
                                    },
                                    icon: const Icon(
                                      Icons.bookmark_add_rounded,
                                      color: AppColor.black70,
                                    ),
                                    padding: const EdgeInsets.all(0),
                                  ),
                      )
                    ],
                  ),
                ],
              ),
              Flexible(
                child: HtmlWidget(
                  data: product.description ?? '',
                  maxLines: 8,
                  fontSize: 12,
                ),
              ),
              const Divider(
                thickness: 2,
                color: AppColor.black10,
              ),
              SizedBox(
                height: AppDimen.h24,
                width: MediaQuery.of(context).size.width,
                child: (product.variant?.isEmpty == true ||
                        product.variant == null)
                    ? FittedBox(
                        alignment: Alignment.centerLeft,
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 16, vertical: 8),
                          decoration: BoxDecoration(
                              color: AppColor.black,
                              borderRadius: BorderRadius.circular(30)),
                          child: Text(
                            Strings.all.tr,
                            textAlign: TextAlign.center,
                            style: AppFont.componentSmall
                                .copyWith(color: AppColor.white),
                          ),
                        ),
                      )
                    : ListView.builder(
                        scrollDirection: Axis.horizontal,
                        itemCount: product.variant?.length ?? 0,
                        itemBuilder: (BuildContext context, int idx) {
                          VariantModel variant = product.variant![idx];
                          return Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 2),
                            child: InkWell(
                              child: Obx(() => FittedBox(
                                    alignment: Alignment.centerLeft,
                                    child: Container(
                                      padding: const EdgeInsets.all(8),
                                      decoration: BoxDecoration(
                                          border: Border.all(
                                              color: AppColor.disable),
                                          color: (tabIndex.value == idx)
                                              ? AppColor.black
                                              : AppColor.white,
                                          borderRadius:
                                              BorderRadius.circular(30)),
                                      child: Text(
                                        '${variant.name}',
                                        style: AppFont.componentSmall.copyWith(
                                            color: (tabIndex.value == idx)
                                                ? AppColor.white
                                                : AppColor.black),
                                      ),
                                    ),
                                  )),
                              onTap: () {
                                tabIndex.value = idx;
                              },
                            ),
                          );
                        },
                      ),
              ),
              const Divider(
                thickness: 2,
                color: AppColor.black10,
              ),
              Container(
                margin: const EdgeInsets.only(bottom: 5),
                width: MediaQuery.of(context).size.width,
                child: Obx(() {
                  return Text(
                      Strings.availableAt.trParams({
                        "outlet": wrapperC.configApp.value.language?.outlet ??
                            "Outlet"
                      }),
                      textAlign: TextAlign.left,
                      style: AppFont.componentSmall);
                }),
              ),
              (product.variant?.isEmpty == true || product.variant == null)
                  ? Flexible(
                      child: ListView.builder(
                          scrollDirection: Axis.vertical,
                          shrinkWrap: true,
                          itemCount: product.available?.length,
                          itemBuilder: (BuildContext context, int i) {
                            AvailableModel available = product.available![i];
                            return Column(
                              children: [
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Column(
                                      mainAxisAlignment:
                                          MainAxisAlignment.start,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          available.name ?? '',
                                          style: AppFont.componentSmallBold,
                                        ),
                                        Text(
                                          "RP ${available.priceSell.toCurrency}",
                                          style: AppFont.componentSmall,
                                        ),
                                        Visibility(
                                          visible: (available.stockQty ?? 0) > 0,
                                          child: Text('${available.stockQty} tersisa', style: AppFont.componentSmall.copyWith(color: Colors.black45, fontSize: 10.sp,  fontWeight: FontWeight.normal))),
                                      ],
                                    ),
                                    available.enableOrder?.status == 'disable'
                                        ? AddOrderButtonWidget(
                                            onTap: () {
                                              controller.repoProduct
                                                  .addNotifyProduct(
                                                      productDetailId: available
                                                              .productDetailId ??
                                                          0,
                                                      qty: 1);
                                            },
                                            text: Strings.notifyMe.tr,
                                          )
                                        : AddOrderButtonWidget(
                                            onTap: () {
                                              Sentry.addBreadcrumb(Breadcrumb(
                                                  type: 'debug',
                                                  category:
                                                      'user.activity.home.order',
                                                  data: {
                                                    'product_id':
                                                        product.productId,
                                                    'product_name':
                                                        product.name,
                                                    'product_price':
                                                        product.price
                                                  },
                                                  level: SentryLevel.debug));
                                              int outletId =
                                                  available.outletId ?? 0;
                                              controller.store.productDetail =
                                                  available.productDetailId;
                                              AnalyticsService
                                                  .observer.analytics
                                                  .logSelectItem(
                                                      itemListId: available
                                                          .productDetailId
                                                          .toString(),
                                                      itemListName:
                                                          product.name);
                                              Navigator.of(context).pop();
                                              Get.toNamed(
                                                  Routes.OUTLET(
                                                      outletId.toString()),
                                                  arguments: product);
                                            },
                                            text: Strings.order2.tr),
                                  ],
                                ),
                                const Divider()
                              ],
                            );
                          }),
                    )
                  : Flexible(
                      child: Obx(
                        () => ListView.builder(
                          shrinkWrap: true,
                          itemCount: product
                                  .variant?[tabIndex.value].available?.length ??
                              0,
                          itemBuilder: (BuildContext context, int i) {
                            AvailableModel? available =
                                product.variant?[tabIndex.value].available![i];
                            return Column(
                              children: [
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Column(
                                      mainAxisAlignment:
                                          MainAxisAlignment.start,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(available?.name ?? '',
                                            style: AppFont.componentSmallBold),
                                        Text(
                                            "RP ${available?.priceSell.toCurrency}",
                                            style: AppFont.componentSmall),
                                      ],
                                    ),
                                    available?.enableOrder?.status == 'disable'
                                        ? AddOrderButtonWidget(
                                            onTap: () {
                                              controller.repoProduct
                                                  .addNotifyProduct(
                                                      productDetailId: available
                                                              ?.productDetailId ??
                                                          0,
                                                      qty: 1);
                                            },
                                            text: Strings.notifyMe.tr,
                                          )
                                        : AddOrderButtonWidget(
                                            onTap: () {
                                              Sentry.addBreadcrumb(Breadcrumb(
                                                  type: 'debug',
                                                  category:
                                                      'user.activity.home.order',
                                                  data: {
                                                    'product_id':
                                                        product.productId,
                                                    'product_name':
                                                        product.name,
                                                    'product_price':
                                                        product.price,
                                                    'product_variant':
                                                        available?.name,
                                                    'product_variant_price':
                                                        available?.priceSell
                                                  },
                                                  level: SentryLevel.debug));
                                              int outletId =
                                                  available?.outletId ?? 0;
                                              controller.store.productDetail =
                                                  available?.productDetailId;
                                              AnalyticsService
                                                  .observer.analytics
                                                  .logSelectItem(
                                                      itemListId: available
                                                          ?.productDetailId
                                                          .toString(),
                                                      itemListName:
                                                          "${product.name}(${available?.name})");
                                              Navigator.of(context).pop();
                                              Get.toNamed(
                                                  Routes.OUTLET(
                                                      outletId.toString()),
                                                  arguments: product);
                                            },
                                            text: Strings.order2.tr),
                                  ],
                                ),
                                const Divider()
                              ],
                            );
                          },
                        ),
                      ),
                    ),
            ],
          ),
        );
      },
    );
  }

  static void showModalProductNotif(
      BuildContext context, Product product, WrapperController controller) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.white,
      constraints: BoxConstraints(maxWidth: Constants.defaultMaxWidth),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
            topLeft: Radius.circular(15.0), topRight: Radius.circular(15.0)),
      ),
      builder: (context) {
        AnalyticsService.observer.analytics.logEvent(
            name: "select_product_menu",
            parameters: {"product_name": product.name});
        Sentry.addBreadcrumb(Breadcrumb(
            type: 'debug',
            category: 'user.activity.home',
            data: {"product_name": product.name},
            level: SentryLevel.debug));
        final wrapperC = Get.find<WrapperController>();
        var tabIndex = 0.obs;
        return Padding(
          padding: EdgeInsets.only(
              left: Constants.defaultPadding,
              right: Constants.defaultPadding,
              bottom: Constants.defaultPadding),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Center(
                  child: Icon(
                Icons.drag_handle,
                color: AppColor.black70,
              )),
              5.0.height,
              Center(
                child: SizedBox(
                  height: 200.h,
                  child: ClipRRect(
                      borderRadius: BorderRadius.circular(10),
                      child: CachedImageWidget(
                          disable: product.availability != "available",
                          width: 300,
                          height: 200,
                          fit: BoxFit.cover,
                          imageUrl: product.photo.toString())),
                ),
              ),
              15.0.height,
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Text(product.name.toString().toUpperCase(),
                          style: AppFont.componentMediumBold),
                      Text(product.subcategory.toString(),
                          style: AppFont.componentSmallBold),
                      Text(Strings.unit.trParams({'unit': product.unit ?? ''}),
                          style: AppFont.componentSmall)
                    ],
                  ),
                  IconButton(
                    onPressed: () {
                      shareProduct(product);
                    },
                    icon: const Icon(
                      Icons.share,
                      color: AppColor.black70,
                    ),
                    padding: const EdgeInsets.all(0),
                  ),
                ],
              ),
              Flexible(
                child: HtmlWidget(
                  data: product.description ?? '',
                ),
              ),
              const Divider(
                thickness: 2,
                color: AppColor.black10,
              ),
              SizedBox(
                height: AppDimen.h24,
                width: MediaQuery.of(context).size.width,
                child: (product.variant?.isEmpty == true ||
                        product.variant == null)
                    ? FittedBox(
                        alignment: Alignment.centerLeft,
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 16, vertical: 8),
                          decoration: BoxDecoration(
                              color: AppColor.black,
                              borderRadius: BorderRadius.circular(30)),
                          child: Text(
                            Strings.all.tr,
                            textAlign: TextAlign.center,
                            style: AppFont.componentSmall
                                .copyWith(color: AppColor.white),
                          ),
                        ),
                      )
                    : ListView.builder(
                        scrollDirection: Axis.horizontal,
                        itemCount: product.variant?.length ?? 0,
                        itemBuilder: (BuildContext context, int idx) {
                          VariantModel variant = product.variant![idx];
                          return Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 2),
                            child: InkWell(
                              child: Obx(() => FittedBox(
                                    alignment: Alignment.centerLeft,
                                    child: Container(
                                      padding: const EdgeInsets.all(8),
                                      decoration: BoxDecoration(
                                          border: Border.all(
                                              color: AppColor.disable),
                                          color: (tabIndex.value == idx)
                                              ? AppColor.black
                                              : AppColor.white,
                                          borderRadius:
                                              BorderRadius.circular(30)),
                                      child: Text(
                                        '${variant.name}',
                                        style: AppFont.componentSmall.copyWith(
                                            color: (tabIndex.value == idx)
                                                ? AppColor.white
                                                : AppColor.black),
                                      ),
                                    ),
                                  )),
                              onTap: () {
                                tabIndex.value = idx;
                              },
                            ),
                          );
                        },
                      ),
              ),
              const Divider(
                thickness: 2,
                color: AppColor.black10,
              ),
              Container(
                margin: const EdgeInsets.only(bottom: 5),
                width: MediaQuery.of(context).size.width,
                child: Obx(() {
                  return Text(
                      Strings.availableAt.trParams({
                        "outlet": wrapperC.configApp.value.language?.outlet ??
                            "Outlet"
                      }),
                      textAlign: TextAlign.left,
                      style: AppFont.componentSmall);
                }),
              ),
              (product.variant?.isEmpty == true || product.variant == null)
                  ? Flexible(
                      child: ListView.builder(
                          scrollDirection: Axis.vertical,
                          shrinkWrap: true,
                          itemCount: product.available?.length,
                          itemBuilder: (BuildContext context, int i) {
                            AvailableModel available = product.available![i];
                            return Column(
                              children: [
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Column(
                                      mainAxisAlignment:
                                          MainAxisAlignment.start,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          available.name ?? '',
                                          style: AppFont.componentSmallBold,
                                        ),
                                        Text(
                                          "RP ${available.priceSell.toCurrency}",
                                          style: AppFont.componentSmall,
                                        ),
                                      ],
                                    ),
                                    available.enableOrder?.status == 'disable'
                                        ? AddOrderButtonWidget(
                                            onTap: () {
                                              controller.repoProduct
                                                  .addNotifyProduct(
                                                      productDetailId: available
                                                              .productDetailId ??
                                                          0,
                                                      qty: 1);
                                            },
                                            text: Strings.notifyMe.tr,
                                          )
                                        : AddOrderButtonWidget(
                                            onTap: () {
                                              Sentry.addBreadcrumb(Breadcrumb(
                                                  type: 'debug',
                                                  category:
                                                      'user.activity.home.order',
                                                  data: {
                                                    'product_id':
                                                        product.productId,
                                                    'product_name':
                                                        product.name,
                                                    'product_price':
                                                        product.price
                                                  },
                                                  level: SentryLevel.debug));
                                              int outletId =
                                                  available.outletId ?? 0;
                                              controller.store.productDetail =
                                                  available.productDetailId;
                                              AnalyticsService
                                                  .observer.analytics
                                                  .logSelectItem(
                                                      itemListId: available
                                                          .productDetailId
                                                          .toString(),
                                                      itemListName:
                                                          product.name);
                                              Navigator.of(context).pop();
                                              Get.toNamed(
                                                  Routes.OUTLET(
                                                      outletId.toString()),
                                                  arguments: product);
                                            },
                                            text: Strings.order2.tr),
                                  ],
                                ),
                                const Divider()
                              ],
                            );
                          }),
                    )
                  : Flexible(
                      child: Obx(
                        () => ListView.builder(
                          shrinkWrap: true,
                          itemCount: product
                                  .variant?[tabIndex.value].available?.length ??
                              0,
                          itemBuilder: (BuildContext context, int i) {
                            AvailableModel? available =
                                product.variant?[tabIndex.value].available![i];
                            return Column(
                              children: [
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Column(
                                      mainAxisAlignment:
                                          MainAxisAlignment.start,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(available?.name ?? '',
                                            style: AppFont.componentSmallBold),
                                        Text(
                                            "RP ${available?.priceSell.toCurrency}",
                                            style: AppFont.componentSmall),
                                      ],
                                    ),
                                    available?.enableOrder?.status == 'disable'
                                        ? AddOrderButtonWidget(
                                            onTap: () {
                                              controller.repoProduct
                                                  .addNotifyProduct(
                                                      productDetailId: available
                                                              ?.productDetailId ??
                                                          0,
                                                      qty: 1);
                                            },
                                            text: Strings.notifyMe.tr,
                                          )
                                        : AddOrderButtonWidget(
                                            onTap: () {
                                              Sentry.addBreadcrumb(Breadcrumb(
                                                  type: 'debug',
                                                  category:
                                                      'user.activity.home.order',
                                                  data: {
                                                    'product_id':
                                                        product.productId,
                                                    'product_name':
                                                        product.name,
                                                    'product_price':
                                                        product.price,
                                                    'product_variant':
                                                        available?.name,
                                                    'product_variant_price':
                                                        available?.priceSell
                                                  },
                                                  level: SentryLevel.debug));
                                              int outletId =
                                                  available?.outletId ?? 0;
                                              controller.store.productDetail =
                                                  available?.productDetailId;
                                              AnalyticsService
                                                  .observer.analytics
                                                  .logSelectItem(
                                                      itemListId: available
                                                          ?.productDetailId
                                                          .toString(),
                                                      itemListName:
                                                          "${product.name}(${available?.name})");
                                              Navigator.of(context).pop();
                                              Get.toNamed(
                                                  Routes.OUTLET(
                                                      outletId.toString()),
                                                  arguments: product);
                                            },
                                            text: Strings.order2.tr),
                                  ],
                                ),
                                const Divider()
                              ],
                            );
                          },
                        ),
                      ),
                    ),
            ],
          ),
        );
      },
    );
  }

  static void addNote(
    BuildContext context,
    Product product,
    TextEditingController c,
    VoidCallback? onPressed,
  ) {
    showModalBottomSheet(
      context: context,
      enableDrag: true,
      isScrollControlled: true,
      showDragHandle: true,
      constraints: BoxConstraints(
          maxWidth: Constants.defaultMaxWidth,
          minHeight: MediaQuery.of(context).size.height * 0.9),
      shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(8), topRight: Radius.circular(8))),
      builder: (BuildContext context) {
        return AnimatedPadding(
          padding: EdgeInsets.only(
              left: 8.0,
              right: 8.0,
              bottom: MediaQuery.of(context).viewInsets.bottom),
          duration: const Duration(milliseconds: 100),
          curve: Curves.decelerate,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                Strings.addNoteToNote.trParams({'note': product.name ?? ''}),
                style: AppFont.componentLarge,
              ),
              const DottedDivider(color: AppColor.black10, width: 2, height: 3),
              AppDimen.h10.height,
              TextFormField(
                controller: c,
                keyboardType: TextInputType.multiline,
                minLines: 1,
                maxLines: 10,
                maxLength: 200,
                autofocus: true,
                style: AppFont.paragraphSmall,
                decoration: InputDecoration(
                    hintText: Strings.exampleNoteTo.tr,
                    border: InputBorder.none,
                    counter: PrimaryButton(
                        onPressed: onPressed,
                        width: AppDimen.h60 + AppDimen.h30,
                        text: Strings.save.tr,
                        borderRadius:
                            BorderRadius.all(Radius.circular(AppDimen.h10)),
                        type: PrimaryButtonType.type5)),
              ),
              10.0.height
            ],
          ),
        );
      },
    );
  }

  static void showModalBottomRedeemPeriod(
      {required BuildContext context, required DealData deal}) {
    showModalBottomSheet(
      context: context,
      enableDrag: true,
      isScrollControlled: true,
      constraints: BoxConstraints(
          minHeight: MediaQuery.of(context).size.height * 0.3,
          maxHeight: MediaQuery.of(context).size.height * 0.8,
          maxWidth: Constants.defaultMaxWidth),
      shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(8), topRight: Radius.circular(8))),
      builder: (BuildContext context) {
        return Padding(
          padding: EdgeInsets.only(
              left: 8.0,
              right: 8.0,
              bottom: MediaQuery.of(context).viewInsets.bottom),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Align(
                alignment: Alignment.topCenter,
                child: Icon(
                  Icons.drag_handle_rounded,
                  color: AppColor.black50,
                ),
              ),
              5.0.height,
              Text(Strings.redeemPeriod.tr, style: AppFont.componentSmallBold),
              5.0.height,
              const DottedDivider(
                color: AppColor.black5,
                width: 3,
                height: 1,
              ),
              5.0.height,
              Flexible(
                  child: deal.timeActive == null
                      ? ListView.builder(
                          shrinkWrap: true,
                          itemBuilder: (context, index) {
                            String day =
                                DealHelper.getPromoDays(deal: deal)[index];
                            return Column(
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  day.tr,
                                  style: AppFont.componentSmall,
                                ),
                                const Divider()
                              ],
                            );
                          },
                          itemCount: DealHelper.getPromoDays(deal: deal).length,
                        )
                      : ListView.builder(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          itemCount: deal.timeActive?.dayActive?.length,
                          itemBuilder: (context, index) {
                            var detail = deal.timeActive?.dayActive![index];
                            return Container(
                              margin: EdgeInsets.only(
                                  bottom: 15, top: index == 0 ? 10 : 0),
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  Icon(
                                    Icons.calendar_today,
                                    color: (detail ?? '').dayIsToday
                                        ? AppColor.black70
                                        : AppColor.black30,
                                    size: AppDimen.h20,
                                  ),
                                  AppDimen.h8.width,
                                  Text(
                                    "$detail"
                                            .capitalizeFirst
                                            ?.tr
                                            .toUpperCase() ??
                                        '',
                                    style: (detail ?? '').dayIsToday
                                        ? AppFont.componentSmallBold
                                        : AppFont.componentSmallBold
                                            .copyWith(color: AppColor.black30),
                                  ),
                                ],
                              ),
                            );
                          },
                        )),
              10.0.height,
            ],
          ),
        );
      },
    );
  }
}
