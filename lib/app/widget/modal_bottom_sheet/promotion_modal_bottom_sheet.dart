import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/helper/promotion_helper.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/values.dart';

import '../../../data/providers/db/database.dart';
import '../full_screen_dialog/promotion_detail/promotion_detail_fdialog.dart';

class PromotionModalBottomSheet {
  PromotionModalBottomSheet._();

  static void show(
      {required BuildContext context, required List<DealData> listPromo}) {
    listPromo = PromotionHelper.filterPromotionByActive(listPromo);
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      constraints: BoxConstraints(
          minHeight: MediaQuery.of(context).size.height * 0.3,
          maxWidth: Constants.defaultMaxWidth,
          maxHeight: MediaQuery.of(context).size.height * 0.8),
      builder: (context) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(
              Icons.drag_handle,
              color: AppColor.black70,
            ),
            Align(
                alignment: Alignment.centerLeft,
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8.0),
                  child: Text(
                    Strings.listPromotion.tr,
                    style: AppFont.componentSmallBold,
                  ),
                )),
            Flexible(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8.0),
                child: ListView.builder(
                  shrinkWrap: true,
                  itemCount: listPromo.length,
                  itemBuilder: (context, index) {
                    var promotion = listPromo[index];
                    var isActive = promotion.timeActive?.dayActive
                        ?.firstWhereOrNull((element) => element.dayIsToday);
                    return isActive == null
                        ? Stack(
                            children: [
                              Container(
                                padding: const EdgeInsets.all(8),
                                margin: EdgeInsets.only(
                                    bottom: 8, top: index == 0 ? 5 : 0),
                                decoration: BoxDecoration(
                                    border: Border.all(color: AppColor.black5),
                                    color: AppColor.black10,
                                    borderRadius: BorderRadius.circular(10.r)),
                                child: PromotionItem(
                                  promotion: promotion,
                                  index: index,
                                  disable: isActive == null,
                                ),
                              ),
                              Container(
                                  padding: const EdgeInsets.all(8),
                                  margin: EdgeInsets.only(
                                      bottom: 8, top: index == 0 ? 5 : 0),
                                  decoration: BoxDecoration(
                                      border:
                                          Border.all(color: AppColor.black5),
                                      borderRadius:
                                          BorderRadius.circular(10.r)),
                                  child: PromotionItem(
                                      promotion: promotion,
                                      index: index,
                                      disable: isActive == null))
                            ],
                          )
                        : Container(
                            padding: const EdgeInsets.all(8),
                            margin: EdgeInsets.only(
                                bottom: 8, top: index == 0 ? 5 : 0),
                            decoration: BoxDecoration(
                                border: Border.all(color: AppColor.black5),
                                borderRadius: BorderRadius.circular(10.r)),
                            child: PromotionItem(
                              promotion: promotion,
                              index: index,
                            ),
                          );
                  },
                ),
              ),
            )
          ],
        );
      },
    );
  }
}

class PromotionItem extends StatelessWidget {
  const PromotionItem(
      {super.key,
      required this.promotion,
      required this.index,
      this.disable = false});

  final DealData promotion;
  final int index;
  final bool disable;

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        SizedBox(
          width: AppDimen.h14,
          height: AppDimen.h14,
          child:
              Image.asset(promotion.promotionTypeName?.contains('free') == true
                  ? ImgStrings.couponFree
                  : promotion.promotionTypeName?.contains('discount') == true
                      ? ImgStrings.couponDiscount
                      : ImgStrings.couponSpecial),
        ),
        10.0.width,
        Text(
          promotion.name ?? '',
          style: disable
              ? AppFont.componentSmallBold.copyWith(color: AppColor.black20)
              : AppFont.componentSmallBold,
        ),
        const Spacer(),
        InkWell(
          onTapDown: (details) {
            PromotionDetailFullScreenDialog.show(
                context: context, promotion: promotion);
          },
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
            margin: const EdgeInsets.only(right: 8),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10.r),
                color: AppColor.black5),
            child: Text(
              Strings.details.tr,
              style: disable
                  ? AppFont.componentSmall.copyWith(color: AppColor.black20)
                  : AppFont.componentSmall,
            ),
          ),
        )
      ],
    );
  }
}
