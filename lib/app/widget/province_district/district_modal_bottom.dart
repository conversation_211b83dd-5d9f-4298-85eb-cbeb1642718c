import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/utils/logger.dart';
import 'package:mobile_crm/app/utils/toast.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/values.dart';

import '../../../data/models/province_model.dart';
import '../../../domain/repository/helper_repository.dart';
import '../app_dotted_separator.dart';
import '../custom_circular_progress_widget.dart';

class DistrictModalBottom extends StatelessWidget {
  const DistrictModalBottom(
      {super.key,
      required this.repo,
      required this.province,
      required this.callBackDistrict});

  final HelperRepository repo;
  final ProvinceModel province;
  final ValueChanged<ProvinceModel> callBackDistrict;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: AppDimen.h16),
          child: Text(
            Strings.selectADistrictOrCityBelow
                .trParams({"parent": province.name ?? ''}),
            style: AppFont.componentSmallBold,
          ),
        ),
        const DottedDivider(
          width: 5,
          height: 2,
          color: AppColor.black5,
        ),
        5.0.height,
        Flexible(
          child: FutureBuilder(
            future: repo.getRegency(provinceId: province.id ?? '0'),
            builder: (BuildContext context,
                AsyncSnapshot<List<ProvinceModel>> snapshot) {
              infoLogger('builderGetRegency snapshot ', snapshot);
              if (snapshot.hasError) {
                infoLogger('getRegency', 'snapshot error: ${snapshot.error}');
                Toast.show('Getting data failed, please check your connection!',
                    type: ToastType.error);
                Get.back();
              }
              if (snapshot.hasData) {
                return ListView.builder(
                  shrinkWrap: true,
                  itemCount: snapshot.data?.length ?? 0,
                  itemBuilder: (context, indexx) {
                    var itemm = snapshot.data![indexx];
                    return Padding(
                      padding: EdgeInsets.symmetric(horizontal: AppDimen.h16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          InkWell(
                            onTapDown: (details) {
                              callBackDistrict(itemm);
                              Get.back();
                            },
                            child: SizedBox(
                              width: MediaQuery.of(context).size.width,
                              child: Text(
                                "${itemm.name}",
                                style: AppFont.componentSmall,
                              ),
                            ),
                          ),
                          const Divider(
                            color: AppColor.black5,
                          )
                        ],
                      ),
                    );
                  },
                );
              }

              return const CustomCircularProgressIndicator(
                valueColor: AppColor.black90,
              );
            },
          ),
        ),
      ],
    );
  }
}
