import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/utils/logger.dart';
import 'package:mobile_crm/app/utils/toast.dart';
import 'package:mobile_crm/app/widget/province_district/district_modal_bottom.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/values.dart';

import '../../../data/models/province_model.dart';
import '../../../domain/repository/helper_repository.dart';
import '../app_dotted_separator.dart';
import '../custom_circular_progress_widget.dart';

class ProvinceModalBottom extends StatelessWidget {
  const ProvinceModalBottom({
    super.key,
    required this.repo,
    required this.callBackProvince,
    required this.callBackDistrict,
  });

  final HelperRepository repo;
  final ValueChanged<ProvinceModel> callBackProvince;
  final ValueChanged<ProvinceModel> callBackDistrict;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: AppDimen.h16),
          child: Text(
            Strings.selectAProvinceBelow.tr,
            style: AppFont.componentSmallBold,
          ),
        ),
        const DottedDivider(
          width: 5,
          height: 2,
          color: AppColor.black5,
        ),
        5.0.height,
        Flexible(
          child: FutureBuilder(
            future: repo.getProvince(),
            builder: (BuildContext context,
                AsyncSnapshot<List<ProvinceModel>> snapshot) {
              if (snapshot.hasError) {
                infoLogger('getProvince', 'snapshot error: ${snapshot.error}');
                Toast.show('Getting data failed, please try again!');
              }
              if (snapshot.hasData) {
                var listProv = snapshot.data;
                return ListView.builder(
                  shrinkWrap: true,
                  itemCount: listProv?.length ?? 0,
                  itemBuilder: (context, index) {
                    var item = listProv![index];
                    return InkWell(
                      onTapDown: (_) => DistrictModalBottom(
                        repo: repo,
                        province: item,
                        callBackDistrict: (value) {
                          callBackProvince(item);
                          callBackDistrict(value);
                          Get.back();
                        },
                      ).toModalBottomSheetNoMinHeight.of(context),
                      child: Padding(
                        padding: EdgeInsets.symmetric(horizontal: AppDimen.h16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              "${item.name}",
                              style: AppFont.componentSmall,
                            ),
                            const Divider(
                              color: AppColor.black5,
                            )
                          ],
                        ),
                      ),
                    );
                  },
                );
              } else {
                infoLogger('getProvince', 'snapshot has no data');
                // Get.back();
              }

              return const CustomCircularProgressIndicator(
                valueColor: AppColor.black90,
              );
            },
          ),
        ),
      ],
    );
  }
}
