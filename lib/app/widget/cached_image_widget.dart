// ignore_for_file: must_be_immutable

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:mobile_crm/core/extensions/color_extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:palette_generator/palette_generator.dart';

import 'shimmer_loading_widget.dart';

class CachedImageWidget extends StatelessWidget {
  const CachedImageWidget(
      {Key? key,
      this.imageUrl,
      this.fit = BoxFit.fill,
      this.height,
      this.width,
      this.errorWidget,
      this.repeat = ImageRepeat.noRepeat,
      this.disable = false,
      this.colorPalette,
      this.needColorFromImage = false,
      this.alignment = Alignment.center})
      : super(key: key);

  final String? imageUrl;
  final BoxFit fit;
  final ImageRepeat repeat;
  final double? width;
  final double? height;
  final bool disable;
  final LoadingErrorWidgetBuilder? errorWidget;
  final ValueChanged<List<PaletteColor>>? colorPalette;
  final bool needColorFromImage;
  final Alignment alignment;

  @override
  Widget build(BuildContext context) {
    generateColors();
    return (imageUrl == '' || imageUrl == 'null' || imageUrl?.isEmpty == true)
        ? errorWidget != null
            ? const Text('')
            : noImageBuild()
        : CachedNetworkImage(
            color: disable ? AppColor.disable : null,
            colorBlendMode: disable ? BlendMode.saturation : BlendMode.color,
            imageUrl: imageUrl ?? '',
            width: width,
            repeat: repeat,
            alignment: alignment,
            height: height,
            fit: fit,
            progressIndicatorBuilder: (context, url, progress) => ShimmerWidget(
                  width: width,
                  height: height,
                  radius: 5,
                ),
            errorWidget:
                errorWidget ?? (context, url, error) => noImageBuild());
  }

  void generateColors() async {
    if (needColorFromImage) {
      if (imageUrl == '' || imageUrl == 'null' || imageUrl?.isEmpty == true) {
        colorPalette!([]);
      } else {
        Future.delayed(
          const Duration(milliseconds: 250),
          () async {
            PaletteGenerator paletteGenerator =
                await PaletteGenerator.fromImageProvider(
                    NetworkImage(imageUrl ?? ''));
            colorPalette!(paletteGenerator.paletteColors);
          },
        );
      }
    }
  }

  Widget noImageBuild() => Container(
        width: width,
        height: height,
        decoration: BoxDecoration(color: HexColor('#f7f7f7')),
        child: Icon(Icons.broken_image_rounded,
            color: HexColor('#b0b0b0'), size: (width ?? 0) >= AppDimen.h24 ? AppDimen.h24 : width),
      );
}
