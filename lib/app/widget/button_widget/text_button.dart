import 'package:flutter/material.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/app_constants.dart';

class TextButtonWidget extends StatelessWidget {
  const TextButtonWidget(
      {Key? key,
      this.type = TextButtonType.type1,
      required this.onPressed,
      required this.text,
      this.child = const Text(''),
      this.width = 78})
      : super(key: key);
  final TextButtonType type;
  final VoidCallback? onPressed;
  final String text;
  final double width;
  final Widget child;

  @override
  Widget build(BuildContext context) {
    return type == TextButtonType.type1
        ? _type1(context)
        : type == TextButtonType.type2
            ? _type2(context)
            : _type3(context);
  }

  Widget _type1(BuildContext context) {
    return SizedBox(
      width: width,
      height: Constants.buttonHeightContent(),
      child: TextButton(
          onPressed: onPressed,
          child: Text(
            text,
            style: AppFont.componentSmall.copyWith(color: AppColor.black),
          )),
    );
  }

  Widget _type2(BuildContext context) {
    return FittedBox(
      child: TextButton(
          onPressed: onPressed,
          style: const ButtonStyle(splashFactory: NoSplash.splashFactory),
          child: child),
    );
  }

  Widget _type3(BuildContext context) {
    return SizedBox(
      width: width,
      height: Constants.buttonHeightContent(),
      child: TextButton(
          onPressed: onPressed,
          child: Text(
            text,
            style: AppFont.componentSmall.copyWith(color: AppColor.black),
          )),
    );
  }
}

enum TextButtonType {
  type1,
  type2,
  type3,
}
