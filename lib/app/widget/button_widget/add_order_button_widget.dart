import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/screen_wrapper/controller/wrapper_controller.dart';
import 'package:mobile_crm/core/extensions/color_extensions.dart';
import 'package:mobile_crm/core/theme/app_color.dart';
import 'package:mobile_crm/core/values/app_constants.dart';

import '../../../core/theme/app_dimen.dart';
import '../../../core/theme/app_font.dart';

class AddOrderButtonWidget extends GetView<WrapperController> {
  const AddOrderButtonWidget(
      {super.key,
      required this.onTap,
      required this.text,
      this.disable = false});
  final GestureTapCallback onTap;
  final String text;
  final bool disable;
  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      splashFactory: NoSplash.splashFactory,
      child: Container(
        padding: EdgeInsets.symmetric(
            vertical: AppDimen.h2, horizontal: AppDimen.h12),
        alignment: Alignment.center,
        height: AppDimen.h20,
        decoration: BoxDecoration(
            color: disable ? AppColor.whiteGrey : controller.getPrimaryColor(),
            borderRadius: BorderRadius.all(Constants.shapeRadius)),
        child: Text(
          text,
          style: AppFont.componentSmallBold.copyWith(
              color: disable
                  ? AppColor.black70
                  : controller
                      .getPrimaryColor()
                      .changeColorBasedOnBackgroundColor()
                      .$1),
        ),
      ),
    );
  }
}
