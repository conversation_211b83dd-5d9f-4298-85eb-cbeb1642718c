import 'package:flutter/material.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/values.dart';

class DisableButton extends StatelessWidget {
  const DisableButton(
      {Key? key,
      this.type = DisableButtonType.type1,
      required this.onPressed,
      required this.text,
      this.width = 78})
      : super(key: key);
  final DisableButtonType type;
  final VoidCallback? onPressed;
  final String text;
  final double width;

  @override
  Widget build(BuildContext context) {
    return type == DisableButtonType.type1
        ? _type1(context)
        : type == DisableButtonType.type2
            ? _type2(context)
            : _type3(context);
  }

  Widget _type1(BuildContext context) {
    return SizedBox(
      width: width,
      height: AppDimen.h24,
      child: ElevatedButton(
          onPressed: onPressed,
          style: ElevatedButton.styleFrom(
              backgroundColor: AppColor.ink04,
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(4))),
          child: Text(
            text,
            style: AppFont.componentSmall.copyWith(color: AppColor.ink02),
          )),
    );
  }

  Widget _type2(BuildContext context) {
    return SizedBox(
      width: width,
      height: 32,
      child: ElevatedButton(
          onPressed: onPressed,
          style: ElevatedButton.styleFrom(
              backgroundColor: AppColor.ink04,
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(4))),
          child: Text(
            text,
            style: AppFont.componentSmall.copyWith(color: AppColor.ink02),
          )),
    );
  }

  Widget _type3(BuildContext context) {
    return SizedBox(
      height: Constants.buttonHeightContent(),
      child: FilledButton.tonal(
          onPressed: onPressed,
          style: FilledButton.styleFrom(
              backgroundColor: AppColor.disable,
              splashFactory: NoSplash.splashFactory),
          child: Text(
            text,
            style: AppFont.componentSmall.copyWith(color: AppColor.ink02),
          )),
    );
  }
}

enum DisableButtonType {
  type1,
  type2,
  type3,
}
