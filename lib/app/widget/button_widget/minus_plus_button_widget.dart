import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/screen_wrapper/controller/wrapper_controller.dart';
import 'package:mobile_crm/core/extensions/widget_extensions.dart';
import 'package:mobile_crm/core/values/app_strings.dart';
import 'package:mobile_crm/data/models/unit_conversion_model.dart';

import '../../../core/theme/app_dimen.dart';
import '../../../core/theme/app_font.dart';

class MinusPlusButtonWidget extends GetView<WrapperController> {
  const MinusPlusButtonWidget(
      {super.key,
      this.onTapTools,
      required this.onTapMinus,
      required this.onTapPlus,
      this.onChanged,
      required this.textEditingController,
      this.listUnit,
      this.showTools = false,
      required this.middleText});
  final GestureTapCallback? onTapTools;
  final List<UnitConversionModel>? listUnit;
  final bool showTools;
  final GestureTapCallback onTapMinus;
  final GestureTapCallback onTapPlus;
  final String middleText;
  final TextEditingController textEditingController;
  final ValueChanged<String>? onChanged;
  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        InkWell(
          onTap: onTapMinus,
          splashFactory: InkRipple.splashFactory,
          borderRadius: BorderRadius.all(Radius.circular(AppDimen.h6)),
          splashColor: controller.getPrimaryColor().withAlpha(100),
          child: Container(
            padding: EdgeInsets.all(AppDimen.h2),
            alignment: Alignment.center,
            width: AppDimen.h20,
            height: AppDimen.h20,
            decoration: BoxDecoration(
                color: controller.getPrimaryColor().withAlpha(10),
                borderRadius: BorderRadius.all(Radius.circular(AppDimen.h6))),
            child: Text(
              "-",
              style: AppFont.componentSmallBold
                  .copyWith(color: controller.getPrimaryColor()),
            ),
          ),
        ),
        Container(
            width: AppDimen.h32,
            height: AppDimen.h20,
            alignment: Alignment.center,
            child: TextField(
              keyboardType: TextInputType.number,
              controller: textEditingController,
              maxLines: 1,
              onSubmitted: (value) => onChanged!(value),
              onEditingComplete: () => onChanged!(textEditingController.text),
              style: AppFont.componentSmallBold,
              autofocus: false,
              textAlignVertical: TextAlignVertical.center,
              textAlign: TextAlign.center,
              decoration: const InputDecoration(border: InputBorder.none),
            )),
        InkWell(
          onTap: onTapPlus,
          splashFactory: InkRipple.splashFactory,
          borderRadius: BorderRadius.all(Radius.circular(AppDimen.h6)),
          splashColor: controller.getPrimaryColor().withAlpha(100),
          child: Container(
            padding: EdgeInsets.all(AppDimen.h2),
            alignment: Alignment.center,
            width: AppDimen.h20,
            height: AppDimen.h20,
            decoration: BoxDecoration(
                color: controller.getPrimaryColor().withAlpha(10),
                borderRadius: BorderRadius.all(Radius.circular(AppDimen.h6))),
            child: Text(
              "+",
              style: AppFont.componentSmallBold
                  .copyWith(color: controller.getPrimaryColor()),
            ),
          ),
        ),
        Visibility(
          visible: showTools ? (listUnit?.isNotEmpty == true) : false,
          child: InkWell(
            onTap: onTapTools,
            splashFactory: InkRipple.splashFactory,
            borderRadius: BorderRadius.all(Radius.circular(AppDimen.h6)),
            splashColor: controller.getPrimaryColor().withAlpha(100),
            child: Container(
              padding: EdgeInsets.all(AppDimen.h2),
              alignment: Alignment.center,
              margin: EdgeInsets.only(left: AppDimen.h2),
              width: AppDimen.h20,
              height: AppDimen.h20,
              decoration: BoxDecoration(
                  color: controller.getPrimaryColor().withAlpha(10),
                  borderRadius: BorderRadius.all(Radius.circular(AppDimen.h6))),
              child: Icon(
                Icons.autorenew_rounded,
                color: controller.getPrimaryColor(),
                size: AppDimen.h14,
              ),
            ),
          ).tooltip(Strings.unitConversion.tr),
        ),
      ],
    ).fadeIn();
  }
}
