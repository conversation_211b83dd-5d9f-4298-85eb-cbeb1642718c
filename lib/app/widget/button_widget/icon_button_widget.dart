import 'package:flutter/material.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';

import '../../utils/utils.dart';

class IconButtonWidget extends StatelessWidget {
  const IconButtonWidget(
      {Key? key,
      this.image,
      this.color = Colors.black,
      this.onPressed,
      this.size,
      required this.icon})
      : super(key: key);
  final String? image;
  final Color? color;
  final IconData icon;
  final VoidCallback? onPressed;
  final double? size;

  @override
  Widget build(BuildContext context) {
    return image != null
        ? FutureBuilder(
            future: Utils.getColorFromImage(image ?? ''),
            builder: (context, snapshot) => IconButton(
                onPressed: onPressed,
                icon: Icon(
                  icon,
                  size: size,
                  color: snapshot.data?.changeColorBasedOnBackgroundColor().$1,
                )),
          )
        : IconButton(
            onPressed: onPressed,
            icon: Icon(
              icon,
              size: size,
              color: color?.changeColorBasedOnBackgroundColor().$1,
            ));
  }
}
