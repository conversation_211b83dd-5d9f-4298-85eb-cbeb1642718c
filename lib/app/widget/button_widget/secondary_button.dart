import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/screen_wrapper/controller/wrapper_controller.dart';
import 'package:mobile_crm/core/extensions/color_extensions.dart';
import 'package:mobile_crm/core/extensions/double_extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/app_constants.dart';
import 'package:mobile_crm/data/models/config_model.dart';

import '../../../core/values/app_strings.dart';

class SecondaryButton extends StatelessWidget {
  const SecondaryButton(
      {Key? key,
      this.type = SecondaryButtonType.type1,
      required this.onPressed,
      this.text = "",
      this.child = const Text(''),
      this.width = 78})
      : super(key: key);
  final SecondaryButtonType type;
  final VoidCallback? onPressed;
  final String text;
  final double width;
  final Widget child;

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<WrapperController>();
    final asset = controller.configApp.value.asset;
    return type == SecondaryButtonType.type1
        ? _type1(context, asset)
        : type == SecondaryButtonType.type2
            ? _type2(context)
            : type == SecondaryButtonType.type3
                ? _type3(context)
                : type == SecondaryButtonType.type1Disable
                    ? _type1Disable(context, asset)
                    : type == SecondaryButtonType.typeCircleAdd
                        ? _typeCircleAdd(context, asset)
                        : type == SecondaryButtonType.typeCircleMinus
                            ? _typeCircleMinus(context, asset)
                            : type == SecondaryButtonType.typeCircleTrash
                                ? _typeCircleRemove(context, asset)
                                : type == SecondaryButtonType.typeCircleNote
                                    ? _typeCircleNote(context, asset)
                                    : _type4(context, asset);
  }

  Widget _type1(BuildContext context, Asset? asset) {
    return SizedBox(
      width: width.h,
      height: AppDimen.h24,
      child: ElevatedButton(
          onPressed: onPressed,
          style: ElevatedButton.styleFrom(
              side: BorderSide(
                  width: 1,
                  color: asset != null
                      ? HexColor(asset.color?.primary ?? '')
                      : AppColor.black),
              backgroundColor: AppColor.white,
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8))),
          child: Text(
            text,
            textAlign: TextAlign.center,
            style: AppFont.componentSmall.copyWith(
                color: asset != null
                    ? HexColor(asset.color?.primary ?? '')
                    : AppColor.black),
          )),
    );
  }

  Widget _type1Disable(BuildContext context, Asset? asset) {
    return SizedBox(
      width: width.h,
      height: AppDimen.h24,
      child: ElevatedButton(
          onPressed: onPressed,
          style: ElevatedButton.styleFrom(
              side: const BorderSide(width: 1, color: AppColor.ink03),
              backgroundColor: AppColor.white,
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8))),
          child: Text(
            text,
            textAlign: TextAlign.center,
            style: AppFont.componentSmall.copyWith(color: AppColor.ink03),
          )),
    );
  }

  Widget _typeCircleAdd(BuildContext context, Asset? asset) {
    return SizedBox(
      width: AppDimen.h20,
      height: AppDimen.h20,
      child: ElevatedButton(
          onPressed: onPressed,
          style: ElevatedButton.styleFrom(
              padding: EdgeInsets.zero,
              side: BorderSide(
                  width: 1,
                  color: asset != null
                      ? HexColor(asset.color?.primary ?? '')
                      : AppColor.black),
              backgroundColor: AppColor.white,
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(50))),
          child: Icon(
            Icons.add,
            color: asset != null
                ? HexColor(asset.color?.primary ?? '')
                : AppColor.black,
            size: AppDimen.h14,
          )),
    );
  }

  Widget _typeCircleMinus(BuildContext context, Asset? asset) {
    return SizedBox(
      width: AppDimen.h20,
      height: AppDimen.h20,
      child: ElevatedButton(
          onPressed: onPressed,
          style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.all(0),
              side: BorderSide(
                  width: 1,
                  color: asset != null
                      ? HexColor(asset.color?.primary ?? '')
                      : AppColor.black),
              backgroundColor: AppColor.white,
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(50))),
          child: Icon(
            Icons.remove,
            color: asset != null
                ? HexColor(asset.color?.primary ?? '')
                : AppColor.black,
            size: AppDimen.h14,
          )),
    );
  }

  Widget _typeCircleRemove(BuildContext context, Asset? asset) {
    return SizedBox(
      width: AppDimen.h20,
      height: AppDimen.h20,
      child: ElevatedButton(
          onPressed: onPressed,
          style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.all(0),
              side: const BorderSide(width: 1, color: AppColor.utilityDanger),
              backgroundColor: AppColor.white,
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(50))),
          child: Icon(
            Icons.delete_rounded,
            color: AppColor.utilityDanger,
            size: AppDimen.h14,
          )),
    );
  }

  Widget _typeCircleNote(BuildContext context, Asset? asset) {
    return SizedBox(
      height: AppDimen.h20,
      child: ElevatedButton(
          onPressed: onPressed,
          style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 4),
              side: const BorderSide(width: 1, color: AppColor.black5),
              backgroundColor: AppColor.white,
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(50))),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 6.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.edit_note_rounded,
                  color: AppColor.black,
                  size: AppDimen.h14,
                ),
                AppDimen.h4.width,
                Text(
                  Strings.notes.tr,
                  textAlign: TextAlign.center,
                  style: AppFont.componentSmall.copyWith(color: AppColor.black),
                )
              ],
            ),
          )),
    );
  }

  Widget _type2(BuildContext context) {
    return SizedBox(
      width: width,
      height: 32.h,
      child: ElevatedButton(
          onPressed: onPressed,
          style: ElevatedButton.styleFrom(
              side: const BorderSide(width: 1, color: AppColor.black),
              backgroundColor: AppColor.white,
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8))),
          child: Text(
            text,
            textAlign: TextAlign.center,
            style: AppFont.componentSmall.copyWith(color: AppColor.black),
          )),
    );
  }

  Widget _type3(BuildContext context) {
    return SizedBox(
      width: width,
      height: 40.h,
      child: ElevatedButton(
          onPressed: onPressed,
          style: ElevatedButton.styleFrom(
              side: const BorderSide(width: 1, color: AppColor.black),
              backgroundColor: AppColor.white,
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8))),
          child: Text(
            text,
            textAlign: TextAlign.center,
            style: AppFont.componentSmall.copyWith(color: AppColor.black),
          )),
    );
  }

  Widget _type4(BuildContext context, Asset? asset) {
    return SizedBox(
      height: Constants.buttonHeightContent(),
      child: OutlinedButton(
          onPressed: onPressed,
          style: OutlinedButton.styleFrom(
              backgroundColor: AppColor.white,
              side: const BorderSide(color: AppColor.black30, width: 1)),
          child: child),
    );
  }
}

enum SecondaryButtonType {
  type1,
  type2,
  type3,
  type4,
  type1Disable,
  typeCircleAdd,
  typeCircleMinus,
  typeCircleTrash,
  typeCircleNote,
}
