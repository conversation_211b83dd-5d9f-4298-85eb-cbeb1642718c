import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/screen_wrapper/controller/wrapper_controller.dart';
import 'package:mobile_crm/core/extensions/double_extensions.dart';
import 'package:mobile_crm/core/theme/app_color.dart';

import '../../../core/theme/app_dimen.dart';
import '../../../core/theme/app_font.dart';

class NoteTextButtonWidget extends GetView<WrapperController> {
  const NoteTextButtonWidget(
      {super.key,
      required this.onTap,
      required this.text,
      this.disable = false});
  final GestureTapCallback onTap;
  final String text;
  final bool disable;
  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      splashFactory: NoSplash.splashFactory,
      child: Container(
        padding: EdgeInsets.symmetric(
            vertical: AppDimen.h2),
        alignment: Alignment.center,
        height: AppDimen.h20,
        child: Row(
          children: [
            Icon(Icons.edit_note_rounded, color: AppColor.black70,size: AppDimen.h14,),
            AppDimen.h4.width,
            Text(
              text,
              style: AppFont.componentSmallBold.copyWith(
                fontSize: 11.sp,
                  color: disable
                      ? AppColor.black70
                      : controller
                          .getPrimaryColor()),
            ),
          ],
        ),
      ),
    );
  }
}
