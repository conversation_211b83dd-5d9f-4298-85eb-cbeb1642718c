import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/screen_wrapper/controller/wrapper_controller.dart';
import 'package:mobile_crm/app/widget/custom_circular_progress_widget.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/app_constants.dart';

class PrimaryButton extends StatelessWidget {
  const PrimaryButton(
      {Key? key,
      this.type = PrimaryButtonType.type1,
      required this.onPressed,
      this.text = '',
      this.width = 100,
      this.child,
      this.color,
      this.borderRadius = BorderRadius.zero,
      this.side = BorderSide.none,
      this.loading = false})
      : super(key: key);
  final PrimaryButtonType type;
  final VoidCallback? onPressed;
  final String text;
  final double width;
  final Color? color;
  final Widget? child;
  final bool loading;
  final BorderRadiusGeometry borderRadius;
  final BorderSide side;

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<WrapperController>();
    final primary = controller.getPrimaryColor();
    return type == PrimaryButtonType.type1
        ? _type1(context, primary, color)
        : type == PrimaryButtonType.type2
            ? _type2(context)
            : type == PrimaryButtonType.type3
                ? _type3(context, primary)
                : type == PrimaryButtonType.type4
                    ? _type4(context, primary, color)
                    : type == PrimaryButtonType.type_2_1
                        ? _type2_1(context, primary, text)
                        : type == PrimaryButtonType.type4t
                            ? _type4t(context)
                            : _type5(context, primary, color, text, width,
                                loading, borderRadius, side, child);
  }

  Widget _type1(BuildContext context, Color primary, Color? color) {
    return SizedBox(
      width: width,
      height: 24.h,
      child: ElevatedButton(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
            backgroundColor: color ?? primary,
            splashFactory: NoSplash.splashFactory,
            shape:
                RoundedRectangleBorder(borderRadius: BorderRadius.circular(8))),
        child: child,
      ),
    );
  }

  Widget _type2(BuildContext context) {
    return SizedBox(
      width: width,
      height: 32.h,
      child: ElevatedButton(
          onPressed: onPressed,
          style: ElevatedButton.styleFrom(
              backgroundColor: AppColor.black,
              splashFactory: NoSplash.splashFactory,
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8))),
          child: Text(
            text,
            style: AppFont.componentSmall.copyWith(color: AppColor.white),
          )),
    );
  }

  Widget _type2_1(BuildContext context, Color primary, String text) {
    return SizedBox(
      width: width,
      height: 32.h,
      child: ElevatedButton(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
            backgroundColor: primary,
            splashFactory: NoSplash.splashFactory,
            shape:
                RoundedRectangleBorder(borderRadius: BorderRadius.circular(8))),
        child: loading
            ? const CircularProgressIndicator.adaptive(
                backgroundColor: AppColor.white,
              )
            : Text(
                text,
                style: AppFont.componentSmall.copyWith(
                  color: AppColor.white,
                ),
              ),
      ),
    );
  }

  Widget _type3(BuildContext context, Color primary) {
    return SizedBox(
      width: width,
      height: Constants.buttonHeightContent(),
      child: ElevatedButton(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
            backgroundColor: primary,
            splashFactory: NoSplash.splashFactory,
            shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16))),
        child: loading
            ? const CircularProgressIndicator.adaptive(
                backgroundColor: AppColor.white,
              )
            : Text(
                text,
                style: AppFont.componentSmall.copyWith(
                    color: primary.changeColorBasedOnBackgroundColor().$1),
              ),
      ),
    );
  }

  Widget _type4(BuildContext context, Color primary, Color? color) {
    return SizedBox(
      height: Constants.buttonHeightContent(),
      child: ElevatedButton(
          style: ButtonStyle(
            backgroundColor: MaterialStatePropertyAll(color ?? primary),
            splashFactory: NoSplash.splashFactory,
          ),
          onPressed: onPressed,
          child: child),
    );
  }

  Widget _type4t(BuildContext context) {
    return SizedBox(
      height: Constants.buttonHeightContent(),
      child: ElevatedButton(
        style: const ButtonStyle(
          padding: WidgetStatePropertyAll(
            EdgeInsets.symmetric(
              horizontal: 1.0,
            ),
          ),
          elevation: WidgetStatePropertyAll(0),
          backgroundColor: MaterialStatePropertyAll(AppColor.white),
          splashFactory: NoSplash.splashFactory,
        ),
        onPressed: onPressed,
        child: child,
      ),
    );
  }

  Widget _type5(
      BuildContext context,
      Color primary,
      Color? color,
      String text,
      double width,
      bool isLoading,
      BorderRadiusGeometry borderRadius,
      BorderSide side,
      Widget? child) {
    return SizedBox(
      height: Constants.buttonHeightContent(),
      width: width,
      child: ElevatedButton(
          style: ButtonStyle(
            shape: MaterialStatePropertyAll<RoundedRectangleBorder>(
                RoundedRectangleBorder(borderRadius: borderRadius, side: side)),
            backgroundColor: MaterialStatePropertyAll(color ?? primary),
            splashFactory: NoSplash.splashFactory,
          ),
          onPressed: isLoading ? () {} : onPressed,
          child: isLoading
              ? CustomCircularProgressIndicator(
                  valueColor: primary.changeColorBasedOnBackgroundColor().$1,
                  backgroundColor: primary)
              : child ??
                  Text(
                    text,
                    style: AppFont.componentSmallBold.copyWith(
                        color: primary.changeColorBasedOnBackgroundColor().$1),
                  )),
    );
  }
}

enum PrimaryButtonType { type1, type2, type_2_1, type3, type4, type4t, type5 }
