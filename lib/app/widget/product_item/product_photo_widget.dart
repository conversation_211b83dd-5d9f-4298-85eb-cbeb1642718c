import 'package:flutter/material.dart';
import 'package:mobile_crm/core/theme/app_dimen.dart';
import 'package:mobile_crm/core/values/app_constants.dart';

import '../cached_image_widget.dart';

class ProductPhotoWidget extends StatelessWidget {
  const ProductPhotoWidget(
      {super.key, this.imageUrl, this.disable = false, this.size});
  final String? imageUrl;
  final bool disable;
  final Size? size;
  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.all(Radius.circular(AppDimen.h6)),
      child: CachedImageWidget(
        imageUrl: imageUrl,
        width: size != null ? size?.width : Constants.productItemSize.width,
        height: size != null ? size?.height : Constants.productItemSize.height,
        disable: disable,
      ),
    );
  }
}
