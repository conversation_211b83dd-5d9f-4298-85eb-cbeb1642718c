import 'dart:math' as math;

import 'package:flutter/material.dart';
import 'package:mobile_crm/core/theme/themes.dart';

class SliverAppBarDelegateWidget extends SliverPersistentHeaderDelegate {
  SliverAppBarDelegateWidget({
    required this.minHeight,
    required this.maxHeight,
    required this.child,
  });

  final double minHeight;
  final double maxHeight;
  final Widget child;

  @override
  double get minExtent => minHeight;

  @override
  double get maxExtent => math.max(maxHeight, minHeight);

  @override
  Widget build(
      BuildContext context, double shrinkOffset, bool overlapsContent) {
    return SizedBox.expand(
        child: Container(color: AppColor.white, child: child));
  }

  @override
  bool shouldRebuild(SliverAppBarDelegateWidget oldDelegate) {
    return maxHeight != oldDelegate.maxHeight ||
        minHeight != oldDelegate.minHeight ||
        child != oldDelegate.child;
  }
}
