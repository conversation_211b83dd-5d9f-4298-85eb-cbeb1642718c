import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/screen_wrapper/controller/wrapper_controller.dart';
import 'package:mobile_crm/app/widget/cached_image_widget.dart';
import 'package:mobile_crm/core/extensions/color_extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:palette_generator/palette_generator.dart';

class SliverAppBarWidget extends StatelessWidget
    implements PreferredSizeWidget {
  const SliverAppBarWidget(
      {Key? key,
      required this.title,
      this.actions,
      this.customTitle,
      this.floating = false,
      this.pinned = false,
      this.snap = false,
      this.stretch = false,
      this.preferredSize = const Size.fromHeight(kToolbarHeight),
      this.elevation = 0})
      : super(key: key);
  final String title;
  @override
  final Size preferredSize; // default is 56.0
  final Widget? customTitle;
  final double elevation; // default is 56.0
  final List<Widget>? actions;
  final bool floating;
  final bool pinned;
  final bool snap;
  final bool stretch;

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<WrapperController>();
    List<PaletteColor> paletteList =
        [PaletteColor(controller.getPrimaryColor(), 10)].obs;
    return SliverAppBar(
      title: Text(
        title,
        style: AppFont.componentLarge.copyWith(
            color:
                (paletteList.firstOrNull?.color ?? controller.getPrimaryColor())
                    .changeColorBasedOnBackgroundColor().$1),
      ),
      actions: actions,
      pinned: pinned,
      floating: floating,
      stretch: stretch,
      snap: snap,
      elevation: elevation,
      systemOverlayStyle: SystemUiOverlayStyle(
          statusBarIconBrightness:
              (paletteList.firstOrNull?.color ?? controller.getPrimaryColor()).changeColorBasedOnBackgroundColor().$2,
          statusBarColor: Colors.transparent),
      backgroundColor: controller.getPrimaryColor(),
      flexibleSpace: Obx(
        () => (controller.configApp.value.asset == null)
            ? const Text('')
            : CachedImageWidget(
                height: kToolbarHeight + MediaQuery.of(context).padding.top,
                width: Get.size.width,
                needColorFromImage:
                    controller.configApp.value.asset?.toolbar_background !=
                        null,
                colorPalette: (value) => paletteList = value,
                fit: BoxFit.cover,
                imageUrl: controller.configApp.value.asset?.toolbar_background
                        .toString() ??
                    '',
                errorWidget: (context, url, error) => const SizedBox(),
              ),
      ),
    );
  }
}
