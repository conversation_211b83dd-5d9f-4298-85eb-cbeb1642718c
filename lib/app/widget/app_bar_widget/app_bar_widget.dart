import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/screen_wrapper/controller/wrapper_controller.dart';
import 'package:mobile_crm/core/extensions/color_extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:palette_generator/palette_generator.dart';

import '../cached_image_widget.dart';

class AppBarWidget extends StatelessWidget implements PreferredSizeWidget {
  const AppBarWidget(
      {Key? key,
      required this.title,
      this.actions,
      this.leading,
      this.customTitle,
      this.centerTitle = true,
      this.preferredSize = const Size.fromHeight(kToolbarHeight),
      this.automaticallyImplyLeading = true,
      this.bottom,
      this.backgroundColor,
      this.elevation = 0})
      : super(key: key);
  final String title;
  final Widget? customTitle;
  @override
  final Size preferredSize; // default is 56.0
  final double elevation; // default is 56.0
  final List<Widget>? actions;
  final Widget? leading;
  final bool centerTitle;
  final PreferredSizeWidget? bottom;
  final bool automaticallyImplyLeading;
  final Color? backgroundColor;

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<WrapperController>();
    List<PaletteColor> paletteList =
        [PaletteColor(controller.getPrimaryColor(), 10)].obs;
    return AppBar(
      title: customTitle ??
          ((backgroundColor == null)
              ? Obx(() {
                  return Text(
                    title,
                    style: AppFont.componentLarge.copyWith(
                        color: (paletteList.firstOrNull?.color)
                            ?.changeColorBasedOnBackgroundColor()
                            .$1),
                  );
                })
              : Text(
                  title,
                  style: AppFont.componentLarge.copyWith(
                      color: (backgroundColor)
                          ?.changeColorBasedOnBackgroundColor()
                          .$1),
                )),
      leading: leading,
      actions: actions,
      bottom: bottom,
      elevation: elevation,
      centerTitle: centerTitle,
      automaticallyImplyLeading: automaticallyImplyLeading,
      backgroundColor: backgroundColor ?? controller.getPrimaryColor(),
      systemOverlayStyle: SystemUiOverlayStyle(
          statusBarIconBrightness: (backgroundColor ??
                  paletteList.firstOrNull?.color ??
                  controller.getPrimaryColor())
              .changeColorBasedOnBackgroundColor()
              .$2,
          statusBarColor: Colors.transparent),
      flexibleSpace: backgroundColor == null
          ? Obx(
              () => (controller.configApp.value.asset?.toolbar_background ==
                      null)
                  ? const Text('')
                  : CachedImageWidget(
                      height: preferredSize.height +
                          MediaQuery.of(context).padding.top,
                      width: Get.size.width,
                      fit: BoxFit.cover,
                      needColorFromImage:
                          (customTitle == null && backgroundColor == null),
                      colorPalette: (value) => paletteList = value,
                      imageUrl: controller
                              .configApp.value.asset?.toolbar_background
                              .toString() ??
                          '',
                      errorWidget: (context, url, error) => const SizedBox(),
                    ),
            )
          : null,
    );
  }
}
