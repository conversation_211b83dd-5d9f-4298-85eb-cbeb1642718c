import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/utils/logger.dart';
import 'package:mobile_crm/app/utils/toast.dart';
import 'package:mobile_crm/app/widget/app_dotted_separator.dart';
import 'package:mobile_crm/app/widget/custom_circular_progress_widget.dart';
import 'package:mobile_crm/app/widget/province_district/province_modal_bottom.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/values.dart';
import 'package:mobile_crm/data/models/province_model.dart';
import 'package:mobile_crm/data/repository/helper_repository.dart';

import '../../modules/login/register/controllers/register_controller.dart';

class AppInputText extends StatefulWidget {
  const AppInputText(
      {Key? key,
      this.controller,
      required this.label,
      this.keyboardType = TextInputType.text,
      this.type = InputFormType.base,
      this.icon,
      this.dateCallBack,
      this.validator,
      this.genderCallBack})
      : super(key: key);
  final TextEditingController? controller;
  final TextInputType? keyboardType;
  final String label;
  final InputFormType type;
  final IconData? icon;
  final ValueChanged<DateTime>? dateCallBack;
  final ValueChanged<String>? genderCallBack;
  final FormFieldValidator<String>? validator;

  @override
  State<AppInputText> createState() => _AppInputTextState();
}

class _AppInputTextState extends State<AppInputText> {
  @override
  Widget build(BuildContext context) {
    return widget.type == InputFormType.base
        ? _type1()
        : widget.type == InputFormType.withIcon
            ? _type2()
            : widget.type == InputFormType.gender
                ? _gender()
                : widget.type == InputFormType.password
                    ? _password()
                    : widget.type == InputFormType.province
                        ? _province()
                        : widget.type == InputFormType.district
                            ? _district()
                            : widget.type == InputFormType.notes
                                ? _notes()
                                : _date();
  }

  Widget _type1() {
    return SizedBox(
      height: Constants.buttonHeight(),
      child: TextFormField(
          keyboardType: widget.keyboardType,
          controller: widget.controller,
          style: AppFont.componentMediumBold,
          validator: widget.validator,
          key: widget.key,
          minLines: 1,
          maxLines: widget.keyboardType == TextInputType.multiline ? 10 : 1,
          autofocus: false,
          decoration: AppInput.defaultTheme.copyWith(hintText: widget.label)),
    );
  }

  Widget _notes() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextFormField(
            key: widget.key,
            keyboardType: widget.keyboardType,
            validator: widget.validator,
            controller: widget.controller,
            style: AppFont.componentSmall,
            minLines: 1,
            maxLines: widget.keyboardType == TextInputType.multiline ? 10 : 1,
            autofocus: false,
            decoration: AppInput.defaultTheme.copyWith(hintText: widget.label)),
      ],
    );
  }

  Widget _type2() {
    return SizedBox(
      height: Constants.buttonHeight(),
      child: TextFormField(
          keyboardType: widget.keyboardType,
          controller: widget.controller,
          validator: widget.validator,
          style: AppFont.componentSmall,
          decoration: AppInput.defaultTheme.copyWith(
            hintText: widget.label,
            icon: Icon(
              widget.icon,
              color: AppColor.black20,
              size: AppDimen.h18,
            ),
          )),
    );
  }

  Widget _gender() {
    return SizedBox(
      height: Constants.buttonHeight(),
      child: LayoutBuilder(builder: (context, constraint) {
        List<String> itemStringList = ["-", "Female", "Male"];
        var chooseGender = "-".obs;
        return FormField(
          initialValue: false,
          enabled: true,
          builder: (FormFieldState<bool> field) {
            return InputDecorator(
              decoration: AppInput.defaultTheme.copyWith(
                hintText: widget.label,
                icon: Icon(
                  widget.icon,
                  color: AppColor.black20,
                  size: AppDimen.h18,
                ),
              ),
              child: DropdownButtonHideUnderline(
                child: ButtonTheme(
                  alignedDropdown: true,
                  child: Obx(() {
                    return DropdownButton<String>(
                      isExpanded: true,
                      value: chooseGender.value,
                      icon: const Icon(
                        Icons.arrow_drop_down_outlined,
                        color: AppColor.black50,
                      ),
                      style: AppFont.componentSmall,
                      onChanged: (String? gender) {
                        chooseGender.value =
                            (gender != 'Male' && gender != 'Female')
                                ? '-'
                                : gender ?? "-";
                        widget.genderCallBack!(chooseGender.value);
                      },
                      items: itemStringList
                          .map<DropdownMenuItem<String>>((String value) {
                        return DropdownMenuItem<String>(
                          key: value == '-'
                              ? const Key('genderNoneKey')
                              : value == 'Male'
                                  ? const Key('genderMaleKey')
                                  : const Key('genderFeMaleKey'),
                          value: value,
                          child: Text(
                            value.tr,
                          ),
                        );
                      }).toList(),
                    );
                  }),
                ),
              ),
            );
          },
        );
      }),
    );
  }

  Widget _date() {
    return InkWell(
      onTap: () async {
        DateTime? pickedDate = await showDatePicker(
            context: context,
            initialDate: DateTime.now(),
            firstDate: DateTime(1900),
            lastDate: DateTime.now(),
            initialDatePickerMode: DatePickerMode.year);
        widget.dateCallBack!(pickedDate ?? DateTime.now());
      },
      child: SizedBox(
        height: Constants.buttonHeight(),
        child: TextFormField(
            // initialValue: '2022-12-12',
            enabled: false,
            style: AppFont.componentSmall,
            validator: widget.validator,
            controller: widget.controller,
            decoration: AppInput.defaultTheme.copyWith(
              hintText: widget.label,
              icon: Icon(
                widget.icon,
                color: AppColor.black20,
                size: AppDimen.h18,
              ),
            )),
      ),
    );
  }

  Widget _district() {
    final hController = Get.find<HelperRepositoryIml>();
    final controller = Get.find<RegisterController>();
    return InkWell(
      onTap: () async => showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        useSafeArea: true,
        constraints:
            BoxConstraints(maxHeight: MediaQuery.of(context).size.height * 0.7),
        builder: (context) => Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Align(
                  alignment: Alignment.center,
                  child: Icon(
                    Icons.drag_handle,
                    color: AppColor.black20,
                    size: AppDimen.h18,
                  )),
              Text(
                "Select a district or city from \"${controller.province}\"",
                style: AppFont.componentSmallBold,
              ),
              const DottedDivider(
                width: 5,
                height: 2,
                color: AppColor.black5,
              ),
              5.0.height,
              Flexible(
                child: FutureBuilder(
                  future:
                      hController.getRegency(provinceId: controller.provinceId),
                  builder: (BuildContext context,
                      AsyncSnapshot<List<ProvinceModel>> snapshot) {
                    infoLogger('builderGetRegency snapshot ', snapshot);
                    if (snapshot.hasError) {
                      infoLogger(
                          'getRegency', 'snapshot error: ${snapshot.error}');
                      Toast.show(
                          'Getting data failed, please check your connection');
                      Get.back();
                    }
                    if (snapshot.hasData) {
                      return ListView.builder(
                        shrinkWrap: true,
                        itemCount: snapshot.data?.length ?? 0,
                        itemBuilder: (context, index) {
                          var item = snapshot.data![index];
                          return Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              InkWell(
                                onTapDown: (details) {
                                  controller.setProvinceAndDistrict(
                                      provId: controller.provinceId,
                                      dist: item.name ?? "",
                                      prov: controller.province);
                                  Get.back();
                                },
                                child: SizedBox(
                                  width: MediaQuery.of(context).size.width,
                                  child: Text(
                                    "${item.name}",
                                    style: AppFont.componentSmall,
                                  ),
                                ),
                              ),
                              const Divider()
                            ],
                          );
                        },
                      );
                    } else {
                      infoLogger('getRegency', 'snapshot has no data');
                      // Get.back();
                    }

                    return const CustomCircularProgressIndicator();
                  },
                ),
              ),
            ],
          ),
        ),
      ),
      child: SizedBox(
        height: Constants.buttonHeight(),
        child: TextFormField(
            enabled: false,
            style: AppFont.componentSmall,
            validator: widget.validator,
            controller: widget.controller,
            decoration: AppInput.defaultTheme.copyWith(
              hintText: widget.label,
              icon: Icon(
                widget.icon,
                color: AppColor.black20,
              ),
            )),
      ),
    );
  }

  Widget _province() {
    final hController = Get.find<HelperRepositoryIml>();
    final controller = Get.find<RegisterController>();
    return InkWell(
      onTap: () async {
        ProvinceModel provinceModel = ProvinceModel();
        ProvinceModalBottom(
          repo: hController,
          callBackProvince: (value) {
            // controller.
            provinceModel = value;
          },
          callBackDistrict: (value) {
            controller.setProvinceAndDistrict(
                provId: provinceModel.id.toString(),
                prov: provinceModel.name ?? '-',
                dist: value.name ?? '-');
            controller.province = provinceModel.name ?? '-';
            controller.provinceId = provinceModel.id ?? '1';
            controller.provinceIdObx.value = provinceModel.id ?? '1';
          },
        ).toModalBottomSheet.of(context);
      },

      //     showModalBottomSheet(
      //   context: context,
      //   isScrollControlled: true,
      //   useSafeArea: true,
      //   constraints: BoxConstraints(
      //       minHeight: MediaQuery.of(context).size.height * 0.4,
      //       maxHeight: MediaQuery.of(context).size.height * 0.8),
      //   builder: (context) => Padding(
      //     padding: const EdgeInsets.symmetric(horizontal: 8.0),
      //     child: Column(
      //       crossAxisAlignment: CrossAxisAlignment.start,
      //       mainAxisSize: MainAxisSize.min,
      //       children: [
      //         const Align(
      //             alignment: Alignment.center,
      //             child: Icon(
      //               Icons.drag_handle,
      //               color: AppColor.black70,
      //             )),
      //         Text(
      //           Strings.selectAProvinceBelow.tr,
      //           style: AppFont.componentSmallBold,
      //         ),
      //         const DottedDivider(
      //           width: 5,
      //           height: 2,
      //           color: AppColor.black5,
      //         ),
      //         5.0.height,
      //         Flexible(
      //           child: FutureBuilder(
      //             future: hController.getProvince(),
      //             builder: (BuildContext context,
      //                 AsyncSnapshot<List<ProvinceModel>> snapshot) {
      //               if (snapshot.hasData) {
      //                 var listProv = snapshot.data;
      //                 if ((snapshot.data?.length ?? 0) > 0) {
      //                   controller.provinces = listProv ?? [];
      //                 }
      //                 return ListView.builder(
      //                   shrinkWrap: true,
      //                   itemCount: controller.provinces.length,
      //                   itemBuilder: (context, index) {
      //                     var item = controller.provinces[index];
      //                     return InkWell(
      //                       onTapDown: (_) => showModalBottomSheet(
      //                         context: context,
      //                         isScrollControlled: true,
      //                         useSafeArea: true,
      //                         constraints: BoxConstraints(
      //                             maxHeight:
      //                                 MediaQuery.of(context).size.height * 0.6),
      //                         builder: (context) => Padding(
      //                           padding:
      //                               const EdgeInsets.symmetric(horizontal: 8.0),
      //                           child: Column(
      //                             crossAxisAlignment: CrossAxisAlignment.start,
      //                             mainAxisSize: MainAxisSize.min,
      //                             children: [
      //                               const Align(
      //                                   alignment: Alignment.center,
      //                                   child: Icon(
      //                                     Icons.drag_handle,
      //                                     color: AppColor.black70,
      //                                   )),
      //                               Text(
      //                                 Strings.selectADistrictOrCityBelow
      //                                     .trParams(
      //                                         {"parent": item.name ?? ''}),
      //                                 style: AppFont.componentSmallBold,
      //                               ),
      //                               const DottedDivider(
      //                                 width: 5,
      //                                 height: 2,
      //                                 color: AppColor.black5,
      //                               ),
      //                               5.0.height,
      //                               Flexible(
      //                                 child: FutureBuilder(
      //                                   future: hController.getRegency(
      //                                       provinceId: item.id ?? '0'),
      //                                   builder: (BuildContext context,
      //                                       AsyncSnapshot<List<ProvinceModel>>
      //                                           snapshot) {
      //                                     if (snapshot.hasData) {
      //                                       return ListView.builder(
      //                                         shrinkWrap: true,
      //                                         itemCount:
      //                                             snapshot.data?.length ?? 0,
      //                                         itemBuilder: (context, indexx) {
      //                                           var itemm =
      //                                               snapshot.data![indexx];
      //                                           return Column(
      //                                             crossAxisAlignment:
      //                                                 CrossAxisAlignment.start,
      //                                             children: [
      //                                               InkWell(
      //                                                 onTapDown: (details) {
      //                                                   controller
      //                                                       .setProvinceAndDistrict(
      //                                                           provId:
      //                                                               item.id ??
      //                                                                   "0",
      //                                                           dist: itemm
      //                                                                   .name ??
      //                                                               "",
      //                                                           prov:
      //                                                               item.name ??
      //                                                                   "");
      //                                                   Get.back();
      //                                                   Get.back();
      //                                                 },
      //                                                 child: SizedBox(
      //                                                   width: MediaQuery.of(
      //                                                           context)
      //                                                       .size
      //                                                       .width,
      //                                                   child: Text(
      //                                                     "${itemm.name}",
      //                                                     style: AppFont
      //                                                         .componentSmall,
      //                                                   ),
      //                                                 ),
      //                                               ),
      //                                               const Divider()
      //                                             ],
      //                                           );
      //                                         },
      //                                       );
      //                                     }
      //
      //                                     return const CustomCircularProgressIndicator(
      //                                       valueColor: AppColor.black90,
      //                                     );
      //                                   },
      //                                 ),
      //                               ),
      //                             ],
      //                           ),
      //                         ),
      //                       ),
      //                       child: Column(
      //                         crossAxisAlignment: CrossAxisAlignment.start,
      //                         children: [
      //                           Text(
      //                             "${item.name}",
      //                             style: AppFont.componentSmall,
      //                           ),
      //                           const Divider()
      //                         ],
      //                       ),
      //                     );
      //                   },
      //                 );
      //               }
      //
      //               return const CustomCircularProgressIndicator(
      //                 valueColor: AppColor.black90,
      //               );
      //             },
      //           ),
      //         ),
      //       ],
      //     ),
      //   ),
      // ),
      child: SizedBox(
        height: Constants.buttonHeight(),
        child: TextFormField(
            enabled: false,
            style: AppFont.componentSmall,
            validator: widget.validator,
            controller: widget.controller,
            decoration: AppInput.defaultTheme.copyWith(
              hintText: widget.label,
              icon: Icon(
                widget.icon,
                color: AppColor.black20,
              ),
            )),
      ),
    );
  }

  Widget _password() {
    return SizedBox(
      height: Constants.buttonHeight(),
      child: TextFormField(
          keyboardType: widget.keyboardType,
          obscureText: true,
          controller: widget.controller,
          validator: widget.validator,
          style: AppFont.componentMediumBold,
          decoration: AppInput.defaultTheme.copyWith(
            hintText: widget.label,
          )),
    );
  }
}

enum InputFormType {
  base,
  withIcon,
  gender,
  date,
  password,
  province,
  district,
  notes
}
