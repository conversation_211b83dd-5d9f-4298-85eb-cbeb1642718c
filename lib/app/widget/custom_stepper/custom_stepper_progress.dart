import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/screen_wrapper/controller/wrapper_controller.dart';
import 'package:mobile_crm/app/widget/shimmer_loading_widget.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/theme/app_color.dart';
import 'package:mobile_crm/core/theme/app_dimen.dart';
import 'package:mobile_crm/core/theme/app_font.dart';

class CustomStepperProgress extends StatelessWidget {
  final int setIndex;
  final List<IconData> steps;
  final String messages;

  const CustomStepperProgress(
      {Key? key,
      this.setIndex = 0,
      required this.messages,
      required this.steps})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<WrapperController>();
    return SizedBox(
      width: MediaQuery.of(context).size.width * 0.9,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Flexible(
            child: Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: List.generate(steps.length, (index) {
                return (index == (steps.length - 1))
                    ? Icon(
                        steps[index],
                        color: index == setIndex
                            ? AppColor.black90
                            : AppColor.black20,
                      )
                    : Expanded(
                        child: Row(
                          children: [
                            Icon(steps[index],
                                color: index > setIndex
                                    ? AppColor.black20
                                    : AppColor.black90),
                            Flexible(
                              child: index < setIndex
                                  ? Container(
                                      margin: const EdgeInsets.symmetric(
                                          horizontal: 8),
                                      height: AppDimen.h4,
                                      decoration: BoxDecoration(
                                          color: controller.getPrimaryColor(),
                                          borderRadius: BorderRadius.all(
                                              Radius.circular(AppDimen.h4))),
                                    )
                                  : index == setIndex
                                      ? Container(
                                          margin: const EdgeInsets.symmetric(
                                              horizontal: 8),
                                          child: ShimmerWidget(
                                            height: AppDimen.h4,
                                            width: MediaQuery.of(context)
                                                .size
                                                .width,
                                            baseColor:
                                                controller.getPrimaryColor(),
                                            highlightColor: controller
                                                .getPrimaryColor()
                                                .withAlpha(125),
                                          ),
                                        )
                                      : Container(
                                          margin: const EdgeInsets.symmetric(
                                              horizontal: 8),
                                          height: AppDimen.h4,
                                          decoration: BoxDecoration(
                                              color: index == setIndex
                                                  ? controller.getPrimaryColor()
                                                  : AppColor.black10,
                                              borderRadius: BorderRadius.all(
                                                  Radius.circular(
                                                      AppDimen.h4))),
                                        ),
                            )
                          ],
                        ),
                      );
              }),
            ),
          ),
          AppDimen.h16.height,
          Text(
            messages,
            style: AppFont.componentSmallBold,
          ),
        ],
      ),
    );
  }
}
