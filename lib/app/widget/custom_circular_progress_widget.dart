import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/core/theme/themes.dart';

class CustomCircularProgressIndicator extends StatelessWidget {
  const CustomCircularProgressIndicator(
      {Key? key,
      this.backgroundColor = AppColor.black5,
      this.valueColor = AppColor.white,
      this.isNeedToCenter = true})
      : super(key: key);
  final bool isNeedToCenter;
  final Color backgroundColor;
  final Color valueColor;

  @override
  Widget build(BuildContext context) {
    return isNeedToCenter ? Center(child: _custom()) : _custom();
  }

  Widget _custom() {
    return SizedBox(
        width: 16,
        height: 16,
        child: CircularProgressIndicator.adaptive(
          backgroundColor: GetPlatform.isIOS ? AppColor.white : backgroundColor,
          valueColor: AlwaysStoppedAnimation<Color>(valueColor),
        ));
  }
}
