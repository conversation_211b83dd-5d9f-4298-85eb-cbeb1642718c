// ignore_for_file: must_be_immutable

import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

class ShimmerWidget extends StatelessWidget {
  const ShimmerWidget(
      {Key? key,
      this.height,
      this.width,
      this.radius = 15,
      this.borderRadius,
      this.baseColor,
      this.highlightColor,
      this.shimmerDirection = ShimmerDirection.ltr})
      : super(key: key);

  final double? width, height;
  final double? radius;
  final BorderRadius? borderRadius;
  final Color? baseColor, highlightColor;
  final ShimmerDirection shimmerDirection;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: height,
      width: width,
      child: Shimmer.fromColors(
          enabled: true,
          direction: shimmerDirection,
          baseColor: baseColor ?? const Color.fromARGB(255, 224, 224, 224),
          highlightColor:
              highlightColor ?? const Color.fromARGB(255, 255, 255, 255),
          child: Container(
              height: height,
              width: width,
              decoration: BoxDecoration(
                  color: const Color.fromARGB(255, 255, 255, 255),
                  borderRadius: (borderRadius == null)
                      ? BorderRadius.all(Radius.circular(radius!))
                      : borderRadius),
              child: const Padding(
                padding: EdgeInsets.all(8),
              ))),
    );
  }
}
