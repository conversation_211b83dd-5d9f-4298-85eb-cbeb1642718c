import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/home/<USER>/home_controller.dart';
import 'package:mobile_crm/app/modules/screen_wrapper/controller/wrapper_controller.dart';
import 'package:mobile_crm/app/widget/shimmer_loading_widget.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/values.dart';
import 'package:mobile_crm/data/providers/db/database.dart';
import 'package:mobile_crm/data/services/analytics_service.dart';

import 'app_time_left.dart';
import 'cached_image_widget.dart';

class VoucherItemWidget extends StatelessWidget {
  const VoucherItemWidget(
      {Key? key, this.type = VoucherItemType.ungroup, required this.voucher})
      : super(key: key);
  final VoucherItemType type;
  final DealData voucher;

  @override
  Widget build(BuildContext context) {
    return type == VoucherItemType.ungroup
        ? _ungroup(context)
        : type == VoucherItemType.group
            ? _group(context)
            : type == VoucherItemType.loading
                ? _loading(context)
                : type == VoucherItemType.dealUnPublish
                    ? _dealUnPublished(context)
                    : _deal(context);
  }

  Widget _deal(BuildContext context) {
    final controller = Get.find<WrapperController>();
    ConfigData configModel = controller.configApp.value;
    AnalyticsService.observer.analytics.logEvent(
        name: "publish_deal", parameters: {"deal_name": voucher.name});
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: Constants.defaultPadding),
      child: Container(
        width: MediaQuery.of(context).size.width,
        height: AppDimen.h192 + AppDimen.h50,
        margin: const EdgeInsets.only(bottom: 10),
        decoration: BoxDecoration(boxShadow: const [
          BoxShadow(
              color: AppColor.black5,
              blurRadius: 1,
              spreadRadius: 1,
              offset: Offset(0, 1))
        ], borderRadius: BorderRadius.all(Radius.circular(10.r))),
        child: ClipRRect(
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(10.r), topRight: Radius.circular(10.r)),
          child: Column(
            children: [
              SizedBox(
                height: AppDimen.h192,
                width: Constants.defaultMaxWidth,
                child: ClipRRect(
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(12.0),
                      topRight: Radius.circular(12.0),
                    ),
                    child: CachedImageWidget(
                      imageUrl: voucher.photo.toString(),
                      fit: BoxFit.fill,
                      height: AppDimen.h192,
                      width: Constants.defaultMaxWidth,
                      disable: ((voucher.maximumRedeem ?? 0) -
                              (voucher.totalRedeem ?? 0) <=
                          0),
                    )),
              ),
              Container(
                padding: EdgeInsets.symmetric(
                    horizontal: AppDimen.h8, vertical: AppDimen.h4),
                height: AppDimen.h50,
                decoration: BoxDecoration(
                    color: AppColor.white,
                    borderRadius: BorderRadius.only(
                        bottomLeft: Radius.circular(10.r),
                        bottomRight: Radius.circular(10.r))),
                width: Constants.defaultMaxWidth,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Flexible(
                          child: Text(
                            "${voucher.name}",
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            style: AppFont.componentMediumBold,
                          ),
                        ),
                        5.0.width,
                        Visibility(
                            visible: ((voucher.maximumRedeem ?? 0) -
                                    (voucher.totalRedeem ?? 0) <=
                                25),
                            child: ((voucher.maximumRedeem ?? 0) -
                                        (voucher.totalRedeem ?? 0)) ==
                                    0
                                ? Text(
                                    Strings.promoQuotaIsOver.tr,
                                    style: AppFont.componentSmallBold.copyWith(
                                        color: AppColor.utilityDanger),
                                  )
                                : Container(
                                    padding: EdgeInsets.symmetric(
                                        horizontal: AppDimen.h8,
                                        vertical: AppDimen.h2),
                                    decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(5),
                                        color: controller.getPrimaryColor()),
                                    child: ((voucher.maximumRedeem ?? 0) -
                                                (voucher.totalRedeem ?? 0))
                                            .isNegative
                                        ? Text(
                                            Strings.overQuota.tr,
                                            style: AppFont.componentSmall
                                                .copyWith(
                                                    color: AppColor.white),
                                          )
                                        : Text(
                                            "${(voucher.maximumRedeem ?? 0) - (voucher.totalRedeem ?? 0)} Left",
                                            style: AppFont.componentSmall
                                                .copyWith(
                                                    color: AppColor.white),
                                          ),
                                  ))
                      ],
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        voucher.voucherPriceType == "money"
                            ? Text(
                                "Rp ${voucher.dealsValue.toCurrency}",
                                style: AppFont.componentSmall,
                              )
                            : voucher.voucherPriceType == "point"
                                ? Text(
                                    "${voucher.dealsValue} ${configModel.language?.point} Point",
                                    style: AppFont.componentSmall,
                                  )
                                : Text(
                                    Strings.free.tr,
                                    style: AppFont.componentSmall,
                                  ),
                        Text(
                          voucher.timeActive?.endPromotionDate.toTimeLeft ?? '',
                          style: AppFont.componentSmall,
                        ),
                      ],
                    )
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _dealUnPublished(BuildContext context) {
    final controller = Get.find<WrapperController>();
    final c = Get.find<HomeController>();
    ConfigData configModel = controller.configApp.value;
    (bool, String, bool, bool, Duration) publishdetail =
        c.isPromoBeenPublished(voucher.publishDate ?? 0);
    var isAlreadyAddNotify = voucher.notify == "on" ? true.obs : false.obs;
    AnalyticsService.observer.analytics.logEvent(
        name: "unpublish_deal", parameters: {"deal_name": voucher.name});
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: Constants.defaultPadding),
      child: Container(
        height: AppDimen.h192 + AppDimen.h50,
        margin: const EdgeInsets.only(bottom: 10),
        decoration: BoxDecoration(boxShadow: const [
          BoxShadow(
              color: AppColor.black5,
              blurRadius: 1,
              spreadRadius: 1,
              offset: Offset(0, 1))
        ], borderRadius: BorderRadius.all(Radius.circular(10.r))),
        child: ClipRRect(
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(10.r), topRight: Radius.circular(10.r)),
          child: Column(
            children: [
              SizedBox(
                height: Constants.voucherContainerWidth(context),
                width: double.infinity,
                child: ClipRRect(
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(12.0),
                      topRight: Radius.circular(12.0),
                    ),
                    child: Stack(
                      children: [
                        CachedImageWidget(
                          imageUrl: voucher.photo.toString(),
                          fit: BoxFit.fill,
                          height: Constants.voucherContainerWidth(context),
                          width: double.infinity,
                          disable: !publishdetail.$1,
                        ),
                        Align(
                          alignment: Alignment.bottomRight,
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 8, vertical: 4),
                            decoration: const BoxDecoration(
                                color: AppColor.black90,
                                borderRadius: BorderRadius.only(
                                    topLeft: Radius.circular(10))),
                            child: Text(
                              voucher.publishDate.toDateAndTime,
                              style: AppFont.componentSmallBold
                                  .copyWith(color: AppColor.white),
                            ),
                          ),
                        ),
                        Container(
                          padding: const EdgeInsets.all(16),
                          height: Constants.voucherContainerWidth(context),
                          width: double.infinity,
                          decoration: const BoxDecoration(
                              gradient: LinearGradient(
                                  colors: [AppColor.black50, AppColor.black10],
                                  begin: Alignment.topCenter,
                                  end: Alignment.bottomCenter)),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              publishdetail.$3
                                  ? Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          publishdetail.$4
                                              ? Strings.availableIn.tr
                                              : Strings.availableOn.tr,
                                          style: AppFont.heading5
                                              .copyWith(color: AppColor.white),
                                        ),
                                        TimeLeftWidget(
                                          time: voucher.publishDate ?? 0,
                                          onTimeChanged: (Duration value) {
                                            if (value.inSeconds.isEqual(0)) {
                                              c.fetchData();
                                            }
                                          },
                                          textStyle: AppFont.heading4
                                              .copyWith(color: AppColor.white),
                                        ),
                                      ],
                                    )
                                  : Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          publishdetail.$4
                                              ? Strings.availableIn.tr
                                              : Strings.availableOn.tr,
                                          style: AppFont.heading5
                                              .copyWith(color: AppColor.white),
                                        ),
                                        Text(
                                          publishdetail.$2,
                                          style: AppFont.heading4
                                              .copyWith(color: AppColor.white),
                                        ),
                                      ],
                                    ),
                              Obx(
                                () => (isAlreadyAddNotify.value ||
                                        voucher.notify == "on")
                                    ? const SizedBox()
                                    : IconButton(
                                        autofocus: true,
                                        tooltip: "Notify Me",
                                        onPressed: () async => await c
                                            .createNotificationForDeal(voucher)
                                            .whenComplete(() =>
                                                isAlreadyAddNotify.value =
                                                    true),
                                        icon: const Icon(
                                          Icons.notifications_active,
                                          color: AppColor.black70,
                                        ),
                                        style: const ButtonStyle(
                                            elevation:
                                                MaterialStatePropertyAll(5),
                                            backgroundColor:
                                                MaterialStatePropertyAll<Color>(
                                                    AppColor.white)),
                                      ),
                              )
                            ],
                          ),
                        )
                      ],
                    )),
              ),
              Container(
                padding: EdgeInsets.symmetric(
                    horizontal: AppDimen.h8, vertical: AppDimen.h6),
                height: AppDimen.h50,
                decoration: BoxDecoration(
                    color: AppColor.white,
                    borderRadius: BorderRadius.only(
                        bottomLeft: Radius.circular(10.r),
                        bottomRight: Radius.circular(10.r))),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          "${voucher.name}",
                          style: AppFont.componentMediumBold,
                        ),
                        Container(
                          padding: EdgeInsets.symmetric(
                              horizontal: AppDimen.h8, vertical: AppDimen.h2),
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(5),
                              color: controller.getPrimaryColor()),
                          child: Text(
                            "Only ${voucher.maximumRedeem}",
                            style: AppFont.componentSmall
                                .copyWith(color: AppColor.white),
                          ),
                        )
                      ],
                    ),
                    voucher.voucherPriceType == "money"
                        ? Text(
                            "Rp ${voucher.dealsValue.toCurrency}",
                            style: AppFont.componentSmall,
                          )
                        : voucher.voucherPriceType == "point"
                            ? Text(
                                "${voucher.dealsValue} ${configModel.language?.point} Point",
                                style: AppFont.componentSmall,
                              )
                            : Text(
                                Strings.free.tr,
                                style: AppFont.componentSmall,
                              ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _ungroup(BuildContext context) {
    return Container(
        margin: const EdgeInsets.symmetric(horizontal: 12),
        child: Container(
          width: MediaQuery.of(context).size.width,
          height: AppDimen.h192 + AppDimen.h46,
          margin: EdgeInsets.symmetric(horizontal: AppDimen.h2),
          decoration: BoxDecoration(boxShadow: const [
            BoxShadow(
                color: AppColor.black5,
                blurRadius: 1,
                spreadRadius: 1,
                offset: Offset(0, 1))
          ], borderRadius: BorderRadius.all(Radius.circular(10.r))),
          child: ClipRRect(
            borderRadius: BorderRadius.only(
                topLeft: Radius.circular(10.r),
                topRight: Radius.circular(10.r)),
            child: Column(children: [
              SizedBox(
                width: MediaQuery.of(context).size.width,
                height: AppDimen.h192,
                child: Hero(
                  tag: "voucher${voucher.promotionFkId}",
                  child: CachedImageWidget(
                    imageUrl: voucher.photo ?? '',
                    height: Constants.voucherContainerWidth(context),
                    width: double.infinity,
                    fit: BoxFit.fill,
                  ),
                ),
              ),
              Container(
                padding: EdgeInsets.only(
                    left: AppDimen.h8, right: AppDimen.h8, top: AppDimen.h8),
                width: MediaQuery.of(context).size.width,
                height: AppDimen.h46,
                decoration: BoxDecoration(
                    color: AppColor.white,
                    borderRadius: BorderRadius.only(
                        bottomLeft: Radius.circular(10.r),
                        bottomRight: Radius.circular(10.r))),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Text(
                      "${voucher.name}",
                      style: AppFont.paragraphSmallBold,
                    ),
                    Text(
                      Strings.voucherOnlyValid.trParams(
                          {'time': voucher.endPromotionDate.toDateAndTime}),
                      style: AppFont.paragraphSmall.copyWith(fontSize: 10.sp),
                    )
                  ],
                ),
              ),
            ]),
          ),
        ));
  }

  Widget _group(BuildContext context) {
    return SizedBox(
      width: MediaQuery.of(context).size.width,
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 8.0),
        child: Stack(
          children: [
            Container(
              margin: EdgeInsets.only(
                  top: AppDimen.h14, left: AppDimen.h8, right: AppDimen.h8),
              width: MediaQuery.of(context).size.width,
              height: (AppDimen.h192 + AppDimen.h64),
              decoration: BoxDecoration(
                  color: Colors.transparent,
                  borderRadius: BorderRadius.only(
                      bottomLeft: Radius.circular(10.r),
                      bottomRight: Radius.circular(10.r))),
            ),
            Container(
              margin: EdgeInsets.only(
                  top: AppDimen.h12, left: AppDimen.h8, right: AppDimen.h8),
              width: MediaQuery.of(context).size.width,
              height: (AppDimen.h192 + AppDimen.h64),
              decoration: BoxDecoration(
                  color: AppColor.whiteGrey,
                  boxShadow: const [
                    BoxShadow(
                        color: AppColor.black5,
                        blurRadius: 1,
                        spreadRadius: 1,
                        offset: Offset(0, 1))
                  ],
                  borderRadius: BorderRadius.only(
                      bottomLeft: Radius.circular(10.r),
                      bottomRight: Radius.circular(10.r))),
            ),
            Container(
              margin: EdgeInsets.only(
                  top: AppDimen.h14, left: AppDimen.h4, right: AppDimen.h4),
              width: MediaQuery.of(context).size.width,
              height: (AppDimen.h192 + AppDimen.h64) - AppDimen.h6,
              decoration: BoxDecoration(
                  color: AppColor.whiteGrey,
                  boxShadow: const [
                    BoxShadow(
                        color: AppColor.black5,
                        blurRadius: 1,
                        spreadRadius: 1,
                        offset: Offset(0, 1))
                  ],
                  borderRadius: BorderRadius.only(
                      bottomLeft: Radius.circular(10.r),
                      bottomRight: Radius.circular(10.r))),
            ),
            Container(
              padding: EdgeInsets.all(4.h),
              margin: EdgeInsets.only(
                  top: AppDimen.h16, left: AppDimen.h2, right: AppDimen.h2),
              width: MediaQuery.of(context).size.width,
              height: (AppDimen.h192 + AppDimen.h64) - AppDimen.h12,
              decoration: BoxDecoration(
                  color: AppColor.whiteGrey,
                  boxShadow: const [
                    BoxShadow(
                        color: AppColor.black5,
                        blurRadius: 1,
                        spreadRadius: 1,
                        offset: Offset(0, 1))
                  ],
                  borderRadius: BorderRadius.only(
                      bottomLeft: Radius.circular(10.r),
                      bottomRight: Radius.circular(10.r))),
              child: Align(
                alignment: Alignment.bottomCenter,
                child: Text(
                  "${voucher.groupTotal} vouchers",
                  style: AppFont.componentSmallBold
                      .copyWith(fontSize: 11.sp, color: AppColor.black70),
                ),
              ),
            ),
            Container(
              width: MediaQuery.of(context).size.width,
              height: AppDimen.h192 + AppDimen.h46,
              margin: EdgeInsets.symmetric(horizontal: AppDimen.h2),
              decoration: BoxDecoration(boxShadow: const [
                BoxShadow(
                    color: AppColor.black5,
                    blurRadius: 1,
                    spreadRadius: 1,
                    offset: Offset(0, 1))
              ], borderRadius: BorderRadius.all(Radius.circular(10.r))),
              child: ClipRRect(
                borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(10.r),
                    topRight: Radius.circular(10.r)),
                child: Column(children: [
                  SizedBox(
                    width: MediaQuery.of(context).size.width,
                    height: AppDimen.h192,
                    child: Hero(
                      tag: "voucher${voucher.promotionFkId}",
                      child: CachedImageWidget(
                        imageUrl: voucher.photo ?? '',
                        height: Constants.voucherContainerWidth(context),
                        width: double.infinity,
                        fit: BoxFit.fill,
                      ),
                    ),
                  ),
                  Container(
                    padding: EdgeInsets.only(left: 8.h, right: 8.h, top: 8.h),
                    width: MediaQuery.of(context).size.width,
                    height: AppDimen.h46,
                    decoration: BoxDecoration(
                        color: AppColor.white,
                        borderRadius: BorderRadius.only(
                            bottomLeft: Radius.circular(10.r),
                            bottomRight: Radius.circular(10.r))),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        Text(
                          "${voucher.name}",
                          style: AppFont.paragraphSmallBold,
                        ),
                        Text(
                          Strings.voucherOnlyValid.trParams(
                              {'time': voucher.endPromotionDate.toDateAndTime}),
                          style:
                              AppFont.paragraphSmall.copyWith(fontSize: 10.sp),
                        )
                      ],
                    ),
                  ),
                ]),
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget _loading(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: Constants.defaultPadding),
      child: Container(
        decoration: const BoxDecoration(
          color: AppColor.white,
          borderRadius: BorderRadius.all(Radius.circular(12)),
        ),
        child: Column(
          children: [
            SizedBox(
              height: Constants.voucherContainerWidth(context),
              width: double.infinity,
              child: ClipRRect(
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(12.0),
                    topRight: Radius.circular(12.0),
                  ),
                  child: ShimmerWidget(
                      width: double.infinity,
                      height: Constants.voucherContainerWidth(context))),
            ),
            Container(
              padding: EdgeInsets.all(Constants.defaultPadding),
              decoration: const BoxDecoration(
                  borderRadius: BorderRadius.only(
                      bottomLeft: Radius.circular(12),
                      bottomRight: Radius.circular(12))),
              width: double.infinity,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ShimmerWidget(width: 100.h, height: 20.h),
                  const Divider(),
                  ShimmerWidget(width: 50.h, height: 20.h),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

enum VoucherItemType { ungroup, group, deal, dealUnPublish, loading }
