import 'package:flutter/material.dart';
import 'package:get/get.dart';

class LabeledCheckbox extends StatelessWidget {
  const LabeledCheckbox(
      {super.key,
      required this.label,
      required this.padding,
      this.value = false,
      required this.onChanged});

  final Widget label;
  final EdgeInsets padding;
  final bool value;
  final ValueChanged<bool> onChanged;

  @override
  Widget build(BuildContext context) {
    var valueState = value.obs;
    return InkWell(
      onTap: () {
        onChanged(!valueState.value);
        valueState.value = !valueState.value;
      },
      splashFactory: NoSplash.splashFactory,
      child: Padding(
        padding: padding,
        child: Row(
          children: <Widget>[
            Expanded(child: label),
            Obx(() {
              return Checkbox(
                      value: valueState.value,
                      onChanged: (bool? newValue) {
                        onChanged(newValue!);
                        valueState.value = newValue;
                      },
                    );
            }),
          ],
        ),
      ),
    );
  }
}
