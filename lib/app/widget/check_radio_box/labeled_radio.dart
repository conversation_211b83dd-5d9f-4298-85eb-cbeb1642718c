import 'package:flutter/material.dart';

class LabeledRadio<T> extends StatelessWidget {
  const LabeledRadio({
    super.key,
    required this.label,
    required this.padding,
    required this.defaultValue,
    required this.groupValue,
    required this.onChanged,
  });

  final Widget label;
  final EdgeInsets padding;
  final T defaultValue;
  final T groupValue;
  final ValueChanged<T> onChanged;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        onChanged(defaultValue);
      },
      child: Padding(
        padding: padding,
        child: Row(
          children: <Widget>[
            Expanded(child: label),
            Radio<T>(
              value: defaultValue,
              onChanged: (T? newValue) {
                onChanged(newValue as T);
              },
              groupValue: groupValue,
            ),
          ],
        ),
      ),
    );
  }
}
