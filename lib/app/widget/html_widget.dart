import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mobile_crm/app/utils/url_helper.dart';
import 'package:mobile_crm/core/extensions/double_extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';

class HtmlWidget extends StatelessWidget {
  const HtmlWidget(
      {Key? key,
      required this.data,
      this.disable = false,
      this.disableColorSwitch = false,
      this.maxLines,
      this.fontSize = 14})
      : super(key: key);

  final String data;
  final bool disable;
  final bool disableColorSwitch;
  final int? maxLines;
  final double fontSize;

  @override
  Widget build(BuildContext context) {
    return data == ''
        ? 0.0.height
        : Html(
            data: data,
            onLinkTap: (url, attributes, element) => openUrl(url ?? ''),
            style: {
              "*": AppFont.htmlStyle.copyWith(
                  textAlign: TextAlign.left,
                  textOverflow: TextOverflow.ellipsis,
                  alignment: Alignment.centerLeft,
                  maxLines: maxLines,
                  fontWeight: FontWeight.w500,
                  fontSize: FontSize(fontSize.sp),
                  color: (disable)
                      ? disableColorSwitch
                          ? AppColor.white
                          : AppColor.black70
                      : AppColor.black)
            },
          );
  }
}
