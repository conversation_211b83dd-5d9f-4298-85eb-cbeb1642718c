// ignore_for_file: must_be_immutable
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:lottie/lottie.dart';
import 'package:mobile_crm/app/widget/button_widget/button_widget.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/theme/app_font.dart';
import 'package:mobile_crm/core/values/app_img_strings.dart';
import 'package:mobile_crm/core/values/app_strings.dart';

class AppPageEmpty extends StatelessWidget {
  const AppPageEmpty(
      {Key? key,
      required this.func,
      this.reason = Strings.errorReason,
      this.buttonText = "Refresh",
      this.hideButton = false})
      : super(key: key);

  final VoidCallback func;
  final String reason;
  final String buttonText;
  final bool hideButton;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Lottie.asset(
          ImgStrings.lottieEmptyBox,
          animate: true,
          repeat: true,
          width: 150.h,
        ),
        Flexible(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 10),
            child: Text(
              reason,
              textAlign: TextAlign.center,
              style: AppFont.paragraphSmall,
              maxLines: 2,
            ),
          ),
        ),
        25.0.height,
        Visibility(
          visible: !hideButton,
          child: PrimaryButton(
            onPressed: (func),
            text: buttonText,
            width: 100.h,
            type: PrimaryButtonType.type3,
          ),
        ),
      ],
    );
  }
}
