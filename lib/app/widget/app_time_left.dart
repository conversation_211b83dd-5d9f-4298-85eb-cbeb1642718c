import 'dart:async';

import 'package:flutter/cupertino.dart';

class TimeLeftWidget extends StatefulWidget {
  const TimeLeftWidget(
      {Key? key,
      required this.time,
      required this.onTimeChanged,
      required this.textStyle})
      : super(key: key);

  final int time;
  final ValueChanged<Duration> onTimeChanged;
  final TextStyle textStyle;

  @override
  TimeLeftWidgetState createState() => TimeLeftWidgetState();
}

class TimeLeftWidgetState extends State<TimeLeftWidget> {
  Timer? _timer;
  Duration _duration = const Duration(seconds: 1);

  void reset() {
    _timer?.cancel();
    var now = DateTime.now();
    var end = DateTime.fromMillisecondsSinceEpoch(widget.time);
    var diff = end.difference(now);
    _duration = Duration(seconds: diff.inSeconds);
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        _duration = _duration - const Duration(seconds: 1);
        widget.onTimeChanged(_duration);
        if (_duration.inSeconds == 0) {
          timer.cancel();
        }
      });
    });
  }

  @override
  void initState() {
    super.initState();
    reset();
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    String timerText =
        '${_duration.inHours.remainder(24).toString().padLeft(2, '0')}:${_duration.inMinutes.remainder(60).toString().padLeft(2, '0')}:${_duration.inSeconds.remainder(60).toString().padLeft(2, '0')}';

    if (_duration.inHours.remainder(24) != 0) {
      timerText =
          '${_duration.inHours.remainder(24).toString().padLeft(2, '0')}:${_duration.inMinutes.remainder(60).toString().padLeft(2, '0')}:${_duration.inSeconds.remainder(60).toString().padLeft(2, '0')}';
    } else {
      timerText =
          '${_duration.inMinutes.remainder(60).toString().padLeft(2, '0')}:${_duration.inSeconds.remainder(60).toString().padLeft(2, '0')}';
    }
    return Text(
      timerText,
      style: widget.textStyle,
      textAlign: TextAlign.left,
    );
  }
}
