import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/screen_wrapper/controller/wrapper_controller.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';

class CustomRefreshIndicator extends StatelessWidget {
  const CustomRefreshIndicator(
      {Key? key, required this.child, required this.onRefresh})
      : super(key: key);
  final Widget child;
  final RefreshCallback onRefresh;

  @override
  Widget build(BuildContext context) {
    final wrapperController = Get.find<WrapperController>();
    return RefreshIndicator(
      color: wrapperController
          .getPrimaryColor()
          .changeColorBasedOnBackgroundColor().$1,
      backgroundColor: wrapperController.getPrimaryColor(),
      onRefresh: onRefresh,
      child: child,
    );
  }
}
