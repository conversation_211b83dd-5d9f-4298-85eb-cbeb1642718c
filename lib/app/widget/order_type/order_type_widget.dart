import 'package:flutter/material.dart';
import 'package:mobile_crm/app/helper/transaction_helper.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/data/models/order_model.dart';

class OrderTypeWidget extends StatelessWidget {
  const OrderTypeWidget({Key? key, required this.orderTypeModel})
      : super(key: key);
  final OrderTypeModel? orderTypeModel;

  @override
  Widget build(BuildContext context) {
    var listType =
        TransactionHelper.filterOrderType(orderTypeModel: orderTypeModel);

    return Wrap(
      crossAxisAlignment: WrapCrossAlignment.start,
      alignment: WrapAlignment.start,
      spacing: 4,
      children: listType
          .map((e) => Container(
                padding: const EdgeInsets.symmetric(horizontal: 8),
                margin: const EdgeInsets.symmetric(horizontal: 3),
                decoration: BoxDecoration(
                    color: AppColor.disable,
                    borderRadius: BorderRadius.circular(15)),
                child: Text(
                  e,
                  style: AppFont.componentSmall,
                ),
              ))
          .toList(),
    );
  }
}
