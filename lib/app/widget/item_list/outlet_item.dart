// ignore_for_file: must_be_immutable

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mobile_crm/app/widget/cached_image_widget.dart';
import 'package:mobile_crm/app/widget/order_type/order_type_widget.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/app_constants.dart';
import 'package:mobile_crm/data/providers/db/database.dart';

import '../../modules/home/<USER>/home_controller.dart';

class OutletItemWidget extends StatelessWidget {
  const OutletItemWidget(
      {super.key,
      required this.outlet,
      required this.onPressed,
      this.controller});

  final Outlet outlet;
  final VoidCallback? onPressed;
  final HomeController? controller;

  @override
  Widget build(BuildContext context) {
    Size size = Constants.productItemSize;
    return InkWell(
      onTap: onPressed,
      child: Container(
        margin: const EdgeInsets.only(bottom: 5),
        decoration: BoxDecoration(borderRadius: BorderRadius.circular(15)),
        child: SizedBox(
          width: size.width,
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Stack(
                children: [
                  ClipRRect(
                      borderRadius: BorderRadius.circular(10),
                      child: CachedImageWidget(
                          width: size.width,
                          height: size.height,
                          imageUrl: outlet.outlet_logo.toString())),
                  outlet.distance != null
                      ? Positioned(
                          top: 2,
                          right: 2,
                          child: Container(
                            padding: const EdgeInsets.symmetric(horizontal: 5),
                            decoration: BoxDecoration(
                                color: AppColor.ink05,
                                borderRadius: BorderRadius.circular(15)),
                            child: Text(
                              outlet.distance ?? '',
                              style: AppFont.paragraphSmall,
                            ),
                          ))
                      : const Text('')
                ],
              ),
              Flexible(
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 8.0.h),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(outlet.name ?? '-',
                          overflow: TextOverflow.ellipsis,
                          style: AppFont.paragraphSmallBold),
                      Row(
                        children: [
                          Icon(
                            Icons.place,
                            size: Constants.iconSizeSmall(context),
                            color: Colors.red,
                          ),
                          5.0.width,
                          Flexible(
                            child: Text(
                              outlet.address.toString(),
                              style: AppFont.paragraphSmall,
                              textAlign: TextAlign.left,
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                          )
                        ],
                      ),
                      outlet.phone.toString().length >= 10
                          ? Flexible(
                              child: Row(
                                children: [
                                  Icon(
                                    Icons.phone,
                                    size: Constants.iconSizeSmall(context),
                                    color: AppColor.black70,
                                  ),
                                  5.0.width,
                                  Text(
                                    outlet.phone.toString(),
                                    style: AppFont.paragraphSmall,
                                    overflow: TextOverflow.ellipsis,
                                    maxLines: 1,
                                  ),
                                ],
                              ),
                            )
                          : const Text(''),
                      OrderTypeWidget(orderTypeModel: outlet.order_type)
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
