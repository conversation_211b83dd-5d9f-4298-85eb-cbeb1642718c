// ignore_for_file: must_be_immutable

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/widget/cached_image_widget.dart';
import 'package:mobile_crm/app/widget/shimmer_loading_widget.dart';
import 'package:mobile_crm/core/extensions/double_extensions.dart';
import 'package:mobile_crm/core/extensions/int_extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/app_constants.dart';
import 'package:mobile_crm/core/values/app_strings.dart';
import 'package:mobile_crm/data/providers/db/database.dart';

import '../../utils/utils.dart';

class ProductItemWidget extends StatelessWidget {
  ProductItemWidget(
      {super.key,
      required this.product,
      this.type = ProductItemWidgetType.menu,
      this.cart,
      required this.onPressed});

  Product product;
  Cart? cart;
  ProductItemWidgetType type;
  VoidCallback? onPressed;

  @override
  Widget build(BuildContext context) {
    return type == ProductItemWidgetType.menu
        ? _menu(context)
        : _loading(context);
  }

  Widget _menu(BuildContext context) {
    Size size = Constants.productItemSize;
    return InkWell(
      onTap: onPressed,
      child: Container(
        margin: const EdgeInsets.only(bottom: 5),
        width: size.width,
        height: size.height,
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ClipRRect(
                borderRadius: BorderRadius.circular(10),
                child: CachedImageWidget(
                    disable: product.availability != "available",
                    width: size.width,
                    height: size.height,
                    imageUrl: product.photo.toString())),
            Container(
              padding: EdgeInsets.symmetric(horizontal: 8.0.h),
              child: product.availability != "available"
                  ? Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        Text("${product.name}",
                            overflow: TextOverflow.ellipsis,
                            style: AppFont.componentSmall
                                .copyWith(color: AppColor.black70)),
                        Text(Strings.notAvailable.tr,
                            style: AppFont.componentSmall
                                .copyWith(color: AppColor.black70)),
                      ],
                    )
                  : Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        Text("${product.name}",
                            overflow: TextOverflow.ellipsis,
                            style: AppFont.componentSmall),
                        Text("Rp ${product.priceSell.toCurrency}",
                            style: AppFont.componentSmall),
                      ],
                    ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _loading(BuildContext context) {
    Size size = Constants.productItemSize;
    return Container(
      margin: const EdgeInsets.only(bottom: 5),
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(borderRadius: BorderRadius.circular(10)),
      clipBehavior: Clip.hardEdge,
      child: SizedBox(
        width: double.infinity,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ClipRRect(
                    borderRadius: BorderRadius.circular(5),
                    child:
                        ShimmerWidget(width: size.width, height: size.height)),
                10.0.width,
                Flexible(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      ShimmerWidget(
                          width: randomRange(
                                  AppDimen.h40.toInt(), AppDimen.h128.toInt())
                              .toDouble(),
                          height: AppDimen.h12),
                      10.0.height,
                      ShimmerWidget(
                          width: randomRange(
                                  AppDimen.h40.toInt(), AppDimen.h128.toInt())
                              .toDouble(),
                          height: AppDimen.h12),
                      5.0.height,
                      ShimmerWidget(
                          width: randomRange(
                                  AppDimen.h40.toInt(), AppDimen.h128.toInt())
                              .toDouble(),
                          height: AppDimen.h12),
                      10.0.height,
                      ShimmerWidget(width: AppDimen.h48, height: AppDimen.h12),
                    ],
                  ),
                ),
              ],
            ),
            AppDimen.h6.height,
            ShimmerWidget(width: AppDimen.h64, height: AppDimen.h24, radius: 5),
          ],
        ),
      ),
    );
  }
}

enum ProductItemWidgetType { menu, loading }
