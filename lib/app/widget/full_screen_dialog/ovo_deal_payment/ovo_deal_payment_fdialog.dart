import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';
import 'package:mobile_crm/app/utils/animations.dart';
import 'package:mobile_crm/app/widget/button_widget/button_widget.dart';
import 'package:mobile_crm/app/widget/cached_image_widget.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/values.dart';
import 'package:mobile_crm/data/models/deals_payment_model.dart';
import 'package:mobile_crm/data/models/payment_method_model.dart';
import 'package:mobile_crm/data/providers/db/database.dart';
import 'package:mobile_crm/routes/app_pages.dart';

import '../../../modules/deal_detail/controllers/deal_detail_controller.dart';
import '../../app_time_left.dart';

class OvoDealPaymentFullScreenDialog {
  static show(
      {required BuildContext context,
      required DealData deal,
      required PaymentMethodDetail paymentMethodDetail,
      required DealsPaymentModel dealPaymentModel}) {
    showGeneralDialog(
      context: context,
      pageBuilder: (context, animation, secondaryAnimation) {
        return Animations.fromBottom(
            animation,
            secondaryAnimation,
            _childWidget(
                context: context,
                dealPaymentModel: dealPaymentModel,
                paymentMethodDetail: paymentMethodDetail,
                deal: deal));
      },
      transitionDuration: const Duration(milliseconds: 200),
    );
  }

  static Dialog _childWidget(
      {required BuildContext context,
      required DealData deal,
      required PaymentMethodDetail paymentMethodDetail,
      required DealsPaymentModel dealPaymentModel}) {
    var isPaymentSuccess = false.obs;
    var isPaymentFailed = false.obs;
    DateTime currentTime = DateTime.now();
    final controller = Get.find<DealDetailController>();
    return Dialog.fullscreen(
        child: WillPopScope(
      onWillPop: () => Future.value(false),
      child: Column(
        children: [
          AppBar(
            leading: IconButton(
              onPressed: () {
                Get.back();
              },
              icon: const Icon(
                Icons.close,
                color: AppColor.black90,
              ),
            ),
            automaticallyImplyLeading: false,
            centerTitle: true,
            backgroundColor: AppColor.white,
          ),
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Column(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    15.0.height,
                    Obx(() {
                      return isPaymentSuccess.value
                          ? Lottie.asset(
                              ImgStrings.lottieDone,
                              animate: true,
                              repeat: false,
                              width: AppDimen.h192,
                            )
                          : isPaymentFailed.value
                              ? Lottie.asset(
                                  ImgStrings.lottieError,
                                  animate: true,
                                  repeat: false,
                                  width: AppDimen.h192,
                                )
                              : Lottie.asset(
                                  ImgStrings.lottieClock,
                                  animate: true,
                                  repeat: true,
                                  width: AppDimen.h192,
                                );
                    }),
                    Obx(() {
                      return Text(
                        isPaymentSuccess.value
                            ? Strings.paymentSuccess.tr
                            : isPaymentFailed.value
                                ? Strings.paymentFailed.tr
                                : Strings.waitingForPayment.tr,
                        style: AppFont.componentMediumBold,
                      );
                    }),
                    AppDimen.h10.height,
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16.0),
                      child: Text(
                          Strings.clickAppWithTimeValue.trParams(
                              {'app': paymentMethodDetail.name ?? ''}),
                          textAlign: TextAlign.center,
                          style: AppFont.componentSmall),
                    ),
                    AppDimen.h10.height,
                    Obx(() {
                      return Visibility(
                        visible:
                            !isPaymentSuccess.value && !isPaymentFailed.value,
                        child: TimeLeftWidget(
                            time: dealPaymentModel.expiredAt ?? 10000,
                            onTimeChanged: (value) async {
                              if (value.inSeconds == 0 || value.isNegative) {
                                isPaymentFailed.value = true;
                              }
                              DateTime newTime = DateTime.now();
                              if ((newTime.second - currentTime.second).abs() ==
                                  10) {
                                var result =
                                    await controller.isSuccessBuyDeals(deal);
                                if (result.$1) {
                                  isPaymentSuccess.value = true;
                                  deal = result.$2 ?? deal;
                                }
                                currentTime = newTime;
                              }
                            },
                            textStyle: AppFont.componentMediumBold
                                .copyWith(color: AppColor.black90)),
                      );
                    }),
                    15.0.height,
                    Container(
                      alignment: Alignment.centerLeft,
                      padding: const EdgeInsets.symmetric(horizontal: 16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(Strings.paymentMethod.tr,
                              textAlign: TextAlign.center,
                              style: AppFont.componentSmallBold),
                          AppDimen.h8.height,
                          Container(
                            width: MediaQuery.of(context).size.width,
                            height: AppDimen.h48,
                            padding: EdgeInsets.symmetric(
                                horizontal: AppDimen.h16, vertical: 4),
                            decoration: BoxDecoration(
                                color: AppColor.white,
                                borderRadius:
                                    BorderRadius.circular(AppDimen.h6.r),
                                boxShadow: const [
                                  BoxShadow(
                                      color: AppColor.disable,
                                      offset: Offset(0, 1),
                                      spreadRadius: 1,
                                      blurRadius: 1)
                                ]),
                            child: Row(
                              children: [
                                SizedBox(
                                    width: AppDimen.h28,
                                    child: CachedImageWidget(
                                      imageUrl:
                                          paymentMethodDetail.icon?.small ?? '',
                                    )),
                                AppDimen.h10.width,
                                Text(
                                  paymentMethodDetail.name ?? '',
                                  style: AppFont.componentSmallBold,
                                ),
                              ],
                            ),
                          )
                        ],
                      ),
                    ),
                    10.0.height,
                    Container(
                      alignment: Alignment.centerLeft,
                      padding: const EdgeInsets.symmetric(horizontal: 16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(Strings.totalBill.tr,
                              textAlign: TextAlign.center,
                              style: AppFont.componentSmallBold),
                          AppDimen.h8.height,
                          Container(
                            width: MediaQuery.of(context).size.width,
                            height: AppDimen.h48,
                            padding: EdgeInsets.symmetric(
                                horizontal: AppDimen.h16, vertical: 4),
                            decoration: BoxDecoration(
                                color: AppColor.white,
                                borderRadius:
                                    BorderRadius.circular(AppDimen.h6.r),
                                boxShadow: const [
                                  BoxShadow(
                                      color: AppColor.disable,
                                      offset: Offset(0, 1),
                                      spreadRadius: 1,
                                      blurRadius: 1)
                                ]),
                            child: Row(
                              children: [
                                SizedBox(
                                  width: AppDimen.h28,
                                  child: Icon(
                                    Icons.payments_rounded,
                                    color: AppColor.black70,
                                    size: AppDimen.h18,
                                  ),
                                ),
                                AppDimen.h10.width,
                                deal.voucherPriceType == "money"
                                    ? Text(
                                        "Rp ${deal.dealsValue.toCurrency}",
                                        style: AppFont.componentSmallBold,
                                      )
                                    : Text(
                                        "${deal.dealsValue} Point",
                                        style: AppFont.componentSmallBold,
                                      ),
                              ],
                            ),
                          )
                        ],
                      ),
                    ),
                    AppDimen.h30.height,
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 8.0),
                      child: Obx(() {
                        return isPaymentSuccess.value
                            ? PrimaryButton(
                                onPressed: () {
                                  Get.back();
                                  Get.toNamed(
                                      Routes.VOUCHERDETAIL(
                                          deal.promotionBuyId.toString()),
                                      arguments: deal);
                                },
                                text: "text",
                                type: PrimaryButtonType.type4,
                                child: Container(
                                  padding:
                                      const EdgeInsets.symmetric(horizontal: 8),
                                  child: SizedBox(
                                    width: MediaQuery.of(context).size.width,
                                    child: Text(
                                      Strings.useDeals.tr,
                                      textAlign: TextAlign.center,
                                      style: AppFont.componentSmall
                                          .copyWith(color: AppColor.white),
                                    ),
                                  ),
                                ),
                              )
                            : isPaymentFailed.value
                                ? const SizedBox()
                                : PrimaryButton(
                                    onPressed: () async {
                                      var result = await controller
                                          .isSuccessBuyDeals(deal);
                                      if (result.$1) {
                                        isPaymentSuccess.value = true;
                                        deal = result.$2 ?? deal;
                                      }
                                    },
                                    text: "text",
                                    type: PrimaryButtonType.type4,
                                    child: Container(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 8),
                                      child: SizedBox(
                                        width:
                                            MediaQuery.of(context).size.width,
                                        child: Text(
                                          Strings.refreshPaymentStatus.tr,
                                          textAlign: TextAlign.center,
                                          style: AppFont.componentSmall
                                              .copyWith(color: AppColor.white),
                                        ),
                                      ),
                                    ),
                                  );
                      }),
                    ),
                  ]),
            ),
          )
        ],
      ),
    ));
  }
}
