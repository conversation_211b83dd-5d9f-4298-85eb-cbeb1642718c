import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/enum/order_status_enum.dart';
import 'package:mobile_crm/app/utils/animations.dart';
import 'package:mobile_crm/app/utils/logger.dart';
import 'package:mobile_crm/app/utils/svg_icon.dart';
import 'package:mobile_crm/app/utils/toast.dart';
import 'package:mobile_crm/app/widget/cached_image_widget.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/values.dart';
import 'package:mobile_crm/data/models/deals_payment_model.dart';
import 'package:mobile_crm/data/providers/db/database.dart';

import '../../../../data/models/order_detail_model.dart';
import '../../../../data/models/payment_method_model.dart';
import '../../../modules/order/controllers/order_controller.dart';

class QrisDealPaymentFullScreenDialog {
  static show(
      {Key? key,
      required BuildContext context,
      required DealData deal,
      required DealsPaymentModel dealPaymentModel}) {
        infoLogger('paymentQris ${jsonEncode(dealPaymentModel)}');
    showGeneralDialog(
      context: context,
      pageBuilder: (context, animation, secondaryAnimation) {
        return Animations.fromBottom(
            animation,
            secondaryAnimation,
            _childWidget(
                key: key,
                context: context,
                dealPaymentModel: dealPaymentModel,
                deal: deal));
      },
      transitionDuration: const Duration(milliseconds: 200),
    );
  }

  static Dialog _childWidget(
      {Key? key,
      required BuildContext context,
      required DealData deal,
      required DealsPaymentModel dealPaymentModel}) {
    return Dialog.fullscreen(
      child: WillPopScope(
        key: key,
        onWillPop: () => Future.value(false),
        child: Column(
          children: [
            AppBar(
              leading: IconButton(
                onPressed: () {
                  Get.back();
                },
                icon: const Icon(
                  Icons.close,
                  color: AppColor.black90,
                ),
              ),
              automaticallyImplyLeading: false,
              title: Text(
                'E Wallet with QR Code',
                style: AppFont.componentSmall,
              ),
              centerTitle: true,
              backgroundColor: AppColor.white,
            ),
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    15.0.height,
                    Text(
                      "Total",
                      style: AppFont.componentSmallBold,
                    ),
                    GestureDetector(
                      onTapDown: (details) => Clipboard.setData(
                              ClipboardData(text: deal.dealsValue.toString()))
                          .then((value) {
                        Toast.show(Strings.textCopied.tr,
                            type: ToastType.dark, context: context);
                      }),
                      child: deal.voucherPriceType == "money"
                          ? Text(
                              "Rp ${deal.dealsValue.toCurrency}",
                              style: AppFont.paragraphLargeBold,
                            )
                          : Text(
                              "${deal.dealsValue} Point",
                              style: AppFont.paragraphLargeBold,
                            ),
                    ),
                    15.0.height,
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16.0),
                      child: Text(
                        Strings.openEWallet.tr,
                        textAlign: TextAlign.center,
                        style: AppFont.componentSmallBold,
                      ),
                    ),
                    15.0.height,
                    ClipRRect(
                      borderRadius: BorderRadius.circular(10),
                      child: CachedImageWidget(
                        imageUrl: dealPaymentModel.payment?.url ?? '',
                        height: 300.h,
                        width: 300.h,
                      ),
                    ),
                    SvgIcon.svgIcon(ImgStrings.qris,
                        color: AppColor.black, size: Size(200.w, 20.h)),
                    30.0.height,
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16.0),
                      // child: Text(
                      //   Strings.thankYouBuyingPromo.trParams(
                      //       {"date": dealPaymentModel.expiredAt.toDateAndTime}),
                      //   textAlign: TextAlign.center,
                      //   style: AppFont.componentSmallBold,
                      // ),
                    ),
                  ],
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}

class QrisOrderPaymentFullScreenDialog {
  static show(
      {Key? key,
      required BuildContext context,
      required OrderDetailModel orderDetailModel,
      required PaymentMethodDetail paymentMethodDetail,
      required OrderController controller}) {
    showGeneralDialog(
      context: context,
      pageBuilder: (context, animation, secondaryAnimation) {
        return Animations.fromBottom(
            animation,
            secondaryAnimation,
            _childWidget(
                key: key,
                context: context,
                orderDetailModel: orderDetailModel,
                paymentMethodDetail: paymentMethodDetail,
                controller: controller));
      },
      transitionDuration: const Duration(milliseconds: 200),
    );
  }

  static Dialog _childWidget(
      {Key? key,
      required BuildContext context,
      required OrderDetailModel orderDetailModel,
      required PaymentMethodDetail paymentMethodDetail,
      required OrderController controller}) {
    // controller.
    bool isAlreadyTrigger = false;
    controller.streamTransactionData.listen((event) {
      if (event.status?.toLowerCase() ==
              OrderStatusEnum.payment_verified.name.toLowerCase() &&
          !isAlreadyTrigger) {
        Get.back();
        isAlreadyTrigger = true;
      }
    });
// Suggested code may be subject to a license. Learn more: ~LicenseLog:**********.
    infoLogger('paymentQris: ${jsonEncode(paymentMethodDetail)}');
    return Dialog.fullscreen(
        child: WillPopScope(
      key: key,
      onWillPop: () => Future.value(false),
      child: Column(
        children: [
          AppBar(
            leading: IconButton(
              onPressed: () {
                Get.back();
              },
              icon: const Icon(
                Icons.close,
                color: AppColor.black90,
              ),
            ),
            automaticallyImplyLeading: false,
            title: Text(
              'E Wallet with QR Code',
              style: AppFont.componentSmall,
            ),
            centerTitle: true,
            backgroundColor: AppColor.white,
          ),
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Column(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    15.0.height,
                    Text(
                      "Total",
                      style: AppFont.componentSmallBold,
                    ),
                    GestureDetector(
                      onTapDown: (details) => Clipboard.setData(ClipboardData(
                              text: orderDetailModel.orderSalesId ?? ''))
                          .then((value) {
                        Toast.show(Strings.textCopied.tr,
                            type: ToastType.dark, context: context);
                      }),
                      child: Text(
                        "Rp${controller.newOrder.value.sumTotalBill().toCurrency}",
                        style: AppFont.paragraphLargeBold,
                      ),
                    ),
                    15.0.height,
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16.0),
                      child: Text(
                        Strings.openEWallet.tr,
                        textAlign: TextAlign.center,
                        style: AppFont.componentSmallBold,
                      ),
                    ),
                    15.0.height,
                    ClipRRect(
                      borderRadius: BorderRadius.circular(10),
                      child: CachedImageWidget(
                        imageUrl: paymentMethodDetail.paymentInfo?.value ?? '',
                        height: 300.h,
                        width: 300.h,
                      ),
                    ),
                    SvgIcon.svgIcon(ImgStrings.qris,
                        color: AppColor.black, size: Size(200.w, 20.h)),
                    30.0.height,
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16.0),
                      // child: Text(
                      //   Strings.thankYouBuyingPromo.trParams({
                      //     "date": paymentMethodDetail
                      //             .paymentInfo?.expiredAt.toDateAndTime ??
                      //         ''
                      //   }),
                      //   textAlign: TextAlign.center,
                      //   style: AppFont.componentSmallBold,
                      // ),
                    ),
                  ]),
            ),
          )
        ],
      ),
    ));
  }
}
