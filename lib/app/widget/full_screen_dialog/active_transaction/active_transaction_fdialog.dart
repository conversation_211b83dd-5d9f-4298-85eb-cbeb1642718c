import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/screen_wrapper/controller/wrapper_controller.dart';
import 'package:mobile_crm/app/utils/animations.dart';
import 'package:mobile_crm/app/utils/logger.dart';
import 'package:mobile_crm/app/utils/utils.dart';
import 'package:mobile_crm/app/widget/app_dotted_separator.dart';
import 'package:mobile_crm/app/widget/app_page_empty.dart';
import 'package:mobile_crm/app/widget/app_time_left.dart';
import 'package:mobile_crm/app/widget/button_widget/button_widget.dart';
import 'package:mobile_crm/app/widget/custom_circular_progress_widget.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/values.dart';
import 'package:mobile_crm/data/models/transaction_new_model.dart';
import 'package:mobile_crm/domain/repository/deal_repository.dart';
import 'package:mobile_crm/domain/repository/transaction_repository.dart';
import 'package:mobile_crm/routes/app_pages.dart';

class ActiveTransactionFullScreenDialog {
  void show(
      {required BuildContext context,
      required TransactionRepository transRepo,
      required DealRepository dealRepo}) {
    showGeneralDialog(
      context: context,
      pageBuilder: (context, animation, secondaryAnimation) {
        return Animations.fromBottom(
            animation,
            secondaryAnimation,
            childWidget(
                context: context, transRepo: transRepo, dealRepo: dealRepo));
      },
      transitionDuration: const Duration(milliseconds: 200),
    );
  }

  Dialog childWidget(
      {required BuildContext context,
      required TransactionRepository transRepo,
      required DealRepository dealRepo}) {
    return Dialog.fullscreen(
        child: WillPopScope(
      onWillPop: () => Future.value(false),
      child: Column(
        children: [
          AppBar(
            leading: IconButton(
              onPressed: () {
                Get.back();
              },
              icon: const Icon(
                Icons.close,
                color: AppColor.black90,
              ),
            ),
            automaticallyImplyLeading: false,
            title: Text(
              Strings.activeTransaction.tr,
              style: AppFont.componentSmall,
            ),
            centerTitle: true,
            backgroundColor: AppColor.white,
          ),
          5.0.height,
          Expanded(
            child: FutureBuilder(
              future: transRepo.getAllTransactionOrder(isOnOrder: true),
              builder: (context, snapshot) {
                if (snapshot.connectionState != ConnectionState.done) {
                  return const CustomCircularProgressIndicator(
                    valueColor: AppColor.black90,
                  );
                }
                if (snapshot.hasData) {
                  if (snapshot.data?.isEmpty ?? false) {
                    return AppPageEmpty(
                      func: () => Get.back(),
                      reason: Strings.noActivePayment.tr,
                      buttonText: Strings.back.tr,
                    );
                  }
                  return ListView.builder(
                      itemCount: snapshot.data?.length ?? 0,
                      itemBuilder: (context, index) {
                        var item = snapshot.data![index];
                        infoLogger(' ---> item order : ', item);
                        if (item.selfOrder == null) {
                          return _orderWithType(item: item);
                        }
                        return _selfOrder(item: item);

                        // if (item.dealPayment == null) {
                        //   if (item.orderDetail != null) {
                        //     return _orderWithType(item: item);
                        //   }
                        //   return _selfOrder(item: item);
                        // }
                        // return _dealItem(item: item);
                      });
                }

                return const Icon(Icons.error_outline);
              },
            ),
          ),
        ],
      ),
    ));
  }

  Widget _orderWithType({required TransactionNewModel item}) {
    infoLogger('--- paymentTimeout', item.paymentTimeout);
    var isExpired = false.obs;
    var milisDate = DateTime.fromMillisecondsSinceEpoch(item.timeOrder ?? 0)
        .add(Duration(minutes: item.paymentTimeout ?? 60 * 24));
    isExpired.value =
        isTimeExpired(milliSeconds: milisDate.millisecondsSinceEpoch);
    final controller = Get.find<WrapperController>();
    return Container(
      margin: const EdgeInsets.only(left: 16, right: 16, bottom: 8),
      decoration: BoxDecoration(
          color: Colors.white,
          boxShadow: const [
            BoxShadow(
                color: AppColor.black5,
                offset: Offset(0, 0),
                spreadRadius: 2,
                blurRadius: 4)
          ],
          borderRadius: BorderRadius.circular(10)),
      child: Stack(
        children: [
          Align(
              alignment: Alignment.centerRight,
              child: Obx(
                () => (isExpired.value)
                    ? Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 8.0),
                        child: Text(
                          "Expired",
                          style:
                              AppFont.heading3.copyWith(color: AppColor.black5),
                        ),
                      )
                    : Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12.0),
                        decoration: const BoxDecoration(
                            borderRadius: BorderRadius.only(
                                topRight: Radius.circular(10),
                                bottomLeft: Radius.circular(10)),
                            color: AppColor.black90),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            Text(
                              "Expired in ",
                              style: AppFont.componentSmall
                                  .copyWith(color: AppColor.white),
                            ),
                            TimeLeftWidget(
                              time: milisDate.millisecondsSinceEpoch,
                              onTimeChanged: (Duration value) {
                                if (value.inSeconds.isEqual(0)) {
                                  // controller.getAllTransaction();
                                  isExpired.value = true;
                                }
                              },
                              textStyle: AppFont.componentSmall
                                  .copyWith(color: AppColor.white),
                            ),
                          ],
                        ),
                      ),
              )),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Obx(
                      () => isExpired.value
                          ? Text(
                              "Expired payment",
                              style: AppFont.componentSmallBold,
                            )
                          : Text(
                              translationOrderStatus(item.status ?? ""),
                              style: AppFont.componentSmallBold,
                            ),
                    ),
                    5.0.height,
                    Text(
                      item.orderType
                              .toString()
                              .replaceAll("_", " ")
                              .capitalizeFirst ??
                          '',
                      style: AppFont.componentSmall,
                    ),
                    5.0.height,
                    Text(
                      item.outlet?.name ?? '',
                      style: AppFont.componentSmall,
                    ),
                    InkWell(
                      onTapDown: (details) => Clipboard.setData(
                          ClipboardData(text: item.orderSalesId.toString())),
                      child: Row(
                        children: [
                          Text(
                            item.orderSalesId ?? '',
                            style: AppFont.componentSmall,
                          ),
                          5.0.width,
                          Icon(
                            Icons.copy,
                            color: AppColor.black70,
                            size: 11.h,
                          )
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              const DottedDivider(
                width: 5,
                height: 3,
                color: AppColor.black5,
              ),
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        Text(
                          Strings.totalBill,
                          style: AppFont.componentSmall,
                        ),
                        5.0.width,
                        Text(
                          item.sumTotalBill().toCurrency,
                          style: AppFont.componentSmallBold,
                        ),
                      ],
                    ),
                    Obx(
                      () => isExpired.value
                          ? SecondaryButton(
                              onPressed: () {
                                Get.toNamed(
                                    Routes.TRANSACTION(item.orderSalesId ?? ''),
                                    arguments: item);
                              },
                              type: SecondaryButtonType.type4,
                              child: Text(
                                "Detail",
                                style: AppFont.componentSmall,
                              ),
                            )
                          : PrimaryButton(
                              onPressed: () {
                                Get.toNamed(
                                    Routes.TRANSACTION(item.orderSalesId ?? ''),
                                    arguments: item);
                              },
                              text: "Details",
                              type: PrimaryButtonType.type4,
                              child: Text(
                                "Details",
                                style: AppFont.componentSmall.copyWith(
                                    color: controller
                                        .getPrimaryColor()
                                        .changeColorBasedOnBackgroundColor()
                                        .$1),
                              ),
                            ),
                    )
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _selfOrder({required TransactionNewModel item}) {
    var isExpired = false.obs;
    var milisDate =
        DateTime.fromMillisecondsSinceEpoch(item.selfOrder?.expired ?? 0);
    isExpired.value =
        isTimeExpired(milliSeconds: milisDate.millisecondsSinceEpoch);
    return Container(
      margin: const EdgeInsets.only(left: 16, right: 16, bottom: 8),
      decoration: BoxDecoration(
          color: Colors.white,
          boxShadow: const [
            BoxShadow(
                color: AppColor.black5,
                offset: Offset(0, 0),
                spreadRadius: 2,
                blurRadius: 4)
          ],
          borderRadius: BorderRadius.circular(10)),
      child: Stack(
        children: [
          Align(
              alignment: Alignment.centerRight,
              child: Obx(
                () => (isExpired.value)
                    ? Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 8.0),
                        child: Text(
                          "Expired",
                          style:
                              AppFont.heading3.copyWith(color: AppColor.black5),
                        ),
                      )
                    : Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12.0),
                        decoration: const BoxDecoration(
                            borderRadius: BorderRadius.only(
                                topRight: Radius.circular(10),
                                bottomLeft: Radius.circular(10)),
                            color: AppColor.black90),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            Text(
                              "Expired in ",
                              style: AppFont.componentSmall
                                  .copyWith(color: AppColor.white),
                            ),
                            TimeLeftWidget(
                              time: milisDate.millisecondsSinceEpoch,
                              onTimeChanged: (Duration value) {
                                if (value.inSeconds.isEqual(0)) {
                                  // controller.getAllTransaction();
                                  isExpired.value = true;
                                }
                              },
                              textStyle: AppFont.componentSmall
                                  .copyWith(color: AppColor.white),
                            ),
                          ],
                        ),
                      ),
              )),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Obx(
                      () => isExpired.value
                          ? Text(
                              "Expired payment",
                              style: AppFont.componentSmallBold,
                            )
                          : Text(
                              Strings.statusAccept.tr,
                              style: AppFont.componentSmallBold,
                            ),
                    ),
                    5.0.height,
                    Text(
                      'Self Order',
                      style: AppFont.componentSmall,
                    ),
                    5.0.height,
                    Text(
                      item.outlet?.name ?? '',
                      style: AppFont.componentSmall,
                    ),
                    InkWell(
                      onTapDown: (details) => Clipboard.setData(ClipboardData(
                          text: item.selfOrder?.orderCode.toString() ?? '')),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            "Order Code",
                            style: AppFont.componentSmall,
                          ),
                          Row(
                            children: [
                              Text(
                                "${item.selfOrder?.orderCode ?? 0}",
                                style: AppFont.componentSmallBold,
                              ),
                              5.0.width,
                              Icon(
                                Icons.copy,
                                color: AppColor.black70,
                                size: 11.h,
                              )
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              const DottedDivider(
                width: 5,
                height: 3,
                color: AppColor.black5,
              ),
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        Text(
                          Strings.totalBill,
                          style: AppFont.componentSmall,
                        ),
                        5.0.width,
                        Text(
                          item.sumTotalBill().toCurrency,
                          style: AppFont.componentSmallBold,
                        ),
                      ],
                    ),
                    Obx(
                      () => isExpired.value
                          ? SecondaryButton(
                              onPressed: () {
                                Get.toNamed(Routes.ORDER, arguments: item);
                              },
                              type: SecondaryButtonType.type4,
                              child: Text(
                                "Detail",
                                style: AppFont.componentSmall,
                              ),
                            )
                          : PrimaryButton(
                              onPressed: () {
                                Get.toNamed(Routes.ORDER, arguments: item);
                              },
                              borderRadius: BorderRadius.all(
                                  Radius.circular(AppDimen.h20)),
                              width: AppDimen.h40 + AppDimen.h30,
                              text: "Details",
                              type: PrimaryButtonType.type5,
                            ),
                    )
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
