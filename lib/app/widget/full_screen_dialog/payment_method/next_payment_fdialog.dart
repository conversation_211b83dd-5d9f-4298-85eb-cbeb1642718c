import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';
import 'package:mobile_crm/app/modules/deal_detail/controllers/deal_detail_controller.dart';
import 'package:mobile_crm/app/utils/animations.dart';
import 'package:mobile_crm/app/utils/url_helper.dart';
import 'package:mobile_crm/app/widget/button_widget/button_widget.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/values.dart';
import 'package:mobile_crm/data/providers/db/database.dart';
import 'package:mobile_crm/routes/app_pages.dart';

import '../../../../data/models/deals_payment_model.dart';
import '../../../../data/models/payment_method_model.dart';
import '../../../enum/order_status_enum.dart';
import '../../../modules/order/controllers/order_controller.dart';
import '../../app_time_left.dart';

class NextPaymentFullScreenDialog {
  static show(
      {required BuildContext context,
      required DealData deal,
      required DealsPaymentModel dealsPaymentModel}) {
    showGeneralDialog(
      context: context,
      pageBuilder: (context, animation, secondaryAnimation) {
        return Animations.fromBottom(
            animation,
            secondaryAnimation,
            _childWidget(
              context: context,
              deal: deal,
              dealsPaymentModel: dealsPaymentModel,
            ));
      },
      transitionDuration: const Duration(milliseconds: 300),
    );
  }

  static Dialog _childWidget(
      {required BuildContext context,
      required DealData deal,
      required DealsPaymentModel dealsPaymentModel}) {
    var isPaymentSuccess = false.obs;
    var isPaymentFailed = false.obs;
    DateTime currentTime = DateTime.now();
    final controller = Get.find<DealDetailController>();
    return Dialog.fullscreen(
        child: WillPopScope(
      onWillPop: () => Future.value(false),
      child: Column(
        children: [
          AppBar(
            leading: IconButton(
              onPressed: () {
                Get.back();
              },
              icon: const Icon(
                Icons.close,
                color: AppColor.black90,
              ),
            ),
            automaticallyImplyLeading: false,
            centerTitle: true,
            backgroundColor: AppColor.white,
          ),
          const Spacer(),
          Obx(() {
            return Text(
              isPaymentSuccess.value
                  ? Strings.paymentSuccess.tr
                  : isPaymentFailed.value
                      ? Strings.paymentFailed.tr
                      : Strings.waitingForPayment.tr,
              style: AppFont.componentMediumBold,
            );
          }),
          Obx(() {
            return Visibility(
              visible: !isPaymentSuccess.value && !isPaymentFailed.value,
              child: Column(
                children: [
                  AppDimen.h6.height,
                  Text(
                    Strings.waitingForPaymentStatus.tr,
                    style: AppFont.componentSmall,
                  ),
                ],
              ),
            );
          }),
          AppDimen.h6.height,
          Obx(() {
            return Visibility(
              visible: !isPaymentSuccess.value && !isPaymentFailed.value,
              child: TimeLeftWidget(
                  time: dealsPaymentModel.expiredAt ?? 10000,
                  onTimeChanged: (value) async {
                    if (value.inSeconds == 0 || value.isNegative) {
                      isPaymentFailed.value = true;
                    }
                    DateTime newTime = DateTime.now();
                    if ((newTime.second - currentTime.second).abs() == 10) {
                      var result = await controller.isSuccessBuyDeals(deal);
                      if (result.$1) {
                        isPaymentSuccess.value = true;
                        deal = result.$2 ?? deal;
                      }
                      currentTime = newTime;
                    }
                  },
                  textStyle:
                      AppFont.componentSmall.copyWith(color: AppColor.black90)),
            );
          }),
          AppDimen.h6.height,
          Text(
            "Rp ${deal.dealsValue.toCurrency}",
            style: AppFont.componentSmallBold,
          ),
          const Spacer(),
          Obx(() {
            return isPaymentSuccess.value
                ? Lottie.asset(
                    ImgStrings.lottieDone,
                    animate: true,
                    repeat: false,
                    width: AppDimen.h192,
                  )
                : isPaymentFailed.value
                    ? Lottie.asset(
                        ImgStrings.lottieError,
                        animate: true,
                        repeat: false,
                        width: AppDimen.h192,
                      )
                    : Lottie.asset(
                        ImgStrings.lottieClock,
                        animate: true,
                        repeat: true,
                        width: AppDimen.h192,
                      );
          }),
          const Spacer(),
          const Spacer(),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8.0),
            child: Obx(() {
              return isPaymentSuccess.value
                  ? PrimaryButton(
                      onPressed: () {
                        Get.back();
                        Get.toNamed(
                            Routes.VOUCHERDETAIL(
                                deal.promotionBuyId.toString()),
                            arguments: deal);
                      },
                      text: "text",
                      type: PrimaryButtonType.type4,
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        child: SizedBox(
                          width: MediaQuery.of(context).size.width,
                          child: Text(
                            Strings.useDeals.tr,
                            textAlign: TextAlign.center,
                            style: AppFont.componentSmall
                                .copyWith(color: AppColor.white),
                          ),
                        ),
                      ),
                    )
                  : isPaymentFailed.value
                      ? const SizedBox()
                      : PrimaryButton(
                          onPressed: () {
                            openUrl(dealsPaymentModel.payment?.url ?? '');
                          },
                          text: "text",
                          type: PrimaryButtonType.type4,
                          child: Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8),
                            child: SizedBox(
                              width: MediaQuery.of(context).size.width,
                              child: Text(
                                Strings.continuePayment.tr,
                                textAlign: TextAlign.center,
                                style: AppFont.componentSmall
                                    .copyWith(color: AppColor.white),
                              ),
                            ),
                          ),
                        );
            }),
          ),
          AppDimen.h4.height,
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8.0),
            child: PrimaryButton(
              onPressed: () {
                Get.offAllNamed(Routes.HOME);
              },
              text: "text",
              type: PrimaryButtonType.type4,
              child: SizedBox(
                width: MediaQuery.of(context).size.width,
                child: Text(
                  Strings.backHome.tr,
                  textAlign: TextAlign.center,
                  style: AppFont.componentSmall.copyWith(color: AppColor.white),
                ),
              ),
            ),
          ),
          AppDimen.h20.height,
        ],
      ),
    ));
  }

  static showNextOrderPayment(
      {required BuildContext context,
      required PaymentMethodDetail paymentMethodDetail,
      required String totalBill}) {
    showGeneralDialog(
      context: context,
      pageBuilder: (context, animation, secondaryAnimation) {
        return Animations.fromBottom(
            animation,
            secondaryAnimation,
            _childWidgetNextOrderPayment(
                context: context,
                totalBill: totalBill,
                paymentMethodDetail: paymentMethodDetail));
      },
      transitionDuration: const Duration(milliseconds: 300),
    );
  }

  static Dialog _childWidgetNextOrderPayment(
      {required BuildContext context,
      required PaymentMethodDetail paymentMethodDetail,
      required String totalBill}) {
    final controller = Get.find<OrderController>();
    return Dialog.fullscreen(
        child: WillPopScope(
      onWillPop: () => Future.value(false),
      child: Column(
        children: [
          AppBar(
            leading: IconButton(
              onPressed: () {
                Get.back();
              },
              icon: const Icon(
                Icons.close,
                color: AppColor.black90,
              ),
            ),
            automaticallyImplyLeading: false,
            centerTitle: true,
            backgroundColor: AppColor.white,
          ),
          const Spacer(),
          Text(
            "${controller.newOrder.value.orderSalesId}",
            style: AppFont.componentMediumBold,
          ),
          AppDimen.h6.height,
          Obx(() {
            return Text(
              controller.newOrder.value.status ==
                      OrderStatusEnum.payment_verified.name
                  ? Strings.paymentSuccess.tr
                  : controller.newOrder.value.status ==
                          OrderStatusEnum.payment_reject.name
                      ? Strings.paymentFailed.tr
                      : Strings.waitingForPayment.tr,
              style: AppFont.componentMediumBold,
            );
          }),
          Obx(() {
            return Visibility(
              visible: !(controller.newOrder.value.status ==
                      OrderStatusEnum.payment_verified.name) &&
                  !(controller.newOrder.value.status ==
                      OrderStatusEnum.payment_reject.name),
              child: Column(
                children: [
                  AppDimen.h6.height,
                  Text(
                    Strings.waitingForPaymentStatus.tr,
                    style: AppFont.componentSmall,
                  ),
                ],
              ),
            );
          }),
          AppDimen.h6.height,
          // Obx(() {
          //   return Visibility(
          //     visible: !isPaymentSuccess.value && !isPaymentFailed.value,
          //     child: TimeLeftWidget(
          //         time: dealsPaymentModel.expiredAt ?? 10000,
          //         onTimeChanged: (value) async {
          //           if (value.inSeconds == 0 || value.isNegative) {
          //             isPaymentFailed.value = true;
          //           }
          //           DateTime newTime = DateTime.now();
          //           if ((newTime.second - currentTime.second).abs() == 10) {
          //             var result = await controller.isSuccessBuyDeals(deal);
          //             if (result.item1) {
          //               isPaymentSuccess.value = true;
          //               deal = result.item2 ?? deal;
          //             }
          //             currentTime = newTime;
          //           }
          //         },
          //         textStyle:
          //         AppFont.componentSmall.copyWith(color: AppColor.black90)),
          //   );
          // }),
          AppDimen.h6.height,
          Text(
            "Rp$totalBill",
            style: AppFont.componentSmallBold,
          ),
          const Spacer(),
          Obx(() {
            return (controller.newOrder.value.status ==
                    OrderStatusEnum.payment_verified.name)
                ? Lottie.asset(
                    ImgStrings.lottieDone,
                    animate: true,
                    repeat: false,
                    width: AppDimen.h192,
                  )
                : (controller.newOrder.value.status ==
                        OrderStatusEnum.payment_reject.name)
                    ? Lottie.asset(
                        ImgStrings.lottieError,
                        animate: true,
                        repeat: false,
                        width: AppDimen.h192,
                      )
                    : Lottie.asset(
                        ImgStrings.lottieClock,
                        animate: true,
                        repeat: true,
                        width: AppDimen.h192,
                      );
          }),
          const Spacer(),
          const Spacer(),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8.0),
            child: Obx(() {
              return (controller.newOrder.value.status ==
                          OrderStatusEnum.payment_verified.name) ||
                      (controller.newOrder.value.status ==
                          OrderStatusEnum.payment_reject.name)
                  ? PrimaryButton(
                      onPressed: () => Get.back(),
                      text: "text",
                      type: PrimaryButtonType.type4,
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        child: SizedBox(
                          width: MediaQuery.of(context).size.width,
                          child: Text(
                            Strings.back.tr,
                            textAlign: TextAlign.center,
                            style: AppFont.componentSmall
                                .copyWith(color: AppColor.white),
                          ),
                        ),
                      ),
                    )
                  : PrimaryButton(
                      onPressed: () {
                        if (paymentMethodDetail.paymentInfo?.type == "link") {
                          openUrl(paymentMethodDetail.paymentInfo?.value ?? '');
                        }
                      },
                      text: "text",
                      type: PrimaryButtonType.type4,
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        child: SizedBox(
                          width: MediaQuery.of(context).size.width,
                          child: Text(
                            Strings.continuePayment.tr,
                            textAlign: TextAlign.center,
                            style: AppFont.componentSmall
                                .copyWith(color: AppColor.white),
                          ),
                        ),
                      ),
                    );
            }),
          ),
          AppDimen.h20.height,
        ],
      ),
    ));
  }
}

class NextOrderPaymentFullScreenDialog {
  static show(
      {required BuildContext context,
      required OrderController controller,
      required PaymentMethodDetail paymentMethodDetail}) {
    showGeneralDialog(
      context: context,
      pageBuilder: (context, animation, secondaryAnimation) {
        return Animations.fromBottom(
            animation,
            secondaryAnimation,
            _childWidgetNextOrderPayment(
                context: context,
                controller: controller,
                paymentMethodDetail: paymentMethodDetail));
      },
      transitionDuration: const Duration(milliseconds: 300),
    );
  }

  static Dialog _childWidgetNextOrderPayment(
      {required BuildContext context,
      required OrderController controller,
      required PaymentMethodDetail paymentMethodDetail}) {
    final controller = Get.find<OrderController>();
    return Dialog.fullscreen(
        child: WillPopScope(
      onWillPop: () => Future.value(false),
      child: Column(
        children: [
          AppBar(
            leading: IconButton(
              onPressed: () {
                Get.back();
              },
              icon: const Icon(
                Icons.close,
                color: AppColor.black90,
              ),
            ),
            automaticallyImplyLeading: false,
            centerTitle: true,
            backgroundColor: AppColor.white,
          ),
          const Spacer(),
          Text(
            "${controller.newOrder.value.orderSalesId}",
            style: AppFont.componentMediumBold,
          ),
          AppDimen.h6.height,
          Obx(() {
            return Text(
              controller.newOrder.value.status ==
                      OrderStatusEnum.payment_verified.name
                  ? Strings.paymentSuccess.tr
                  : controller.newOrder.value.status ==
                          OrderStatusEnum.payment_reject.name
                      ? Strings.paymentFailed.tr
                      : Strings.waitingForPayment.tr,
              style: AppFont.componentMediumBold,
            );
          }),
          Obx(() {
            return Visibility(
              visible: !(controller.newOrder.value.status ==
                      OrderStatusEnum.payment_verified.name) &&
                  !(controller.newOrder.value.status ==
                      OrderStatusEnum.payment_reject.name),
              child: Column(
                children: [
                  AppDimen.h6.height,
                  Text(
                    Strings.waitingForPaymentStatus.tr,
                    style: AppFont.componentSmall,
                  ),
                ],
              ),
            );
          }),
          AppDimen.h6.height,
          // Obx(() {
          //   return Visibility(
          //     visible: !isPaymentSuccess.value && !isPaymentFailed.value,
          //     child: TimeLeftWidget(
          //         time: dealsPaymentModel.expiredAt ?? 10000,
          //         onTimeChanged: (value) async {
          //           if (value.inSeconds == 0 || value.isNegative) {
          //             isPaymentFailed.value = true;
          //           }
          //           DateTime newTime = DateTime.now();
          //           if ((newTime.second - currentTime.second).abs() == 10) {
          //             var result = await controller.isSuccessBuyDeals(deal);
          //             if (result.item1) {
          //               isPaymentSuccess.value = true;
          //               deal = result.item2 ?? deal;
          //             }
          //             currentTime = newTime;
          //           }
          //         },
          //         textStyle:
          //         AppFont.componentSmall.copyWith(color: AppColor.black90)),
          //   );
          // }),
          AppDimen.h6.height,
          Text(
            "Rp${controller.newOrder.value.sumTotalBill().toCurrency}",
            style: AppFont.componentSmallBold,
          ),
          const Spacer(),
          Obx(() {
            return (controller.newOrder.value.status ==
                    OrderStatusEnum.payment_verified.name)
                ? Lottie.asset(
                    ImgStrings.lottieDone,
                    animate: true,
                    repeat: false,
                    width: AppDimen.h192,
                  )
                : (controller.newOrder.value.status ==
                        OrderStatusEnum.payment_reject.name)
                    ? Lottie.asset(
                        ImgStrings.lottieError,
                        animate: true,
                        repeat: false,
                        width: AppDimen.h192,
                      )
                    : Lottie.asset(
                        ImgStrings.lottieClock,
                        animate: true,
                        repeat: true,
                        width: AppDimen.h192,
                      );
          }),
          const Spacer(),
          const Spacer(),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8.0),
            child: Obx(() {
              return (controller.newOrder.value.status ==
                          OrderStatusEnum.payment_verified.name) ||
                      (controller.newOrder.value.status ==
                          OrderStatusEnum.payment_reject.name)
                  ? PrimaryButton(
                      onPressed: () => Get.back(),
                      text: "text",
                      type: PrimaryButtonType.type4,
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        child: SizedBox(
                          width: MediaQuery.of(context).size.width,
                          child: Text(
                            Strings.back.tr,
                            textAlign: TextAlign.center,
                            style: AppFont.componentSmall
                                .copyWith(color: AppColor.white),
                          ),
                        ),
                      ),
                    )
                  : PrimaryButton(
                      onPressed: () {
                        if (paymentMethodDetail.paymentInfo?.type == "link") {
                          openUrl(paymentMethodDetail.paymentInfo?.value ?? '');
                        }
                      },
                      text: "text",
                      type: PrimaryButtonType.type4,
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        child: SizedBox(
                          width: MediaQuery.of(context).size.width,
                          child: Text(
                            Strings.continuePayment.tr,
                            textAlign: TextAlign.center,
                            style: AppFont.componentSmall
                                .copyWith(color: AppColor.white),
                          ),
                        ),
                      ),
                    );
            }),
          ),
          AppDimen.h20.height,
        ],
      ),
    ));
  }
}
