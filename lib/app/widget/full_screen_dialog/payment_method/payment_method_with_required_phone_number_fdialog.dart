import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/utils/animations.dart';
import 'package:mobile_crm/app/widget/button_widget/button_widget.dart';
import 'package:mobile_crm/app/widget/cached_image_widget.dart';
import 'package:mobile_crm/app/widget/input_widget/input_form_widget.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/values.dart';
import 'package:mobile_crm/data/models/payment_method_model.dart';

class PaymentMethodWithPhoneNumberFullScreenDialog {
  static show(
      {required BuildContext context,
      required VoidCallback onPay,
      required TextEditingController controller,
      required PaymentMethodDetail paymentMethodDetail}) {
    showGeneralDialog(
      context: context,
      pageBuilder: (context, animation, secondaryAnimation) {
        return Animations.fromBottom(
            animation,
            secondaryAnimation,
            _childWidget(
                context: context,
                paymentMethodDetail: paymentMethodDetail,
                controller: controller,
                onPay: onPay));
      },
      transitionDuration: const Duration(milliseconds: 400),
    );
  }

  static Dialog _childWidget(
      {required BuildContext context,
      required VoidCallback onPay,
      required TextEditingController controller,
      required PaymentMethodDetail paymentMethodDetail}) {
    return Dialog.fullscreen(
        child: WillPopScope(
      onWillPop: () => Future.value(false),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          AppBar(
            leading: IconButton(
              onPressed: () {
                Get.back();
              },
              icon: const Icon(
                Icons.close,
                color: AppColor.black90,
              ),
            ),
            automaticallyImplyLeading: false,
            backgroundColor: AppColor.white,
          ),
          SizedBox(
              width: AppDimen.h128 + AppDimen.h32,
              child: CachedImageWidget(
                imageUrl: paymentMethodDetail.icon?.small ?? '',
              )),
          20.0.height,
          Text(Strings.pleaseInputYourPaymentValue.trParams({
            'value': Strings.phoneNumber.tr,
            'payment': paymentMethodDetail.name ?? ''
          })),
          20.0.height,
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: AppInputText(
              label: Strings.phoneNumber.tr,
              controller: controller,
            ),
          ),
          const Spacer(),
          PrimaryButton(
            onPressed: onPay,
            text: '',
            type: PrimaryButtonType.type4,
            child: SizedBox(
              width: AppDimen.h64 + AppDimen.h32,
              child: Text(
                Strings.payBtn.tr,
                textAlign: TextAlign.center,
                style: AppFont.componentSmall.copyWith(color: AppColor.white),
              ),
            ),
          ),
          10.0.height,
        ],
      ),
    ));
  }
}
