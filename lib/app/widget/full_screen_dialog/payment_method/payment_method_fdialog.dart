import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/utils/animations.dart';
import 'package:mobile_crm/app/widget/cached_image_widget.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/values.dart';
import 'package:mobile_crm/data/models/payment_method_model.dart';

class PaymentMethodFullScreenDialog {
  static show(
      {required BuildContext context,
      required ValueChanged<PaymentMethodDetail> onChoose,
      required List<PaymentMethodModel> paymentMethods}) {
    showGeneralDialog(
      context: context,
      pageBuilder: (context, animation, secondaryAnimation) {
        return Animations.fromBottom(
            animation,
            secondaryAnimation,
            _childWidget(
                context: context,
                paymentMethods: paymentMethods,
                onChoose: onChoose));
      },
      transitionDuration: const Duration(milliseconds: 300),
    );
  }

  static Dialog _childWidget(
      {required BuildContext context,
      required ValueChanged<PaymentMethodDetail> onChoose,
      required List<PaymentMethodModel> paymentMethods}) {
    return Dialog.fullscreen(
        child: WillPopScope(
      onWillPop: () => Future.value(false),
      child: Column(
        children: [
          AppBar(
            leading: IconButton(
              onPressed: () {
                Get.back();
              },
              icon: const Icon(
                Icons.close,
                color: AppColor.black90,
              ),
            ),
            automaticallyImplyLeading: false,
            title: Text(
              Strings.selectPaymentMethod.tr,
              style: AppFont.componentSmall,
            ),
            centerTitle: true,
            backgroundColor: AppColor.white,
          ),
          Expanded(
              child: MediaQuery.removePadding(
            removeTop: true,
            context: context,
            child: ListView.builder(
                shrinkWrap: true,
                itemBuilder: (context, index) {
                  var method = paymentMethods[index];
                  method.detail
                      ?.sort((a, b) => (a.name ?? '').compareTo(b.name ?? ''));
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Container(
                        width: double.infinity,
                        decoration:
                            const BoxDecoration(color: AppColor.disable),
                        child: Padding(
                          padding: EdgeInsets.symmetric(
                              horizontal: AppDimen.h8, vertical: AppDimen.h4),
                          child: Text(
                            "${method.name}",
                            style: AppFont.componentSmallBold
                                .copyWith(fontSize: 11.sp),
                          ),
                        ),
                      ),
                      MediaQuery.removePadding(
                        context: context,
                        removeTop: true,
                        child: ListView.builder(
                          shrinkWrap: true,
                          itemBuilder: (context, index) {
                            var detail = method.detail![index];
                            return Padding(
                              padding:
                                  EdgeInsets.symmetric(horizontal: AppDimen.h8),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  InkWell(
                                    onTapDown: (details) {
                                      detail.methodDetailType = method.type;
                                      onChoose(detail);
                                      Get.back();
                                    },
                                    child: Row(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      mainAxisAlignment:
                                          MainAxisAlignment.start,
                                      children: [
                                        SizedBox(
                                          width: AppDimen.h32,
                                          height: AppDimen.h32,
                                          child: CachedImageWidget(
                                            imageUrl: detail.icon?.medium ?? '',
                                            alignment: Alignment.centerLeft,
                                            fit: BoxFit.contain,
                                            errorWidget: (context, url,
                                                    error) =>
                                                SizedBox(height: AppDimen.h32),
                                          ),
                                        ),
                                        20.0.width,
                                        Text(
                                          "${detail.name}",
                                          style: AppFont.componentSmallBold
                                              .copyWith(
                                                  color: AppColor.black90,
                                                  fontSize: 11.sp),
                                        ),
                                        const Spacer(),
                                        const Icon(
                                          Icons.keyboard_arrow_right_rounded,
                                          color: AppColor.black50,
                                        )
                                      ],
                                    ),
                                  ),
                                  const Divider(
                                    color: AppColor.black5,
                                  )
                                ],
                              ),
                            );
                          },
                          itemCount: method.detail?.length,
                        ),
                      )
                    ],
                  );
                },
                itemCount: paymentMethods.length),
          ))
        ],
      ),
    ));
  }
}
