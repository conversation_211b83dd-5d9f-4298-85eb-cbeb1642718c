import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/outlet/controllers/outlet_controller.dart';
import 'package:mobile_crm/app/modules/screen_wrapper/controller/wrapper_controller.dart';
import 'package:mobile_crm/app/utils/animations.dart';
import 'package:mobile_crm/app/widget/app_dotted_separator.dart';
import 'package:mobile_crm/app/widget/custom_circular_progress_widget.dart';
import 'package:mobile_crm/app/widget/html_widget.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/values.dart';
import 'package:mobile_crm/data/models/product_model.dart';

import '../../../../data/providers/db/database.dart';
import '../../app_page_empty.dart';
import '../../modal_bottom_sheet/app_modal_bottom_sheet.dart';

class PromotionDetailFullScreenDialog {
  static show({required BuildContext context, DealData? promotion}) {
    showGeneralDialog(
      context: context,
      pageBuilder: (context, animation, secondaryAnimation) {
        return Animations.fromBottom(animation, secondaryAnimation,
            _childWidget(context: context, promotion: promotion ?? DealData()));
      },
      transitionDuration: const Duration(milliseconds: 200),
    );
  }

  static Dialog _childWidget(
      {required BuildContext context, required DealData promotion}) {
    String? promotionType = promotion.promotionTypeName?.split('_')[1];
    final controller = Get.find<WrapperController>();
    final outletController = Get.find<OutletController>();
    String outlet = controller.configApp.value.language?.outlet ?? "Outlet";
    return Dialog.fullscreen(
        child: Column(
      children: [
        AppBar(
          leading: IconButton(
            onPressed: () {
              Get.back();
            },
            icon: const Icon(
              Icons.close,
              color: AppColor.black90,
            ),
          ),
          automaticallyImplyLeading: false,
          title: Text(
            'Detail Promotion',
            style: AppFont.componentSmall,
          ),
          centerTitle: true,
          backgroundColor: AppColor.white,
        ),
        Flexible(
          child: FutureBuilder(
            future: outletController
                .getDetailPromotion((promotion.promotionId ?? 0).toString()),
            builder: (context, snapshot) {
              if (snapshot.connectionState != ConnectionState.done) {
                return const CustomCircularProgressIndicator(
                  valueColor: AppColor.black90,
                );
              }
              if (snapshot.hasData) {
                promotion = snapshot.data ?? DealData();
                var filteredProducts = outletController
                    .filterDuplicateProduct(promotion.promotionProduct ?? []);
                return snapshot.data?.name == null
                    ? Flexible(
                        child: AppPageEmpty(
                          reason: "Promotion Not Found",
                          func: () => Get.back(),
                        ),
                      )
                    : Column(
                        children: [
                          5.0.height,
                          Flexible(
                            child: SingleChildScrollView(
                                child: Padding(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 8.0),
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    "${promotion.name}",
                                    style: AppFont.componentSmallBold,
                                  ),
                                  Text(
                                    promotionType == 'specialprice'
                                        ? "Special Price"
                                        : promotionType == 'discount'
                                            ? promotion.discountType == "items"
                                                ? "${promotionType?.capitalizeFirst}"
                                                : promotion.maximumDiscountNominal ==
                                                        0
                                                    ? "${promotionType?.capitalizeFirst} ${promotion.amount}% ${Strings.minimumTransaction.tr} Rp ${promotion.minOrder.toCurrency}"
                                                    : "${promotionType?.capitalizeFirst} ${promotion.amount}% ${Strings.minimumTransaction.tr} Rp ${promotion.minOrder.toCurrency} s/d Rp ${promotion.maximumDiscountNominal.toCurrency}"
                                            : "${promotionType?.capitalizeFirst}",
                                    style: AppFont.paragraphSmall,
                                  ),
                                  10.0.height,
                                  Text(
                                    Strings.termsAndConditions.tr,
                                    style: AppFont.componentSmallBold,
                                  ),
                                  Visibility(
                                    visible: promotion.termProduct != null,
                                    child: InkWell(
                                      onTapDown: (details) =>
                                          showPromoProductTerm(
                                              context: context,
                                              promotion: promotion,
                                              promotionType:
                                                  promotionType ?? '',
                                              controller: outletController),
                                      child: Column(
                                        mainAxisSize: MainAxisSize.min,
                                        mainAxisAlignment:
                                            MainAxisAlignment.start,
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            Strings.termProduct.tr,
                                            style: AppFont.paragraphMedium,
                                          ),
                                          Visibility(
                                            visible:
                                                promotion.termProduct?.qty != 0,
                                            child: Text(
                                              Strings.minPurchaseAmount
                                                  .trParams({
                                                "count":
                                                    "${promotion.termProduct?.qty}"
                                              }),
                                              style: AppFont.paragraphSmall,
                                            ),
                                          ),
                                          Row(
                                            children: [
                                              Text(
                                                  "${outletController.filterDuplicateProduct(promotion.termProduct?.products ?? []).length} Products",
                                                  style:
                                                      AppFont.paragraphSmall),
                                              Icon(
                                                Icons
                                                    .keyboard_arrow_right_rounded,
                                                color: AppColor.black,
                                                size: Constants.iconSizeSmall(
                                                    context),
                                              )
                                            ],
                                          ),
                                          10.0.height,
                                        ],
                                      ),
                                    ),
                                  ),
                                  Visibility(
                                    visible: promotion.minOrder != 0,
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(Strings.minimumTransaction.tr,
                                            style: AppFont.paragraphMedium),
                                        Text(
                                            "Rp ${promotion.minOrder.toCurrency}",
                                            style: AppFont.paragraphSmall),
                                        10.0.height,
                                      ],
                                    ),
                                  ),
                                  Visibility(
                                    visible: promotion
                                            .promotionProduct?.isNotEmpty ??
                                        false,
                                    child: InkWell(
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                              promotionType == 'specialprice'
                                                  ? Strings.specialPrice.tr
                                                  : promotionType == 'discount'
                                                      ? Strings.discount.tr
                                                      : 'Free',
                                              style: AppFont.paragraphMedium),
                                          Row(
                                            children: [
                                              Text(
                                                  "${filteredProducts.length} Products",
                                                  style:
                                                      AppFont.paragraphSmall),
                                              Icon(
                                                Icons
                                                    .keyboard_arrow_right_rounded,
                                                color: AppColor.black,
                                                size: Constants.iconSizeSmall(
                                                    context),
                                              )
                                            ],
                                          ),
                                          10.0.height,
                                        ],
                                      ),
                                      onTap: () {
                                        showPromoProduct(
                                            context: context,
                                            promotion: promotion,
                                            promotionType: promotionType ?? '',
                                            controller: outletController);
                                      },
                                    ),
                                  ),
                                  Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        Strings.validityPeriod.tr,
                                        style: AppFont.paragraphMedium,
                                      ),
                                      Text(
                                          "${promotion.timeActive?.startPromotionDate.toDate} ${promotion.timeActive?.startPromotionTime?.substring(0, 5)} - ${promotion.timeActive?.endPromotionDate.toDate} ${promotion.timeActive?.endPromotionTime?.substring(0, 5)}",
                                          style: AppFont.paragraphSmall),
                                      10.0.height,
                                    ],
                                  ),
                                  InkWell(
                                    onTapDown: (details) {
                                      ShowCustomModalBottom
                                          .showModalBottomRedeemPeriod(
                                              context: context,
                                              deal: promotion);
                                    },
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(Strings.redeemPeriod.tr,
                                            style: AppFont.paragraphMedium),
                                        Row(
                                          children: [
                                            Text(
                                                outletController
                                                    .getPromoDay(promotion),
                                                style: AppFont.paragraphSmall),
                                            Icon(
                                              Icons
                                                  .keyboard_arrow_right_rounded,
                                              color: AppColor.black,
                                              size: Constants.iconSizeSmall(
                                                  context),
                                            )
                                          ],
                                        ),
                                        10.0.height,
                                      ],
                                    ),
                                  ),
                                  Visibility(
                                    visible:
                                        promotion.outlet?.isNotEmpty == true,
                                    child: InkWell(
                                      child: Padding(
                                        padding: const EdgeInsets.symmetric(
                                            vertical: 5),
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                                Strings.availableAt.trParams(
                                                    {"outlet": outlet}),
                                                style: AppFont.paragraphMedium),
                                            Row(
                                              children: [
                                                Text(
                                                    "${promotion.outlet?.length} $outlet's",
                                                    style:
                                                        AppFont.paragraphSmall),
                                                Icon(
                                                  Icons
                                                      .keyboard_arrow_right_rounded,
                                                  color: AppColor.black,
                                                  size: Constants.iconSizeSmall(
                                                      context),
                                                )
                                              ],
                                            ),
                                            10.0.height,
                                          ],
                                        ),
                                      ),
                                      onTap: () {
                                        showPromoOutlet(
                                            context: context,
                                            promotionType: promotionType ?? '',
                                            outlet: outlet,
                                            promotion: promotion);
                                      },
                                    ),
                                  ),
                                  InkWell(
                                    child: Padding(
                                      padding: const EdgeInsets.symmetric(
                                          vertical: 5),
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(Strings.aboutPromo.tr,
                                              style: AppFont.paragraphMedium),
                                          Row(
                                            children: [
                                              Text("Details",
                                                  style:
                                                      AppFont.paragraphSmall),
                                              Icon(
                                                Icons
                                                    .keyboard_arrow_right_rounded,
                                                color: AppColor.black,
                                                size: Constants.iconSizeSmall(
                                                    context),
                                              )
                                            ],
                                          ),
                                          10.0.height,
                                        ],
                                      ),
                                    ),
                                    onTap: () {
                                      showPromoDescription(
                                          context: context,
                                          promotionType: promotionType ?? '',
                                          outlet: outlet,
                                          promotion: promotion);
                                    },
                                  )
                                ],
                              ),
                            )),
                          ),
                        ],
                      );
              }
              return const CustomCircularProgressIndicator();
            },
          ),
        )
      ],
    ));
  }

  static showPromoProduct(
      {required BuildContext context,
      required String promotionType,
      required DealData promotion,
      required OutletController controller}) {
    showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        constraints: BoxConstraints(
            minHeight: Get.size.height * 0.3,
            maxHeight: Get.size.height * 0.8,
            minWidth: Constants.defaultBoxConstraints(context),
            maxWidth: Constants.defaultBoxConstraints(context)),
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(15.0), topRight: Radius.circular(15.0)),
        ),
        builder: (context) {
          var filteredProducts = controller
              .filterDuplicateProduct(promotion.promotionProduct ?? []);
          return Padding(
            padding: EdgeInsets.symmetric(horizontal: Constants.defaultPadding),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                const Icon(Icons.drag_handle),
                Align(
                  alignment: Alignment.centerLeft,
                  child: Text(
                      promotionType == 'specialprice'
                          ? Strings.specialPrice.tr
                          : promotionType == 'discount'
                              ? Strings.discount.tr
                              : 'Free',
                      style: AppFont.componentSmall),
                ),
                Align(
                  alignment: Alignment.centerLeft,
                  child: Text("${filteredProducts.length} Product",
                      style: AppFont.componentSmall),
                ),
                const SizedBox(height: 5),
                Flexible(
                  child: ListView.builder(
                      shrinkWrap: true,
                      itemCount: filteredProducts.length,
                      itemBuilder: (BuildContext ctxt, int index) {
                        ProductModel product = filteredProducts[index];
                        return Container(
                          padding: EdgeInsets.symmetric(
                              vertical: Constants.defaultPadding),
                          decoration: const BoxDecoration(
                              border: Border(
                                  bottom: BorderSide(
                                      width: 1, color: AppColor.black10))),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                product.name.toString(),
                                style: AppFont.componentSmall,
                              ),
                              product.pricePromo == product.priceSell
                                  ? Row(
                                      children: [
                                        Text(
                                            "Rp.${product.priceSell.toCurrency}",
                                            style: AppFont.componentSmall
                                                .copyWith(
                                                    decoration:
                                                        TextDecoration.combine([
                                                      TextDecoration.lineThrough
                                                    ]),
                                                    color: AppColor.black50)),
                                        5.0.width,
                                        Text("Rp.0",
                                            style: AppFont.componentSmall),
                                      ],
                                    )
                                  : Row(
                                      children: [
                                        Text(
                                            "Rp.${product.priceSell.toCurrency}",
                                            style: AppFont.componentSmall
                                                .copyWith(
                                                    decoration:
                                                        TextDecoration.combine([
                                                      TextDecoration.lineThrough
                                                    ]),
                                                    color: AppColor.black50)),
                                        5.0.width,
                                        Text(
                                            "Rp.${product.pricePromo.toCurrency}",
                                            style: AppFont.componentSmall),
                                      ],
                                    )
                            ],
                          ),
                        );
                      }),
                ),
              ],
            ),
          );
        });
  }

  static showPromoProductTerm(
      {required BuildContext context,
      required String promotionType,
      required DealData promotion,
      required OutletController controller}) {
    showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        constraints: BoxConstraints(
            minHeight: Get.size.height * 0.3,
            maxHeight: Get.size.height * 0.8,
            minWidth: Constants.defaultBoxConstraints(context),
            maxWidth: Constants.defaultBoxConstraints(context)),
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(15.0), topRight: Radius.circular(15.0)),
        ),
        builder: (context) {
          var filteredProducts = controller
              .filterDuplicateProduct(promotion.termProduct?.products ?? []);
          return Padding(
            padding: EdgeInsets.symmetric(horizontal: Constants.defaultPadding),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                const Icon(
                  Icons.drag_handle,
                  color: AppColor.black70,
                ),
                Align(
                  alignment: Alignment.centerLeft,
                  child: Text(Strings.termProduct.tr,
                      style: AppFont.componentSmall),
                ),
                Align(
                  alignment: Alignment.centerLeft,
                  child: Text("${filteredProducts.length} Product",
                      style: AppFont.componentSmall),
                ),
                const SizedBox(height: 5),
                Flexible(
                  child: ListView.builder(
                      shrinkWrap: true,
                      itemCount: filteredProducts.length,
                      itemBuilder: (BuildContext ctxt, int index) {
                        ProductModel product = filteredProducts[index];
                        return Container(
                          padding: EdgeInsets.symmetric(
                              vertical: Constants.defaultPadding),
                          decoration: const BoxDecoration(
                              border: Border(
                                  bottom: BorderSide(
                                      width: 1, color: AppColor.black10))),
                          child: Text(
                            product.name.toString(),
                            style: AppFont.componentSmall,
                          ),
                        );
                      }),
                ),
              ],
            ),
          );
        });
  }

  static showPromoOutlet(
      {required BuildContext context,
      required String promotionType,
      required String outlet,
      required DealData promotion}) {
    showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        constraints: BoxConstraints(
            minHeight: 200, maxWidth: Constants.defaultBoxConstraints(context)),
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(15.0), topRight: Radius.circular(15.0)),
        ),
        builder: (context) {
          return Padding(
            padding: EdgeInsets.symmetric(horizontal: Constants.defaultPadding),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                const Icon(
                  Icons.drag_handle,
                  color: AppColor.black70,
                ),
                Align(
                  alignment: Alignment.centerLeft,
                  child: Text("Availability Location",
                      style: AppFont.componentSmall),
                ),
                Align(
                  alignment: Alignment.centerLeft,
                  child: Text("${promotion.outlet?.length} $outlet's",
                      style: AppFont.componentSmall),
                ),
                const SizedBox(height: 5),
                ListView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: promotion.outlet?.length ?? 0,
                    itemBuilder: (BuildContext ctxt, int index) {
                      return Container(
                        padding: EdgeInsets.symmetric(
                            vertical: Constants.defaultPadding),
                        decoration: const BoxDecoration(
                            border: Border(
                                bottom: BorderSide(
                                    width: 1, color: AppColor.black10))),
                        child: Text(
                          "${promotion.outlet?[index].name.toString()}",
                          style: AppFont.componentSmall,
                        ),
                      );
                    }),
              ],
            ),
          );
        });
  }

  static showPromoDescription(
      {required BuildContext context,
      required String promotionType,
      required String outlet,
      required DealData promotion}) {
    showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        constraints: BoxConstraints(
            minHeight: 200, maxWidth: Constants.defaultBoxConstraints(context)),
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(15.0), topRight: Radius.circular(15.0)),
        ),
        builder: (context) {
          return Padding(
            padding: EdgeInsets.symmetric(horizontal: Constants.defaultPadding),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                const Icon(
                  Icons.drag_handle,
                  color: AppColor.black70,
                ),
                Align(
                  alignment: Alignment.centerLeft,
                  child:
                      Text("Promo Descriptions", style: AppFont.componentSmall),
                ),
                const DottedDivider(
                  width: 4,
                  height: 3,
                  color: AppColor.black5,
                ),
                const SizedBox(height: 5),
                HtmlWidget(data: promotion.term ?? ''),
                20.0.height,
              ],
            ),
          );
        });
  }
}
