import 'dart:io' show File;

import 'package:flutter/material.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:mobile_crm/app/utils/animations.dart';
import 'package:mobile_crm/app/widget/button_widget/button_widget.dart';
import 'package:mobile_crm/app/widget/custom_circular_progress_widget.dart';
import 'package:mobile_crm/app/widget/input_widget/input_form_widget.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/app_strings.dart';
import 'package:mobile_crm/data/models/feedback_model.dart';
import 'package:mobile_crm/domain/repository/transaction_repository.dart';
import 'package:speech_to_text/speech_to_text.dart' as stt;
import 'package:avatar_glow/avatar_glow.dart';

import '../../../utils/image_picker_helper.dart';
import '../../../utils/toast.dart';

class FeedbackFullScreenDialog {
  var star = (5.0).obs;
  var isLoading = false.obs;
  Rx<XFile?> xFile = XFile("").obs;
  TextEditingController textEditingController = TextEditingController();
  final stt.SpeechToText _speech = stt.SpeechToText();
  var isListening = false.obs;
  var _isInitialized = false;

  void show(
      {required BuildContext context,
      required TransactionRepository transRepo,
      String? salesId}) {
    showGeneralDialog(
      context: context,
      pageBuilder: (context, animation, secondaryAnimation) {
        return Animations.fromBottom(
            animation,
            secondaryAnimation,
            childWidget(
                context: context, transRepo: transRepo, salesId: salesId));
      },
      transitionDuration: const Duration(milliseconds: 200),
    );
  }

  Dialog childWidget(
      {required BuildContext context,
      required TransactionRepository transRepo,
      String? salesId}) {
    return Dialog.fullscreen(
        child: WillPopScope(
      onWillPop: () => Future.value(false),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          AppBar(
            leading: IconButton(
              onPressed: () {
                Get.back();
              },
              icon: const Icon(
                Icons.close,
                color: AppColor.black90,
              ),
            ),
            automaticallyImplyLeading: false,
            title: Text(
              'How was your experience?',
              style: AppFont.componentLarge,
            ),
            centerTitle: true,
            backgroundColor: AppColor.white,
            elevation: 0,
          ),
          Expanded(
            child: SingleChildScrollView(
              child: Padding(
                padding: EdgeInsets.all(AppDimen.h24),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    30.0.height,
                    Text(
                      'Rate your experience',
                      style: AppFont.paragraphMedium.copyWith(
                        color: AppColor.black70,
                      ),
                    ),
                    20.0.height,
                    Obx(() {
                      return Text(
                        star.value == 1
                            ? Strings.veryBad.tr
                            : star.value == 2
                                ? Strings.bad.tr
                                : star.value == 3
                                    ? Strings.normal.tr
                                    : star.value == 4
                                        ? Strings.good.tr
                                        : Strings.excellent.tr,
                        style: AppFont.componentLarge.copyWith(
                          color: AppColor.black90,
                        ),
                      );
                    }),
                    15.0.height,
                    RatingBar.builder(
                      initialRating: star.value,
                      glow: false,
                      direction: Axis.horizontal,
                      allowHalfRating: false,
                      itemCount: 5,
                      itemSize: 48,
                      unratedColor: AppColor.black10,
                      itemPadding: const EdgeInsets.symmetric(horizontal: 8.0),
                      itemBuilder: (context, _) => const Icon(
                        Icons.star_rounded,
                        color: Colors.amber,
                      ),
                      onRatingUpdate: (double value) {
                        star.value = value;
                      },
                    ),
                    40.0.height,
                    _buildInputContainer(),
                    24.0.height,
                    Obx(() => xFile.value?.path != ''
                        ? Container(
                            width: double.infinity,
                            decoration: BoxDecoration(
                              border: Border.all(
                                color: AppColor.black10,
                              ),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Column(
                              children: [
                                Stack(
                                  children: [
                                    ClipRRect(
                                      borderRadius: BorderRadius.circular(12),
                                      child: Image.file(
                                        File(xFile.value?.path ?? ''),
                                        width: double.infinity,
                                        height: 200,
                                        fit: BoxFit.cover,
                                      ),
                                    ),
                                    Positioned(
                                      top: 8,
                                      right: 8,
                                      child: InkWell(
                                        onTap: () {
                                          xFile.value = XFile('');
                                        },
                                        child: Container(
                                          padding: const EdgeInsets.all(4),
                                          decoration: const BoxDecoration(
                                            color: Colors.black54,
                                            shape: BoxShape.circle,
                                          ),
                                          child: const Icon(
                                            Icons.close,
                                            color: Colors.white,
                                            size: 20,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                                InkWell(
                                  onTap: () => showModalBottomSheet(
                                    constraints: BoxConstraints(
                                        maxHeight:
                                            MediaQuery.of(context).size.height *
                                                .8,
                                        minWidth:
                                            MediaQuery.of(context).size.width),
                                    context: context,
                                    shape: const RoundedRectangleBorder(
                                        borderRadius:
                                            BorderRadiusDirectional.only(
                                                topStart: Radius.circular(15),
                                                topEnd: Radius.circular(15))),
                                    isScrollControlled: true,
                                    builder: (context) =>
                                        _buildImagePickerBottomSheet(context),
                                  ),
                                  child: Padding(
                                    padding: const EdgeInsets.all(12),
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        const Icon(
                                          Icons.photo_camera,
                                          size: 20,
                                          color: AppColor.black70,
                                        ),
                                        8.0.width,
                                        Text(
                                          Strings.changePhoto.tr,
                                          style:
                                              AppFont.paragraphSmall.copyWith(
                                            color: AppColor.black70,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          )
                        : InkWell(
                            onTap: () => showModalBottomSheet(
                              constraints: BoxConstraints(
                                  maxHeight:
                                      MediaQuery.of(context).size.height * .8,
                                  minWidth: MediaQuery.of(context).size.width),
                              context: context,
                              shape: const RoundedRectangleBorder(
                                  borderRadius: BorderRadiusDirectional.only(
                                      topStart: Radius.circular(15),
                                      topEnd: Radius.circular(15))),
                              isScrollControlled: true,
                              builder: (context) =>
                                  _buildImagePickerBottomSheet(context),
                            ),
                            child: Container(
                              width: double.infinity,
                              padding: EdgeInsets.all(AppDimen.h16),
                              decoration: BoxDecoration(
                                border: Border.all(
                                  color: AppColor.black10,
                                  style: BorderStyle.solid,
                                ),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Column(
                                children: [
                                  Icon(
                                    Icons.add_photo_alternate_outlined,
                                    size: 32,
                                    color: AppColor.black50,
                                  ),
                                  8.0.height,
                                  Text(
                                    Strings.addPhoto.tr,
                                    style: AppFont.paragraphSmall.copyWith(
                                      color: AppColor.black70,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          )),
                  ],
                ),
              ),
            ),
          ),
          Container(
            padding: EdgeInsets.all(AppDimen.h16),
            decoration: BoxDecoration(
              color: AppColor.white,
              boxShadow: [
                BoxShadow(
                  color: AppColor.black10,
                  blurRadius: 4,
                  offset: Offset(0, -2),
                ),
              ],
            ),
            child: Obx(() {
              return isLoading.value
                  ? PrimaryButton(
                      onPressed: () {},
                      text: "",
                      type: PrimaryButtonType.type4,
                      child: const CustomCircularProgressIndicator(),
                    )
                  : PrimaryButton(
                      onPressed: () async {
                        isLoading.value = true;
                        FeedbackModel feedback = FeedbackModel(
                            comment: textEditingController.text,
                            stars: star.value.toInt(),
                            file: xFile.value,
                            salesId: salesId);
                        await transRepo
                            .postGiveFeedbackV2(feedback)
                            .whenComplete(() => isLoading.value = false);
                        Toast.show(Strings.thankYouForFeedback.tr,
                            type: ToastType.success, duration: 4);
                        Get.back();
                      },
                      type: PrimaryButtonType.type4,
                      text: Strings.submit.tr,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.send_rounded,
                            color: AppColor.white,
                            size: 20,
                          ),
                          8.0.width,
                          Text(
                            'Submit Feedback',
                            style: AppFont.componentMediumBold
                                .copyWith(color: AppColor.white),
                          ),
                        ],
                      ),
                    );
            }),
          ),
        ],
      ),
    ));
  }

  Widget _buildInputContainer() {
    return Container(
      decoration: BoxDecoration(
        color: AppColor.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColor.black10),
      ),
      child: Column(
        children: [
          Stack(
            alignment: Alignment.centerRight,
            children: [
              AppInputText(
                label: 'Tell us about your experience...',
                type: InputFormType.notes,
                controller: textEditingController,
                keyboardType: TextInputType.multiline,
              ),
            ],
          ),
          Divider(height: 1, color: AppColor.black10),
          Obx(() => InkWell(
                onTap: () async {
                  if (!isListening.value) {
                    isListening.value = true;
                    await _initSpeech();
                  } else {
                    print('[Speech] Stopping speech recognition');
                    isListening.value = false;
                    _speech.stop();
                  }
                },
                child: Container(
                  padding: EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      if (isListening.value)
                        AvatarGlow(
                          endRadius: 25.0,
                          animate: true,
                          duration: Duration(milliseconds: 15000),
                          glowColor: Colors.blue,
                          repeat: true,
                          repeatPauseDuration: Duration(milliseconds: 100),
                          child: Icon(
                            Icons.mic,
                            color: Colors.blue,
                            size: 28,
                          ),
                        )
                      else
                        Icon(
                          Icons.mic_none,
                          color: AppColor.black50,
                          size: 28,
                        ),
                      SizedBox(width: 8),
                      Text(
                        isListening.value
                            ? 'Listening... Tap to stop'
                            : 'Tap to speak your feedback',
                        style: AppFont.paragraphMedium.copyWith(
                          color: isListening.value
                              ? Colors.blue
                              : AppColor.black70,
                        ),
                      ),
                    ],
                  ),
                ),
              )),
        ],
      ),
    );
  }

  Future<void> _initSpeech() async {
    try {
      if (!_isInitialized) {
        print('[Speech] Initializing speech recognition...');
        _isInitialized = await _speech.initialize(
          onStatus: (status) {
            print('[Speech] Status: $status');
          },
          onError: (errorNotification) {
            print('[Speech] Error: ${errorNotification.errorMsg}');
            Toast.show(
              'Speech recognition error: ${errorNotification.errorMsg}',
              type: ToastType.error,
            );
            isListening.value = false;
          },
        );
        if (!_isInitialized) {
          print('[Speech] Speech recognition not available');
          Toast.show(
            Strings.speechNotAvailable.tr,
            type: ToastType.error,
          );
          return;
        }
      }

      print('[Speech] Getting available locales');
      var locales = await _speech.locales();
      locales.forEach((p) {
        print('[Speech] ${p.name}:${p.localeId}');
      });

      print('[Speech] Looking for Indonesian locale');
      var indonesianLocale = locales.firstWhere(
        (locale) => locale.name.toLowerCase().contains('indonesia'),
        orElse: () {
          print(
              '[Speech] Indonesian locale not found, using first available locale');
          return locales.first;
        },
      );

      Toast.show(
        "Mulai berbicara... Ketuk lagi untuk berhenti",
        type: ToastType.info,
        duration: 2,
      );

      print(
          '[Speech] Starting listening with locale: ${indonesianLocale.localeId}');
      _speech.listen(
        listenFor: Duration(seconds: 15),        
        onResult: (result) {
          if (result.finalResult) {
            print('[Speech] Final result: ${result.recognizedWords}');
            textEditingController.text =
                '${textEditingController.text} ${result.recognizedWords}'
                    .trim();
            Toast.show(
              "Berhasil menambahkan teks",
              type: ToastType.success,
              duration: 2,
            );
          }
          isListening.value = false;
        },
        localeId: indonesianLocale.localeId,
        listenOptions: stt.SpeechListenOptions(
          cancelOnError: true,
          partialResults: true,
          autoPunctuation: true,
          enableHapticFeedback: true,
          onDevice: false,
        ),

      );
    } catch (e) {
      print('[Speech] Error in _initSpeech: $e');
      Toast.show(
        Strings.speechError.tr,
        type: ToastType.error,
      );
      isListening.value = false;
    }
  }

  Widget _buildImagePickerBottomSheet(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Icon(
            Icons.horizontal_rule_rounded,
            color: AppColor.black70,
          ),
          Align(
            alignment: Alignment.centerLeft,
            child: Text(
              Strings.selectImageFrom.tr,
              style: AppFont.paragraphMedium,
            ),
          ),
          10.0.height,
          PrimaryButton(
            onPressed: () async {
              Get.back();
              final image = await ImagePickerHelper.getImageFromCamera();
              if (image != null) {
                xFile.value = image;
              }
            },
            text: Strings.camera.tr,
            type: PrimaryButtonType.type4,
            child: SizedBox(
              width: MediaQuery.of(context).size.width,
              child: Text(
                Strings.camera.tr,
                textAlign: TextAlign.center,
                style:
                    AppFont.componentSmallBold.copyWith(color: AppColor.white),
              ),
            ),
          ),
          10.0.height,
          PrimaryButton(
            onPressed: () async {
              Get.back();
              final image = await ImagePickerHelper.getImageFromGallery();
              if (image != null) {
                xFile.value = image;
              }
            },
            text: Strings.gallery.tr,
            width: MediaQuery.of(context).size.width,
            type: PrimaryButtonType.type4,
            child: SizedBox(
              width: MediaQuery.of(context).size.width,
              child: Text(
                Strings.gallery.tr,
                style:
                    AppFont.componentSmallBold.copyWith(color: AppColor.white),
                textAlign: TextAlign.center,
              ),
            ),
          ),
          20.0.height,
        ],
      ),
    );
  }
}
