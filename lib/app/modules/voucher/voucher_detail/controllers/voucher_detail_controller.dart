// ignore_for_file: prefer_typing_uninitialized_variables

import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/utils/app_alert.dart';
import 'package:mobile_crm/app/utils/logger.dart';
import 'package:mobile_crm/app/utils/toast.dart';
import 'package:mobile_crm/core/extensions/future_extensions.dart';
import 'package:mobile_crm/core/theme/app_color.dart';
import 'package:mobile_crm/core/theme/app_font.dart';
import 'package:mobile_crm/core/values/values.dart';
import 'package:mobile_crm/data/models/secret_id_model.dart';
import 'package:mobile_crm/data/repository/deal_repository.dart';
import 'package:mobile_crm/data/services/in_app_review_service.dart';
import 'package:mobile_crm/domain/repository/deal_repository.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

import '../../../../../data/providers/db/database.dart';

class VoucherDetailController extends GetxController {
  final DealRepository _repoDeal = Get.find<DealRepositoryIml>();
  final StreamController<DealData> _currentDealModelStreamCtrl =
      StreamController<DealData>.broadcast();

  Stream<DealData> get onCurrentDealModelChanged =>
      _currentDealModelStreamCtrl.stream;

  var isLoading = false.obs;
  var vouchers = DealData().obs;
  var secretId = SecretIdModel().obs;
  var voucherId = '1';
  var previousRoute = "".obs;
  bool isVoucherExist = true;

  Timer? timer;

  @override
  void onInit() async {
    voucherId = Get.parameters['id'] ?? '0';
    getPreviousRoute();
    await fetchVoucherDetail.withRetries(3);
    if (Get.arguments.runtimeType == DealData().runtimeType) {
      try {
        updateStream(Get.arguments);
      } catch (e, s) {
        errorLogger(pos: "pos", error: e, stackTrace: s);
      }
    }
    Future.delayed(const Duration(seconds: 1), () async {
      await fetchVoucherDetail.withRetries(3);
    });
    timer = Timer.periodic(
      const Duration(seconds: 30),
      (it) async {
        if (isVoucherExist) {
          await fetchVoucherDetail.withRetries(1);
        } else {
          it.cancel();
          await InAppReviewService.requestReview();
        }
      },
    );
    // InAppReviewService.openStore();
    await Sentry.addBreadcrumb(Breadcrumb(
        type: 'debug',
        category: 'user.activity.voucher.detail',
        data: {'name': vouchers.value.name},
        level: SentryLevel.debug));
    super.onInit();
  }

  void getPreviousRoute() => previousRoute.value = Get.routing.previous;

  @override
  void dispose() {
    _currentDealModelStreamCtrl.close();
    isVoucherExist = false;
    timer?.cancel();
    super.dispose();
  }

  @override
  void onClose() {
    _currentDealModelStreamCtrl.close();
    isVoucherExist = false;
    timer?.cancel();
    super.onClose();
  }

  void updateStream(DealData voucher) {
    if (_currentDealModelStreamCtrl.isClosed ||
        _currentDealModelStreamCtrl.isBlank == true) {
      return;
    }
    _currentDealModelStreamCtrl.sink.add(voucher);
  }

  Future fetchVoucherDetail() async {
    var result = await _repoDeal.getDetailMyVoucher(promotionId: voucherId);
    isVoucherExist = result == null ? false : true;
    vouchers.value = result ?? DealData();
    updateStream(vouchers.value);
    if (!isVoucherExist) {
      timer?.cancel();
    }
  }

  Future<SecretIdModel> getSecretId() async {
    isLoading.value = true;
    var result = await _repoDeal
        .getMyVoucherSecretId(voucherId)
        .whenComplete(() => isLoading.value = false);

    if (result != null) {
      secretId.value = result;
      isLoading.value = false;
    } else {
      isLoading.value = false;
      Toast.show("Get Secret Id Failed");
      Get.back();
    }

    return secretId.value;
  }

  String getPromoDay(DealData voucher) {
    List redeemPeriod = [];
    List day = [
      voucher.sunday,
      voucher.monday,
      voucher.tuesday,
      voucher.wednesday,
      voucher.thursday,
      voucher.friday,
      voucher.saturday,
    ];

    if (day[0] == 1) redeemPeriod.add(Strings.sunday.tr);
    if (day[1] == 1) redeemPeriod.add(Strings.monday.tr);
    if (day[2] == 1) redeemPeriod.add(Strings.tuesday.tr);
    if (day[3] == 1) redeemPeriod.add(Strings.wednesday.tr);
    if (day[4] == 1) redeemPeriod.add(Strings.thursday.tr);
    if (day[5] == 1) redeemPeriod.add(Strings.friday.tr);
    if (day[6] == 1) redeemPeriod.add(Strings.saturday.tr);

    if (redeemPeriod.isNotEmpty) {
      if (redeemPeriod.length == 1) {
        return "$redeemPeriod".replaceAll("[", "").replaceAll("]", "");
      } else if (redeemPeriod.length == 7) {
        return Strings.everyDay.tr;
      } else if (redeemPeriod.length > 1) {
        return "$redeemPeriod".replaceAll("[", "").replaceAll("]", "");
      }
    }

    return '-';
  }

  Future<void> getPopUpMenu(int item) async {
    if (item == 1) {
      refundVoucher();
    }
  }

  void refundVoucher() {
    AppAlert.showConfirmInfoDialog(
        context: Get.context!,
        barrierDismissible: false,
        message: Strings.refundMessage.tr,
        actions: [
          TextButton(
              onPressed: () {
                Get.back();
              },
              child: Text(
                Strings.cancel.tr,
                style: AppFont.componentSmall.copyWith(color: AppColor.white),
              )),
          TextButton(
              onPressed: () async {
                Get.back();
                if (vouchers.value.priceType == 'money') {
                  Toast.show(Strings.voucherWithCastCantRefund.tr,
                      type: ToastType.warning, duration: 5);
                } else {
                  var resp = await _repoDeal
                      .refundVoucher("${vouchers.value.promotionBuyId ?? 0}");
                  if (resp.status) {
                    Toast.show(resp.message, type: ToastType.success);
                    await fetchVoucherDetail();
                  } else {
                    Toast.show(resp.message, type: ToastType.error);
                  }
                }
              },
              child: Text(
                'Refund',
                style: AppFont.componentSmall.copyWith(color: AppColor.white),
              ))
        ]);
  }
}
