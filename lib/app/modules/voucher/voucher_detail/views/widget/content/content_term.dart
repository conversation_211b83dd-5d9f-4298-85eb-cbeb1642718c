import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/helper/deal_helper.dart';
import 'package:mobile_crm/app/helper/product_helper.dart';
import 'package:mobile_crm/app/modules/screen_wrapper/controller/wrapper_controller.dart';
import 'package:mobile_crm/app/widget/modal_bottom_sheet/app_modal_bottom_sheet.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/values.dart';
import 'package:mobile_crm/data/models/deal_model.dart';
import 'package:mobile_crm/data/models/product_model.dart';

import '../../../../../../../data/providers/db/database.dart';

class ContentTerm extends StatelessWidget {
  const ContentTerm({Key? key, required this.voucher}) : super(key: key);
  final DealData voucher;

  @override
  Widget build(BuildContext context) {
    final wrapperC = Get.find<WrapperController>();
    String outlet = wrapperC.configApp.value.language?.outlet ?? "Outlet";
    return SliverToBoxAdapter(
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: Constants.defaultPadding),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            10.0.height,
            Align(
              alignment: Alignment.centerLeft,
              child: Text(Strings.terms.tr, style: AppFont.componentSmall),
            ),
            voucher.promoNominal == null
                ? const SizedBox()
                : Container(
                    padding: const EdgeInsets.symmetric(vertical: 5),
                    decoration: const BoxDecoration(
                        border: Border(
                            bottom:
                                BorderSide(width: 1, color: AppColor.black10))),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(Strings.discount, style: AppFont.componentSmall),
                        voucher.promoDiscountType?.toLowerCase() ==
                                DealDiscountType.percent.name.toLowerCase()
                            ? Text("${voucher.promoNominal}%",
                                style: AppFont.componentSmall)
                            : Text("Rp ${voucher.promoNominal}",
                                style: AppFont.componentSmall),
                      ],
                    ),
                  ),
            Container(
              padding: const EdgeInsets.symmetric(vertical: 5),
              decoration: const BoxDecoration(
                  border: Border(
                      bottom: BorderSide(width: 1, color: AppColor.black10))),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    Strings.availableUntil.tr,
                    style: AppFont.componentSmall,
                  ),
                  Text(
                      "${voucher.endPromotionDate.toDate}, ${voucher.endPromotionTime?.toTimeFormat}",
                      style: AppFont.componentSmall)
                ],
              ),
            ),
            voucher.promoType?.toLowerCase() == DealPromoType.special_price.name
                ? InkWell(
                    child: Container(
                      padding: const EdgeInsets.symmetric(vertical: 5),
                      decoration: const BoxDecoration(
                          border: Border(
                              bottom: BorderSide(
                                  width: 1, color: AppColor.black10))),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(Strings.specialPrice.tr,
                              style: AppFont.componentSmall),
                          Row(
                            children: [
                              Text(
                                  "${ProductHelper.filterDuplicateProduct(voucher.promotionProduct ?? []).length} Products",
                                  style: AppFont.componentSmall),
                              Icon(
                                Icons.keyboard_arrow_right_rounded,
                                color: AppColor.black,
                                size: Constants.iconSizeSmall(context),
                              )
                            ],
                          ),
                        ],
                      ),
                    ),
                    onTap: () {
                      showModalBottomSheet(
                          context: context,
                          isScrollControlled: true,
                          constraints: BoxConstraints(
                              minHeight: 200,
                              maxHeight:
                                  MediaQuery.of(context).size.height * 0.8,
                              maxWidth:
                                  Constants.defaultBoxConstraints(context)),
                          shape: const RoundedRectangleBorder(
                            borderRadius: BorderRadius.only(
                                topLeft: Radius.circular(15.0),
                                topRight: Radius.circular(15.0)),
                          ),
                          builder: (context) {
                            var listPromotionProduct =
                                ProductHelper.filterDuplicateProduct(
                                    voucher.promotionProduct ?? []);
                            return Padding(
                              padding: EdgeInsets.symmetric(
                                  horizontal: Constants.defaultPadding),
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                children: <Widget>[
                                  Icon(
                                    Icons.drag_handle,
                                    size: Constants.iconSizeSmall(context),
                                    color: AppColor.black70,
                                  ),
                                  Align(
                                    alignment: Alignment.centerLeft,
                                    child: Text(Strings.specialPrice.tr,
                                        style: AppFont.componentSmall),
                                  ),
                                  Align(
                                    alignment: Alignment.centerLeft,
                                    child: Text(
                                        "${listPromotionProduct.length} Product",
                                        style: AppFont.componentSmall),
                                  ),
                                  const SizedBox(height: 5),
                                  Flexible(
                                    child: ListView.builder(
                                        shrinkWrap: true,
                                        itemCount: listPromotionProduct.length,
                                        itemBuilder:
                                            (BuildContext ctxt, int index) {
                                          ProductModel product =
                                              listPromotionProduct[index];
                                          return Container(
                                            padding: EdgeInsets.symmetric(
                                                vertical:
                                                    Constants.defaultPadding),
                                            decoration: const BoxDecoration(
                                                border: Border(
                                                    bottom: BorderSide(
                                                        width: 1,
                                                        color:
                                                            AppColor.black10))),
                                            child: Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment
                                                      .spaceBetween,
                                              children: [
                                                Text(
                                                  product.name.toString(),
                                                  style: AppFont.componentSmall,
                                                ),
                                                product.priceSellPromo == 0
                                                    ? Text(
                                                        "Rp.${product.priceSell.toCurrency}",
                                                        style: AppFont
                                                            .componentSmall)
                                                    : Row(
                                                        children: [
                                                          Text(
                                                              "Rp.${product.priceSell.toCurrency}",
                                                              style: AppFont
                                                                  .componentSmall
                                                                  .copyWith(
                                                                      decoration:
                                                                          TextDecoration
                                                                              .combine([
                                                                        TextDecoration
                                                                            .lineThrough
                                                                      ]),
                                                                      color: AppColor
                                                                          .black50)),
                                                          5.0.width,
                                                          Text(
                                                              "Rp.${product.priceSellPromo.toCurrency}",
                                                              style: AppFont
                                                                  .componentSmall),
                                                        ],
                                                      )
                                              ],
                                            ),
                                          );
                                        }),
                                  ),
                                ],
                              ),
                            );
                          });
                    },
                  )
                : const SizedBox(),
            voucher.maxQtyPromo == null
                ? const SizedBox()
                : Container(
                    padding: const EdgeInsets.symmetric(vertical: 5),
                    decoration: const BoxDecoration(
                        border: Border(
                            bottom:
                                BorderSide(width: 1, color: AppColor.black10))),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          Strings.maxPurchaseAmount.trParams({"count": ""}),
                          style: AppFont.componentSmall,
                        ),
                        Text(
                          "${voucher.maxQtyPromo}",
                          style: AppFont.componentSmall,
                        ),
                      ],
                    ),
                  ),
            Container(
              padding: const EdgeInsets.symmetric(vertical: 5),
              decoration: const BoxDecoration(
                  border: Border(
                      bottom: BorderSide(width: 1, color: AppColor.black10))),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(Strings.minimumTransaction.tr,
                      style: AppFont.componentSmall),
                  Text("Rp ${voucher.minOrder.toCurrency}",
                      style: AppFont.componentSmall),
                ],
              ),
            ),
            voucher.maximumDiscountNominal == null
                ? const SizedBox()
                : Container(
                    padding: const EdgeInsets.symmetric(vertical: 5),
                    decoration: const BoxDecoration(
                        border: Border(
                            bottom:
                                BorderSide(width: 1, color: AppColor.black10))),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(Strings.maxDiscount,
                            style: AppFont.componentSmall),
                        Text("Rp ${voucher.maximumDiscountNominal}",
                            style: AppFont.componentSmall),
                      ],
                    ),
                  ),
            InkWell(
              onTapDown: (details) =>
                  ShowCustomModalBottom.showModalBottomRedeemPeriod(
                      context: context, deal: voucher),
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 5),
                decoration: const BoxDecoration(
                    border: Border(
                        bottom: BorderSide(width: 1, color: AppColor.black10))),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(Strings.redeemPeriod.tr,
                        style: AppFont.componentSmall),
                    Row(
                      children: [
                        Text(DealHelper.getPromoDaysV2(voucher),
                            style: AppFont.componentSmall),
                        Icon(
                          Icons.keyboard_arrow_right_rounded,
                          color: AppColor.black,
                          size: Constants.iconSizeSmall(context),
                        )
                      ],
                    ),
                  ],
                ),
              ),
            ),
            voucher.termProduct != null
                ? Container(
                    padding: const EdgeInsets.symmetric(vertical: 5),
                    width: MediaQuery.of(context).size.width,
                    decoration: const BoxDecoration(
                        border: Border(
                            bottom:
                                BorderSide(width: 1, color: AppColor.black10))),
                    child: InkWell(
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                Strings.termProduct.tr,
                                style: AppFont.componentSmall,
                              ),
                              Visibility(
                                visible: voucher.termProduct?.qty != 0,
                                child: Text(
                                  Strings.minPurchaseAmount.trParams(
                                      {"count": "${voucher.termProduct?.qty}"}),
                                  style: AppFont.paragraphSmall,
                                ),
                              ),
                            ],
                          ),
                          Row(
                            children: [
                              Text(
                                  "${ProductHelper.filterDuplicateProduct(voucher.termProduct?.products ?? []).length} Products",
                                  style: AppFont.componentSmall),
                              Icon(
                                Icons.keyboard_arrow_right_rounded,
                                color: AppColor.black,
                                size: Constants.iconSizeSmall(context),
                              )
                            ],
                          ),
                        ],
                      ),
                      onTapDown: (details) {
                        showModalBottomSheet(
                            context: context,
                            isScrollControlled: true,
                            constraints: BoxConstraints(
                                minHeight: 200,
                                maxHeight:
                                    MediaQuery.of(context).size.height * 0.8,
                                maxWidth:
                                    Constants.defaultBoxConstraints(context)),
                            shape: const RoundedRectangleBorder(
                              borderRadius: BorderRadius.only(
                                  topLeft: Radius.circular(15.0),
                                  topRight: Radius.circular(15.0)),
                            ),
                            builder: (context) {
                              var listPromotionProduct =
                                  ProductHelper.filterDuplicateProduct(
                                      voucher.termProduct?.products ?? []);
                              return Padding(
                                padding: EdgeInsets.symmetric(
                                    horizontal: Constants.defaultPadding),
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  children: <Widget>[
                                    Icon(
                                      Icons.drag_handle,
                                      size: Constants.iconSizeSmall(context),
                                      color: AppColor.black70,
                                    ),
                                    Align(
                                      alignment: Alignment.centerLeft,
                                      child: Text(Strings.termProduct.tr,
                                          style: AppFont.componentSmall),
                                    ),
                                    Align(
                                      alignment: Alignment.centerLeft,
                                      child: Text(
                                          "${listPromotionProduct.length} Product",
                                          style: AppFont.componentSmall),
                                    ),
                                    const SizedBox(height: 5),
                                    Flexible(
                                      child: ListView.builder(
                                          shrinkWrap: true,
                                          itemCount:
                                              listPromotionProduct.length,
                                          itemBuilder:
                                              (BuildContext ctxt, int index) {
                                            ProductModel product =
                                                listPromotionProduct[index];
                                            return Container(
                                              padding: EdgeInsets.symmetric(
                                                  vertical:
                                                      Constants.defaultPadding),
                                              decoration: const BoxDecoration(
                                                  border: Border(
                                                      bottom: BorderSide(
                                                          width: 1,
                                                          color: AppColor
                                                              .black10))),
                                              child: Text(
                                                product.name.toString(),
                                                style: AppFont.componentSmall,
                                              ),
                                            );
                                          }),
                                    ),
                                  ],
                                ),
                              );
                            });
                      },
                    ),
                  )
                : const SizedBox(),
            InkWell(
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 5),
                decoration: const BoxDecoration(
                    border: Border(
                        bottom: BorderSide(width: 1, color: AppColor.black10))),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(Strings.availableAt.trParams({"outlet": outlet}),
                        style: AppFont.componentSmall),
                    Row(
                      children: [
                        Text("${voucher.outlet?.length} $outlet's",
                            style: AppFont.componentSmall),
                        Icon(
                          Icons.keyboard_arrow_right_rounded,
                          color: AppColor.black,
                          size: Constants.iconSizeSmall(context),
                        )
                      ],
                    ),
                  ],
                ),
              ),
              onTap: () {
                showModalBottomSheet(
                    context: context,
                    isScrollControlled: true,
                    constraints: BoxConstraints(
                        minHeight: 200,
                        maxHeight: MediaQuery.of(context).size.height * 0.8,
                        maxWidth: Constants.defaultBoxConstraints(context)),
                    shape: const RoundedRectangleBorder(
                      borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(15.0),
                          topRight: Radius.circular(15.0)),
                    ),
                    builder: (context) {
                      return Padding(
                        padding: EdgeInsets.symmetric(
                            horizontal: Constants.defaultPadding),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: <Widget>[
                            Icon(
                              Icons.drag_handle,
                              size: Constants.iconSizeSmall(context),
                              color: AppColor.black70,
                            ),
                            Align(
                              alignment: Alignment.centerLeft,
                              child: Text("Availability Location",
                                  style: AppFont.componentSmall),
                            ),
                            Align(
                              alignment: Alignment.centerLeft,
                              child: Text("${voucher.outlet?.length} $outlet's",
                                  style: AppFont.componentSmall),
                            ),
                            const SizedBox(height: 5),
                            Flexible(
                              child: ListView.builder(
                                  shrinkWrap: true,
                                  itemCount: voucher.outlet?.length ?? 0,
                                  itemBuilder: (BuildContext ctxt, int index) {
                                    return Container(
                                      padding: EdgeInsets.symmetric(
                                          vertical: Constants.defaultPadding),
                                      decoration: const BoxDecoration(
                                          border: Border(
                                              bottom: BorderSide(
                                                  width: 1,
                                                  color: AppColor.black10))),
                                      child: Text(
                                        "${voucher.outlet?[index].name.toString()}",
                                        style: AppFont.componentSmall,
                                      ),
                                    );
                                  }),
                            ),
                          ],
                        ),
                      );
                    });
              },
            ),
          ],
        ),
      ),
    );
  }
}
