import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/screen_wrapper/controller/wrapper_controller.dart';
import 'package:mobile_crm/app/widget/button_widget/button_widget.dart';
import 'package:mobile_crm/app/widget/custom_circular_progress_widget.dart';
import 'package:mobile_crm/core/extensions/color_extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/app_strings.dart';

import '../../../controllers/voucher_detail_controller.dart';

class ContentFooter extends GetView<VoucherDetailController> {
  const ContentFooter({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final wrapperController = Get.find<WrapperController>();
    return controller.previousRoute.value.contains("cartdetail")
        ? Container(
            color: AppColor.white,
            padding: const EdgeInsets.all(8.0),
            child: PrimaryButton(
              text: "",
              type: PrimaryButtonType.type4,
              child: Obx(() => controller.isLoading.value
                  ? const CustomCircularProgressIndicator()
                  : Text(Strings.useDeals.tr,
                      style: AppFont.componentSmallBold.copyWith(color: wrapperController.getPrimaryColor().changeColorBasedOnBackgroundColor().$1),
                    )),
              onPressed: () {
                controller.isLoading.value = true;
                Future.delayed(const Duration(milliseconds: 500), () {
                  Get.back(result: controller.vouchers.value);
                  controller.isLoading.value = false;
                });
              },
            ),
          )
        : const SizedBox();
  }
}
