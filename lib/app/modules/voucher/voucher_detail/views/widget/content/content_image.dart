import 'package:flutter/material.dart';
import 'package:mobile_crm/app/widget/cached_image_widget.dart';
import 'package:mobile_crm/core/values/values.dart';

import '../../../../../../../data/providers/db/database.dart';

class ContentImage extends StatelessWidget {
  const ContentImage({Key? key, required this.voucher}) : super(key: key);
  final DealData voucher;

  @override
  Widget build(BuildContext context) {
    return SliverToBoxAdapter(
      child: Padding(
        padding: EdgeInsets.symmetric(
            horizontal: Constants.defaultPadding, vertical: 8),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(15),
          child: SizedBox(
            height: Constants.voucherContainerWidth(context),
            width: double.infinity,
            child: <PERSON>(
              tag: "voucher${voucher.promotionFkId}",
              child: CachedImageWidget(
                imageUrl: voucher.photo.toString(),
                fit: BoxFit.fill,
                height: Constants.voucherContainerWidth(context),
                width: double.infinity,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
