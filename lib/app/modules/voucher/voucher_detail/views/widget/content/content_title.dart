import 'package:flutter/material.dart';
import 'package:mobile_crm/app/widget/html_widget.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/values.dart';
import 'package:flutter_html/flutter_html.dart';

import '../../../../../../../data/providers/db/database.dart';

class ContentTitle extends StatelessWidget {
  const ContentTitle({Key? key, required this.voucher}) : super(key: key);
  final DealData voucher;

  @override
  Widget build(BuildContext context) {
    return SliverToBoxAdapter(
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: Constants.defaultPadding),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            10.0.height,
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text("${voucher.name}", style: AppFont.componentSmall),
                Text("${voucher.promoType?.toUpperCase().replaceAll("_", " ")}",
                    style: AppFont.componentSmall),
              ],
            ),
            Html(
              data: voucher.term ?? '',
              style: {
                "*": AppFont.htmlStyle.copyWith(
                  fontSize: FontSize(12.0)
                )
              },
              ),
            // HtmlWidget(
            //   data: voucher.term ?? '',
            //   fontSize: 14,
            // ),
          ],
        ),
      ),
    );
  }
}
