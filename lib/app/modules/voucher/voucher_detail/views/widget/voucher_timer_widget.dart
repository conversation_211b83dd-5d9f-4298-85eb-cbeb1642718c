import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/widget/button_widget/button_widget.dart';
import 'package:mobile_crm/app/widget/custom_circular_progress_widget.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/app_constants.dart';
import 'package:mobile_crm/data/models/secret_id_model.dart';

import '../../../../../../core/values/app_strings.dart';
import '../../controllers/voucher_detail_controller.dart';

class VoucherTimerWidget extends StatefulWidget {
  const VoucherTimerWidget({Key? key}) : super(key: key);

  @override
  State<VoucherTimerWidget> createState() => _VoucherTimerWidgetState();
}

class _VoucherTimerWidgetState extends State<VoucherTimerWidget> {
  Timer? countdownTimer;
  Duration myDuration = const Duration(seconds: 0);

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    countdownTimer?.cancel();
    super.dispose();
  }

  void startTimer() {
    countdownTimer =
        Timer.periodic(const Duration(seconds: 1), (_) => setCountDown());
  }

  void stopTimer() {
    setState(() => countdownTimer?.cancel());
  }

  void resetTimer() {
    stopTimer();
    setState(() => myDuration = const Duration(seconds: 300));
    startTimer();
  }

  void setCountDown() {
    const reduceSecondsBy = 1;
    setState(() {
      final seconds = myDuration.inSeconds - reduceSecondsBy;
      if (seconds < 0) {
        countdownTimer!.cancel();
      } else {
        myDuration = Duration(seconds: seconds);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final c = Get.find<VoucherDetailController>();
    String strDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = strDigits(myDuration.inMinutes.remainder(60));
    final seconds = strDigits(myDuration.inSeconds.remainder(60));

    if (myDuration.inSeconds.isLowerThan(1)) {
      return Obx(
        () => c.isLoading.value
            ? const CustomCircularProgressIndicator()
            : PrimaryButton(
                onPressed: () async {
                  SecretIdModel secretIdModel = await c.getSecretId();
                  if (secretIdModel.expired != 0) {
                    resetTimer();
                  }
                },
                text: Strings.getSecretId.tr,
                width: double.infinity,
                type: PrimaryButtonType.type4,
                child: SizedBox(
                    width: MediaQuery.of(context).size.width,
                    child: Text(
                      Strings.getSecretId.tr,
                      textAlign: TextAlign.center,
                      style: AppFont.componentSmall
                          .copyWith(color: AppColor.white),
                    ))),
      );
    }

    return Container(
      margin: EdgeInsets.symmetric(horizontal: AppDimen.h16),
      height: Constants.buttonHeightContent(),
      decoration: BoxDecoration(
          color: AppColor.ink05, borderRadius: BorderRadius.circular(15)),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          Flexible(
            child: Obx(() => Text(
                  'Secret ID : ${c.secretId.value.secretCode}',
                  style: AppFont.componentSmall,
                )),
          ),
          Flexible(
            child: Text(
              '$minutes:$seconds',
              style: AppFont.componentSmall,
            ),
          ),
        ],
      ),
    );
  }
}
