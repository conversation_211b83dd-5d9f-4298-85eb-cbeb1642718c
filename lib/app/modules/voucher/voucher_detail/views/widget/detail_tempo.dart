import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/screen_wrapper/controller/wrapper_controller.dart';
import 'package:mobile_crm/app/widget/app_bar_widget/app_bar_widget.dart';
import 'package:mobile_crm/core/theme/app_dimen.dart';

class VoucherDetailTempo extends GetView<WrapperController> {
  const VoucherDetailTempo({Key? key, required this.child}) : super(key: key);

  final Widget child;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Column(
        children: [
          AppBarWidget(
              title: 'Voucher Detail',
              leading: IconButton(
                  onPressed: () {
                    Get.back(result: true);
                  },
                  icon: Icon(
                    Icons.arrow_back,size: AppDimen.h18,
                  ))),
          Expanded(child: child)
        ],
      ),
    );
  }
}
