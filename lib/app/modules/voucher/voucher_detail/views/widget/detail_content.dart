import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/voucher/voucher_detail/controllers/voucher_detail_controller.dart';
import 'package:mobile_crm/app/widget/app_bar_widget/app_bar_widget.dart';
import 'package:mobile_crm/app/widget/cached_image_widget.dart';
import 'package:mobile_crm/core/extensions/double_extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/values.dart';

import '../../../../../../data/providers/db/database.dart';
import '../../../../../../data/services/analytics_service.dart';
import 'content/content_footer.dart';
import 'content/content_image.dart';
import 'content/content_term.dart';
import 'content/content_title.dart';
import 'voucher_timer_widget.dart';

class VoucherDetailContent extends GetView<VoucherDetailController> {
  const VoucherDetailContent({Key? key, required this.voucher})
      : super(key: key);

  final DealData voucher;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      bottomNavigationBar: const ContentFooter(),
      floatingActionButton: FloatingActionButton(
        backgroundColor: Colors.white,
        onPressed: () => showQrDialog(context),
        child: SizedBox(
          width: AppDimen.h40,
          height: AppDimen.h40,
          child: ClipRRect(
              borderRadius: BorderRadius.circular(25),
              child: CachedImageWidget(
                imageUrl: voucher
                    .redeemQrCodeUrl, //"https://chart.googleapis.com/chart?chs=250x250&cht=qr&chl=${voucher.redeemQrCode.toString()}"
              )),
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
      body: Column(
        children: [
          AppBarWidget(
            title: voucher.name ?? 'Voucher Detail',
            actions: [
              Obx(() {
                return Visibility(
                  visible: controller.vouchers.value.name != null,
                  child: PopupMenuButton<int>(
                    position: PopupMenuPosition.under,
                    color: AppColor.white,
                    itemBuilder: (context) => [
                      PopupMenuItem<int>(
                          value: 1,
                          child: Row(
                            children: [
                              Icon(
                                Icons.refresh_rounded,
                                color: AppColor.black90,
                                size: AppDimen.h12,
                              ),
                              AppDimen.h6.width,
                              Text(
                                "Refund",
                                style: AppFont.componentSmallBold,
                              ),
                            ],
                          )),
                    ],
                    onSelected: (item) {
                      controller.getPopUpMenu(item);
                    },
                  ),
                );
              }),
            ],
          ),
          Expanded(
            child: CustomScrollView(shrinkWrap: true, slivers: [
              ContentImage(
                voucher: voucher,
              ),
              ContentTitle(voucher: voucher),
              ContentTerm(voucher: voucher),
            ]),
          ),
        ],
      ),
    );
  }

  void showQR(BuildContext context) {
    AnalyticsService.observer.analytics
        .logEvent(name: "get_secret_id", parameters: {"position": "voucher"});
    showModalBottomSheet<dynamic>(
      context: context,
      isScrollControlled: true,
      constraints: BoxConstraints(
          maxWidth: Constants.defaultBoxConstraints(context),
          minWidth: Constants.defaultBoxConstraints(context)),
      shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(15), topRight: Radius.circular(15))),
      builder: (context) {
        return Container(
          padding: const EdgeInsets.only(left: 8, right: 8, bottom: 8),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.drag_handle,
                size: Constants.iconSizeSmall(context),
                color: AppColor.black70,
              ),
              5.0.height,
              Text(Strings.redeemVoucherByShowingQR.tr,
                  style: AppFont.componentSmallBold),
              const Divider(),
              10.0.height,
              SizedBox(
                child: ClipRRect(
                    borderRadius: BorderRadius.circular(10),
                    child: CachedImageWidget(
                      imageUrl: voucher
                          .redeemQrCodeUrl, //"https://chart.googleapis.com/chart?chs=300x300&cht=qr&chl=${voucher.redeemQrCode.toString()}",
                      width: 300,
                      height: 300,
                    )),
              ),
              10.0.height,
              const Divider(),
              const VoucherTimerWidget()
            ],
          ),
        );
      },
    );
  }

  void showQrDialog(BuildContext context) {
    AnalyticsService.observer.analytics
        .logEvent(name: "get_secret_id", parameters: {"position": "voucher_dialog"});
    showDialog(
      context: context,
      builder: (context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(15),
          ),
          child: Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(Strings.redeemVoucherByShowingQR.tr,
                    style: AppFont.componentSmallBold),
                const Divider(),
                10.0.height,
                SizedBox(
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(10),
                    child: CachedImageWidget(
                      imageUrl: voucher.redeemQrCodeUrl,
                      width: 300,
                      height: 300,
                    ),
                  ),
                ),
                10.0.height,
                const Divider(),
                const VoucherTimerWidget()
              ],
            ),
          ),
        );
      },
    );
  }
}
