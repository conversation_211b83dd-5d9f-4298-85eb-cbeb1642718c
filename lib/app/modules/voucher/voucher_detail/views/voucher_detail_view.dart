import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/screen_wrapper/views/app_page_wrapper.dart';
import 'package:mobile_crm/app/widget/app_page_empty.dart';
import 'package:mobile_crm/app/widget/custom_circular_progress_widget.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/app_strings.dart';

import '../../../../../data/providers/db/database.dart';
import '../controllers/voucher_detail_controller.dart';
import 'widget/detail_content.dart';
import 'widget/detail_tempo.dart';

class VoucherDetailScreen extends GetView<VoucherDetailController> {
  const VoucherDetailScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return PageWrapper(
      onWillPop: () {
        Get.back(result: true);
        return Future.value(true);
      },
      child: SizedBox(
        height: MediaQuery.of(context).size.height,
        child: StreamBuilder(
            stream: controller.onCurrentDealModelChanged,
            builder: (BuildContext context, snapshot) {
              DealData voucher = snapshot.data ?? DealData();
              if (snapshot.hasData) {
                return (snapshot.data?.name != null)
                    ? VoucherDetailContent(
                        voucher: voucher,
                      )
                    : VoucherDetailTempo(
                        child: AppPageEmpty(
                          func: () {
                            Get.back(result: true);
                          },
                          reason: Strings.valueNotFound
                              .trParams({'value': 'Voucher'}),
                          buttonText: 'Back',
                        ),
                      );
              } else if (snapshot.hasError) {
                return const Icon(Icons.error_outline);
              } else {
                return const VoucherDetailTempo(
                    child: CustomCircularProgressIndicator(
                  valueColor: AppColor.black90,
                ));
              }
            }),
      ),
    );
  }
}
