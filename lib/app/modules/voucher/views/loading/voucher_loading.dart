import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/widget/shimmer_loading_widget.dart';

class VoucherLoading extends StatelessWidget {
  const VoucherLoading({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
      itemCount: 3,
      itemBuilder: (context, index) => itemLoading(),
      separatorBuilder: (context, index) => const SizedBox(height: 6),
    );
  }

  Widget itemLoading() {
    return Container(
      margin: const EdgeInsets.only(bottom: 15, left: 10, right: 10, top: 10),
      child: Column(
        children: [
          SizedBox(
              child: ShimmerWidget(
            width: Get.size.width,
            height: 200,
          )),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                const ShimmerWidget(
                  width: 20,
                  height: 20,
                ),
                ShimmerWidget(width: Get.size.width * 0.1),
                const SizedBox(),
                const ShimmerWidget(
                  width: 30,
                  height: 20,
                ),
              ],
            ),
          )
        ],
      ),
    );
  }
}
