import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/widget/app_page_empty.dart';
import 'package:mobile_crm/app/widget/custom_refresh_indicator_widget.dart';
import 'package:mobile_crm/app/widget/voucher_item.dart';
import 'package:mobile_crm/core/values/values.dart';
import 'package:mobile_crm/routes/app_pages.dart';

import '../../../../../data/providers/db/database.dart';
import '../../controllers/voucher_controller.dart';
import '../loading/voucher_loading.dart';

class VoucherBody extends GetView<VoucherController> {
  const VoucherBody({super.key});

  @override
  Widget build(BuildContext context) {
    return CustomRefreshIndicator(
        onRefresh: () => controller.fetchVouchers(),
        child: MediaQuery.removePadding(
          context: context,
          removeTop: true,
          child: Obx(() {
            return FutureBuilder<List<DealData>>(
                future: controller.voucher.value,
                builder: (BuildContext context, snapshot) {
                  if (snapshot.hasData) {
                    return snapshot.data?.isEmpty == true
                        ? Center(
                            child: AppPageEmpty(
                              func: () => Get.back(),
                              reason: Strings.valueNotFound
                                  .trParams({'value': 'voucher'}),
                              buttonText: Strings.back.tr,
                            ),
                          )
                        : content(snapshot.data ?? <DealData>[]);
                  } else if (snapshot.hasError) {
                    return const Text('');
                  } else {
                    return const VoucherLoading();
                  }
                });
          }),
        ));
  }

  Widget content(List<DealData> vouchers) {
    vouchers.sort(
        (a, b) => (a.endPromotionDate ?? 0).compareTo(b.endPromotionDate ?? 0));
    return ListView.builder(
      itemCount: vouchers.length,
      itemBuilder: (BuildContext context, int index) {
        DealData voucher = vouchers[index];
        return Container(
          margin: const EdgeInsets.symmetric(vertical: 10),
          child: InkWell(
            splashFactory: NoSplash.splashFactory,
              onTap: () async {
                var result = await Get.toNamed(
                    Routes.VOUCHERDETAIL(voucher.promotionBuyId.toString()),
                    arguments: voucher);
                if(result == true){
                  controller.fetchVouchers();
                }
              },
              child: ((voucher.groupDetail?.length ?? 1) >= 2)
                  ? VoucherItemWidget(
                      voucher: voucher, type: VoucherItemType.group)
                  : VoucherItemWidget(
                      voucher: voucher, type: VoucherItemType.ungroup)),
        );
      },
    );
  }
}
