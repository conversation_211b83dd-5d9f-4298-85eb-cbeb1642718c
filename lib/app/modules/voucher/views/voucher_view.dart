import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/screen_wrapper/views/app_page_wrapper.dart';
import 'package:mobile_crm/app/widget/app_bar_widget/app_bar_widget.dart';

import '../../../../core/values/app_strings.dart';
import '../controllers/voucher_controller.dart';
import 'widget/voucher_body.dart';

class VoucherScreen extends GetView<VoucherController> {
  const VoucherScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return PageWrapper(
      child: Column(
        children: [
          AppBarWidget(title: Strings.myVoucher.tr),
          const Expanded(child: VoucherBody()),
        ],
      ),
    );
  }
}
