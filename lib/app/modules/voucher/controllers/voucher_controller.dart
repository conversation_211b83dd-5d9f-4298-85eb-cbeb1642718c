// ignore_for_file: prefer_typing_uninitialized_variables

import 'package:get/get.dart';
import 'package:mobile_crm/data/repository/user_repository.dart';
import 'package:mobile_crm/domain/repository/user_repository.dart';
import 'package:mobile_crm/routes/app_pages.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

import '../../../../data/providers/db/database.dart';

class VoucherController extends GetxController {
  final UserRepository _repoUser = Get.find<UserRepositoryIml>();

  var voucher = Future.value(<DealData>[]).obs;
  var vouchers = <DealData>[].obs;

  @override
  Future<void> onInit() async {
    await fetchVouchers();
    Sentry.addBreadcrumb(Breadcrumb(
        type: 'debug',
        category: 'user.activity.voucher.init',
        data: {
          "voucher_length": await voucher.value.then((value) => value.length)
        },
        level: SentryLevel.debug));
    super.onInit();
  }

  Future<List<DealData>> fetchVouchers() async {
    voucher.value = _repoUser.getUserVoucherGroup();
    await voucher.value.then((value) => checkParameter(value));
    return voucher.value;
  }

  List<DealData> checkParameter(List<DealData> deal) {
    var id = Get.parameters['promoId'];
    if (id == null) {
      return deal;
    }

    Sentry.addBreadcrumb(Breadcrumb(
        type: 'debug',
        category: 'user.activity.voucher.checkParameter',
        data: {"promoId": id, "voucher_length": deal.length},
        level: SentryLevel.debug));

    var dealFound = deal
        .firstWhereOrNull((element) => element.promotionFkId.toString() == id);
    if (dealFound != null) {
      if (dealFound.promotionBuyId != null) {
        Get.toNamed(Routes.VOUCHERDETAIL("${dealFound.promotionBuyId}"));
      }
    }
    return deal;
  }
}
