import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/helper/deal_helper.dart';
import 'package:mobile_crm/app/helper/outlet_helper.dart';
import 'package:mobile_crm/app/modules/screen_wrapper/controller/wrapper_controller.dart';
import 'package:mobile_crm/app/utils/logger.dart';
import 'package:mobile_crm/app/utils/toast.dart';
import 'package:mobile_crm/app/widget/modal_bottom_sheet/app_modal_bottom_sheet.dart';
import 'package:mobile_crm/core/extensions/datetime_extensions.dart';
import 'package:mobile_crm/core/extensions/future_extensions.dart';
import 'package:mobile_crm/core/values/values.dart';
import 'package:mobile_crm/data/models/notification_model.dart';
import 'package:mobile_crm/data/models/product_group_model.dart';
import 'package:mobile_crm/data/models/search_model.dart';
import 'package:mobile_crm/data/models/secret_id_model.dart';
import 'package:mobile_crm/data/models/server_response.dart';
import 'package:mobile_crm/data/models/user_model.dart';
import 'package:mobile_crm/data/providers/db/database.dart';
import 'package:mobile_crm/data/repository/app_repository.dart';
import 'package:mobile_crm/data/repository/deal_repository.dart';
import 'package:mobile_crm/data/repository/inbox_repository.dart';
import 'package:mobile_crm/data/repository/membership_repository.dart';
import 'package:mobile_crm/data/repository/notification_me_repository.dart';
import 'package:mobile_crm/data/repository/outlet_repository.dart';
import 'package:mobile_crm/data/repository/product_repository.dart';
import 'package:mobile_crm/data/repository/transaction_repository.dart';
import 'package:mobile_crm/data/repository/user_repository.dart';
import 'package:mobile_crm/data/repository/wishlist_repository.dart';
import 'package:mobile_crm/data/services/local_storage.dart';
import 'package:mobile_crm/data/services/notification_service.dart';
import 'package:mobile_crm/domain/repository/app_repository.dart';
import 'package:mobile_crm/domain/repository/cart_repository.dart';
import 'package:mobile_crm/domain/repository/deal_repository.dart';
import 'package:mobile_crm/domain/repository/inbox_repository.dart';
import 'package:mobile_crm/domain/repository/membership_repository.dart';
import 'package:mobile_crm/domain/repository/notification_me_repository.dart';
import 'package:mobile_crm/domain/repository/outlet_repository.dart';
import 'package:mobile_crm/domain/repository/product_repository.dart';
import 'package:mobile_crm/domain/repository/user_repository.dart';
import 'package:mobile_crm/domain/repository/wishlist_repository.dart';
import 'package:palette_generator/palette_generator.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';
import 'package:sentry_flutter/sentry_flutter.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../data/models/transaction_new_model.dart';
import '../../../../data/repository/cart_repository.dart';
import '../../../../data/services/analytics_service.dart';
import '../../../../data/services/in_app_review_service.dart';
import '../../../../routes/app_pages.dart';
import '../../../enum/order_status_enum.dart';
import '../../../widget/modal_bottom_sheet/fill_province_modal_sheet.dart';

class HomeController extends FullLifeCycleController
    with FullLifeCycleMixin, GetSingleTickerProviderStateMixin {
  final InboxRepository _repoInbox = Get.find<InboxRepositoryIml>();
  final OutletRepository repoOutlet = Get.find<OutletRepositoryIml>();
  final DealRepository repoDeal = Get.find<DealRepositoryIml>();
  final ProductRepository repoProduct = Get.find<ProductRepositoryIml>();
  final AppRepository _repoApp = Get.find<AppRepositoryIml>();
  final CartRepository _repoCart = Get.find<CartRepositoryIml>();
  final MembershipRepository _repoMember = Get.find<MembershipRepositoryIml>();
  final UserRepository _repoUser = Get.find<UserRepositoryIml>();
  final WishlistRepository repoWishlist = Get.find<WishlistRepositoryIml>();
  final TransactionRepositoryIml repoTransaction =
      Get.find<TransactionRepositoryIml>();
  final NotificationMeRepository _repoNotification =
      Get.find<NotificationMeRepositoryIml>();
  final WrapperController wrapperC = Get.find<WrapperController>();

  // ------- //
  ItemScrollController itemScrollController = ItemScrollController();
  ScrollOffsetController scrollOffsetController = ScrollOffsetController();
  ScrollOffsetListener scrollOffsetListener = ScrollOffsetListener.create();

  final itemPositionsListener = ItemPositionsListener.create();
  var scrollControllerNested = ScrollController();
  late var tabController =
      TabController(initialIndex: 0, length: 3, vsync: this);

  final store = LocalStorageService();

  var listOutlet = Future.value(<Outlet>[]).obs;
  var listProductsByCategory = Future.value(<ProductGroupModel>[]).obs;
  var listBanner = Future.value(<BannerAppData>[]).obs;
  var userDetail = Future.value(UserModel()).obs;
  var listDeal = Future.value(<DealData>[]).obs;
  var listCategoryNameFloatingActionButton = <ProductGroupModel>[].obs;
  var listPaletteColorImage = <PaletteColor>[].obs;

  var secretId = SecretIdModel().obs;

  var totalVoucher = 0.obs;
  var totalUnread = 0.obs;
  var tabIndex = 0.obs;

  var isLoggedIn = false.obs;
  var isLoadingGettingSecretId = false.obs;
  var isLoadingAddRemoveWishlist = false.obs;
  var isAlreadyOpen = false;
  var isMainScrolling = false.obs;

  var voucher = Rx<Future<List<DealData>?>>(Future.value(null));

  StreamSubscription<double>? streamController;

  @override
  void onInit() {
    if (!itemScrollController.isAttached) {
      itemScrollController = ItemScrollController();
    }

    tabController = TabController(initialIndex: 0, length: 3, vsync: this);

    tabController.addListener(() {
      tabIndex.value = tabController.index;
      tabController.index = tabIndex.value;
    });
    isLoggedIn.value = store.token != null;
    if (isLoggedIn.value) {
      _repoCart.getCart();
      checkUserHasBeenFillProvince();
    }

    fetchData();
    super.onInit();
  }

  void fetchData() {
    if (store.token != null) {
      fetchUser();
      getUserTotalVoucher();
      getUserTotalInbox();
      if (!kIsWeb) {
        _repoUser.updateFCMToken.withRetries(3);
      }
    }
    fetchDeal();
    fetchOutlet();
    fetchProducts();
    fetchBanner();
    fetchMembership();
    getSearchHistory();
  }

  Future<List<DealData>?> fetchVoucherBanner() async {
    voucher.value = _repoUser.getUserVoucherGroup();
    await voucher.value.then((value) => checkParameter(value!));
    return voucher.value;
  }

  List<DealData> checkParameter(List<DealData> deal) {
    var id = Get.parameters['promoId'];
    if (id == null) {
      return deal;
    }

    Sentry.addBreadcrumb(Breadcrumb(
        type: 'debug',
        category: 'user.activity.voucher.checkParameter',
        data: {"promoId": id, "voucher_length": deal.length},
        level: SentryLevel.debug));

    var dealFound = deal
        .firstWhereOrNull((element) => element.promotionFkId.toString() == id);
    if (dealFound != null) {
      if (dealFound.promotionBuyId != null) {
        Get.toNamed(Routes.VOUCHERDETAIL("${dealFound.promotionBuyId}"));
      }
    }
    return deal;
  }

  void fetchUser() async {
    userDetail.value = Future.value(await _repoUser.getUser() ?? UserModel());
  }

  void fetchMembership() async {
    await _repoMember.getMembershipLocal();
  }

  void getUserTotalVoucher() async {
    try {
      List<DealData>? listDeal = await _repoUser.getUserVoucher();
      totalVoucher.value = listDeal?.length ?? 0;
    } catch (e, s) {
      errorLogger(pos: 'Home Controller', error: e, stackTrace: s);
    }
  }

  void getUserTotalInbox() async {
    totalUnread.value = await _repoInbox.getTotalInbox();
  }

  (bool, String, bool, bool, Duration) isPromoBeenPublished(int publishDate) =>
      DealHelper.isPromoBeenPublished(publishDate);

  Future<SecretIdModel> fetchSecretId() async {
    isLoadingGettingSecretId.value = true;
    var res = await _repoUser
        .getSecretId()
        .whenComplete(() => isLoadingGettingSecretId.value = false);
    secretId.value = res.data ?? SecretIdModel();
    isLoadingGettingSecretId.value = false;
    return secretId.value;
  }

  void fetchDeal() async {
    listDeal.value = Future.value(await repoDeal
        .getAll()
        .then((value) => DealHelper.dealSortFilter(value)));
  }

  void fetchOutlet() async {
    listOutlet.value = repoOutlet
        .getOutlet()
        .then((value) => OutletHelper.sortOutlet(outlets: value));
  }

  Future<void> fetchProducts() async {
    await repoProduct.getAllTaxes();
    await repoProduct.getAllLinkMenu();
    await repoProduct.getUnitConversion();
    listProductsByCategory.value = repoProduct.getFilteredProduct();
    listCategoryNameFloatingActionButton.value =
        await listProductsByCategory.value;
    scrollControllerProductList();
  }

  Future<void> fetchBanner() async {
    var dataBanner = await _repoApp.getBannerApp();
    dataBanner.sort((a, b) => a.position.compareTo(b.position));
    listBanner.value = Future.value(dataBanner);
  }

  Future<void> checkUserHasBeenFillProvince() async {
    var user = await _repoUser.getUser();
    Future.delayed(
      const Duration(seconds: 1),
      () {
        if (user?.province == null && !isAlreadyOpen) {
          FillProvinceBottomSheet.show(
            context: Get.context!,
            onPressed: () async {
              var result = await Get.toNamed(Routes.PROFILE, arguments: 10);
              if (result.runtimeType == UserModel) {
                Get.back();
              }
            },
          );
          isAlreadyOpen = true;
        }
      },
    );
  }

  Future<List<SearchModel>?> getSearchHistory() async =>
      repoProduct.getSearchHistoryProduct();

  @override
  void dispose() {
    store.deepProduct = null;
    super.dispose();
  }

  @override
  void onDetached() {}

  @override
  void onInactive() {}

  @override
  void onPaused() {}

  @override
  void onResumed() async {
    checkDynamicLink();
  }

  void checkDynamicLink() {
    Future.delayed(const Duration(seconds: 1), () async {
      if (store.deepProduct != null && store.deepType == 'product') {
        Product? product =
            await repoProduct.getProductById(store.deepProduct ?? '0');
        if (product == null) {
          Toast.show(Strings.valueNotFound.trParams({'value': "Product"}),
              type: ToastType.dark);
          store.deepProduct = null;
          return;
        }
        ShowCustomModalBottom.showModalProductHome(Get.context!, product, this);
        store.deepProduct = null;
      }
    });
  }

  Future saveSearch(String query) async =>
      await repoProduct.saveSearchHistoryProduct(search: query);

  Future<void> createNotificationForDeal(DealData deal) async {
    try {
      if (store.token == null) {
        return Toast.show(Strings.needLogin.tr, type: ToastType.dark);
      }
      if (deal.notify == "on") {
        return Toast.show(Strings.reminderAlreadyCreated.tr,
            type: ToastType.dark);
      }

      AnalyticsService.observer.analytics
          .logEvent(name: "notify_me", parameters: {"deal_name": deal.name});

      var notificationData = NotificationMeData(
          id: deal.promotionId,
          title: Strings.promoWillBeOpenSoon.tr,
          body: Strings.promoDoNotMissIt.trParams({"deal": deal.name ?? ''}),
          notificationType: NotificationMeType.deals.name,
          scheduleTime: deal.publishDate ?? 0,
          androidAllowWhileIdle: true);
      NotificationService.pushNotificationWithSchedule(notificationData);

      // Add notif local
      if (await _repoNotification.getSingle("${deal.promotionId}") == null) {
        await _repoNotification.insert(notificationData);
      }

      // Add notif remote
      await repoDeal.notifyMe(dealId: deal.promotionId.toString());
      Toast.show(Strings.reminderHasBeenCreated.tr, type: ToastType.dark);
    } catch (e, s) {
      errorLogger(pos: "HC - Notify Me", error: e, stackTrace: s);
    }
  }

  Future<void> sendWhatsapp() async {
    try {
      String number = wrapperC.configApp.value.contact?.whatsapp ?? '0';
      String whatsappURl =
          kIsWeb ? "https://wa.me/$number" : "whatsapp://send/?phone=$number";
      await launchUrl(Uri.parse(whatsappURl));
    } catch (e, s) {
      errorLogger(pos: "HC - Send whatsapp", error: e, stackTrace: s);
      if (e.runtimeType == PlatformException) {
        return Toast.show(Strings.whatsAppWontOpen.tr,
            type: ToastType.error, duration: 5);
      }

      Toast.show("Failed to open whatsapp. $e");
    }
  }

  Future<ServerResponse<WishlistData>> addWishlist(Product product) async {
    if (!isLoggedIn.value) {
      Toast.show("Login is required to add a product to the wishlist",
          type: ToastType.warning);
      Future.delayed(const Duration(milliseconds: 500), () {
        Get.toNamed(Routes.LOGIN);
      });
      return ServerResponse(status: false);
    }
    AnalyticsService.observer.analytics.logEvent(
        name: "add_wishlist", parameters: {"product_name": product.name});
    isLoadingAddRemoveWishlist.value = true;
    var result = await repoWishlist
        .addWishlist(product: product)
        .whenComplete(() => isLoadingAddRemoveWishlist.value = false);

    if (result.status) {
      await repoWishlist.getAllWishlist();
      await changeProductWishlistValue(true, product);
      Toast.show(
          Strings.valueAddedToWishlist
              .trParams({'value': ('"${product.name}"')}),
          type: ToastType.success);
    } else {
      Toast.show(
          Strings.valueAlreadyInYourWishlist
              .trParams({'value': ('"${product.name}"')}),
          type: ToastType.error);
    }
    return result;
  }

  Future<void> removeWishlist(Product product) async {
    if (!isLoggedIn.value) {
      Toast.show("Login is required to remove a product to the wishlist",
          type: ToastType.warning);
      Future.delayed(const Duration(milliseconds: 500), () {
        Get.toNamed(Routes.LOGIN);
      });
      return;
    }
    isLoadingAddRemoveWishlist.value = true;
    AnalyticsService.observer.analytics.logEvent(
        name: "remove_wishlist", parameters: {"product_name": product.name});
    WishlistData? isWishlistExist = await repoWishlist
        .getSingleWishlistByProductFkId(product.productId ?? 0);

    if (isWishlistExist != null) {
      var result = await repoWishlist
          .deleteWishlist("${isWishlistExist.crmProductWishlistId}")
          .whenComplete(() => isLoadingAddRemoveWishlist.value = false);
      if (result.status) {
        await changeProductWishlistValue(false, product);
      }
      Toast.show(
          result.status
              ? Strings.valueRemovedFromWishlist
                  .trParams({'value': product.name ?? ''})
              : result.message,
          type: result.status ? ToastType.success : ToastType.error);
      Get.back();
    }
    isLoadingAddRemoveWishlist.value = false;
  }

  Future<void> changeProductWishlistValue(bool value, Product product) async {
    listProductsByCategory.update((val) async {
      var resultFilter = await val?.then((value) => value.firstWhereOrNull(
          (element) => element.categoryName == product.subcategory));
      var resultFilterProduct = resultFilter?.products?.firstWhereOrNull(
          (element) => element.productId == product.productId);
      resultFilterProduct?.is_in_wishlist = value;
    });
  }

  onStartScroll(ScrollMetrics metrics) {
    if (metrics.runtimeType.toString() == 'FixedScrollMetrics') {
      isMainScrolling.value = true;
    }
  }

  onUpdateScroll(ScrollMetrics metrics) {
    if (metrics.runtimeType.toString() == 'FixedScrollMetrics') {
      isMainScrolling.value = true;
    }
  }

  onEndScroll(ScrollMetrics metrics) {
    if (metrics.runtimeType.toString() == 'FixedScrollMetrics') {
      isMainScrolling.value = false;
    }
  }

  void scrollControllerProductList() {
    double totalScroll = 0.0;
    Future.delayed(
      const Duration(seconds: 1),
      () {
        streamController ??= scrollOffsetListener.changes.listen((event) {
          totalScroll += event;
          scrollControllerNested.jumpTo(totalScroll.clamp(
              0, scrollControllerNested.position.maxScrollExtent));
        });
      },
    );
  }

  Future<void> checkUpdateTransactionOnline(
      TransactionNewTableData transactionNewModel) async {
    if (transactionNewModel.orderSalesId != null) {
      var result = await repoTransaction
          .getOrderDetail2(transactionNewModel.orderSalesId ?? '');
      Future.delayed(const Duration(seconds: 4), () async {
        if (result.status) {
          transactionNewModel.status = result.data?.status;
          if (transactionNewModel.status?.toLowerCase() ==
                  OrderStatusEnum.arrived.name.toLowerCase() ||
              transactionNewModel.status?.toLowerCase() ==
                  OrderStatusEnum.received.name.toLowerCase() ||
              transactionNewModel.status?.toLowerCase() ==
                  OrderStatusEnum.cancel.name.toLowerCase() ||
              transactionNewModel.status?.toLowerCase() ==
                  OrderStatusEnum.ready.name.toLowerCase() ||
              transactionNewModel.status?.toLowerCase() ==
                  OrderStatusEnum.reject.name.toLowerCase() ||
              transactionNewModel.status?.toLowerCase() ==
                  OrderStatusEnum.payment_reject.name.toLowerCase()) {
            await InAppReviewService.requestReview();
            Future.delayed(
              const Duration(seconds: 10),
              () async {
                await repoTransaction.db.transactionNewDao
                    .deleteTrans(transactionNewModel, isOnOrder: true);
              },
            );
          } else if (DateTime.fromMillisecondsSinceEpoch(
                  transactionNewModel.timeOrder ?? 0)
              .add(const Duration(hours: 12))
              .isTimeExpired) {
            Future.delayed(
              const Duration(seconds: 5),
              () async {
                await repoTransaction.db.transactionNewDao
                    .deleteTrans(transactionNewModel, isOnOrder: true);
              },
            );
          } else {
            await repoTransaction.db.transactionNewDao
                .updateTrans(transactionNewModel, isOnOrder: true);
          }
        }
      });
    } else if (transactionNewModel.selfOrder != null) {
      // if (transactionNewModel.status != OrderStatusEnum.expired.name) {
      //   await getSelfOrderStatus(transactionNewModel);
      // } else if (transactionNewModel.status !=
      //     OrderStatusEnum.already_paid.name) {
      //   await getSelfOrderStatus(transactionNewModel);
      // } else {
      //   Future.delayed(
      //     const Duration(seconds: 5),
      //     () async {
      //       await repoTransaction.db.transactionNewDao
      //           .deleteTrans(transactionNewModel, isOnOrder: true);
      //     },
      //   );
      // }
      if (DateTime.fromMillisecondsSinceEpoch(
              transactionNewModel.selfOrder?.expired ?? 0)
          .isTimeExpired) {
        Future.delayed(
          const Duration(seconds: 5),
          () async {
            await repoTransaction.db.transactionNewDao
                .deleteTrans(transactionNewModel, isOnOrder: true);
          },
        );
      }
    }
  }

  Future<void> getSelfOrderStatus(
      TransactionNewTableData transactionNewModel) async {
    var resp = await repoTransaction.getStatusSelfOrder(
        transactionNewModel: TransactionNewModel(
            outletId: transactionNewModel.outletId,
            selfOrder: transactionNewModel.selfOrder));
    await repoTransaction.db.transactionNewDao.updateTrans(
        transactionNewModel.copyWith(status: resp.data),
        isOnOrder: true);
  }

  @override
  void onHidden() {
    // TODO: implement onHidden
  }
}
