import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/home/<USER>/home_controller.dart';
import 'package:mobile_crm/app/utils/share_helper.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/theme/app_color.dart';
import 'package:mobile_crm/core/theme/app_font.dart';
import 'package:mobile_crm/routes/app_pages.dart';

class VoucherDealsWidget extends StatelessWidget {
  final HomeController controller;
  const VoucherDealsWidget({super.key, required this.controller});

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
      future: controller.fetchVoucherBanner(),
      builder: (context, snapshot) {
        if (snapshot.hasData && snapshot.data!.isNotEmpty) {
          return Card(
            elevation: 0,
            color: AppColor.white,
            child: Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 16,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        "Your Active Vouchers",
                        style: AppFont.componentMediumBold,
                      ),
                      GestureDetector(
                        onTap: () {
                          Get.toNamed(Routes.VOUCHER);
                        },
                        child: Text(
                          "See all",
                          style: AppFont.componentSmall
                              .copyWith(color: AppColor.utilityDanger),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(
                    height: 8,
                  ),
                  SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Row(
                      spacing: 16.0,
                      children: snapshot.data!.take(5).map((it) {
                        return GestureDetector(
                          onTap: () async {
                            var result = await Get.toNamed(
                                Routes.VOUCHERDETAIL(
                                    it.promotionBuyId.toString()),
                                arguments: it);
                            if (result == true) {
                              controller.fetchVoucherBanner();
                            }
                          },
                          child: Container(
                            height: 130,
                            width: 180,
                            decoration: BoxDecoration(
                              color: Colors.grey.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(10),
                              border: Border.all(color: Colors.grey),
                            ),
                            padding: const EdgeInsets.symmetric(
                                horizontal: 16, vertical: 8),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  truncateText(it.name!, maxLength: 20),
                                  textAlign: TextAlign.left,
                                  style: AppFont.componentMediumBold.copyWith(),
                                ),
                                Text(
                                  "Valid ${it.endPromotionDate.toTimeLeft}",
                                  textAlign: TextAlign.left,
                                  style: AppFont.componentSmall.copyWith(
                                    color: Colors.grey,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        );
                      }).toList(),
                    ),
                  ),
                ],
              ),
            ),
          );
        } else {
          return const SizedBox();
        }
      },
    );
  }
}
