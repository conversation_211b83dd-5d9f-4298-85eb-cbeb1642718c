// ignore_for_file: must_be_immutable, invalid_use_of_protected_member

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/utils/logger.dart';
import 'package:mobile_crm/app/widget/app_dotted_separator.dart';
import 'package:mobile_crm/app/widget/custom_refresh_indicator_widget.dart';
import 'package:mobile_crm/app/widget/item_list/product_item.dart';
import 'package:mobile_crm/app/widget/modal_bottom_sheet/app_modal_bottom_sheet.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/data/models/product_group_model.dart';
import 'package:mobile_crm/data/providers/db/database.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';

import '../../../../../core/values/app_strings.dart';
import '../../../../widget/app_page_empty.dart';
import '../home_screen.dart';
import 'loading/product_loading.dart';

class MenuListHome extends HomeScreen {
  const MenuListHome({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return CustomRefreshIndicator(
      onRefresh: () {
        controller.fetchData();
        return Future(() => true);
      },
      child: Stack(
        children: [
          ListView(
            shrinkWrap: true,
          ),
          MediaQuery.removePadding(
              removeTop: true,
              context: context,
              child: Obx(() {
                return FutureBuilder(
                    future: controller.listProductsByCategory.value,
                    builder: (BuildContext context, snapshot) {
                      if (snapshot.connectionState != ConnectionState.done) {
                        return const ProductLoading();
                      }
                      if (snapshot.hasData) {
                        return ScrollablePositionedList.builder(
                          shrinkWrap: true,
                          itemScrollController: controller.itemScrollController,
                          scrollOffsetController:
                              controller.scrollOffsetController,
                          scrollOffsetListener: controller.scrollOffsetListener,
                          itemCount: snapshot.data?.length ?? 0,
                          itemBuilder: (BuildContext ctx, int index) {
                            ProductGroupModel category = snapshot.data![index];
                            return Column(
                              children: [
                                Container(
                                  height: 25.h,
                                  margin: EdgeInsets.only(
                                      top: (index == 0) ? 0 : 15),
                                  child: Align(
                                    alignment: Alignment.centerLeft,
                                    child: Text(
                                      category.categoryName,
                                      style: AppFont.paragraphMediumBold,
                                    ),
                                  ),
                                ),
                                const Padding(
                                  padding: EdgeInsets.only(bottom: 8.0),
                                  child: DottedDivider(
                                    height: 2,
                                    width: 3,
                                    color: AppColor.disable,
                                  ),
                                ),
                                ListView.builder(
                                  shrinkWrap: true,
                                  physics: const NeverScrollableScrollPhysics(),
                                  itemCount: category.products?.length ?? 0,
                                  itemBuilder: (context, index) {
                                    Product product = category.products![index];
                                    return ProductItemWidget(
                                        product: product,
                                        onPressed: () {
                                          infoLogger(
                                              '>>> show bottom... ${product.name}',
                                              product.toJsonString());
                                          ShowCustomModalBottom
                                              .showModalProductHome(
                                                  context, product, controller);
                                        },
                                        type: ProductItemWidgetType.menu);
                                  },
                                )
                              ],
                            );
                          },
                        );
                      } else if (snapshot.hasError) {
                        return Center(
                          child: AppPageEmpty(
                            func: () {},
                            reason: Strings.valueNotFound
                                .trParams({'value': 'Product'}),
                            hideButton: true,
                          ),
                        );
                      } else {
                        return const ProductLoading();
                      }
                    });
              })),
        ],
      ),
    );
  }
}
