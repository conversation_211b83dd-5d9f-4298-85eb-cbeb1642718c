import 'package:flutter/material.dart';
import 'package:mobile_crm/app/widget/shimmer_loading_widget.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/values/app_constants.dart';

class ProductLoading extends StatelessWidget {
  const ProductLoading({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    Size size = Constants.productItemSize;
    return ListView.builder(
        itemCount: 3,
        itemBuilder: (context, index) {
          return Column(
            children: [
              SizedBox(
                height: size.height,
                child: Row(
                  children: [
                    ShimmerWidget(
                      height: size.height,
                      width: size.width,
                    ),
                    const SizedBox(width: 3),
                    const Expanded(
                      child: Padding(
                        padding: EdgeInsets.symmetric(vertical: 10),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            ShimmerWidget(width: 100),
                            ShimmerWidget(width: 100 / 2),
                          ],
                        ),
                      ),
                    )
                  ],
                ),
              ),
              10.0.height
            ],
          );
        });
  }
}
