import 'package:flutter/material.dart';
import 'package:mobile_crm/app/widget/shimmer_loading_widget.dart';
import 'package:mobile_crm/core/values/app_constants.dart';

class DealLoading extends StatelessWidget {
  const DealLoading({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: 4,
      itemBuilder: (context, index) => ClipRRect(
        borderRadius: BorderRadius.all(Constants.shapeRadius),
        child: Column(
          children: [
            ShimmerWidget(
              borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(10), topRight: Radius.circular(10)),
              height: 120,
              width: MediaQuery.of(context).size.width,
            ),
            SizedBox(
              height: 50,
              width: MediaQuery.of(context).size.width,
              child: const Padding(
                padding: EdgeInsets.only(top: 2),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    ShimmerWidget(
                      height: 15,
                      width: 150,
                      borderRadius: BorderRadius.only(
                          bottomRight: Radius.circular(10),
                          topRight: Radius.circular(10)),
                    ),
                    SizedBox(
                      height: 1,
                    ),
                    ShimmerWidget(
                      height: 15,
                      width: 100,
                      borderRadius: BorderRadius.only(
                          bottomRight: Radius.circular(10),
                          topRight: Radius.circular(10)),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
