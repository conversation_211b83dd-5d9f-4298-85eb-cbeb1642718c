import 'package:flutter/material.dart';
import 'package:mobile_crm/app/widget/shimmer_loading_widget.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/app_constants.dart';

class UserCardLoading extends StatelessWidget {
  const UserCardLoading({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: [
        const Expanded(
          flex: 2,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              ShimmerWidget(
                width: 30,
                height: 30,
                radius: 5,
              ),
              SizedBox(height: 2),
              ShimmerWidget(
                width: 30,
                height: 5,
                radius: 5,
              ),
            ],
          ),
        ),
        const VerticalDivider(
          color: AppColor.disable,
          thickness: 1,
        ),
        Expanded(
          flex: 4,
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: Constants.defaultPadding),
            child: const Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ShimmerWidget(
                  width: 80,
                  height: 10,
                  radius: 5,
                ),
                SizedBox(height: 3),
                ShimmerWidget(
                  width: 60,
                  height: 10,
                  radius: 5,
                ),
              ],
            ),
          ),
        ),
        const VerticalDivider(
          color: AppColor.disable,
          thickness: 1,
        ),
        Expanded(
          flex: 4,
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: Constants.defaultPadding),
            child: const Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ShimmerWidget(
                  width: 80,
                  height: 10,
                  radius: 5,
                ),
                SizedBox(height: 3),
                ShimmerWidget(
                  width: 60,
                  height: 10,
                  radius: 5,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
