import 'package:flutter/material.dart';
import 'package:mobile_crm/app/widget/shimmer_loading_widget.dart';
import 'package:mobile_crm/core/values/app_constants.dart';

class CarouselLoading extends StatelessWidget {
  const CarouselLoading({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 8),
      child: ClipRRect(
          borderRadius: const BorderRadius.all(Radius.circular(15)),
          child: ShimmerWidget(
            width: double.infinity,
            height: Constants.bannerHeight(context),
          )),
    );
  }
}
