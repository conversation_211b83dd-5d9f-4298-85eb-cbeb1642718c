// import 'package:badges/badges.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/helper/product_helper.dart';
import 'package:mobile_crm/app/modules/home/<USER>/home_controller.dart';
import 'package:mobile_crm/app/modules/screen_wrapper/controller/wrapper_controller.dart';
import 'package:mobile_crm/app/utils/svg_icon.dart';
import 'package:mobile_crm/app/utils/url_helper.dart';
import 'package:mobile_crm/app/widget/custom_circular_progress_widget.dart';
import 'package:mobile_crm/app/widget/item_list/product_item.dart';
import 'package:mobile_crm/app/widget/modal_bottom_sheet/app_modal_bottom_sheet.dart';
import 'package:mobile_crm/app/widget/shimmer_loading_widget.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/app_constants.dart';
import 'package:mobile_crm/core/values/app_img_strings.dart';
import 'package:mobile_crm/core/values/app_strings.dart';
import 'package:mobile_crm/data/models/search_model.dart';
import 'package:mobile_crm/data/providers/db/database.dart';
import 'package:mobile_crm/data/services/analytics_service.dart';
import 'package:mobile_crm/data/services/local_storage.dart';
import 'package:mobile_crm/routes/app_pages.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

class HomeSearch extends StatelessWidget {
  const HomeSearch({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<HomeController>();
    final wrapperC = Get.find<WrapperController>();
    final store = LocalStorageService();
    return SafeArea(
      bottom: false,
      top: true,
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Flexible(
                child: InkWell(
                  onTapDown: (details) => showSearch(
                      context: context, delegate: HomeSearchDelegate()),
                  child: Obx(() {
                    return Container(
                      padding: EdgeInsets.symmetric(
                          horizontal: Constants.defaultPadding, vertical: 4),
                      decoration: BoxDecoration(
                          color: (controller.listPaletteColorImage.firstOrNull
                                      ?.color ??
                                  wrapperC.getPrimaryColor())
                              .changeColorBasedOnBackgroundColor(reverse: true)
                              .$1,
                          borderRadius: BorderRadius.circular(20)),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Icon(
                            Icons.search,
                            size: AppDimen.h18,
                            color: AppColor.black70,
                          ),
                          5.0.width,
                          Text(
                            Strings.search.tr,
                            style: AppFont.componentSmall
                                .copyWith(color: AppColor.black70),
                          ),
                        ],
                      ),
                    );
                  }),
                ),
              ),
              IconButton(
                visualDensity: VisualDensity.compact,
                icon: Obx(
                  () => Badge(
                    label: Text(
                        "${controller.totalUnread.value > 99 ? '99+' : controller.totalUnread.value}"),
                    isLabelVisible: controller.totalUnread.value != 0,
                    textStyle: AppFont.paragraphSmall,
                    child: Obx(() {
                      return Icon(Icons.notifications,
                          color: (controller.listPaletteColorImage.firstOrNull
                                      ?.color ??
                                  wrapperC.getPrimaryColor())
                              .changeColorBasedOnBackgroundColor(reverse: true)
                              .$1);
                    }),
                  ),
                ),
                onPressed: () {
                  Get.toNamed(Routes.NOTIFICATION);
                },
              ),
              Obx(() {
                return IconButton(
                  onPressed: () {
                    if (store.token != null) {
                      controller.repoWishlist.getAllWishlist();
                    }
                    Get.toNamed(Routes.PROFILE);
                  },
                  icon: Icon(Icons.account_circle,
                      color: (controller
                                  .listPaletteColorImage.firstOrNull?.color ??
                              wrapperC.getPrimaryColor())
                          .changeColorBasedOnBackgroundColor(reverse: true)
                          .$1),
                );
              }),
            ],
          ),
          SizedBox(
            height: 30.h,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Obx(() {
                  return FutureBuilder(
                      future: controller.userDetail.value,
                      builder: (BuildContext context, AsyncSnapshot snapshot) {
                        if (snapshot.hasData) {
                          return Flexible(
                              child: snapshot.data?.name != null
                                  ? Text(
                                      Strings.welcome.trParams(
                                          {'user': snapshot.data?.name}),
                                      style: AppFont.componentSmall
                                          .copyWith(color: AppColor.white))
                                  : Text(Strings.welcomeGuest.tr,
                                      style: AppFont.componentSmall
                                          .copyWith(color: AppColor.white)));
                        } else if (snapshot.hasError) {
                          return const Text('');
                        } else {
                          return const ShimmerWidget(
                            height: 10,
                            width: 100,
                          );
                        }
                      });
                }),
                Row(
                  children: [
                    Obx(() {
                      return Visibility(
                        visible:
                            wrapperC.configApp.value.socialMedia?.instagram !=
                                null,
                        child: SvgIcon.svgIcon(
                          ImgStrings.instagram,
                          onPressed: () {
                            AnalyticsService.observer.analytics.logEvent(
                                name: "social_media",
                                parameters: {"name": "instagram"});
                            openUrl(
                                '${wrapperC.configApp.value.socialMedia?.instagram}');
                          },
                        ),
                      );
                    }),
                    AppDimen.h14.width,
                    Obx(() {
                      return Visibility(
                        visible: wrapperC.configApp.value.socialMedia?.tiktok !=
                            null,
                        child: SvgIcon.svgIcon(
                          ImgStrings.tiktok,
                          onPressed: () {
                            AnalyticsService.observer.analytics.logEvent(
                                name: "social_media",
                                parameters: {"name": "tiktok"});
                            openUrl(
                                '${wrapperC.configApp.value.socialMedia?.tiktok}');
                          },
                        ),
                      );
                    }),
                    AppDimen.h14.width,
                    Obx(() {
                      return Visibility(
                        visible:
                            wrapperC.configApp.value.socialMedia?.twitter !=
                                null,
                        child: SvgIcon.svgIcon(
                          ImgStrings.twitter,
                          onPressed: () {
                            AnalyticsService.observer.analytics.logEvent(
                                name: "social_media",
                                parameters: {"name": "twitter"});
                            openUrl(
                                '${wrapperC.configApp.value.socialMedia?.twitter}');
                          },
                        ),
                      );
                    })
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class HomeSearchDelegate extends SearchDelegate {
  final hController = Get.find<HomeController>();
  final wController = Get.find<WrapperController>();
  List<Product> masterProducts = [];
  List<Product> resultProducts = [];

  @override
  List<Widget>? buildActions(BuildContext context) => [
        IconButton(
            onPressed: () {
              if (query.isEmpty) {
                close(context, null);
              }
              resultProducts = masterProducts;
              query = '';
            },
            icon: Icon(Icons.clear, size: Constants.iconSize(context)))
      ];

  @override
  Widget? buildLeading(BuildContext context) => IconButton(
      onPressed: () => close(context, null),
      icon: Icon(Icons.arrow_back, size: Constants.iconSize(context)));

  @override
  Widget buildResults(BuildContext context) {
    return Padding(
        padding: const EdgeInsets.only(left: 8.0, right: 8.0, top: 10.0),
        child: ListView.builder(
          itemCount: resultProducts.length,
          itemBuilder: (context, index) {
            return ProductItemWidget(
                product: resultProducts[index],
                onPressed: () {
                  Sentry.addBreadcrumb(Breadcrumb(
                      type: 'debug',
                      category: 'user.activity.home.search.onPressed',
                      data: {"product_name": resultProducts[index].name},
                      level: SentryLevel.debug));
                  hController.saveSearch(query);
                  AnalyticsService.observer.analytics
                      .logSearch(searchTerm: "${resultProducts[index].name}");
                  ShowCustomModalBottom.showModalProductHome(
                      context, resultProducts[index], hController);
                },
                type: ProductItemWidgetType.menu);
          },
        ));
  }

  @override
  Widget buildSuggestions(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        query != ''
            ? const SizedBox()
            : FutureBuilder(
                future: hController.getSearchHistory(),
                builder: (BuildContext context, snapshot) {
                  if (snapshot.hasData) {
                    return snapshot.data?.isEmpty == true
                        ? const SizedBox()
                        : Padding(
                            padding: const EdgeInsets.only(
                                left: 8.0, right: 8.0, top: 15.0),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                Text(Strings.searchHistory.tr,
                                    style: AppFont.componentSmall),
                                Wrap(
                                    spacing: 5.0,
                                    children: List<Widget>.generate(
                                      (snapshot.data?.length ?? 0) > 5
                                          ? 5
                                          : (snapshot.data?.length ?? 0),
                                      (int index) {
                                        SearchModel search =
                                            snapshot.data![index];
                                        return ActionChip(
                                          backgroundColor:
                                              wController.getPrimaryColor(),
                                          label: Text('${search.searchText}'),
                                          labelStyle: AppFont.componentSmall
                                              .copyWith(color: AppColor.white),
                                          onPressed: () {
                                            query = search.searchText ?? '';
                                          },
                                        );
                                      },
                                    ).toList()),
                                const Divider(
                                  indent: 1,
                                )
                              ],
                            ),
                          );
                  } else if (snapshot.hasError) {
                    return const SizedBox();
                  } else {
                    return const Text('');
                  }
                }),
        Flexible(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8.0),
            child: FutureBuilder(
                future: hController.repoProduct.getProductLocal(),
                builder: (BuildContext context, snapshot) {
                  if (snapshot.hasData) {
                    masterProducts = ProductHelper.filterProductByAvailability(
                        products: snapshot.data
                                ?.where((element) => element.isHeader == false)
                                .toList() ??
                            []);
                    resultProducts = masterProducts.where((element) {
                      if (query.runtimeType == int) {
                        int resultPrice = element.price ?? 0;
                        return resultPrice <= (int.tryParse(query) ?? 0);
                      }
                      final resultName = element.name?.toLowerCase();
                      final resultDesc = element.description?.toLowerCase();
                      final input = query.toLowerCase();
                      final result = (resultName?.contains(input) ?? false) ||
                          (resultDesc?.contains(input) ?? false);
                      return result;
                    }).toList();
                    return resultProducts.isEmpty
                        ? Center(
                            child: Text(Strings.valueNotFound
                                .trParams({'value': 'Product'})),
                          )
                        : ListView.builder(
                            shrinkWrap: true,
                            itemCount: resultProducts.length,
                            itemBuilder: (BuildContext ctx, int index) {
                              Product product = resultProducts[index];
                              return Container(
                                margin:
                                    EdgeInsets.only(top: index == 0 ? 10 : 0),
                                child: ProductItemWidget(
                                    product: product,
                                    onPressed: () {
                                      hController.saveSearch(query);
                                      ShowCustomModalBottom
                                          .showModalProductHome(
                                              context, product, hController);
                                    },
                                    type: ProductItemWidgetType.menu),
                              );
                            },
                          );
                  } else if (snapshot.hasError) {
                    return Icon(Icons.error_outline,
                        size: Constants.iconSize(context));
                  } else {
                    return const CustomCircularProgressIndicator();
                  }
                }),
          ),
        ),
      ],
    );
  }

  @override
  ThemeData appBarTheme(BuildContext context) {
    final controller = Get.find<WrapperController>();
    return ThemeData(
        appBarTheme: AppBarTheme(
            color: controller.getPrimaryColor(),
            systemOverlayStyle: SystemUiOverlayStyle(
                statusBarIconBrightness: controller
                    .getPrimaryColor()
                    .changeColorBasedOnBackgroundColor()
                    .$2)),
        primaryColorDark: AppColor.white,
        primaryColorLight: AppColor.black,
        textTheme: TextTheme(
            titleLarge: AppFont.componentSmallBold.copyWith(
                color: controller
                    .getPrimaryColor()
                    .changeColorBasedOnBackgroundColor()
                    .$1)),
        inputDecorationTheme: InputDecorationTheme(
          hintStyle: AppFont.componentSmallBold
              .copyWith(color: AppColor.white, letterSpacing: 2),
          border: InputBorder.none,
          labelStyle: AppFont.componentSmallBold
              .copyWith(color: AppColor.white, letterSpacing: 2),
        ));
  }
}
