import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/home/<USER>/home_controller.dart';
import 'package:mobile_crm/app/utils/url_helper.dart';
import 'package:mobile_crm/app/widget/cached_image_widget.dart';
import 'package:mobile_crm/core/values/app_constants.dart';
import 'package:mobile_crm/data/services/analytics_service.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

import 'loading/banner_loading.dart';

class HomeBanner extends StatelessWidget {
  const HomeBanner({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<HomeController>();
    var sliderIndex = 0.obs;
    return SizedBox(
        height: Constants.bannerHeight(context),
        child: Obx(() {
          return FutureBuilder(
            future: controller.listBanner.value,
            builder: (context, snapshot) {
              switch (snapshot.connectionState) {
                case ConnectionState.none:
                  return const Text('');
                case ConnectionState.waiting:
                  return const CarouselLoading();
                case ConnectionState.active:
                  return const Text('');
                case ConnectionState.done:
                  return snapshot.data?.isEmpty == true
                      ? const CarouselLoading()
                      : Stack(
                          children: [
                            CarouselSlider(
                              items: snapshot.data
                                  ?.map(
                                    (e) => InkWell(
                                      onTap: () {
                                        AnalyticsService.observer.analytics
                                            .logSelectContent(
                                                contentType: "banner",
                                                itemId: "${e.crmBannerId}");
                                        Sentry.addBreadcrumb(Breadcrumb(
                                            type: 'debug',
                                            category:
                                                'user.activity.home.banner',
                                            data: {
                                              "banner_link": e.actionDetail
                                            },
                                            level: SentryLevel.debug));
                                        openUrl(e.actionDetail);
                                      },
                                      child: Padding(
                                        padding: const EdgeInsets.symmetric(
                                            vertical: 4, horizontal: 8),
                                        child: ClipRRect(
                                          borderRadius: const BorderRadius.all(
                                              Radius.circular(8)),
                                          child: CachedImageWidget(
                                            imageUrl: e.photo,
                                            fit: BoxFit.fill,
                                            width: MediaQuery.of(context)
                                                .size
                                                .width,
                                            height:
                                                Constants.bannerHeight(context),
                                          ),
                                        ),
                                      ),
                                    ),
                                  )
                                  .toList(),
                              options: CarouselOptions(
                                  height: Constants.bannerHeight(context),
                                  aspectRatio: 2 / 1,
                                  viewportFraction: 1,
                                  initialPage: 3,
                                  enableInfiniteScroll: true,
                                  reverse: false,
                                  autoPlay: true,
                                  autoPlayInterval: const Duration(seconds: 5),
                                  autoPlayAnimationDuration:
                                      const Duration(milliseconds: 500),
                                  autoPlayCurve: Curves.fastLinearToSlowEaseIn,
                                  enlargeCenterPage: true,
                                  scrollDirection: Axis.horizontal,
                                  onPageChanged: (index, reason) {
                                    sliderIndex.value = index;
                                  }),
                            ),
                            Positioned(
                              bottom: 15,
                              left: 0,
                              right: 0,
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: snapshot.data == null
                                    ? [const SizedBox()]
                                    : snapshot.data!.map((e) {
                                        int index = snapshot.data!.indexOf(e);
                                        return Obx(() => Container(
                                              width: 10,
                                              height: 5,
                                              margin:
                                                  const EdgeInsets.symmetric(
                                                      vertical: 5,
                                                      horizontal: 2),
                                              decoration: BoxDecoration(
                                                borderRadius:
                                                    BorderRadius.circular(8),
                                                shape: BoxShape.rectangle,
                                                color:
                                                    sliderIndex.value == index
                                                        ? const Color.fromRGBO(
                                                            255, 255, 255, 1)
                                                        : const Color.fromRGBO(
                                                            1, 1, 1, 0.2),
                                              ),
                                            ));
                                      }).toList(),
                              ),
                            ),
                          ],
                        );
              }
            },
          );
        }));
  }
}
