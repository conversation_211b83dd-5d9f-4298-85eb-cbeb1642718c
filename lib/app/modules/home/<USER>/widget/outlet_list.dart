// ignore_for_file: must_be_immutable

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/home/<USER>/widget/home_widget.dart';
import 'package:mobile_crm/app/widget/custom_refresh_indicator_widget.dart';
import 'package:mobile_crm/app/widget/item_list/outlet_item.dart';
import 'package:mobile_crm/data/providers/db/database.dart';
import 'package:mobile_crm/routes/app_pages.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

import '../home_screen.dart';

class OutletList extends HomeScreen {
  const OutletList({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return CustomRefreshIndicator(
      onRefresh: () {
        controller.fetchData();
        return Future(() => true);
      },
      child: Stack(
        fit: StackFit.loose,
        children: [
          ListView(
            shrinkWrap: true,
          ),
          MediaQuery.removePadding(
            context: context,
            removeTop: true,
            child: Obx(() {
              return FutureBuilder(
                future: controller.listOutlet.value,
                builder: (context, snapshot) {
                  switch (snapshot.connectionState) {
                    case ConnectionState.none:
                      return const Text('');
                    case ConnectionState.waiting:
                      return const ProductLoading();
                    case ConnectionState.active:
                      return const Text('');
                    case ConnectionState.done:
                      return (snapshot.data?.isEmpty ?? true)
                          ? const ProductLoading()
                          : ListView.builder(
                              shrinkWrap: true,
                              physics: const NeverScrollableScrollPhysics(),
                              itemCount: snapshot.data?.length,
                              itemBuilder: (BuildContext context, int index) {
                                Outlet outlet = snapshot.data![index];
                                return OutletItemWidget(
                                  outlet: outlet,
                                  controller: controller,
                                  onPressed: () {
                                    String? nameRoute =
                                        ModalRoute.of(context)?.settings.name;
                                    controller.store.outlet = outlet;
                                    Sentry.addBreadcrumb(Breadcrumb(
                                        type: 'debug',
                                        category:
                                            'user.activity.home.outlet_list',
                                        data: {"outlet_id": outlet.outlet_id},
                                        level: SentryLevel.debug));
                                    if (nameRoute == "/") {
                                      (outlet.name.toString().isNotEmpty)
                                          ? Get.toNamed(Routes.OUTLET(
                                              outlet.outlet_id.toString()))
                                          : null;
                                    }
                                    if (nameRoute == "/listoulet") {
                                      (outlet.name.toString().isNotEmpty)
                                          ? Get.offAndToNamed(Routes.OUTLET(
                                              outlet.outlet_id.toString()))
                                          : null;
                                    }
                                  },
                                );
                              },
                            );
                  }
                },
              );
            }),
          ),
        ],
      ),
    );
  }
}
