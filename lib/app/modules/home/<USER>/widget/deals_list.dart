// ignore_for_file: invalid_use_of_protected_member

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/home/<USER>/widget/voucher_deals_widget.dart';
import 'package:mobile_crm/app/utils/logger.dart';
import 'package:mobile_crm/app/widget/app_page_empty.dart';
import 'package:mobile_crm/app/widget/custom_refresh_indicator_widget.dart';
import 'package:mobile_crm/app/widget/voucher_item.dart';
import 'package:mobile_crm/core/values/app_strings.dart';
import 'package:mobile_crm/routes/app_pages.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

import '../../../../../data/providers/db/database.dart';
import '../home_screen.dart';
import 'loading/deal_loading.dart';

class DealsList extends HomeScreen {
  const DealsList({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return CustomRefreshIndicator(
      onRefresh: () {
        controller.fetchData();
        infoLogger("refresh", "data");
        if (controller.isLoggedIn.value) {
          controller.fetchVoucherBanner();
        }
        return Future(() => true);
      },
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Obx(() {
            return (!controller.isLoggedIn.value)
                ? const SizedBox()
                : VoucherDealsWidget(
                    controller: controller,
                  );
          }),
          const SizedBox(
            height: 16,
          ),
          Expanded(
            child: Stack(
              fit: StackFit.loose,
              children: [
                MediaQuery.removePadding(
                  removeTop: true,
                  context: context,
                  child: Obx(() {
                    return FutureBuilder(
                      future: Future.value(controller.listDeal.value),
                      builder: (context, snapshot) {
                        switch (snapshot.connectionState) {
                          case ConnectionState.none:
                            return const Text("");
                          case ConnectionState.waiting:
                            return const DealLoading();
                          case ConnectionState.active:
                            return const Text("");
                          case ConnectionState.done:
                            return snapshot.data == null
                                ? Center(
                                    child: AppPageEmpty(
                                      func: () {},
                                      reason: Strings.noPromotionFound.tr,
                                      hideButton: true,
                                    ),
                                  )
                                : MediaQuery.removePadding(
                                    context: context,
                                    removeTop: true,
                                    child: ListView.builder(
                                      shrinkWrap: true,
                                      physics:
                                          const NeverScrollableScrollPhysics(),
                                      itemCount: snapshot.data?.length ?? 0,
                                      itemBuilder:
                                          (BuildContext context, int index) {
                                        DealData deal = snapshot.data![index];
                                        return InkWell(
                                          onTap: () async {
                                            Sentry.addBreadcrumb(Breadcrumb(
                                                type: 'debug',
                                                category:
                                                    'user.activity.home.deal_list',
                                                data: {
                                                  "deal_id": deal.promotionId
                                                },
                                                level: SentryLevel.debug));
                                            await Get.toNamed(
                                                Routes.DEALDETAIL(deal
                                                    .promotionId
                                                    .toString()),
                                                preventDuplicates: true,
                                                arguments: deal);
                                          },
                                          child: !controller
                                                  .isPromoBeenPublished(
                                                      deal.publishDate ?? 0)
                                                  .$1
                                              ? VoucherItemWidget(
                                                  voucher: deal,
                                                  type: VoucherItemType
                                                      .dealUnPublish,
                                                )
                                              : VoucherItemWidget(
                                                  voucher: deal,
                                                  type: VoucherItemType.deal),
                                        );
                                      },
                                    ),
                                  );
                        }
                      },
                    );
                  }),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
