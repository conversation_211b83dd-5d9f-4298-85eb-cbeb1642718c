import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/widget/button_widget/button_widget.dart';
import 'package:mobile_crm/app/widget/custom_circular_progress_widget.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/values.dart';
import 'package:mobile_crm/data/models/secret_id_model.dart';

import '../../controller/home_controller.dart';

class SecretIDTimer extends StatefulWidget {
  const SecretIDTimer({Key? key}) : super(key: key);

  @override
  State<SecretIDTimer> createState() => _SecretIDTimerState();
}

class _SecretIDTimerState extends State<SecretIDTimer> {
  Timer? countdownTimer;
  Duration myDuration = const Duration(seconds: 0);

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    countdownTimer?.cancel();
    super.dispose();
  }

  void startTimer() {
    countdownTimer =
        Timer.periodic(const Duration(seconds: 1), (_) => setCountDown());
  }

  void stopTimer() {
    setState(() => countdownTimer?.cancel());
  }

  void resetTimer() {
    stopTimer();
    setState(() => myDuration = const Duration(seconds: 300));
    startTimer();
  }

  void setCountDown() {
    const reduceSecondsBy = 1;
    setState(() {
      final seconds = myDuration.inSeconds - reduceSecondsBy;
      if (seconds < 0) {
        countdownTimer?.cancel();
      } else {
        myDuration = Duration(seconds: seconds);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final c = Get.find<HomeController>();
    String strDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = strDigits(myDuration.inMinutes.remainder(60));
    final seconds = strDigits(myDuration.inSeconds.remainder(60));

    if (myDuration.inSeconds.isLowerThan(1)) {
      return PrimaryButton(
        onPressed: () async {
          SecretIdModel secretIdModel = await c.fetchSecretId();
          if (secretIdModel.expired != 0) {
            resetTimer();
          }
        },
        text: "Get Secret ID",
        width: double.infinity,
        type: PrimaryButtonType.type4,
        child: SizedBox(
          width: double.infinity,
          child: Align(
            alignment: Alignment.center,
            child: Obx(() {
              return c.isLoadingGettingSecretId.value
                  ? const CustomCircularProgressIndicator()
                  : Text(
                      Strings.getSecretId.tr,
                      style: AppFont.componentSmallBold
                          .copyWith(color: AppColor.white),
                    );
            }),
          ),
        ),
      );
    }

    return Container(
      margin: EdgeInsets.symmetric(horizontal: AppDimen.h16),
      height: Constants.buttonHeightContent(),
      decoration: BoxDecoration(
          color: AppColor.ink05, borderRadius: BorderRadius.circular(15)),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          Flexible(
            child: Obx(() => Text(
                  'Secret ID : ${c.secretId.value.secretId}',
                  style: AppFont.componentSmall,
                )),
          ),
          Flexible(
            child: Text(
              '$minutes:$seconds',
              style: AppFont.componentSmall,
            ),
          ),
        ],
      ),
    );
  }
}
