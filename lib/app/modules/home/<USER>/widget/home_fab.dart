import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/home/<USER>/home_controller.dart';
import 'package:mobile_crm/app/modules/screen_wrapper/controller/wrapper_controller.dart';
import 'package:mobile_crm/app/utils/svg_icon.dart';
import 'package:mobile_crm/core/extensions/color_extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/values.dart';

import '../../../../../data/services/analytics_service.dart';
import '../../../../utils/utils.dart';
import '../../../../widget/full_screen_dialog/active_transaction/active_transaction_fdialog.dart';

class HomeFAB extends StatelessWidget {
  const HomeFAB({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<HomeController>();
    final wrapperC = Get.find<WrapperController>();
    return Column(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        SizedBox(
          width: double.infinity,
          child: Wrap(
              direction: Axis.horizontal,
              alignment: WrapAlignment.spaceBetween,
              children: [
                Obx(
                  () => controller.tabIndex.value != 0
                      ? const SizedBox()
                      : Obx(() {
                          return Container(
                            margin: EdgeInsets.only(
                                left: 8.h + kFloatingActionButtonMargin),
                            child: FloatingActionButton(
                              heroTag: null,
                              onPressed: () => _offsetPopup(
                                  controller: controller, context: context),
                              tooltip: "Menu",
                              backgroundColor: wrapperC.getPrimaryColor(),
                              child: _offsetPopup(
                                  controller: controller, context: context),
                            ),
                          );
                        }),
                ),
                Obx(() {
                  return wrapperC.configApp.value.contact?.whatsapp == null
                      ? const SizedBox()
                      : FloatingActionButton(
                          heroTag: null,
                          onPressed: () async =>
                              await controller.sendWhatsapp(),
                          tooltip: "Whatsapp",
                          backgroundColor: Colors.green,
                          child: SvgIcon.svgIcon(ImgStrings.whatsapp,
                              size: Size.square(AppDimen.h20)),
                        );
                }),
              ]),
        ),
        StreamBuilder(
          stream: controller.repoTransaction.streamAllNewTransaction(),
          builder: (BuildContext context, snapshot) {
            if (snapshot.hasData) {
              var transaction = snapshot.data
                  ?.where((element) => element.isOnOrder == true)
                  .firstOrNull;

              if (transaction != null) {
                controller.checkUpdateTransactionOnline(transaction);
                return Obx(() {
                  return AnimatedContainer(
                    height: controller.isMainScrolling.value ? 0 : null,
                    duration: const Duration(seconds: 1),
                    curve: Curves.fastOutSlowIn,
                    child: Obx(() {
                      return AnimatedOpacity(
                        opacity: controller.isMainScrolling.value ? 0.0 : 1.0,
                        duration: const Duration(seconds: 1),
                        child: InkWell(
                          onTap: () => ActiveTransactionFullScreenDialog().show(
                              context: context,
                              transRepo: controller.repoTransaction,
                              dealRepo: controller.repoDeal),
                          child: Container(
                              padding: EdgeInsets.symmetric(
                                  horizontal: AppDimen.h8,
                                  vertical: AppDimen.h8),
                              margin: EdgeInsets.only(
                                  left: 8.h + kFloatingActionButtonMargin,
                                  top: AppDimen.h10),
                              width: Constants.defaultMaxWidth,
                              decoration: BoxDecoration(
                                  color: wrapperC.getPrimaryColor(),
                                  borderRadius: const BorderRadius.all(
                                      Radius.circular(10))),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        transaction.orderSalesId == null
                                            ? "Order Code: ${transaction.selfOrder?.orderCode ?? ''}"
                                            : "Order: ${transaction.orderSalesId ?? ''}",
                                        style: AppFont.componentSmallBold.copyWith(
                                            color: wrapperC
                                                .getPrimaryColor()
                                                .changeColorBasedOnBackgroundColor()
                                                .$1),
                                      ),
                                      Text(
                                          transaction.status == null
                                              ? "Self Order"
                                              : translationOrderStatus(
                                                  transaction.status ?? ''),
                                          style: AppFont.componentSmall.copyWith(
                                              color: wrapperC
                                                  .getPrimaryColor()
                                                  .changeColorBasedOnBackgroundColor()
                                                  .$1)),
                                    ],
                                  ),
                                ],
                              )),
                        ),
                      );
                    }),
                  );
                });
              }
            }
            return const SizedBox();
          },
        ),
      ],
    );
  }

  Widget _offsetPopup(
          {required HomeController controller,
          required BuildContext context}) =>
      PopupMenuButton<int>(
        itemBuilder: (context) =>
            controller.listCategoryNameFloatingActionButton
                .map(
                  (e) => PopupMenuItem(
                      onTap: () {
                        int index = controller
                            .listCategoryNameFloatingActionButton
                            .indexOf(e);
                        if (controller.itemScrollController.isAttached) {
                          controller.itemScrollController.scrollTo(
                              index: index,
                              duration: const Duration(milliseconds: 500),
                              alignment: (index <= 4
                                      ? (index / (5.0 * (index + 4)))
                                          .clamp(0, 0.113)
                                      : 0.113)
                                  .h,
                              curve: Curves.easeIn);
                          AnalyticsService.observer.analytics
                              .logEvent(name: "select_category", parameters: {
                            "name": e.categoryName,
                            "position": e.categoryPosition,
                          });
                        }
                      },
                      height: 0,
                      padding: const EdgeInsets.symmetric(horizontal: 4),
                      value: controller.listCategoryNameFloatingActionButton
                          .indexOf(e),
                      textStyle: AppFont.componentSmallBold,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(e.categoryName),
                              Text(
                                "${e.products?.length ?? 0}",
                                style: AppFont.componentSmall
                                    .copyWith(color: AppColor.black70),
                              ),
                            ],
                          ),
                          const Divider()
                        ],
                      )),
                )
                .toList(),
        offset: const Offset(0, -100),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Icon(Icons.book,
                color: controller.wrapperC
                    .getPrimaryColor()
                    .changeColorBasedOnBackgroundColor()
                    .$1,
                size: AppDimen.h18),
            Text(
              'Menu',
              style: AppFont.componentSmallBold.copyWith(
                  color: controller.wrapperC
                      .getPrimaryColor()
                      .changeColorBasedOnBackgroundColor()
                      .$1,
                  fontSize: 10.sp),
            ),
          ],
        ),
      );
}
