import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/home/<USER>/home_controller.dart';
import 'package:mobile_crm/app/modules/screen_wrapper/controller/wrapper_controller.dart';
import 'package:mobile_crm/app/widget/cached_image_widget.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/app_constants.dart';
import 'package:mobile_crm/data/models/user_model.dart';
import 'package:mobile_crm/routes/app_pages.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

import '../../../../../core/values/app_strings.dart';
import '../../../../../data/services/analytics_service.dart';
import 'loading/user_card_loading.dart';

class UserCard extends StatelessWidget {
  const UserCard({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<HomeController>();
    return SizedBox(
      width: double.infinity,
      height: Constants.containerUserDetailHeight(context),
      child: Padding(
        padding: const EdgeInsets.all(8),
        child: Container(
          decoration: BoxDecoration(boxShadow: const [
            BoxShadow(
                color: AppColor.black5,
                blurRadius: 1,
                spreadRadius: 1,
                offset: Offset(0, 1))
          ], color: AppColor.white, borderRadius: BorderRadius.circular(10)),
          child: Obx(
            () => (!controller.isLoggedIn.value)
                ? InkWell(
                    key: const Key('loginKey'),
                    child: Center(
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceAround,
                        children: [
                          Text(
                            Strings.pleaseLoginToContinue.tr,
                            style: AppFont.componentSmall,
                          ),
                          ClipOval(
                            child: Material(
                                color: Colors.black,
                                child: Icon(Icons.keyboard_arrow_right,
                                    color: Colors.white,
                                    size: Constants.iconSize(context))),
                          )
                        ],
                      ),
                    ),
                    onTap: () {
                      Get.toNamed(Routes.LOGIN);
                    })
                : FutureBuilder(
                    future: controller.userDetail.value,
                    builder: (context, snapshot) {
                      if (snapshot.hasData) {
                        return content(context, snapshot.data);
                      } else if (snapshot.hasError) {
                        return Icon(Icons.error_outline,
                            size: Constants.iconSize(context));
                      } else {
                        return const UserCardLoading();
                      }
                    },
                  ),
          ),
        ),
      ),
    );
  }

  Widget content(BuildContext context, UserModel? user) {
    final controller = Get.find<HomeController>();
    final c = Get.find<WrapperController>();
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: [
        Expanded(
          flex: 2,
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: Constants.defaultPadding),
            child: InkWell(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CachedImageWidget(
                      imageUrl: user?.barcodeUrl ?? '', width: 30, height: 30),
                  Flexible(
                    child: Text("scan me",
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        style:
                            AppFont.componentSmall.copyWith(fontSize: 10.sp)),
                  )
                ],
              ),
              onTap: () {
                AnalyticsService.observer.analytics.logEvent(
                    name: "get_secret_id", parameters: {"position": "home"});
                Sentry.addBreadcrumb(Breadcrumb(
                    type: 'debug',
                    category: 'user.activity.home.scan_me',
                    data: {"user": user?.barcode},
                    level: SentryLevel.debug));
                Get.toNamed(Routes.MYQRCODE);
              },
            ),
          ),
        ),
        const VerticalDivider(
          color: AppColor.black5,
          thickness: 1,
        ),
        Expanded(
          flex: 4,
          child: InkWell(
            onTap: () {
              Get.toNamed(Routes.POINT);
            },
            child: Padding(
              padding:
                  EdgeInsets.symmetric(horizontal: Constants.defaultPadding),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(user?.memberType ?? '-', style: AppFont.componentSmall),
                  user?.totalPoint.toString() == "null"
                      ? Text(
                          "0 ${c.configApp.value.language?.point ?? 'Point'}",
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          style: AppFont.componentSmall)
                      : Text(
                          "${user?.totalPoint.toCurrency} ${c.configApp.value.language?.point ?? 'Point'}",
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                          style: AppFont.componentSmall)
                ],
              ),
            ),
          ),
        ),
        const VerticalDivider(
          color: AppColor.black5,
          thickness: 1,
        ),
        Expanded(
          flex: 4,
          child: InkWell(
            onTap: () {
              Get.toNamed(Routes.VOUCHER);
            },
            child: Padding(
              padding:
                  EdgeInsets.symmetric(horizontal: Constants.defaultPadding),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text("Voucher", style: AppFont.componentSmall),
                  Obx(() => Text(
                        "${controller.totalVoucher.value.toString()} ${Strings.available.tr}",
                        style: AppFont.componentSmall,
                      ))
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}
