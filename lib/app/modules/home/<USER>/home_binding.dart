import 'package:get/get.dart';
import 'package:mobile_crm/data/repository/app_repository.dart';
import 'package:mobile_crm/data/repository/deal_repository.dart';
import 'package:mobile_crm/data/repository/inbox_repository.dart';
import 'package:mobile_crm/data/repository/membership_repository.dart';
import 'package:mobile_crm/data/repository/notification_me_repository.dart';
import 'package:mobile_crm/data/repository/outlet_repository.dart';
import 'package:mobile_crm/data/repository/product_repository.dart';
import 'package:mobile_crm/data/repository/transaction_repository.dart';
import 'package:mobile_crm/data/repository/user_repository.dart';
import 'package:mobile_crm/data/repository/wishlist_repository.dart';

import '../../../../data/repository/cart_repository.dart';
import '../controller/home_controller.dart';

class HomeBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => InboxRepositoryIml(), fenix: true);
    Get.lazyPut(() => OutletRepositoryIml(), fenix: true);
    Get.lazyPut(() => DealRepositoryIml(), fenix: true);
    Get.lazyPut(() => ProductRepositoryIml(), fenix: true);
    Get.lazyPut(() => AppRepositoryIml(), fenix: true);
    Get.lazyPut(() => UserRepositoryIml(), fenix: true);
    Get.lazyPut(() => MembershipRepositoryIml(), fenix: true);
    Get.lazyPut(() => CartRepositoryIml(), fenix: true);
    Get.lazyPut(() => NotificationMeRepositoryIml(), fenix: true);
    Get.lazyPut(() => TransactionRepositoryIml(), fenix: true);
    Get.lazyPut(() => WishlistRepositoryIml(), fenix: true);

    Get.lazyPut(() => HomeController(), fenix: true);
  }
}
