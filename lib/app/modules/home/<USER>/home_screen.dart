import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/home/<USER>/widget/home_widget.dart';
import 'package:mobile_crm/app/modules/screen_wrapper/controller/wrapper_controller.dart';
import 'package:mobile_crm/app/modules/screen_wrapper/views/app_page_wrapper.dart';
import 'package:mobile_crm/app/widget/app_bar_widget/sliver_app_bar_delegate_widget.dart';
import 'package:mobile_crm/app/widget/cached_image_widget.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/app_constants.dart';
import 'package:mobile_crm/core/values/values.dart';

import '../controller/home_controller.dart';
import 'widget/home_fab.dart';

class HomeScreen extends GetView<HomeController> {
  const HomeScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final WrapperController wrapperC = Get.find();
    return PageWrapper(
      child: Scaffold(
        backgroundColor: Colors.transparent,
        floatingActionButton: const HomeFAB(),
        body: NotificationListener(
          onNotification: (notification) {
            if (notification is ScrollStartNotification) {
              controller.onStartScroll(notification.metrics);
            } else if (notification is ScrollUpdateNotification) {
              controller.onUpdateScroll(notification.metrics);
            } else if (notification is ScrollEndNotification) {
              controller.onEndScroll(notification.metrics);
            }
            return true;
          },
          child: NestedScrollView(
            controller: controller.scrollControllerNested,
            headerSliverBuilder:
                (BuildContext context, bool innerBoxIsScrolled) {
              return <Widget>[
                SliverAppBar(
                  backgroundColor: wrapperC.getPrimaryColor(),
                  automaticallyImplyLeading: false,
                  pinned: true,
                  floating: true,
                  elevation: 0,
                  systemOverlayStyle: SystemUiOverlayStyle(
                      statusBarIconBrightness: (controller.listPaletteColorImage
                                          .firstOrNull?.color ??
                                      wrapperC.getPrimaryColor())
                                  .computeLuminance() >
                              0.55
                          ? Brightness.dark
                          : Brightness.light,
                      statusBarColor: Colors.transparent),
                  title: const HomeSearch(),
                  toolbarHeight: kToolbarHeight +
                      MediaQuery.of(context).padding.top +
                      30.h,
                  flexibleSpace: FlexibleSpaceBar(
                    background: Obx(
                      () => (wrapperC.configApp.value.asset == null)
                          ? const Text('')
                          : CachedImageWidget(
                              height: kToolbarHeight +
                                  MediaQuery.of(context).padding.top +
                                  30.h,
                              width: Get.size.width,
                              fit: BoxFit.fitWidth,
                              repeat: ImageRepeat.repeat,
                              needColorFromImage: true,
                              colorPalette: (value) {
                                controller.listPaletteColorImage.value = value;
                              },
                              imageUrl: wrapperC
                                      .configApp.value.asset?.toolbar_background
                                      .toString() ??
                                  '',
                              errorWidget: (context, url, error) =>
                                  const SizedBox(),
                            ),
                    ),
                  ),
                ),
                const SliverToBoxAdapter(
                  child: Column(
                    children: [UserCard(), HomeBanner()],
                  ),
                ),
                SliverPersistentHeader(
                    pinned: true,
                    delegate: SliverAppBarDelegateWidget(
                      minHeight: 40,
                      maxHeight: 50,
                      child: Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: TabBar(
                          controller: controller.tabController,
                          indicatorColor: AppColor.black,
                          dividerColor: Colors.transparent,
                          automaticIndicatorColorAdjustment: false,
                          labelColor: AppColor.black,
                          labelStyle: AppFont.componentSmall,
                          indicatorWeight: 2,
                          isScrollable: true,
                          tabs: [
                            Tab(child: Obx(() {
                              return Text(
                                wrapperC.configApp.value.language
                                        ?.getMenuLanguage ??
                                    "Menu",
                              );
                            })),
                            Tab(child: Obx(() {
                              return Text(
                                wrapperC.configApp.value.language
                                        ?.getOutletLanguage ??
                                    "Outlet",
                              );
                            })),
                            Tab(child: Obx(() {
                              return Text(
                                wrapperC.configApp.value.language
                                        ?.getDealsLanguage ??
                                    "Deals",
                              );
                            })),
                          ],
                        ),
                      ),
                    ))
              ];
            },
            body: Padding(
              padding: EdgeInsets.only(
                  left: Constants.defaultPadding,
                  right: Constants.defaultPadding,
                  top: Constants.defaultPadding),
              child: TabBarView(
                controller: controller.tabController,
                children: const [MenuListHome(), OutletList(), DealsList()],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
