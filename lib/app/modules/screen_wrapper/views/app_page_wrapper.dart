import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/widget/cached_image_widget.dart';
import 'package:mobile_crm/core/values/app_constants.dart';

import '../controller/wrapper_controller.dart';

class PageWrapper extends GetView<WrapperController> {
  const PageWrapper(
      {Key? key,
      required this.child,
      this.onWillPop,
      this.bottomNavigationBar,
      this.appBar,
      this.bottomSheet,
      this.disableBackground = false,
      this.resizeToAvoidBottomInset = false})
      : super(key: key);
  final Widget child;
  final Widget? bottomNavigationBar;
  final Widget? bottomSheet;
  final WillPopCallback? onWillPop;
  final bool resizeToAvoidBottomInset;
  final bool disableBackground;
  final PreferredSizeWidget? appBar;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: appBar,
      resizeToAvoidBottomInset: resizeToAvoidBottomInset,
      bottomNavigationBar: bottomNavigationBar != null
          ? SizedBox(
              width: Constants.defaultMaxWidth, child: bottomNavigationBar)
          : null,
      bottomSheet: bottomSheet != null
          ? SizedBox(width: Constants.defaultMaxWidth, child: bottomSheet)
          : null,
      body: WillPopScope(
        onWillPop: onWillPop,
        child: Center(
          child: SizedBox(
            width: Constants.defaultMaxWidth,
            child: Stack(
              children: [
                disableBackground
                    ? const SizedBox()
                    : SizedBox(
                        height: double.infinity,
                        width: Constants.defaultMaxWidth,
                        child: Obx(
                          () => (controller.getAsset()?.app_background == null)
                              ? const SizedBox()
                              : CachedImageWidget(
                                  fit: BoxFit.fill,
                                  imageUrl:
                                      controller.getAsset()?.app_background ??
                                          '',
                                  width: Constants.defaultMaxWidth,
                                  errorWidget: (context, url, error) =>
                                      const SizedBox(),
                                ),
                        ),
                      ),
                child
              ],
            ),
          ),
        ),
      ),
    );
  }
}
