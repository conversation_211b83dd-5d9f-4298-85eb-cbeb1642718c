import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/inbox/inbox_detail/controller/inbox_detail_controller.dart';
import 'package:mobile_crm/app/modules/screen_wrapper/views/app_page_wrapper.dart';
import 'package:mobile_crm/app/widget/app_bar_widget/app_bar_widget.dart';
import 'package:mobile_crm/app/widget/custom_circular_progress_widget.dart';
import 'package:mobile_crm/app/widget/html_widget.dart';
import 'package:mobile_crm/core/extensions/double_extensions.dart';
import 'package:mobile_crm/core/extensions/int_extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';

import '../../../../../core/values/app_strings.dart';
import '../../../../widget/app_page_empty.dart';

class InboxDetailScreen extends GetView<InboxDetailController> {
  const InboxDetailScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: PageWrapper(
        child: SingleChildScrollView(
          child: SizedBox(
            height: MediaQuery.of(context).size.height,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const AppBarWidget(title: "Inbox Detail"),
                Flexible(
                  child: FutureBuilder(
                    future: controller.getDetailInbox(),
                    builder: (context, snapshot) {
                      if (snapshot.hasData) {
                        return Column(
                          children: [
                            Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    (snapshot.data?.notification_type ?? '')
                                        .toUpperCase(),
                                    style: AppFont.paragraphSmall,
                                  ),
                                  Text(
                                    snapshot.data?.data_created.toDate ?? '',
                                    style: AppFont.paragraphSmall,
                                  ),
                                ],
                              ),
                            ),
                            const Divider(),
                            Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: SizedBox(
                                width: Get.size.width,
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  children: [
                                    Text(
                                      snapshot.data?.title ?? '',
                                      style: AppFont.componentMediumBold,
                                    ),
                                    5.0.height,
                                    HtmlWidget(
                                      data: snapshot.data?.message ?? '',
                                      fontSize: 12,
                                      maxLines: 12,
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        );
                      }

                      if (!snapshot.hasData &&
                          snapshot.connectionState == ConnectionState.done) {
                        return AppPageEmpty(
                          reason: Strings.valueNotFound
                              .trParams({'value': 'Inbox'}),
                          buttonText: Strings.back.tr,
                          func: () => Get.back(),
                        );
                      }
                      return SizedBox(
                        height: MediaQuery.of(context).size.height / 2,
                        child: const CustomCircularProgressIndicator(
                          valueColor: AppColor.black90,
                          backgroundColor: AppColor.white,
                        ),
                      );
                    },
                  ),
                )
              ],
            ),
          ),
        ),
      ),
    );
  }
}
