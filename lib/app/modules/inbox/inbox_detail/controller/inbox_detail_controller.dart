import 'package:get/get.dart';
import 'package:mobile_crm/data/providers/db/database.dart';

import '../../../../../domain/repository/inbox_repository.dart';
import '../../../../../routes/app_pages.dart';

class InboxDetailController extends GetxController {
  final InboxRepository _repo;

  InboxDetailController(this._repo);

  @override
  Future<void> onInit() async {
    // await getDetailInbox(Get.parameters['id'] ?? '0');
    super.onInit();
  }

  @override
  void onClose() {
    dispose();
    super.onClose();
  }

  Future<InboxData?> getDetailInbox() async {
    InboxData? check = Get.arguments;
    if (check == null) {
      var result = await _repo
          .getDetailInbox(Get.parameters['id'] ?? '0')
          .then((value) => value.data);
      if (result?.notification_data?.sales_id != null) {
        Get.offAndToNamed(
            Routes.NOTIFICATIONSALES(
                result?.notification_data?.sales_id ?? '0'),
            arguments: check);
        return result;
      } else {
        return result;
      }
    } else {
      if (check.notification_data?.sales_id != null) {
        Get.offAndToNamed(
            Routes.NOTIFICATIONSALES(check.notification_data?.sales_id ?? '0'),
            arguments: check);
        return check;
      } else {
        return check;
      }
    }
  }
}
