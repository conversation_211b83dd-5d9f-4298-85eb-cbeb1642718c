// ignore_for_file: invalid_use_of_protected_member

import 'dart:async';

import 'package:drift/drift.dart' as drift;
import 'package:get/get.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:mobile_crm/app/helper/inbox_helper.dart';
import 'package:mobile_crm/data/providers/db/database.dart';
import 'package:mobile_crm/data/services/local_storage.dart';
import 'package:mobile_crm/domain/repository/deal_repository.dart';
import 'package:mobile_crm/domain/repository/inbox_repository.dart';
import 'package:mobile_crm/domain/repository/transaction_repository.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

import '../../../utils/logger.dart';
import '../../../utils/toast.dart';

class InboxController extends GetxController {
  final InboxRepository _repo;
  final DealRepository repoDeal;
  final TransactionRepository repoTransaction;

  InboxController(this._repo, this.repoTransaction, this.repoDeal);

  final store = LocalStorageService();
  final PagingController<int, InboxData> pagingController =
      PagingController(firstPageKey: 0);

  var listTransaction = <TransactionDataData>[].obs;
  InboxData inboxData = InboxData();

  var isUpdate = false.obs;
  var isLogIn = false.obs;

  @override
  void onInit() async {
    isLogIn.value = store.token != null;
    Sentry.addBreadcrumb(Breadcrumb(
        type: 'debug',
        category: 'user.activity.inbox.init',
        data: {"isLogIn": isLogIn.value},
        level: SentryLevel.debug));
    if (isLogIn.value) {
      await getAllTransaction();
      pagingController.nextPageKey = 1;
      pagingController.addPageRequestListener((pageKey) async {
        await _fetchPage(pageKey);
      });
      pagingController.addStatusListener((status) {
        infoLogger("Status Paging", "Paging status $status");
        if (status == PagingStatus.subsequentPageError) {
          Toast.show("Something went wrong",
              onAction: () => pagingController.retryLastFailedRequest(),
              type: ToastType.error);
        }
      });
      pagingController.refresh();
    }
    super.onInit();
  }

  Future<void> _fetchPage(int pageKey) async {
    try {
      int limitData = 15;
      final newItems =
          await _repo.getInboxPagination(page: pageKey, limit: limitData);
      final isLastPage = newItems.length < limitData;
      if (isLastPage) {
        pagingController.appendLastPage(newItems);
      } else {
        final nextPageKey = pageKey += 1;
        pagingController.appendPage(newItems, nextPageKey);
      }
      pagingController.itemList =
          InboxHelper.filterInbox(pagingController.itemList ?? []);
      isUpdate.value = false;
    } catch (error, s) {
      pagingController.error = error;
      errorLogger(pos: "fetch page", error: error, stackTrace: s);
    }
  }

  @override
  void onClose() {
    pagingController.dispose();
    dispose();
    super.onClose();
  }

  Future<List<TransactionDataData>> getAllTransaction() async =>
      listTransaction.value =
          await repoTransaction.getAllTransaction(dealRepo: repoDeal);

  Future refreshData() async {
    isUpdate.value = true;
    await _repo.getInbox().then((value) async {
      isUpdate.value = false;
    });
    Sentry.addBreadcrumb(Breadcrumb(
        type: 'debug',
        category: 'user.activity.inbox.refreshData',
        level: SentryLevel.debug));
  }

  void updateInbox(InboxData data) async {
    inboxData = data;
    data.is_read == 0 ? await _repo.updateInbox(data) : null;
    List<InboxData>? currentList = pagingController.itemList;
    List<InboxData> newList = [];
    currentList?.forEach((ibx) {
      if (ibx.notification_id == data.notification_id) {
        InboxData newIbx = ibx.copyWith(is_read: const drift.Value(1));
        newList.add(newIbx);
      } else {
        newList.add(ibx);
      }
    });
    pagingController.itemList = newList;
  }
}
