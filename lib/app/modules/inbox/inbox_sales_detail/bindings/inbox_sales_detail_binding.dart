import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/inbox/inbox_export.dart';
import 'package:mobile_crm/data/repository/transaction_repository.dart';

import '../../../../../data/repository/inbox_repository.dart';

class InboxSalesDetailBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => TransactionRepositoryIml());
    Get.lazyPut(() => InboxRepositoryIml());
    Get.lazyPut(() => InboxSalesDetailController());
  }
}
