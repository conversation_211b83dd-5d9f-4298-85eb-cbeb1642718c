import 'package:get/get.dart';
import 'package:mobile_crm/data/models/feedback_model.dart';
import 'package:mobile_crm/data/models/server_response.dart';
import 'package:mobile_crm/data/models/transaction_history_model.dart';
import 'package:mobile_crm/data/repository/inbox_repository.dart';
import 'package:mobile_crm/data/repository/transaction_repository.dart';

class InboxSalesDetailController extends GetxController {
  final transRepo = Get.find<TransactionRepositoryIml>();
  final inboxRepo = Get.find<InboxRepositoryIml>();

  var sales = Future.value(TransactionHistoryModel()).obs;

  @override
  void onInit() {
    fetchTransaction();
    super.onInit();
  }

  @override
  void onClose() {
    dispose();
    super.onClose();
  }

  void fetchTransaction() async {
    sales.value =
        transRepo.getTransactionHistoryDetail(getSalesIdByParameterPath());
  }

  String getSalesIdByParameterPath() => Get.parameters['id'] ?? '0';

  Future<bool> sendFeedback(String comment, int star) async {
    FeedbackModel feedbackModel = FeedbackModel(
        comment: comment, salesId: Get.parameters['id'], stars: star);
    ServerResponse resp = await transRepo.postGiveFeedback(feedbackModel);
    return resp.status;
  }
}
