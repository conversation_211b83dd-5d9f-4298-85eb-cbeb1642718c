import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/inbox/inbox_sales_detail/views/widgets/sales_webview.dart'
if (dart.library.html) 'package:mobile_crm/app/modules/inbox/inbox_sales_detail/views/widgets/sales_webview_web_mode.dart';
import 'package:mobile_crm/app/modules/screen_wrapper/views/app_page_wrapper.dart';
import 'package:mobile_crm/app/utils/app_alert.dart';
import 'package:mobile_crm/app/widget/app_bar_widget/app_bar_widget.dart';
import 'package:mobile_crm/app/widget/app_page_empty.dart';
import 'package:mobile_crm/app/widget/button_widget/button_widget.dart';
import 'package:mobile_crm/app/widget/custom_circular_progress_widget.dart';
import 'package:mobile_crm/app/widget/full_screen_dialog/feedback/feedback_fdialog.dart';
import 'package:mobile_crm/data/models/transaction_history_model.dart';
import 'package:mobile_crm/data/services/analytics_service.dart';

import '../controllers/inbox_sales_detail_controller.dart';

class InboxSalesDetailScreen extends GetView<InboxSalesDetailController> {
  const InboxSalesDetailScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return PageWrapper(
      child: Scaffold(
        backgroundColor: Colors.transparent,
        bottomNavigationBar: BottomAppBar(
          child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: Obx(() {
                return FutureBuilder(
                    future: controller.sales.value,
                    builder: (context, snapshot) {
                      if (snapshot.hasData) {
                        return snapshot.data?.dataHtml == null
                            ? const SizedBox()
                            : PrimaryButton(
                                onPressed: () async {
                                  AnalyticsService.observer.analytics
                                      .logEvent(name: "Feedback", parameters: {
                                    "invoice":
                                        controller.getSalesIdByParameterPath()
                                  });
                                  String comment =
                                      snapshot.data?.feedback?.comment ?? "";
                                  int star =
                                      snapshot.data?.feedback?.stars ?? 5;

                                  if (snapshot.data?.feedback?.stars != null) {
                                    return AppAlert.afterFeedback(
                                        star: star.toDouble(),
                                        comment: comment);
                                  }

                                  FeedbackFullScreenDialog().show(
                                      context: context,
                                      transRepo: controller.transRepo,
                                      salesId: controller
                                          .getSalesIdByParameterPath());
                                },
                                width: double.infinity,
                                text: 'Feedback',
                                type: PrimaryButtonType.type3,
                              );
                        // return content(snapshot.data);
                      } else if (snapshot.hasError) {
                        return const Text('');
                      } else {
                        return const SizedBox();
                      }
                    });
              })),
        ),
        body: SizedBox(
          height: MediaQuery.of(context).size.height,
          width: MediaQuery.of(context).size.width,
          child: Column(
            children: [
              const AppBarWidget(title: "Transaction Detail"),
              Expanded(
                child: Obx(() {
                  return FutureBuilder(
                      future: controller.sales.value,
                      builder: (context, snapshot) {
                        if (snapshot.hasData) {
                          return snapshot.data?.dataHtml == null
                              ? AppPageEmpty(
                                  reason: "Data not found!",
                                  buttonText: "Back",
                                  func: () {
                                    Get.back();
                                  },
                                )
                              : WebViewPage(
                                  html: snapshot.data ??
                                      TransactionHistoryModel(),
                                );
                          // return content(snapshot.data);
                        } else if (snapshot.hasError) {
                          return const Text('');
                        } else {
                          return const CustomCircularProgressIndicator();
                        }
                      });
                }),
              )
            ],
          ),
        ),
      ),
    );
  }
}
