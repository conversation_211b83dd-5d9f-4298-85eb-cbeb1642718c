import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:mobile_crm/data/models/transaction_history_model.dart';

class WebViewPage extends StatelessWidget {
  const WebViewPage({Key? key, required this.html}) : super(key: key);
  final TransactionHistoryModel html;

  @override
  Widget build(BuildContext context) {
    var size = MediaQuery.of(context).size;
    return InAppWebView(
      initialData: InAppWebViewInitialData(data: html.dataHtml ?? "<p></p>"),
      onLoadStop: (controller, url) async =>
          Future.delayed(const Duration(milliseconds: 200), () async {
        await controller.zoomBy(zoomFactor: 2.2, animated: true);
        int x = await controller.getScrollX() ?? size.width.toInt();
        await controller.scrollTo(x: x, y: 0);
      }),
    );
  }
}
