import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/screen_wrapper/controller/wrapper_controller.dart';
import 'package:mobile_crm/app/widget/cached_image_widget.dart';

class SalesAppBar extends StatelessWidget {
  const SalesAppBar({Key? key, required this.title}) : super(key: key);
  final String title;

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<WrapperController>();
    return AppBar(
      title: Text(title),
      backgroundColor: controller.getPrimaryColor(),
      flexibleSpace: Obx(
        () => (controller.configApp.value.asset == null)
            ? const Text('')
            : CachedImageWidget(
                height: kToolbarHeight + MediaQuery.of(context).padding.top,
                width: Get.size.width,
                fit: BoxFit.cover,
                imageUrl: controller.configApp.value.asset?.toolbar_background
                        .toString() ??
                    '',
              ),
      ),
    );
  }
}
