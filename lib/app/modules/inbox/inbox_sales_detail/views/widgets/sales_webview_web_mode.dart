// ignore_for_file: avoid_web_libraries_in_flutter

import 'dart:html';
import 'dart:ui' as ui;

import 'package:flutter/material.dart';

import '../../../../../../data/models/transaction_history_model.dart';

class WebViewPage extends StatelessWidget {
  const WebViewPage({Key? key, required this.html}) : super(key: key);
  final TransactionHistoryModel html;

  @override
  Widget build(BuildContext context) {
    // ignore: undefined_prefixed_name
    ui.platformViewRegistry.registerViewFactory(
        'hello-html',
        (int viewId) => IFrameElement()
          ..srcdoc = html.dataHtml
          ..style.border = 'none');
    return const SizedBox.expand(
      child: HtmlElementView(viewType: 'hello-html'),
    );
  }
}
