import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/enum/inbox_type_enum.dart';
import 'package:mobile_crm/data/providers/db/database.dart';

import '../../controllers/inbox_controller.dart';
import '../itemView/item_view_bar.dart';

class InboxTabWidgetPaginationSwitch extends StatelessWidget {
  const InboxTabWidgetPaginationSwitch({Key? key, required this.type})
      : super(key: key);
  final InboxTypeEnum type;

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<InboxController>();
    return ListView.builder(
      itemCount: controller.pagingController.itemList
              ?.where((ibx) => ibx.notification_type == type.name)
              .toList()
              .length ??
          0,
      itemBuilder: (BuildContext ctxt, int index) {
        var ibx = controller.pagingController.itemList
            ?.where((ibx) => ibx.notification_type == type.name)
            .toList();
        InboxData inbox = ibx![index];
        return ItemViewBar(
          inbox: inbox,
          index: index,
        );
      },
    );
  }
}
