import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:mobile_crm/app/enum/inbox_type_enum.dart';
import 'package:mobile_crm/app/widget/custom_refresh_indicator_widget.dart';
import 'package:mobile_crm/data/providers/db/database.dart';

import '../../../../../core/values/app_strings.dart';
import '../../../../../routes/app_pages.dart';
import '../../../../widget/app_page_empty.dart';
import '../../controllers/inbox_controller.dart';
import '../itemView/item_view_bar.dart';
import '../loading/item_view_loading.dart';

class InboxTabWidgetPagination extends StatelessWidget {
  const InboxTabWidgetPagination({Key? key, InboxTypeEnum? type})
      : super(key: key);
  final InboxTypeEnum? type = InboxTypeEnum.all;

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<InboxController>();
    return CustomRefreshIndicator(
        onRefresh: () =>
            Future.sync(() => controller.pagingController.refresh()),
        child: PagedListView<int, InboxData>(
          shrinkWrap: true,
          pagingController: controller.pagingController,
          builderDelegate: PagedChildBuilderDelegate<InboxData>(
            itemBuilder: (context, item, index) => ItemViewBar(
              inbox: item,
              index: index,
            ),
            firstPageProgressIndicatorBuilder: (context) =>
                const ItemViewLoading(),
            newPageProgressIndicatorBuilder: (context) =>
                const ItemViewLoading(),
            noItemsFoundIndicatorBuilder: (context) => SizedBox(
              height: MediaQuery.of(context).size.height,
              child: AppPageEmpty(
                func: () {
                  if (Get.previousRoute.isEmpty || Get.previousRoute == '') {
                    Get.offAllNamed(Routes.HOME);
                  } else {
                    Get.back();
                  }
                },
                reason: "Empty inbox",
                buttonText: Strings.back.tr,
              ),
            ),
          ),
        ));
  }
}
