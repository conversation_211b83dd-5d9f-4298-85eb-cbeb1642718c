import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/widget/custom_refresh_indicator_widget.dart';
import 'package:mobile_crm/data/providers/db/database.dart';

import '../../../../../core/values/app_strings.dart';
import '../../../../widget/app_page_empty.dart';
import '../../controllers/inbox_controller.dart';
import '../itemView/item_view_bar.dart';
import '../loading/item_view_loading.dart';

class InboxTabWidget extends StatelessWidget {
  const InboxTabWidget({Key? key, required this.inboxs}) : super(key: key);
  final List<InboxData> inboxs;

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<InboxController>();
    return Obx(
      () => controller.isUpdate.value
          ? const ItemViewLoading()
          : inboxs.isEmpty
              ? AppPageEmpty(
                  func: () => Get.back(),
                  reason: "Empty inbox",
                  buttonText: Strings.back.tr,
                )
              : CustomRefreshIndicator(
                  onRefresh: () => controller.refreshData(),
                  child: ListView.builder(
                    itemCount: inboxs.length,
                    itemBuilder: (BuildContext ctxt, int index) {
                      InboxData inbox = inboxs[index];
                      return ItemViewBar(
                        inbox: inbox,
                        index: index,
                      );
                    },
                  ),
                ),
    );
  }
}
