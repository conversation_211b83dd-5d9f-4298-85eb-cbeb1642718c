import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/widget/shimmer_loading_widget.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';

import '../../controllers/inbox_controller.dart';

class ItemViewLoading extends GetView<InboxController> {
  const ItemViewLoading({super.key});

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: 3,
      itemBuilder: (context, index) => itemLoading(),
      separatorBuilder: (context, index) => const SizedBox(height: 6),
    );
  }

  Widget itemLoading() {
    return Container(
      margin:
          EdgeInsets.symmetric(vertical: AppDimen.h6, horizontal: AppDimen.h4),
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
          color: AppColor.white,
          borderRadius: BorderRadius.all(Radius.circular(10.r)),
          boxShadow: const [
            BoxShadow(
                color: AppColor.disable,
                offset: Offset(1, 1),
                blurRadius: 4,
                spreadRadius: 1),
          ]),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              ShimmerWidget(
                width: AppDimen.h50,
                height: AppDimen.h12,
              ),
              ShimmerWidget(
                width: AppDimen.h48,
                height: AppDimen.h12,
              ),
            ],
          ),
          const Divider(),
          Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ShimmerWidget(
                width: AppDimen.h128,
                height: AppDimen.h12,
              ),
              AppDimen.h6.height,
              ShimmerWidget(
                width: AppDimen.h192,
                height: AppDimen.h12,
              ),
              AppDimen.h2.height,
              ShimmerWidget(
                width: AppDimen.h64,
                height: AppDimen.h12,
              ),
            ],
          ),
        ],
      ),
    );
  }
}
