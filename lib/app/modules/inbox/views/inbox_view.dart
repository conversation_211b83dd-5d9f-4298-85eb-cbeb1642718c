// ignore_for_file: invalid_use_of_protected_member

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/enum/inbox_type_enum.dart';
import 'package:mobile_crm/app/modules/screen_wrapper/controller/wrapper_controller.dart';
import 'package:mobile_crm/app/modules/screen_wrapper/views/app_page_wrapper.dart';
import 'package:mobile_crm/app/utils/utils.dart';
import 'package:mobile_crm/app/widget/app_bar_widget/app_bar_widget.dart';
import 'package:mobile_crm/app/widget/full_screen_dialog/active_transaction/active_transaction_fdialog.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/values.dart';
import 'package:mobile_crm/routes/app_pages.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

import '../controllers/inbox_controller.dart';
import 'widget/inbox_tab_widget_pagination.dart';
import 'widget/inbox_tab_widget_pagination_switch.dart';

class InboxScreen extends GetView<InboxController> {
  const InboxScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final WrapperController wrapperController = Get.find();
    return PageWrapper(
      child: Column(
        children: [
          AppBarWidget(
              title: Strings.notification.tr,
              actions: [
                IconButton(
                    onPressed: () {
                      Sentry.addBreadcrumb(Breadcrumb(
                          type: 'debug',
                          category:
                              'user.activity.inbox.active_transaction_full_screen',
                          level: SentryLevel.debug));
                      ActiveTransactionFullScreenDialog().show(
                          context: context,
                          transRepo: controller.repoTransaction,
                          dealRepo: controller.repoDeal);
                    },
                    icon: Obx(
                      () => Badge(
                          isLabelVisible:
                              controller.listTransaction.value.isNotEmpty,
                          label: Text(
                              convertNumber(controller.listTransaction.length)),
                          child: Icon(Icons.receipt_long_outlined,
                              size: Constants.iconSize(context))),
                    )),
              ],
              leading: IconButton(
                  onPressed: () {
                    if(Get.previousRoute.isEmpty || Get.previousRoute == '') {
                      Get.offAllNamed(Routes.HOME);
                    } else {
                      Get.back();
                    }
                  },
                  icon: Icon(
                    Icons.arrow_back,
                    size: Constants.iconSize(context),
                  ))),
          Expanded(
            child: DefaultTabController(
              length: 4,
              child: Column(
                children: [
                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: SizedBox(
                      height: 40.h,
                      child: TabBar(
                        dividerColor: Colors.transparent,
                        indicatorSize: TabBarIndicatorSize.tab,
                        indicator: BoxDecoration(
                            color: wrapperController.getPrimaryColor(),
                            borderRadius: BorderRadius.circular(10)),
                        labelColor: Colors.white,
                        labelStyle: AppFont.componentSmall,
                        unselectedLabelColor: Colors.black,
                        tabs: [
                          Tab(
                            text: Strings.all.tr,
                          ),
                          const Tab(
                            text: "Sales",
                          ),
                          const Tab(
                            text: "General",
                          ),
                          Tab(
                            text: Strings.order.tr,
                          )
                        ],
                      ),
                    ),
                  ),
                  Expanded(
                    child: MediaQuery.removePadding(
                      removeTop: true,
                      context: context,
                      child: const Padding(
                        padding: EdgeInsets.symmetric(horizontal: 8),
                        child: TabBarView(
                          children: [
                            InboxTabWidgetPagination(),
                            InboxTabWidgetPaginationSwitch(
                                type: InboxTypeEnum.sales),
                            InboxTabWidgetPaginationSwitch(
                                type: InboxTypeEnum.general),
                            InboxTabWidgetPaginationSwitch(
                                type: InboxTypeEnum.order_online),
                          ],
                        ),
                      ),
                    ),
                  )
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
