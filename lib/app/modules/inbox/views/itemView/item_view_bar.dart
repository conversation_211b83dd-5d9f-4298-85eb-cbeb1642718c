// ignore_for_file: must_be_immutable

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/screen_wrapper/controller/wrapper_controller.dart';
import 'package:mobile_crm/app/widget/html_widget.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/app_constants.dart';
import 'package:mobile_crm/data/models/config_model.dart';
import 'package:mobile_crm/data/providers/db/database.dart';
import 'package:mobile_crm/routes/app_pages.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

import '../inbox_view.dart';

class ItemViewBar extends InboxScreen {
  // ignore: prefer_typing_uninitialized_variables
  InboxData inbox;
  IconData icon;
  int index;

  ItemViewBar(
      {Key? key,
      required this.inbox,
      this.icon = Icons.ac_unit,
      this.index = 0})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    final WrapperController wrapperController = Get.find();
    ConfigData configData = wrapperController.configApp.value;
    Asset? asset = configData.asset;

    if (inbox.isHeader ?? false) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          index != 0 ? 10.0.height : 0.0.height,
          Text(
            inbox.data_created.toDayAgo,
            style: AppFont.paragraphSmallBold.copyWith(color: AppColor.ink02),
          ),
        ],
      );
    } else {
      return InkWell(
        onTap: () {
          controller.updateInbox(inbox);
          print(
              'inboxType: ${inbox.notification_type}, dataId: ${inbox.notification_data?.id}');
          if (inbox.notification_data?.sales_id != null) {
            Sentry.addBreadcrumb(Breadcrumb(
                type: 'debug',
                category: 'user.activity.inbox.detail_sales',
                data: {"inbox_id": inbox.notification_data?.sales_id},
                level: SentryLevel.debug));
            Get.toNamed(
                Routes.NOTIFICATIONSALES(
                    inbox.notification_data?.sales_id ?? '0'),
                arguments: inbox);
            return;
          }

          if (inbox.notification_data?.id != null) {
            final notifType = inbox.notification_type?.toLowerCase() ?? "";
            if (notifType == "promo" || notifType == "promotion") {
              Get.toNamed(
                  Routes.VOUCHERDETAIL(
                      inbox.notification_data?.id?.toString() ?? '0'),
                  arguments: inbox);
              return;
            }
          }

          Sentry.addBreadcrumb(Breadcrumb(
              type: 'debug',
              category: 'user.activity.inbox.detail_no_sales',
              data: {"inbox_id": inbox.notification_id},
              level: SentryLevel.debug));
          Get.toNamed(
              Routes.NOTIFICATIONDETAIL("${inbox.notification_id ?? 0}"),
              arguments: inbox);
        },
        child: Container(
          margin: EdgeInsets.symmetric(
              vertical: AppDimen.h6, horizontal: AppDimen.h4),
          padding: EdgeInsets.all(Constants.defaultPadding),
          decoration: BoxDecoration(
              color: (inbox.is_read == 1)
                  ? AppColor.white
                  : (asset != null)
                      ? wrapperController.getPrimaryColor().withOpacity(0.6)
                      : AppColor.ink02,
              borderRadius: BorderRadius.all(Radius.circular(10.r)),
              boxShadow: const [
                BoxShadow(
                    color: AppColor.disable,
                    offset: Offset(1, 1),
                    blurRadius: 4,
                    spreadRadius: 1),
              ]),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    (inbox.notification_type ?? '').toUpperCase(),
                    style: AppFont.componentSmall.copyWith(
                        color: (inbox.is_read == 1)
                            ? AppColor.black
                            : AppColor.white),
                  ),
                  Text(
                    inbox.data_created.toDate,
                    style: AppFont.paragraphSmall.copyWith(
                        color: (inbox.is_read == 1)
                            ? AppColor.ink02
                            : AppColor.ink05),
                  ),
                ],
              ),
              const Divider(),
              Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    inbox.title ?? '',
                    style: AppFont.componentSmall.copyWith(
                        color: (inbox.is_read == 1)
                            ? AppColor.black
                            : AppColor.white),
                  ),
                  5.0.height,
                  HtmlWidget(
                    data: inbox.message ?? '',
                    maxLines: 2,
                    fontSize: 12,
                    disable: inbox.is_read == 0,
                    disableColorSwitch: true,
                  ),
                ],
              ),
            ],
          ),
        ),
      );
    }
  }
}
