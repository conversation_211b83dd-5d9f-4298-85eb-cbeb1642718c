import 'package:get/get.dart';
import 'package:mobile_crm/data/repository/deal_repository.dart';
import 'package:mobile_crm/data/repository/inbox_repository.dart';
import 'package:mobile_crm/data/repository/transaction_repository.dart';

import '../controllers/inbox_controller.dart';

class NotificationBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => InboxRepositoryIml());
    Get.lazyPut(() => TransactionRepositoryIml());
    Get.lazyPut(() => DealRepositoryIml());
    Get.lazyPut(() => InboxController(Get.find<InboxRepositoryIml>(),
        Get.find<TransactionRepositoryIml>(), Get.find<DealRepositoryIml>()));
  }
}
