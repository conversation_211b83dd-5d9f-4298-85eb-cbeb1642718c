// ignore_for_file: invalid_use_of_protected_member

import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/helper/deal_helper.dart';
import 'package:mobile_crm/app/helper/product_helper.dart';
import 'package:mobile_crm/app/modules/outlet/views/widget/outlet_modal_bottom_sheet/outlet_product_detail_modal_bottom_sheet.dart';
import 'package:mobile_crm/app/utils/logger.dart';
import 'package:mobile_crm/app/utils/toast.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/values/values.dart';
import 'package:mobile_crm/data/models/available_model.dart';
import 'package:mobile_crm/data/models/outlet_model.dart';
import 'package:mobile_crm/data/models/product/link_menu_model.dart';
import 'package:mobile_crm/data/models/product_group_model.dart';
import 'package:mobile_crm/data/models/product_model.dart';
import 'package:mobile_crm/data/models/server_response.dart';
import 'package:mobile_crm/data/models/transaction_new_model.dart';
import 'package:mobile_crm/data/models/unit_conversion_model.dart';
import 'package:mobile_crm/data/models/variant_model.dart';
import 'package:mobile_crm/data/providers/db/database.dart';
import 'package:mobile_crm/data/repository/cart_repository.dart';
import 'package:mobile_crm/data/repository/deal_repository.dart';
import 'package:mobile_crm/data/repository/outlet_repository.dart';
import 'package:mobile_crm/data/repository/transaction_repository.dart';
import 'package:mobile_crm/data/repository/wishlist_repository.dart';
import 'package:mobile_crm/data/services/analytics_service.dart';
import 'package:mobile_crm/data/services/local_storage.dart';
import 'package:mobile_crm/domain/repository/cart_repository.dart';
import 'package:mobile_crm/domain/repository/deal_repository.dart';
import 'package:mobile_crm/domain/repository/outlet_repository.dart';
import 'package:mobile_crm/domain/repository/product_repository.dart';
import 'package:mobile_crm/domain/repository/transaction_repository.dart';
import 'package:mobile_crm/domain/repository/wishlist_repository.dart';
import 'package:mobile_crm/routes/app_pages.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

import '../../../../data/models/cart_model.dart';
import '../../../../data/repository/product_repository.dart';

class OutletController extends GetxController
    with GetSingleTickerProviderStateMixin {
  final OutletRepository _repoOutlet = Get.find<OutletRepositoryIml>();
  final ProductRepository repoProduct = Get.find<ProductRepositoryIml>();
  final TransactionRepository repoTrans = Get.find<TransactionRepositoryIml>();
  final CartRepository _repoCart = Get.find<CartRepositoryIml>();
  final DealRepository _repoDeal = Get.find<DealRepositoryIml>();
  final WishlistRepository repoWishlist = Get.find<WishlistRepositoryIml>();
  late BuildContext context;

  OutletController(this.context);

  // Global Key
  var outletNameKey = GlobalKey();

  // ========= //
  final store = LocalStorageService();
  ItemScrollController listProductItemController = ItemScrollController();
  final ScrollOffsetListener scrollOffsetListener =
      ScrollOffsetListener.create();
  ScrollController scrollControllerNested = ScrollController();

  StreamSubscription<double>? streamScrollController;

  var noteController = TextEditingController();
  var scrollController = ScrollController();

  var listCategoryForFAB = <ProductGroupModel>[].obs;

  var listProduct = <Product>[].obs;
  List<LinkMenuModel> listLinkMenu = [];
  List<UnitConversionModel> listUnitConversion = [];
  var newOrder = TransactionNewModel().obs;
  var outletLocalDetail = Outlet(outlet_id: 0).obs;

  var isLogin = false.obs;
  var isOutletNameVisible = true.obs;
  var isLoadingAddRemoveWishlist = false.obs;

  var productRocomendations = <ProductRecomendationData>[].obs;
  var loadingRec = false.obs;

  var countCart = 0.obs;
  String outletId = '0';

  @override
  void onInit() async {
    repoProduct.getProduct();
    isLogin.value = store.token != null;
    outletId = Get.parameters['id'] ?? '0';
    getAllLinkMenu();
    getAllUnitConversion();
    getProductRecomendations();
    await initOrder();
    if (store.outlet != null &&
        store.outlet?.outlet_id.toString() == outletId) {
      outletLocalDetail.value = store.outlet ?? OutletModel();
      newOrder.value.setOutlet(outletLocalDetail.value);
    } else {
      getOutlet();
    }
    getCountCart();
    getArgFromHome();
    Sentry.addBreadcrumb(Breadcrumb(
        type: 'debug',
        category: 'user.activity.outlet_detail.init',
        data: {"outlet_id": outletId},
        level: SentryLevel.debug));

    scrollController.addListener(() {
      var offsetScroll = scrollController.offset;
      var rectOutlet = outletNameKey.getRect;
      if (rectOutlet.bottom > offsetScroll) {
        isOutletNameVisible.value = true;
      } else {
        isOutletNameVisible.value = false;
      }
    });

    super.onInit();
  }

  Future<void> getProductRecomendations() async {
    loadingRec.value = true;
    update();
    productRocomendations.value =
        await repoProduct.getProductRecomendation(int.tryParse(outletId) ?? 0);
    loadingRec.value = false;
    update();
  }

  Future<void> getAllLinkMenu() async {
    listLinkMenu =
        await repoProduct.getLinkMenuBy(outletId: int.tryParse(outletId));
  }

  Future<void> getAllUnitConversion() async {
    listUnitConversion = await repoProduct.getUnitConversion();
  }

  List<LinkMenuModel> getLinkMenuBy(int productId, int? productDetailId) {
    var i = listLinkMenu
        .where((element) =>
            element.productFkId == productId &&
            element.productDetailFkId == productDetailId)
        .toList();
    for (var element in i) {
      element.linkMenuDetail?.forEach((lmd) {
        var p = listProduct.value.firstWhereOrNull(
            (element) => element.product_detail_id == lmd.productDetailFkId);
        lmd.tax = p?.tax
            ?.where((element) => (element.statusTax == 'permanent'))
            .toList();
      });
    }
    infoLogger("get link menu by",
        "Product id $productId, Product detail id $productDetailId, List result ${i.length}, List menu master ${listLinkMenu.length}");
    return i;
  }

  Future<void> initOrder() async {
    var check = await _repoCart.getTransactionOrderByOutletId(
        int.tryParse(outletId) ?? 0,
        isOnOrder: false);
    if (check == null) {
      var user = store.user;
      newOrder.value.outletId = int.tryParse(outletId);
      newOrder.value.customerName = user?.name;
      newOrder.value.receiptReceiver = user?.phone;
      if (Get.arguments.runtimeType.toString() == "_Map<String, String?>") {
        newOrder.value.diningTable = Get.arguments['tableNumber'];
      }
    } else {
      newOrder.value = check;
      await newOrder.value.checkProductList(repo: repoProduct);
      await _repoCart.updateTransactionOrder(newOrder.value);
      newOrder.refresh();
    }
  }

  Future<Product?> addRecommendationToCart(
      ProductRecomendationData productRec) async {
    List<Product> products =
        await _repoOutlet.getOutletProductByOutletId(outletId);
    Product? filteredProduct = products
        .where((it) => it.productId == productRec.productId)
        .firstOrNull;

    return filteredProduct;
  }

  Future<List<Product>> getAllProductFromRecommended() async {
    List<Product> productRecommended = [];

    List<Product> products =
        await _repoOutlet.getOutletProductByOutletId(outletId);

    for (var product in products) {
      for (var recommended in productRocomendations.value) {
        if (product.productId == recommended.productId) {
          productRecommended.add(product);
        }
      }
    }
    return productRecommended;
  }

  @override
  void onClose() {
    scrollController.removeListener(() {});
    scrollController.dispose();
    super.onClose();
  }

  getArgFromHome() {
    var args = Get.arguments;
    if (args.runtimeType == Product) {
      Product productArgs = args;
      Future.delayed(const Duration(seconds: 1), () {
        for (var element in listCategoryForFAB) {
          if (element.categoryName == productArgs.subcategory) {
            for (var el in element.products ?? []) {
              if (el.productId == productArgs.productId) {
                OutletProductDetailModalBottomSheet(
                        product: el, cart: isProductOnCart(el))
                    .toModalBottomSheet
                    .of(context);
                break;
              }
            }
            break;
          }
        }
      });
    }
  }

  dynamic getArgFromLink() {
    var args = Get.arguments;
    if (args.runtimeType.toString() == "_Map<String, String?>") {
      return args;
    }
  }

  /// Get product local if exist return if not get remote
  Future<List<Product>> checkProductIsExist() async {
    var result = await repoProduct.getProductLocal();
    if (result.isEmpty) {
      return await repoProduct.getProduct();
    }
    return result;
  }

  Future<List<ProductGroupModel>> getProductByCategory() async {
    var res = await checkProductIsExist().then((value) async =>
        await _repoOutlet.getOutletProductByOutletId(outletId).then((value) {
          var result = ProductHelper.filterBySubCategory(
              products: value, outletId: int.tryParse(outletId));
          listCategoryForFAB.value = result;
          listProduct.value = value;
          return result;
        }));
    scrollPageController();
    return res;
  }

  Future<Outlet?> getOutletById() async {
    var outlet = await _repoOutlet.getOutletById(int.tryParse(outletId) ?? 0);
    outletLocalDetail.value = outlet ?? OutletModel();
    return outlet;
  }

  Future<List<DealData>> getAllPromos() async =>
      await _repoDeal.getOutletPromotion(outletId: outletId);

  Future<int> getCountCart() async {
    var resp = await _repoCart
        .getAllTransactionOrder()
        .then((value) => value.where((element) => element.isOnOrder == false));
    int countTot = resp.fold(
        0,
        (previousValue, element) =>
            previousValue + (element.orderList?.length ?? 0));
    countCart.value = countTot;
    return countCart.value;
  }

  Future<DealData?> getDetailPromotion(String id) async => _repoDeal
      .getOutletDetailPromotion(promotionId: id)
      .then((value) => value.data);

  onStartScroll(ScrollMetrics metrics) {
    if (metrics.runtimeType.toString() == 'FixedScrollMetrics') {}
  }

  onUpdateScroll(ScrollMetrics metrics) {
    if (metrics.runtimeType.toString() == 'FixedScrollMetrics') {}
  }

  onEndScroll(ScrollMetrics metrics) {
    if (metrics.runtimeType.toString() == 'FixedScrollMetrics') {}
  }

  Future<void> addToCart(int id,
      {required Product product,
      List<LinkMenuModel>? links,
      int qty = 1,
      String? note}) async {
    var newCart = Cart();
    AnalyticsService.observer.analytics
        .logEvent(name: "add_to_cart", parameters: {
      "product_name": product.name,
      "product_detail_id": id,
    });
    if (product.variant != null && (product.variant?.isNotEmpty == true)) {
      var after = filterProductVariant(product.variant);
      for (var element in after) {
        var avai = isProductVariantAvailable(element);
        if (avai?.productDetailId == id) {
          newCart = Cart(
              outlet_fkid: int.tryParse(outletId) ?? 0,
              product_detail_fkid: id,
              product_detail_id: id,
              product_fkid: product.productId,
              qty: qty,
              note: note,
              linkMenu: links,
              outlet: OutletModel(
                  outlet_id: outletLocalDetail.value.outlet_id,
                  business_hour: outletLocalDetail.value.business_hour,
                  receipt_logo: outletLocalDetail.value.receipt_logo,
                  province: outletLocalDetail.value.province,
                  postal_code: outletLocalDetail.value.postal_code,
                  phone: outletLocalDetail.value.phone,
                  outlet_logo: outletLocalDetail.value.outlet_logo,
                  longitude: outletLocalDetail.value.longitude,
                  order_type: outletLocalDetail.value.order_type,
                  enable_order: outletLocalDetail.value.enable_order,
                  latitude: outletLocalDetail.value.latitude,
                  distance_value: outletLocalDetail.value.distance_value,
                  distance: outletLocalDetail.value.distance,
                  city: outletLocalDetail.value.city,
                  address: outletLocalDetail.value.address,
                  name: outletLocalDetail.value.name,
                  promotion_id: outletLocalDetail.value.promotion_id),
              product: ProductModel(
                  description: product.description,
                  name: "${product.name}(${element.name})",
                  minQtyOrder: product.min_qty_order,
                  photo: product.photo,
                  priceSell: avai?.priceSell,
                  product_detail_id: avai?.productDetailId,
                  product_fkid: product.product_fkid,
                  availability: product.availability,
                  available: product.available,
                  hourStart: product.hourStart,
                  hourEnd: product.hourEnd,
                  stockStatus: avai?.enableOrder?.status == 'enable'
                      ? 'available'
                      : 'unavailable',
                  tax: await repoProduct
                      .getTaxByProductDetailId(avai?.productDetailId),
                  unit: product.unit));
          break;
        }
      }
    } else {
      var avai = isProductAvailableOnThisOutlet(product);
      newCart = Cart(
          outlet_fkid: int.tryParse(outletId) ?? 0,
          product_detail_fkid: id,
          product_detail_id: id,
          product_fkid: product.productId,
          qty: qty,
          note: note,
          linkMenu: links,
          outlet: OutletModel(
              outlet_id: outletLocalDetail.value.outlet_id,
              business_hour: outletLocalDetail.value.business_hour,
              receipt_logo: outletLocalDetail.value.receipt_logo,
              province: outletLocalDetail.value.province,
              postal_code: outletLocalDetail.value.postal_code,
              phone: outletLocalDetail.value.phone,
              outlet_logo: outletLocalDetail.value.outlet_logo,
              longitude: outletLocalDetail.value.longitude,
              order_type: outletLocalDetail.value.order_type,
              enable_order: outletLocalDetail.value.enable_order,
              latitude: outletLocalDetail.value.latitude,
              distance_value: outletLocalDetail.value.distance_value,
              distance: outletLocalDetail.value.distance,
              city: outletLocalDetail.value.city,
              address: outletLocalDetail.value.address,
              name: outletLocalDetail.value.name,
              promotion_id: outletLocalDetail.value.promotion_id),
          product: ProductModel(
              description: product.description,
              name: product.name,
              minQtyOrder: product.min_qty_order,
              photo: product.photo,
              price: product.price,
              priceSell: avai?.priceSell,
              product_detail_id: product.product_detail_id,
              product_fkid: product.product_fkid,
              availability: product.availability,
              hourStart: product.hourStart,
              hourEnd: product.hourEnd,
              available: product.available,
              stockStatus: avai?.enableOrder?.status == 'enable'
                  ? 'available'
                  : 'unavailable',
              tax: await repoProduct
                  .getTaxByProductDetailId(avai?.productDetailId),
              unit: product.unit));
    }
    newOrder.value.addToOrder(newCart);
    newOrder.refresh();
    await getAllLinkMenu();
    await getAllUnitConversion();
    await _repoCart.updateTransactionOrder(newOrder.value);
    getCountCart();
  }

  void updateCart(Cart? cart) async {
    newOrder.value.updateOrder(cart);
    newOrder.refresh();
    await _repoCart.updateTransactionOrder(newOrder.value);
    getCountCart();
  }

  void updateCartNote(Cart cart) async {
    updateCart(cart);
  }

  void deleteCart(Cart? cart) async {
    newOrder.value.removeOrder(cart);
    newOrder.refresh();
    await _repoCart.updateTransactionOrder(newOrder.value);
    getCountCart();
  }

  Future<Outlet?> getOutlet() async {
    Outlet? outlet =
        await _repoOutlet.getOutletDetailById(int.tryParse(outletId) ?? 0);
    try {
      outletLocalDetail.value = outlet ?? (store.outlet ?? Outlet());
      newOrder.value.setOutlet(outletLocalDetail.value);
      return outletLocalDetail.value;
    } catch (e, s) {
      errorLogger(pos: "OC GET OUTLET", error: e, stackTrace: s);
    }
    return outlet;
  }

  CartModel? isProductOnCart(Product product) =>
      newOrder.value.orderList?.firstWhereOrNull(
          (element) => element.product_fkid == product.productId);

  CartModel? isVariantOnCart(VariantModel variant) =>
      newOrder.value.orderList?.firstWhereOrNull((element) {
        int? proDetailId = variant.available
            ?.firstWhereOrNull((el) => el.outletId == element.outlet_fkid)
            ?.productDetailId;
        return element.product_detail_fkid == proDetailId;
      });

  List<VariantModel> filterProductVariant(List<VariantModel>? variants) {
    if (variants != null) {
      List<VariantModel> temp = [];
      for (var el in variants) {
        List<AvailableModel> availableVariant = el.available ?? [];
        for (var available in availableVariant) {
          if (available.outletId.toString() == outletId) {
            temp.add(el);
            break;
          }
        }
      }
      return temp;
    }
    return [];
  }

  AvailableModel? isProductVariantAvailable(VariantModel variantModel) =>
      variantModel.available?.firstWhereOrNull(
          (element) => element.outletId.toString() == outletId);

  (int? lowestPrice, int? highestPrice) getLowHighPrice(Product product) {
    if (product.variant?.isNotEmpty == true && product.variant != null) {
      List<int> price = [];
      List<VariantModel> variants = product.variant ?? [];
      for (var element in variants) {
        var cek = element.available?.firstWhereOrNull(
            (element) => element.outletId.toString() == outletId);
        if (cek != null) {
          price.add(cek.priceSell ?? 0);
        }
      }
      price.sort((a, b) => a.compareTo(b));
      return ((price.firstOrNull ?? 0), (price.lastOrNull ?? 0));
    }
    return (null, null);
  }

  AvailableModel? isProductAvailableOnThisOutlet(Product product) {
    if (ProductHelper.isProductAvailableOnHours(product)) {
      if (product.variant?.isNotEmpty == true && product.variant != null) {
        AvailableModel? found;
        List<VariantModel> variants = product.variant ?? [];
        for (var element in variants) {
          var cek = element.available?.firstWhereOrNull(
              (element) => element.outletId.toString() == outletId);
          if (cek?.enableOrder?.status == "enable") {
            found = cek;
            break;
          }
        }
        return found;
      }
      return product.available?.firstWhereOrNull(
          (element) => element.outletId.toString() == outletId);
    } else {
      return null;
    }
  }

  // Promotion
  List<ProductModel> filterDuplicateProduct(List<ProductModel> products) {
    List<ProductModel> listP = [];
    List<ProductModel> prod = [];
    prod.addAll(products);
    prod.sort(
      (a, b) => (a.productId ?? 0).compareTo(b.productId ?? 0),
    );
    for (var index = 0; index < (prod.length); index++) {
      if (index == 0 || (prod[index - 1].productId != prod[index].productId)) {
        listP.add(prod[index]);
      }
    }
    listP.sort((a, b) => (a.name ?? '').compareTo(b.name ?? ''));
    return listP;
  }

  String getPromoDay(DealData voucher) => DealHelper.getPromoDaysV2(voucher);

  Future<ServerResponse<WishlistData>> addWishlist(Product product) async {
    if (!isLogin.value) {
      Toast.show("Login is required to add a product to the wishlist",
          type: ToastType.warning);
      Future.delayed(const Duration(milliseconds: 500), () {
        Get.toNamed(Routes.LOGIN);
      });
      return ServerResponse(status: false);
    }
    AnalyticsService.observer.analytics.logEvent(
        name: "add_wishlist", parameters: {"product_name": product.name});
    isLoadingAddRemoveWishlist.value = true;
    var result = await repoWishlist
        .addWishlist(product: product)
        .whenComplete(() => isLoadingAddRemoveWishlist.value = false);

    if (result.status) {
      await repoWishlist.getAllWishlist();
      // await changeProductWishlistValue(true, product);
      Toast.show(
          Strings.valueAddedToWishlist
              .trParams({'value': ('"${product.name}"')}),
          type: ToastType.success);
    } else {
      Toast.show(
          Strings.valueAlreadyInYourWishlist
              .trParams({'value': ('"${product.name}"')}),
          type: ToastType.error);
    }
    return result;
  }

  Future<ServerResponse> removeWishlist(Product product) async {
    if (!isLogin.value) {
      Toast.show("Login is required to remove a product to the wishlist",
          type: ToastType.warning);
      Future.delayed(const Duration(milliseconds: 500), () {
        Get.toNamed(Routes.LOGIN);
      });
      return Future.error("Login Required");
    }
    isLoadingAddRemoveWishlist.value = true;
    AnalyticsService.observer.analytics.logEvent(
        name: "remove_wishlist", parameters: {"product_name": product.name});
    WishlistData? isWishlistExist = await repoWishlist
        .getSingleWishlistByProductFkId(product.productId ?? 0);

    if (isWishlistExist != null) {
      var result = await repoWishlist
          .deleteWishlist("${isWishlistExist.crmProductWishlistId}")
          .whenComplete(() => isLoadingAddRemoveWishlist.value = false);
      if (result.status) {
        // await changeProductWishlistValue(false, product);
      }
      Toast.show(
          result.status
              ? Strings.valueRemovedFromWishlist
                  .trParams({'value': product.name ?? ''})
              : result.message,
          type: result.status ? ToastType.success : ToastType.error);
      Get.back();
      return result;
    }
    isLoadingAddRemoveWishlist.value = false;
    return ServerResponse();
  }

  @override
  void dispose() {
    scrollControllerNested.dispose();
    scrollController.dispose();
    super.dispose();
  }

  void scrollPageController() {
    double totalScroll = 0.0;
    Future.delayed(
      const Duration(seconds: 1),
      () {
        streamScrollController ??= scrollOffsetListener.changes.listen((event) {
          totalScroll += event;
          scrollControllerNested.jumpTo(totalScroll.clamp(
              0, scrollControllerNested.position.maxScrollExtent));
          var rectOutlet = outletNameKey.getRect;
          if (scrollControllerNested.position.pixels.clamp(0, 300) >
              rectOutlet.bottom.clamp(0, 100)) {
            isOutletNameVisible.value = true;
          } else {
            isOutletNameVisible.value = false;
          }
        });
      },
    );
  }
}
