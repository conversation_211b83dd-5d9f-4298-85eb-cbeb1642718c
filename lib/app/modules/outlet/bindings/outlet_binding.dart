import 'package:get/get.dart';
import 'package:mobile_crm/data/repository/cart_repository.dart';
import 'package:mobile_crm/data/repository/deal_repository.dart';
import 'package:mobile_crm/data/repository/outlet_repository.dart';
import 'package:mobile_crm/data/repository/product_repository.dart';
import 'package:mobile_crm/data/repository/transaction_repository.dart';
import 'package:mobile_crm/data/repository/wishlist_repository.dart';

class OutletBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => WishlistRepositoryIml());
    Get.lazyPut(() => OutletRepositoryIml());
    Get.lazyPut(() => ProductRepositoryIml());
    Get.lazyPut(() => TransactionRepositoryIml());
    Get.lazyPut(() => CartRepositoryIml());
    Get.lazyPut(() => DealRepositoryIml());
  }
}
