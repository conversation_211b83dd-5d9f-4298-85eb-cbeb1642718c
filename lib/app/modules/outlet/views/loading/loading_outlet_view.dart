import 'package:flutter/material.dart';
import 'package:mobile_crm/app/utils/utils.dart';
import 'package:mobile_crm/app/widget/shimmer_loading_widget.dart';

class OutletListLoading extends StatelessWidget {
  const OutletListLoading({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8.0),
      child: MediaQuery.removePadding(
        removeTop: true,
        context: context,
        child: ListView.separated(
          padding: const EdgeInsets.symmetric(horizontal: 8),
          shrinkWrap: true,
          itemCount: 5,
          itemBuilder: (context, index) => loading(),
          separatorBuilder: (context, index) => const SizedBox(height: 1),
        ),
      ),
    );
  }

  Widget loading() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          const ShimmerWidget(
            width: 120,
            height: 80,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ShimmerWidget(width: randomRange(50, 100).toDouble()),
                const SizedBox(height: 4),
                ShimmerWidget(width: randomRange(80, 200).toDouble()),
                const SizedBox(height: 15),
                ShimmerWidget(width: randomRange(40, 80).toDouble()),
              ],
            ),
          )
        ],
      ),
    );
  }
}
