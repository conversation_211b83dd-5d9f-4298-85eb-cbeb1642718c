// ignore_for_file: must_be_immutable, unrelated_type_equality_checks

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/outlet/views/widget/outlet_bottom_navbar.dart';
import 'package:mobile_crm/app/modules/outlet/views/widget/recomendation/recomendation_widget.dart';
import 'package:mobile_crm/app/modules/screen_wrapper/views/app_page_wrapper.dart';
import 'package:mobile_crm/app/widget/app_bar_widget/sliver_app_bar_widget.dart';
import 'package:mobile_crm/core/extensions/double_extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/routes/app_pages.dart';

import '../../screen_wrapper/controller/wrapper_controller.dart';
import '../controllers/outlet_controller.dart';
import 'widget/outlet_fab/outlet_floating_button_menu.dart';
import 'widget/outlet_header/outlet_name.dart';
import 'widget/outlet_product/outlet_list_product.dart';
import 'widget/outlet_promotion/outlet_promotion.dart';
import 'widget/outlet_search_delegate.dart';

class OutletScreen extends GetView<OutletController> {
  const OutletScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    Get.put(OutletController(context));
    final wrapperController = Get.find<WrapperController>();
    return PageWrapper(
      resizeToAvoidBottomInset: false,
      bottomNavigationBar: OutletBottomNavBar(
        controller: controller,
      ),
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        backgroundColor: Colors.transparent,
        floatingActionButton: OutletFAB(
          controller: controller,
        ),
        body: NotificationListener(
          onNotification: (notification) {
            if (notification is ScrollStartNotification) {
              controller.onStartScroll(notification.metrics);
            } else if (notification is ScrollUpdateNotification) {
              controller.onUpdateScroll(notification.metrics);
            } else if (notification is ScrollEndNotification) {
              controller.onEndScroll(notification.metrics);
            }
            return true;
          },
          child: NestedScrollView(
            controller: controller.scrollControllerNested,
            headerSliverBuilder: (context, innerBoxIsScrolled) {
              return [
                SliverAppBarWidget(
                  title: '',
                  customTitle: Obx(() {
                    return AnimatedContainer(
                      duration: const Duration(milliseconds: 300),
                      curve: Curves.fastOutSlowIn,
                      height: controller.isOutletNameVisible.value
                          ? 0
                          : AppFont.componentLarge.fontSize,
                      child: AnimatedOpacity(
                        duration: const Duration(milliseconds: 200),
                        opacity: controller.isOutletNameVisible.value ? 0 : 1,
                        child: Text(
                          controller.outletLocalDetail.value.name ?? '',
                          style: AppFont.componentLarge
                              .copyWith(color: AppColor.white),
                        ),
                      ),
                    );
                  }),
                  actions: [
                    IconButton(
                        onPressed: () {
                          showSearch(
                              context: context,
                              delegate: OutletSearchDelegate());
                        },
                        icon: const Icon(Icons.search)),
                    IconButton(
                      onPressed: () {
                        Get.toNamed(Routes.CART)?.then((value) {
                          if (value is bool) {
                            return value ? controller.initOrder() : null;
                          }
                        });
                      },
                      icon: Obx(
                        () => Badge(
                          label:
                              Obx(() => Text("${controller.countCart.value}")),
                          isLabelVisible: controller.countCart.value != 0,
                          textStyle: AppFont.paragraphSmall,
                          alignment: Alignment.topRight,
                          child: const Icon(Icons.shopping_cart_rounded),
                        ),
                      ),
                    ),
                  ],
                  elevation: 10,
                  pinned: true,
                  floating: true,
                ),
                SliverToBoxAdapter(
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: AppDimen.h16),
                    child: OutletName(controller: controller),
                  ),
                ),
                15.0.sHeight,
                SliverToBoxAdapter(
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: AppDimen.h16),
                    child: OutletPromotionWidget(
                        controller: controller,
                        wrapperController: wrapperController),
                  ),
                ),
                15.0.sHeight,
                SliverToBoxAdapter(
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: AppDimen.h16),
                    child: RecomendationWidget(),
                  ),
                ),
              ];
            },
            body: Padding(
              padding: EdgeInsets.symmetric(horizontal: AppDimen.h16),
              child: OutletListProduct(
                controller: controller,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
