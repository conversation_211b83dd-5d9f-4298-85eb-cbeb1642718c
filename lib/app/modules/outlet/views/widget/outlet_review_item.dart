import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/widget/cached_image_widget.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';

import '../../../../../core/values/app_constants.dart';
import '../../../../../data/models/transaction/feedback/rating_review_model.dart';
import '../../../../utils/app_alert.dart';
import '../../../../widget/app_dotted_separator.dart';
import '../../../screen_wrapper/controller/wrapper_controller.dart';

class OutletReviewItem extends GetView<WrapperController> {
  const OutletReviewItem({super.key, required this.review});
  final RatingReviewModel review;
  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            CircleAvatar(
                backgroundColor: controller.getPrimaryColor(),
                child: Text(
                  (review.customer ?? '').takeFirstWord,
                  style: AppFont.componentSmall.copyWith(
                      color: controller
                          .getPrimaryColor()
                          .changeColorBasedOnBackgroundColor()
                          .$1),
                )),
            AppDimen.h10.width,
            Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  review.customer ?? '',
                  style: AppFont.componentSmallBold,
                ),
                review.dateMillis == null
                    ? const SizedBox()
                    : Text(
                        (review.dateMillis).toDayAgoV2,
                        style: AppFont.componentSmall
                            .copyWith(color: AppColor.black90, fontSize: 11.sp),
                      ),
              ],
            ),
            const Spacer(),
            Container(
              padding: EdgeInsets.symmetric(
                  vertical: AppDimen.h2, horizontal: AppDimen.h4),
              alignment: Alignment.center,
              height: AppDimen.h20,
              decoration: BoxDecoration(
                  color: AppColor.white,
                  border: Border.all(color: AppColor.black10),
                  borderRadius: BorderRadius.all(Constants.shapeRadius)),
              child: Row(
                children: [
                  Icon(
                    Icons.star,
                    color: Colors.deepOrange,
                    size: AppDimen.h14,
                  ),
                  AppDimen.h2.width,
                  Text(
                    "${review.rating ?? 0.0}",
                    style: AppFont.componentSmallBold
                        .copyWith(color: AppColor.black90),
                  ),
                ],
              ),
            )
          ],
        ),
        AppDimen.h4.height,
        Container(
            padding: EdgeInsets.symmetric(
                horizontal: AppDimen.h12, vertical: AppDimen.h8),
            width: MediaQuery.of(context).size.width,
            decoration: BoxDecoration(
                border: Border.all(color: AppColor.black5),
                borderRadius: BorderRadius.all(Radius.circular(AppDimen.h6))),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Visibility(
                  visible: review.comment != null,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Flexible(
                          child: Text(
                        review.comment ?? '',
                        style: AppFont.paragraphSmall,
                        overflow: TextOverflow.ellipsis,
                        maxLines: 5,
                      )),
                      AppDimen.h8.height,
                    ],
                  ),
                ),
                Visibility(
                  visible: (review.attachment != null &&
                      review.attachment?.isNotEmpty == true),
                  child: SizedBox(
                    width: MediaQuery.of(context).size.width,
                    height: AppDimen.h40,
                    child: ListView.builder(
                      shrinkWrap: true,
                      scrollDirection: Axis.horizontal,
                      itemCount: review.attachment?.length ?? 0,
                      itemBuilder: (context, index) {
                        var item = review.attachment![index];
                        return InkWell(
                          onTap: () {
                            AppAlert.showPreviewImageNetwork(
                                url: item, context: context);
                          },
                          child: Container(
                            margin: EdgeInsets.only(
                                right: AppDimen.h4, bottom: AppDimen.h4),
                            child: CachedImageWidget(
                              imageUrl: item,
                              width: AppDimen.h34,
                              height: AppDimen.h34,
                              fit: BoxFit.cover,
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ),
                Visibility(
                    visible: (review.attachment != null &&
                        review.attachment?.isNotEmpty == true),
                    child: const Divider(
                      color: AppColor.black5,
                    )),
                Visibility(
                  visible: (review.orderItem != null &&
                      review.orderItem?.isNotEmpty == true),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Icon(
                        Icons.receipt_long_rounded,
                        color: AppColor.black90,
                        size: AppDimen.h14,
                      ),
                      AppDimen.h4.width,
                      Flexible(
                        child: Text(
                          "${review.orderItem?.fold('', (previousValue, element) => previousValue.isEmpty ? element : previousValue + (', ') + element)}",
                          overflow: TextOverflow.ellipsis,
                          style: AppFont.componentSmall.copyWith(
                              color: AppColor.black90, fontSize: 11.sp),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            )),
        AppDimen.h4.height,
        const DottedDivider(
          width: 3,
          height: 2,
          color: AppColor.black10,
        ),
        AppDimen.h6.height,
      ],
    );
  }
}
