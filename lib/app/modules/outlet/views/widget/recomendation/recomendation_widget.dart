import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/outlet/controllers/outlet_controller.dart';
import 'package:mobile_crm/app/modules/outlet/views/widget/outlet_modal_bottom_sheet/outlet_product_recommendation.dart';
import 'package:mobile_crm/app/modules/outlet/views/widget/recomendation/product_card.dart';
import 'package:mobile_crm/app/widget/shimmer_loading_widget.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/theme/app_color.dart';
import 'package:mobile_crm/core/theme/app_font.dart';
import 'package:mobile_crm/data/providers/db/database.dart';

class RecomendationWidget extends StatelessWidget {
  const RecomendationWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GetBuilder<OutletController>(builder: (controller) {
      return controller.productRocomendations.isNotEmpty
          ? Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      "Recomended for You",
                      style: AppFont.paragraphMediumBold,
                    ),
                    InkWell(
                      child: Row(
                        children: [
                          Text(
                            "See All",
                            style: AppFont.componentSmall
                                .copyWith(color: AppColor.couponDiscount),
                          ),
                          const Icon(
                            Icons.chevron_right,
                            color: AppColor.couponDiscount,
                          )
                        ],
                      ),
                      onTap: () async {
                        List<Product> productRecommended =
                            await controller.getAllProductFromRecommended();
                        if (productRecommended.isNotEmpty) {
                          OutletProductRecommendationModalBottomSheet(
                            products: productRecommended,
                          ).toModalBottomSheet.of(context);
                        }
                      },
                    )
                  ],
                ),
                SizedBox(
                  height: 193,
                  child: Obx(
                    () {
                      if (!controller.loadingRec.value) {
                        return ListView.builder(
                          scrollDirection:
                              Axis.horizontal, // Enables horizontal scrolling
                          itemCount: controller.productRocomendations.length > 5
                              ? 5
                              : controller.productRocomendations.length,
                          itemBuilder: (context, index) {
                            ProductRecomendationData product =
                                controller.productRocomendations[index];
                            return ProductCard(
                              product: product,
                            );
                          },
                        );
                      } else {
                        return ListView.builder(
                          scrollDirection:
                              Axis.horizontal, // Enables horizontal scrolling
                          itemCount: 3,
                          itemBuilder: (context, index) {
                            return const Card(
                              elevation: 0.0,
                              child: ShimmerWidget(
                                height: 193,
                                width: 150,
                              ),
                            );
                          },
                        );
                      }
                    },
                  ),
                ),
              ],
            )
          : const SizedBox();
    });
  }
}
