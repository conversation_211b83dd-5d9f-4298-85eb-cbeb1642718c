import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/outlet/controllers/outlet_controller.dart';
import 'package:mobile_crm/app/modules/outlet/views/widget/outlet_modal_bottom_sheet/outlet_cart_link_menu.dart';
import 'package:mobile_crm/app/modules/outlet/views/widget/outlet_modal_bottom_sheet/outlet_link_menu.dart';
import 'package:mobile_crm/app/modules/outlet/views/widget/outlet_modal_bottom_sheet/outlet_product_detail_modal_bottom_sheet.dart';
import 'package:mobile_crm/app/utils/logger.dart';
import 'package:mobile_crm/app/utils/toast.dart';
import 'package:mobile_crm/app/widget/cached_image_widget.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/theme/app_color.dart';
import 'package:mobile_crm/core/theme/app_dimen.dart';
import 'package:mobile_crm/core/theme/app_font.dart';
import 'package:mobile_crm/core/values/app_strings.dart';
import 'package:mobile_crm/data/models/available_model.dart';
import 'package:mobile_crm/data/models/cart_model.dart';
import 'package:mobile_crm/data/providers/db/database.dart';

class ProductCard extends StatelessWidget {
  final ProductRecomendationData product;
  const ProductCard({super.key, required this.product});

  @override
  Widget build(BuildContext context) {
    var controller = Get.find<OutletController>();

    return SizedBox(
      width: 150,
      child: InkWell(
        onTap: () async {
          Product? productF = await controller.addRecommendationToCart(product);
          if (productF != null) {
            AvailableModel? available =
                controller.isProductAvailableOnThisOutlet(productF);
            bool isProductAvailable = available?.stockStatus != "available";
            CartModel? cart = controller.isProductOnCart(productF);
            bool isProductOnCart = cart == null;
            OutletProductDetailModalBottomSheet(product: productF, cart: cart)
                .toModalBottomSheet
                .of(context);
            // if (isProductOnCart) {
            //   if (!isProductAvailable) {
            //     if (productF.variant?.isNotEmpty == true &&
            //         productF.variant != null) {
            //       OutletProductDetailModalBottomSheet(
            //               product: productF, cart: cart)
            //           .toModalBottomSheet
            //           .of(context);
            //       return;
            //     } else {
            //       var search = controller.getLinkMenuBy(
            //           productF.productId ?? 0, available?.productDetailId);
            //       if (search.isNotEmpty) {
            //         search.sort(
            //             (a, b) => (a.orderNo ?? 0).compareTo(b.orderNo ?? 0));
            //         OutletLinkMenu(
            //           listLinkMenuMaster: search,
            //           noteController: TextEditingController(),
            //           product: productF,
            //         ).toModalBottomSheet.of(context);
            //       } else {
            //         controller.addToCart(available?.productDetailId ?? 0,
            //             product: productF);
            //       }
            //     }
            //   } else {
            //     Toast.show(
            //       Strings.productNotAvailable,
            //       duration: 3,
            //       type: ToastType.dark,
            //     );
            //   }
            // } else {
            //   if ((controller.newOrder.value.orderList
            //               ?.where((it) =>
            //                   it.product_detail_fkid ==
            //                   available?.productDetailId)
            //               .where((item) => item.linkMenu?.isNotEmpty == true)
            //               .length ??
            //           0) >
            //       0) {
            //     if (productF.variant != null) {
            //       OutletProductDetailModalBottomSheet(
            //               product: productF, cart: cart)
            //           .toModalBottomSheet
            //           .of(context);
            //     } else {
            //       OutletCartLinkMenu(
            //         product: productF,
            //         orderList: controller.newOrder.value.orderList
            //                 ?.where((element) =>
            //                     element.product_detail_fkid ==
            //                     available?.productDetailId)
            //                 .where((element) =>
            //                     element.linkMenu?.isNotEmpty == true)
            //                 .toList() ??
            //             [],
            //       ).toModalBottomSheetNoMinHeight.of(context);
            //     }
            //   }
            // }
          }
        },
        child: Card(
          elevation: 0.0,
          color: AppColor.white,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ClipRRect(
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(AppDimen.h8),
                  topRight: Radius.circular(AppDimen.h8),
                ),
                child: CachedImageWidget(
                  fit: BoxFit.cover,
                  width: (MediaQuery.of(context).size.width / 2) - AppDimen.h24,
                  height:
                      (MediaQuery.of(context).size.width / 3.2) - AppDimen.h24,
                  imageUrl: product.photo,
                ),
              ),
              SizedBox(
                height:
                    (MediaQuery.of(context).size.width / 3.9) - AppDimen.h24,
                child: Padding(
                  padding: const EdgeInsets.all(8),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "${product.name}",
                        style: AppFont.paragraphSmallBold,
                      ),
                      Text(
                        "Rp${(product.priceSell).toCurrency}",
                        style: AppFont.paragraphSmallBold.copyWith(
                          color: AppColor.couponDiscount,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
