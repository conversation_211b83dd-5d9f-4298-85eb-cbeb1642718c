import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/helper/product_helper.dart';
import 'package:mobile_crm/app/modules/outlet/controllers/outlet_controller.dart';
import 'package:mobile_crm/app/modules/screen_wrapper/controller/wrapper_controller.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/data/providers/db/database.dart';

import 'outlet_bottom_summary/outlet_summary.dart';
import 'outlet_modal_bottom_sheet/outlet_product_detail_modal_bottom_sheet.dart';
import 'outlet_product/outlet_product_item.dart';

class OutletSearchDelegate extends SearchDelegate {
  final controller = Get.find<OutletController>();
  final wrapperController = Get.find<WrapperController>();
  List<Product> masterProducts = [];
  List<Product> resultProducts = [];

  @override
  List<Widget>? buildActions(BuildContext context) => [
        IconButton(
            onPressed: () {
              if (query.isEmpty) {
                close(context, null);
              }
              resultProducts = masterProducts;
              query = '';
            },
            icon: const Icon(Icons.clear))
      ];

  @override
  Widget? buildLeading(BuildContext context) => IconButton(
      onPressed: () => close(context, null),
      icon: const Icon(Icons.arrow_back));

  @override
  Widget buildResults(BuildContext context) {
    return Scaffold(
      floatingActionButton: outletSummary(context, controller),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
      body: Padding(
          padding: const EdgeInsets.only(left: 8.0, right: 8.0, top: 10.0),
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: resultProducts.length,
            itemBuilder: (context, index) {
              return Obx(() {
                return OutletProductItem(
                  product: resultProducts[index],
                  cart: controller.isProductOnCart(resultProducts[index]),
                  onTap: () => OutletProductDetailModalBottomSheet(
                          product: resultProducts[index],
                          cart:
                              controller.isProductOnCart(resultProducts[index]))
                      .toModalBottomSheet
                      .of(context),
                  // ShowCustomModalBottom.showModalProductOutlet(
                  // context, resultProducts[index], controller),
                );
              });
            },
          )),
    );
  }

  @override
  Widget buildSuggestions(BuildContext context) {
    return Scaffold(
      floatingActionButton: outletSummary(context, controller),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
      resizeToAvoidBottomInset: true,
      body: Padding(
          padding: const EdgeInsets.only(left: 8.0, right: 8.0, top: 10.0),
          child: FutureBuilder(
            future: Future.value(controller.listProduct),
            builder: (context, snapshot) {
              if (snapshot.hasData) {
                masterProducts = snapshot.data
                        ?.where((element) => element.isHeader == false)
                        .toList() ??
                    [];
                masterProducts = ProductHelper.filterProductByStockStatus(
                    products: masterProducts,
                    outletId: int.tryParse(controller.outletId) ?? 0);
                resultProducts = masterProducts.where((element) {
                  final resultName = element.name?.toLowerCase();
                  final resultDesc = element.description?.toLowerCase();
                  final input = query.toLowerCase();
                  final result = (resultName?.contains(input) ?? false) ||
                      (resultDesc?.contains(input) ?? false);
                  return result;
                }).toList();
                return resultProducts.isEmpty
                    ? const Center(
                        child: Text("Product not found"),
                      )
                    : ListView.builder(
                        shrinkWrap: true,
                        itemCount: resultProducts.length,
                        itemBuilder: (BuildContext ctx, int index) {
                          Product product = resultProducts[index];
                          return Obx(() {
                            return OutletProductItem(
                              product: product,
                              cart: controller.isProductOnCart(product),
                              onTap: () => OutletProductDetailModalBottomSheet(
                                      product: product,
                                      cart: controller.isProductOnCart(product))
                                  .toModalBottomSheet
                                  .of(context),
                            );
                          });
                        },
                      );
              } else if (snapshot.hasError) {
                return const Icon(Icons.error_outline);
              } else {
                return const CircularProgressIndicator();
              }
            },
          )),
    );
  }

  @override
  ThemeData appBarTheme(BuildContext context) {
    final controller = Get.find<WrapperController>();
    return ThemeData(
        appBarTheme: AppBarTheme(color: controller.getPrimaryColor()),
        primaryColorDark: AppColor.white,
        primaryColorLight: AppColor.black,
        textTheme: TextTheme(
            titleLarge:
                AppFont.componentSmallBold.copyWith(color: AppColor.white)),
        inputDecorationTheme: InputDecorationTheme(
          hintStyle: AppFont.componentSmallBold
              .copyWith(color: AppColor.white, letterSpacing: 2),
          border: InputBorder.none,
          labelStyle: AppFont.componentSmallBold
              .copyWith(color: AppColor.white, letterSpacing: 2),
        ));
  }
}
