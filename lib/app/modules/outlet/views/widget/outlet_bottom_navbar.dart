import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/outlet/controllers/outlet_controller.dart';
import 'package:mobile_crm/app/utils/app_alert.dart';
import 'package:mobile_crm/app/widget/button_widget/button_widget.dart';
import 'package:mobile_crm/core/extensions/color_extensions.dart';
import 'package:mobile_crm/core/extensions/double_extensions.dart';
import 'package:mobile_crm/core/extensions/int_extensions.dart';
import 'package:mobile_crm/core/extensions/widget_extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/values.dart';
import 'package:mobile_crm/data/providers/db/database.dart';
import 'package:mobile_crm/routes/app_pages.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

import '../../../../widget/button_widget/minus_plus_button_widget.dart';
import '../../../../widget/html_widget.dart';
import '../../../screen_wrapper/controller/wrapper_controller.dart';
import 'outlet_modal_bottom_sheet/outlet_update_link_menu.dart';

class OutletBottomNavBar extends StatelessWidget {
  const OutletBottomNavBar({Key? key, required this.controller})
      : super(key: key);
  final OutletController controller;

  @override
  Widget build(BuildContext context) {
    final wrapperController = Get.find<WrapperController>();
    return Obx(() {
      return AnimatedContainer(
        height: (controller.newOrder.value.sumQTY() > 0)
            ? Constants.containerMenuHeight(context)
            : 0,
        duration: const Duration(milliseconds: 600),
        curve: Curves.fastOutSlowIn,
        padding: EdgeInsets.symmetric(
            horizontal: AppDimen.h8, vertical: AppDimen.h8),
        child: Obx(() {
          return AnimatedOpacity(
            duration: const Duration(milliseconds: 300),
            opacity: (controller.newOrder.value.sumQTY() > 0) ? 1.0 : 0.0,
            child: Row(children: [
              InkWell(
                onTap: () {
                  showModalBottomSheet(
                    context: context,
                    useRootNavigator: true,
                    isScrollControlled: true,
                    constraints: BoxConstraints(
                        maxWidth: Constants.defaultMaxWidth,
                        minHeight: MediaQuery.of(context).size.height * 0.6,
                        maxHeight: MediaQuery.of(context).size.height * 0.8),
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.only(
                            topLeft: Radius.circular(AppDimen.h8),
                            topRight: Radius.circular(AppDimen.h8))),
                    backgroundColor: AppColor.whiteGrey,
                    enableDrag: true,
                    builder: (context) {
                      return Column(
                        children: [
                          Container(
                            decoration: BoxDecoration(
                                color: AppColor.white,
                                boxShadow: const [
                                  BoxShadow(
                                      color: AppColor.black30,
                                      offset: Offset(0, 1),
                                      spreadRadius: 2,
                                      blurRadius: 2)
                                ],
                                borderRadius: BorderRadius.only(
                                    topLeft: Radius.circular(AppDimen.h8),
                                    topRight: Radius.circular(AppDimen.h8))),
                            padding: EdgeInsets.symmetric(
                                horizontal: AppDimen.h8, vertical: AppDimen.h4),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                IconButton(
                                    onPressed: () =>
                                        Navigator.of(context).pop(),
                                    icon: const Icon(
                                      Icons.close,
                                      color: AppColor.black90,
                                    )),
                                Text(
                                  "Cart",
                                  style: AppFont.componentSmallBold,
                                ),
                                TextButton(
                                  child: const Text(
                                    "",
                                  ),
                                  onPressed: () {},
                                ),
                              ],
                            ),
                          ),
                          Expanded(
                            child: Obx(() {
                              return ListView.builder(
                                shrinkWrap: true,
                                itemCount: controller
                                        .newOrder.value.orderList?.length ??
                                    0,
                                itemBuilder: (context, index) {
                                  var item = controller
                                      .newOrder.value.orderList![index];
                                  return InkWell(
                                    splashFactory: NoSplash.splashFactory,
                                    onTap: () => (item.linkMenu?.length ?? 0) >
                                            0
                                        ? OutletUpdateLinkMenu(
                                            cart: item,
                                            product: Product(
                                              name: item.product?.name,
                                              priceSell:
                                                  item.product?.priceSell,
                                              available:
                                                  item.product?.available,
                                              variant: item.product?.variant,
                                              price: item.product?.price,
                                              tax: item.product?.tax,
                                            ),
                                            noteController:
                                                TextEditingController(
                                                    text: item.note),
                                          ).toModalBottomSheet.of(context)
                                        : null,
                                    child: Container(
                                        margin: EdgeInsets.only(
                                            bottom: AppDimen.h4),
                                        padding: EdgeInsets.symmetric(
                                            horizontal: AppDimen.h12,
                                            vertical: AppDimen.h12),
                                        decoration: const BoxDecoration(
                                            color: AppColor.white),
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              "${item.product?.name}",
                                              style: AppFont.componentSmallBold,
                                            ),
                                            Visibility(
                                              visible:
                                                  (item.linkMenu?.length ?? 0) >
                                                      0,
                                              child: ListView.builder(
                                                shrinkWrap: true,
                                                physics:
                                                    const NeverScrollableScrollPhysics(),
                                                itemCount:
                                                    item.linkMenu?.length ?? 0,
                                                itemBuilder: (context, index) {
                                                  var lm =
                                                      item.linkMenu![index];
                                                  return Text(
                                                    "${lm.name}: ${lm.linkMenuDetail?.fold('', (previousValue, element) => previousValue.isEmpty ? (element.name ?? '') : previousValue + (', ') + (element.name ?? ''))}",
                                                    style: AppFont
                                                        .componentSmall
                                                        .copyWith(
                                                            fontSize: 11.sp),
                                                  );
                                                },
                                              ),
                                            ),
                                            HtmlWidget(
                                              data: item.product?.description ??
                                                  '',
                                              fontSize: 12,
                                              maxLines: 2,
                                            ),
                                            AppDimen.h4.height,
                                            Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment
                                                      .spaceBetween,
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.end,
                                              children: [
                                                Text(
                                                  "Rp${(((item.product?.priceSell ?? item.product?.price ?? 0) * (item.qty ?? 0)) + (item.linkMenu?.fold(0, (previousValue, element) => (previousValue ?? 0) + element.sumAdditionalPrice(item.qty ?? 0)) ?? 0)).toInt().toCurrency}",
                                                  style: AppFont.componentSmall,
                                                ),
                                                MinusPlusButtonWidget(
                                                  textEditingController:
                                                      TextEditingController(
                                                          text:
                                                              '${item.qty ?? 0}'),
                                                  middleText:
                                                      "${item.qty ?? 0}",
                                                  onTapMinus: () {
                                                    if (item.qty != null &&
                                                        (item.qty ?? 1) > 1) {
                                                      item.qty =
                                                          (item.qty ?? 1) - 1;
                                                      controller
                                                          .updateCart(item);
                                                    } else {
                                                      AppAlert
                                                          .showConfirmDeleteDialog(
                                                        context,
                                                        "${item.product?.name}",
                                                        () {
                                                          controller
                                                              .deleteCart(item);
                                                          Get.back();
                                                        },
                                                      );
                                                    }
                                                  },
                                                  onChanged: (value) {
                                                    if (int.tryParse(value) !=
                                                        null) {
                                                      int convert =
                                                          int.tryParse(value) ??
                                                              1;
                                                      if (item.qty != null &&
                                                          convert > 0) {
                                                        if ((item.qty ?? 0) !=
                                                            convert) {
                                                          item.qty = convert;
                                                          controller
                                                              .updateCart(item);
                                                        }
                                                      } else {
                                                        AppAlert
                                                            .showConfirmDeleteDialog(
                                                          context,
                                                          "${item.product?.name}",
                                                          () {
                                                            controller
                                                                .deleteCart(
                                                                    item);
                                                            Get.back();
                                                          },
                                                        );
                                                      }
                                                    }
                                                  },
                                                  onTapPlus: () {
                                                    if (item.qty != null) {
                                                      item.qty =
                                                          (item.qty ?? 1) + 1;
                                                      controller
                                                          .updateCart(item);
                                                    }
                                                  },
                                                ),
                                              ],
                                            ),
                                          ],
                                        )),
                                  );
                                },
                              );
                            }),
                          ),
                          Align(
                            alignment: Alignment.bottomCenter,
                            child: Padding(
                              padding: EdgeInsets.symmetric(
                                  horizontal: AppDimen.h8,
                                  vertical: AppDimen.h4),
                              child: PrimaryButton(
                                onPressed: () {
                                  var items = controller
                                      .newOrder.value.orderList
                                      ?.where((element) {
                                    bool itemUnavailable =
                                        element.product?.stock_status ==
                                            'unavailable';
                                    return itemUnavailable;
                                  }).toList();
                                  if (items?.isNotEmpty == true) {
                                    AppAlert.showConfirmWarningDialog(
                                        context: context,
                                        message: Strings.someCartUnavailable.tr,
                                        content: [
                                          Text(
                                            Strings.someCartUnavailable.tr,
                                            style: AppFont.componentSmall
                                                .copyWith(color: Colors.white),
                                          ),
                                          const Divider(),
                                          Flexible(
                                            child: Container(
                                              width:
                                                  Constants.alertWidth(context),
                                              constraints: BoxConstraints(
                                                  maxHeight:
                                                      MediaQuery.of(context)
                                                              .size
                                                              .height *
                                                          0.4),
                                              child: ListView.builder(
                                                shrinkWrap: true,
                                                padding:
                                                    const EdgeInsets.all(6.0),
                                                itemCount: items?.length ?? 0,
                                                itemBuilder: (context, index) {
                                                  var item = items![index];
                                                  return Container(
                                                    margin:
                                                        const EdgeInsets.only(
                                                            bottom: 5),
                                                    child: Row(
                                                      mainAxisAlignment:
                                                          MainAxisAlignment
                                                              .spaceBetween,
                                                      children: [
                                                        Text(
                                                          "${item.product?.name}",
                                                          style: AppFont
                                                              .paragraphSmall
                                                              .copyWith(
                                                                  color: AppColor
                                                                      .white),
                                                        ),
                                                        Text(
                                                          "${item.qty ?? 0}",
                                                          style: AppFont
                                                              .paragraphSmall
                                                              .copyWith(
                                                                  color: AppColor
                                                                      .white),
                                                        ),
                                                      ],
                                                    ),
                                                  );
                                                },
                                              ),
                                            ),
                                          ),
                                          const Divider()
                                        ],
                                        actions: [
                                          TextButton(
                                              onPressed: () {
                                                Get.back();
                                              },
                                              child: Text(
                                                Strings.cancel.tr,
                                                style: AppFont.componentSmall
                                                    .copyWith(
                                                        color: AppColor.white),
                                              )),
                                          TextButton(
                                              onPressed: () {
                                                Get.back();
                                                Get.toNamed(
                                                        Routes.CARTDETAIL(
                                                            controller
                                                                .outletId),
                                                        arguments: controller
                                                            .getArgFromLink())
                                                    ?.then((value) {
                                                  if (value is bool) {
                                                    return value
                                                        ? controller.initOrder()
                                                        : null;
                                                  }
                                                });
                                              },
                                              child: Text(
                                                Strings.next.tr,
                                                style: AppFont.componentSmall
                                                    .copyWith(
                                                        color: AppColor.white),
                                              )),
                                        ]);
                                  } else {
                                    Sentry.addBreadcrumb(Breadcrumb(
                                        type: 'debug',
                                        category: 'user.activity.outlet.cart',
                                        level: SentryLevel.debug));
                                    Get.toNamed(
                                            Routes.CARTDETAIL(
                                                controller.outletId),
                                            arguments:
                                                controller.getArgFromLink())
                                        ?.then((value) {
                                      if (value is bool) {
                                        return value
                                            ? controller.initOrder()
                                            : null;
                                      }
                                    });
                                  }
                                },
                                type: PrimaryButtonType.type5,
                                width: MediaQuery.of(context).size.width,
                                borderRadius: BorderRadius.all(
                                    Radius.circular(AppDimen.h6)),
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Obx(() {
                                      return Text(
                                          "${controller.newOrder.value.totalQTY} Item",
                                          style: AppFont.componentSmallBold
                                              .copyWith(
                                                  color: wrapperController
                                                      .getPrimaryColor()
                                                      .changeColorBasedOnBackgroundColor()
                                                      .$1));
                                    }),
                                    Text(Strings.next.tr,
                                        style: AppFont.componentSmallBold.copyWith(
                                            color: wrapperController
                                                .getPrimaryColor()
                                                .changeColorBasedOnBackgroundColor()
                                                .$1)),
                                    Obx(() {
                                      return Text(
                                          "Rp${controller.newOrder.value.subTotal.toCurrency}",
                                          style: AppFont.componentSmallBold
                                              .copyWith(
                                                  color: wrapperController
                                                      .getPrimaryColor()
                                                      .changeColorBasedOnBackgroundColor()
                                                      .$1));
                                    }),
                                  ],
                                ),
                              ),
                            ),
                          )
                        ],
                      );
                    },
                  );
                },
                child: Container(
                  padding: EdgeInsets.all(AppDimen.h6),
                  decoration: BoxDecoration(
                      border: Border.all(
                          color: wrapperController.getPrimaryColor(),
                          width: 2,
                          style: BorderStyle.solid),
                      borderRadius:
                          BorderRadius.all(Radius.circular(AppDimen.h6))),
                  child: Row(
                    children: [
                      AppDimen.h4.width,
                      Icon(
                        Icons.shopping_basket_rounded,
                        color: wrapperController.getPrimaryColor(),
                        size: AppDimen.h16,
                      ),
                      AppDimen.h8.width,
                      Obx(() {
                        return Text(
                          "${controller.newOrder.value.totalQTY}",
                          style: AppFont.componentSmallBold.copyWith(
                              color: wrapperController.getPrimaryColor()),
                        );
                      }),
                      AppDimen.h4.width,
                    ],
                  ),
                ),
              ),
              AppDimen.h10.width,
              Flexible(
                child: Obx(() {
                  return PrimaryButton(
                    onPressed: () {
                      var items =
                          controller.newOrder.value.orderList?.where((element) {
                        bool itemUnavailable =
                            element.product?.stock_status == 'unavailable';
                        return itemUnavailable;
                      }).toList();
                      if (items?.isNotEmpty == true) {
                        AppAlert.showConfirmWarningDialog(
                            context: context,
                            message: Strings.someCartUnavailable.tr,
                            content: [
                              Text(
                                Strings.someCartUnavailable.tr,
                                style: AppFont.componentSmall
                                    .copyWith(color: Colors.white),
                              ),
                              const Divider(),
                              Flexible(
                                child: Container(
                                  width: Constants.alertWidth(context),
                                  constraints: BoxConstraints(
                                      maxHeight:
                                          MediaQuery.of(context).size.height *
                                              0.4),
                                  child: ListView.builder(
                                    shrinkWrap: true,
                                    padding: const EdgeInsets.all(6.0),
                                    itemCount: items?.length ?? 0,
                                    itemBuilder: (context, index) {
                                      var item = items![index];
                                      return Container(
                                        margin:
                                            const EdgeInsets.only(bottom: 5),
                                        child: Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            Text(
                                              "${item.product?.name}",
                                              style: AppFont.paragraphSmall
                                                  .copyWith(
                                                      color: AppColor.white),
                                            ),
                                            Text(
                                              "${item.qty ?? 0}",
                                              style: AppFont.paragraphSmall
                                                  .copyWith(
                                                      color: AppColor.white),
                                            ),
                                          ],
                                        ),
                                      );
                                    },
                                  ),
                                ),
                              ),
                              const Divider()
                            ],
                            actions: [
                              TextButton(
                                  onPressed: () {
                                    Get.back();
                                  },
                                  child: Text(
                                    Strings.cancel.tr,
                                    style: AppFont.componentSmall
                                        .copyWith(color: AppColor.white),
                                  )),
                              TextButton(
                                  onPressed: () {
                                    Get.back();
                                    Get.toNamed(
                                            Routes.CARTDETAIL(
                                                controller.outletId),
                                            arguments:
                                                controller.getArgFromLink())
                                        ?.then((value) {
                                      if (value is bool) {
                                        return value
                                            ? controller.initOrder()
                                            : null;
                                      }
                                    });
                                  },
                                  child: Text(
                                    Strings.next.tr,
                                    style: AppFont.componentSmall
                                        .copyWith(color: AppColor.white),
                                  )),
                            ]);
                      } else {
                        Sentry.addBreadcrumb(Breadcrumb(
                            type: 'debug',
                            category: 'user.activity.outlet.cart',
                            level: SentryLevel.debug));
                        Get.toNamed(Routes.CARTDETAIL(controller.outletId),
                                arguments: controller.getArgFromLink())
                            ?.then((value) {
                          if (value is bool) {
                            return value ? controller.initOrder() : null;
                          }
                        });
                      }
                    },
                    text:
                        "${Strings.next.tr} Rp${controller.newOrder.value.subTotal.toCurrency}",
                    type: PrimaryButtonType.type5,
                    width: MediaQuery.of(context).size.width,
                    borderRadius:
                        BorderRadius.all(Radius.circular(AppDimen.h6)),
                  );
                }),
              )
            ]),
          );
        }),
      );
    });
  }
}
