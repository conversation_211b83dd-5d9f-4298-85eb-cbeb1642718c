import 'package:flutter/material.dart';
import 'package:mobile_crm/core/extensions/double_extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';

import '../../../../../utils/utils.dart';
import '../../../../../widget/shimmer_loading_widget.dart';

class OutletNameLoading extends StatelessWidget {
  const OutletNameLoading({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(top: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Flexible(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ShimmerWidget(
                    width:
                        randomRange(AppDimen.h40.toInt(), AppDimen.h64.toInt())
                            .toDouble(),
                    height: AppDimen.h12),
                5.0.height,
                ShimmerWidget(
                    width:
                        randomRange(AppDimen.h64.toInt(), AppDimen.h192.toInt())
                            .toDouble(),
                    height: AppDimen.h12),
                10.0.height,
                Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: List.generate(
                      4,
                      (index) => Row(
                        children: [
                          ShimmerWidget(
                              width: AppDimen.h40, height: AppDimen.h12),
                          AppDimen.h8.width
                        ],
                      ),
                    ))
              ],
            ),
          ),
          AppDimen.h10.width,
          ShimmerWidget(width: AppDimen.h12, height: AppDimen.h12),
        ],
      ),
    );
  }
}
