import 'package:flutter/material.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:mobile_crm/app/modules/outlet/controllers/outlet_controller.dart';
import 'package:mobile_crm/app/modules/outlet/views/widget/outlet_modal_bottom_sheet/outlet_about.dart';
import 'package:mobile_crm/core/extensions/double_extensions.dart';
import 'package:mobile_crm/core/extensions/widget_extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/data/models/order_model.dart';
import 'package:mobile_crm/data/providers/db/database.dart';

import '../../../../../widget/order_type/order_type_widget.dart';
import 'outlet_name_loading.dart';

class OutletName extends StatelessWidget {
  const OutletName({Key? key, required this.controller}) : super(key: key);
  final OutletController controller;

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
      future: controller.getOutletById(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const OutletNameLoading();
        }

        if (snapshot.hasData) {
          if (snapshot.data?.name == '' || snapshot.data?.name == null) {
            return const SizedBox();
          }
          return InkWell(
            key: controller.outletNameKey,
            splashFactory: NoSplash.splashFactory,
            onTapDown: (details) {
              OutletAbout(outlet: snapshot.data ?? Outlet())
                  .toModalBottomSheetNoMinHeight
                  .of(context);
            },
            child: Container(
              margin: const EdgeInsets.only(top: 8),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Flexible(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "${snapshot.data?.name}",
                          style: AppFont.componentLarge,
                        ),
                        FutureBuilder(
                          future: controller.repoTrans.getRatingSummary(
                              outletId: snapshot.data?.outlet_id ?? 0),
                          builder: (context, rating) {
                            if (rating.hasData) {
                              return Visibility(
                                visible: (rating.data?.data?.count ?? 0) > 0,
                                child: Row(
                                  children: [
                                    Text(
                                      "${rating.data?.data?.rating ?? 0.0}",
                                      style: AppFont.componentSmall
                                          .copyWith(color: AppColor.black90),
                                    ),
                                    AppDimen.h4.width,
                                    RatingBar.builder(
                                      initialRating:
                                          rating.data?.data?.rating ?? 0,
                                      glow: false,
                                      direction: Axis.horizontal,
                                      allowHalfRating: true,
                                      itemCount: 5,
                                      ignoreGestures: true,
                                      unratedColor:
                                          AppColor.utilityDark.withAlpha(100),
                                      itemSize: AppDimen.h14,
                                      itemPadding: EdgeInsets.zero,
                                      itemBuilder: (context, _) => const Icon(
                                        Icons.star,
                                        color: Colors.amber,
                                      ),
                                      onRatingUpdate: (double value) {},
                                    ),
                                    AppDimen.h4.width,
                                    Text(
                                      "(${rating.data?.data?.count ?? 0})",
                                      style: AppFont.componentSmall
                                          .copyWith(color: AppColor.black90),
                                    ),
                                  ],
                                ).fadeIn(),
                              );
                            }
                            return const SizedBox();
                          },
                        ),
                        Text(
                          "${snapshot.data?.address}",
                          overflow: TextOverflow.ellipsis,
                          maxLines: 1,
                          style: AppFont.componentSmall
                              .copyWith(color: AppColor.black70),
                        ),
                        10.0.height,
                        OrderTypeWidget(
                            orderTypeModel:
                                snapshot.data?.order_type ?? OrderTypeModel())
                      ],
                    ),
                  ),
                  AppDimen.h10.width,
                  const Icon(
                    Icons.keyboard_arrow_right_rounded,
                    color: AppColor.black90,
                  )
                ],
              ),
            ),
          );
        }

        if (snapshot.hasError) {
          return const SizedBox();
        }

        return const OutletNameLoading();
      },
    );
  }
}
