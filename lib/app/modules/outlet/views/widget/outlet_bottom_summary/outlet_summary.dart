import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/outlet/outlet_export.dart';
import 'package:mobile_crm/app/modules/screen_wrapper/controller/wrapper_controller.dart';
import 'package:mobile_crm/app/utils/app_alert.dart';
import 'package:mobile_crm/core/extensions/color_extensions.dart';
import 'package:mobile_crm/core/extensions/double_extensions.dart';
import 'package:mobile_crm/core/extensions/int_extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/values.dart';
import 'package:mobile_crm/routes/app_pages.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

Widget outletSummary(BuildContext context, OutletController controller) {
  final wrapperController = Get.find<WrapperController>();
  return Obx(() {
    return AnimatedContainer(
      curve: Curves.fastOutSlowIn,
      duration: const Duration(milliseconds: 400),
      height: (controller.newOrder.value.sumQTY() > 0) ? AppDimen.h46 : 0,
      margin: EdgeInsets.symmetric(horizontal: AppDimen.h8),
      padding: EdgeInsets.symmetric(horizontal: AppDimen.h8),
      decoration: BoxDecoration(
          color: wrapperController.getPrimaryColor(),
          borderRadius: BorderRadius.all(Radius.circular(AppDimen.h10))),
      child: InkWell(
        onTapDown: (details) {
          var items = controller.newOrder.value.orderList?.where((element) {
            bool itemUnavailable =
                element.product?.stock_status == 'unavailable';
            return itemUnavailable;
          }).toList();
          if (items?.isNotEmpty == true) {
            AppAlert.showConfirmWarningDialog(
                context: context,
                message: Strings.someCartUnavailable.tr,
                content: [
                  Text(
                    Strings.someCartUnavailable.tr,
                    style: AppFont.componentSmall.copyWith(color: Colors.white),
                  ),
                  const Divider(),
                  Flexible(
                    child: Container(
                      width: Constants.alertWidth(context),
                      constraints: BoxConstraints(
                          maxHeight: MediaQuery.of(context).size.height * 0.4),
                      child: ListView.builder(
                        shrinkWrap: true,
                        padding: const EdgeInsets.all(6.0),
                        itemCount: items?.length ?? 0,
                        itemBuilder: (context, index) {
                          var item = items![index];
                          return Container(
                            margin: const EdgeInsets.only(bottom: 5),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  "${item.product?.name}",
                                  style: AppFont.paragraphSmall
                                      .copyWith(color: AppColor.white),
                                ),
                                Text(
                                  "${item.qty ?? 0}",
                                  style: AppFont.paragraphSmall
                                      .copyWith(color: AppColor.white),
                                ),
                              ],
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                  const Divider()
                ],
                actions: [
                  TextButton(
                      onPressed: () {
                        Get.back();
                      },
                      child: Text(
                        Strings.cancel.tr,
                        style: AppFont.componentSmall
                            .copyWith(color: AppColor.white),
                      )),
                  TextButton(
                      onPressed: () {
                        Get.back();
                        Get.toNamed(Routes.CARTDETAIL(controller.outletId));
                      },
                      child: Text(
                        Strings.next.tr,
                        style: AppFont.componentSmall
                            .copyWith(color: AppColor.white),
                      )),
                ]);
          } else {
            Sentry.addBreadcrumb(Breadcrumb(
                type: 'debug',
                category: 'user.activity.outlet.cart',
                level: SentryLevel.debug));
            Get.toNamed(Routes.CARTDETAIL(controller.outletId));
          }
        },
        child: Obx(() {
          return AnimatedOpacity(
            duration: const Duration(milliseconds: 200),
            opacity: (controller.newOrder.value.sumQTY() > 0) ? 1.0 : 0.0,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Obx(() {
                      return Text(
                          "${controller.newOrder.value.totalQTY} Items",
                          style: AppFont.componentSmallBold.copyWith(
                              color: wrapperController
                                  .getPrimaryColor()
                                  .changeColorBasedOnBackgroundColor().$1));
                    }),
                    Obx(() {
                      return Text(controller.newOrder.value.outlet?.name ?? '',
                          style: AppFont.componentSmall.copyWith(
                              color: wrapperController
                                  .getPrimaryColor()
                                  .changeColorBasedOnBackgroundColor().$1));
                    }),
                  ],
                ),
                Row(
                  children: [
                    Obx(() {
                      return Text(
                          controller.newOrder.value.subTotal.toCurrency,
                          style: AppFont.componentMediumBold.copyWith(
                              color: wrapperController
                                  .getPrimaryColor()
                                  .changeColorBasedOnBackgroundColor().$1));
                    }),
                    10.0.width,
                    Icon(Icons.shopping_basket,
                        color: wrapperController
                            .getPrimaryColor()
                            .changeColorBasedOnBackgroundColor().$1)
                  ],
                )
              ],
            ),
          );
        }),
      ),
    );
  });
}
