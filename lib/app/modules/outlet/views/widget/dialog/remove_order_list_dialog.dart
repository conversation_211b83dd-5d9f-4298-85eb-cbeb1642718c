import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/screen_wrapper/controller/wrapper_controller.dart';
import 'package:mobile_crm/core/extensions/double_extensions.dart';
import 'package:mobile_crm/core/theme/app_color.dart';
import 'package:mobile_crm/core/theme/app_dimen.dart';
import 'package:mobile_crm/core/theme/app_font.dart';
import 'package:mobile_crm/core/values/app_strings.dart';
import 'package:mobile_crm/data/models/cart_model.dart';

class RemoveOrderListDialog extends GetView<WrapperController> {
  const RemoveOrderListDialog({super.key, required this.nonActive});
  final List<CartModel> nonActive;
  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Padding(
          padding: EdgeInsets.only(
              left: AppDimen.h16, right: AppDimen.h16, top: AppDimen.h16),
          child: Text(Strings.theItemInCartIsUnavailable.tr,
            style: AppFont.componentSmall,
          ),
        ),
        AppDimen.h2.height,
        const Divider(color: AppColor.black10),
        AppDimen.h2.height,
        Padding(
          padding: EdgeInsets.symmetric(horizontal: AppDimen.h16),
          child: Container(
            constraints: BoxConstraints(
                minHeight: AppDimen.h128, maxHeight: AppDimen.h128 * 2),
            child: MediaQuery.removePadding(
              context: context,
              removeTop: true,
              child: ListView.builder(
                shrinkWrap: true,
                itemCount: nonActive.length,
                itemBuilder: (context, index) {
                  var item = nonActive[index];
                  return Padding(
                    padding: EdgeInsets.symmetric(vertical: AppDimen.h2),
                    child: Text("${item.product?.name}", style: AppFont.componentSmall,),
                  );
                },
              ),
            ),
          ),
        ),
        AppDimen.h2.height,
        const Divider(
          color: AppColor.black10,
        ),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: AppDimen.h16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              TextButton(
                child: Text(
                  'Ok',
                  style: AppFont.componentSmallBold
                      .copyWith(color: controller.getPrimaryColor()),
                ),
                onPressed: () {
                  Navigator.of(context).pop();
                },
              ),
            ],
          ),
        )
      ],
    );
  }
}
