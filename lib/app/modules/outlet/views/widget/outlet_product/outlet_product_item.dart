import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/outlet/controllers/outlet_controller.dart';
import 'package:mobile_crm/app/modules/outlet/views/widget/outlet_modal_bottom_sheet/outlet_cart_link_menu.dart';
import 'package:mobile_crm/app/utils/logger.dart';
import 'package:mobile_crm/app/utils/utils.dart';
import 'package:mobile_crm/app/widget/button_widget/add_order_button_widget.dart';
import 'package:mobile_crm/app/widget/button_widget/minus_plus_button_widget.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/theme/app_color.dart';
import 'package:mobile_crm/core/theme/app_dimen.dart';
import 'package:mobile_crm/core/theme/app_font.dart';
import 'package:mobile_crm/core/values/app_constants.dart';
import 'package:mobile_crm/data/providers/db/database.dart';

import '../../../../../../core/values/app_strings.dart';
import '../../../../../../data/models/available_model.dart';
import '../../../../../../data/models/cart_model.dart';
import '../../../../../utils/app_alert.dart';
import '../../../../../utils/toast.dart';
import '../../../../../widget/html_widget.dart';
import '../../../../../widget/product_item/product_photo_widget.dart';
import '../outlet_modal_bottom_sheet/outlet_link_menu.dart';
import '../outlet_modal_bottom_sheet/outlet_product_detail_modal_bottom_sheet.dart';
import '../outlet_modal_bottom_sheet/outlet_unit_conversion.dart';

class OutletProductItem extends GetView<OutletController> {
  const OutletProductItem(
      {super.key, required this.product, this.cart, this.onTap});
  final Product product;
  final CartModel? cart;
  final GestureTapCallback? onTap;
  @override
  Widget build(BuildContext context) {
    AvailableModel? available =
        controller.isProductAvailableOnThisOutlet(product);
    bool isProductAvailable = available?.enableOrder?.status != 'enable';
    bool isProductOnCart = cart == null;
    return InkWell(
      onTap: onTap,
      splashFactory: NoSplash.splashFactory,
      child: Container(
        margin: EdgeInsets.symmetric(vertical: AppDimen.h6),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Flexible(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        product.name ?? '',
                        style: AppFont.componentMediumBold.copyWith(
                            color: isProductAvailable
                                ? AppColor.black70
                                : Colors.black),
                      ),
                      AppDimen.h4.height,
                      HtmlWidget(
                        data: product.description ?? '',
                        fontSize: 12,
                        maxLines: 2,
                        disable: isProductAvailable,
                      ),
                      AppDimen.h4.height,
                      Text(
                        !isProductAvailable
                            ? "Rp${(available?.priceSell ?? product.price).toCurrency}"
                            : Strings.productNotAvailable.tr,
                        style: AppFont.componentSmallBold.copyWith(
                          color: !isProductAvailable
                              ? Colors.black
                              : AppColor.utilityDanger,
                        ),
                      )
                    ],
                  ),
                ),
                AppDimen.h8.width,
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.all(Constants.shapeRadius),
                    boxShadow: const [
                      BoxShadow(
                        color: AppColor.black5,
                        blurRadius: 3,
                        spreadRadius: 1,
                        offset: Offset(0, 0),
                      )
                    ],
                  ),
                  child: ProductPhotoWidget(
                    imageUrl: product.photo,
                    disable: isProductAvailable,
                  ),
                ),
              ],
            ),
            AppDimen.h10.height,
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Expanded(child: Visibility(
                  visible: (product.stockQty ?? 0) > 0,
                  child: Text('${simplifyStockNumber((product.stockQty ?? 0))} tersisa', style: AppFont.componentSmall.copyWith(color: Colors.black45)))),
                isProductOnCart
                    ? AddOrderButtonWidget(
                        onTap: () {
                          if (!isProductAvailable) {
                            if (product.variant?.isNotEmpty == true &&
                                product.variant != null) {
                              OutletProductDetailModalBottomSheet(
                                      product: product, cart: cart)
                                  .toModalBottomSheet
                                  .of(context);
                              return;
                            } else {
                              var search = controller.getLinkMenuBy(
                                  product.productId ?? 0,
                                  available?.productDetailId);
                              if (search.isNotEmpty) {
                                search.sort((a, b) =>
                                    (a.orderNo ?? 0).compareTo(b.orderNo ?? 0));
                                OutletLinkMenu(
                                  listLinkMenuMaster: search,
                                  noteController: TextEditingController(),
                                  product: product,
                                ).toModalBottomSheet.of(context);
                              } else {
                                controller.addToCart(
                                    available?.productDetailId ?? 0,
                                    product: product);
                              }
                            }
                          } else {
                            Toast.show(Strings.productNotAvailable,
                                duration: 3, type: ToastType.dark);
                          }
                        },
                        text: Strings.add.tr,
                        disable: isProductAvailable,
                      ).fadeIn(
                        duration: const Duration(milliseconds: 300),
                      )
                    : (controller.newOrder.value.orderList
                                    ?.where((element) =>
                                        element.product_detail_fkid ==
                                        available?.productDetailId)
                                    .where((element) =>
                                        element.linkMenu?.isNotEmpty == true)
                                    .length ??
                                0) >
                            0
                        ? AddOrderButtonWidget(
                            onTap: () {
                              if (product.variant != null) {
                                OutletProductDetailModalBottomSheet(
                                        product: product, cart: cart)
                                    .toModalBottomSheet
                                    .of(context);
                              } else {
                                OutletCartLinkMenu(
                                  product: product,
                                  orderList: controller.newOrder.value.orderList
                                          ?.where((element) =>
                                              element.product_detail_fkid ==
                                              available?.productDetailId)
                                          .where((element) =>
                                              element.linkMenu?.isNotEmpty ==
                                              true)
                                          .toList() ??
                                      [],
                                ).toModalBottomSheetNoMinHeight.of(context);
                              }
                            },
                            text:
                                '${controller.newOrder.value.sumQtyWhereProductId(product.productId ?? 0)} Items',
                            disable: isProductAvailable,
                          ).fadeIn(duration: const Duration(milliseconds: 300))
                        : MinusPlusButtonWidget(
                            textEditingController: TextEditingController(
                                text: '${cart?.qty ?? 0}'),
                            listUnit: controller.listUnitConversion
                                .where((element) =>
                                    element.productFkId == product.productId)
                                .toList(),
                            showTools: product.variant == null,
                            onTapTools: () {
                              OutletUnitConversion(
                                teController: TextEditingController(),
                                product: product,
                                conversion: (value) {
                                  cart?.qty = value;
                                  controller.updateCart(cart);
                                  Get.back();
                                },
                                listUnit: controller.listUnitConversion
                                    .where((element) =>
                                        element.productFkId ==
                                        product.productId)
                                    .toList(),
                              ).toModalBottomSheetNoMinHeight.of(context);
                            },
                            onTapMinus: () {
                              if (product.variant?.isNotEmpty == true &&
                                  product.variant != null) {
                                OutletProductDetailModalBottomSheet(
                                  product: product,
                                ).toModalBottomSheet.of(context);
                                return;
                              } else {
                                if (cart?.qty != null && (cart?.qty ?? 1) > 1) {
                                  cart?.qty = (cart?.qty ?? 1) - 1;
                                  controller.updateCart(cart);
                                } else {
                                  AppAlert.showConfirmDeleteDialog(
                                    context,
                                    "${product.name}",
                                    () {
                                      controller.deleteCart(cart);
                                      Get.back();
                                    },
                                  );
                                }
                              }
                            },
                            onChanged: (value) {
                              if (product.variant?.isNotEmpty == true &&
                                  product.variant != null) {
                                OutletProductDetailModalBottomSheet(
                                  product: product,
                                ).toModalBottomSheet.of(context);
                                return;
                              }
                              if (int.tryParse(value) != null) {
                                int convert = int.tryParse(value) ?? 1;
                                if (cart?.qty != null && convert > 0) {
                                  if ((cart?.qty ?? 0) != convert) {
                                    cart?.qty = convert;
                                    controller.updateCart(cart);
                                  }
                                } else {
                                  AppAlert.showConfirmDeleteDialog(
                                    context,
                                    "${cart?.product?.name}",
                                    () {
                                      controller.deleteCart(cart);
                                      Get.back();
                                    },
                                  );
                                }
                              }
                            },
                            onTapPlus: () {
                              if (product.variant?.isNotEmpty == true &&
                                  product.variant != null) {
                                OutletProductDetailModalBottomSheet(
                                  product: product,
                                ).toModalBottomSheet.of(context);
                              } else {
                                if (cart?.qty != null) {
                                  cart?.qty = (cart?.qty ?? 1) + 1;
                                  controller.updateCart(cart);
                                }
                              }
                            },
                            middleText: product.variant != null &&
                                    (product.variant?.isNotEmpty == true)
                                ? '${controller.newOrder.value.sumQtyWhereProductId(product.productId ?? 0)}'
                                : '${cart?.qty ?? 0}')
             ,             
              ],
            ),            
            AppDimen.h6.height,
            const Divider(
              color: AppColor.black5,
            )
          ],
        ),
      ),
    );
  }
}
