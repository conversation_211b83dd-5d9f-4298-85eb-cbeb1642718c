import 'package:flutter/material.dart';
import 'package:mobile_crm/app/utils/utils.dart';
import 'package:mobile_crm/core/theme/themes.dart';

import '../../../../../../data/providers/db/database.dart';
import '../../../../../widget/app_dotted_separator.dart';
import '../../../../../widget/item_list/product_item.dart';
import '../../../../../widget/shimmer_loading_widget.dart';

class OutletListProductLoading extends StatelessWidget {
  const OutletListProductLoading({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      scrollDirection: Axis.vertical,
      itemCount: 2,
      itemBuilder: (BuildContext ctx, int index) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              margin: const EdgeInsets.only(top: 15),
              height: AppDimen.h12,
              child: ShimmerWidget(
                height: AppDimen.h12,
                width: randomRange(AppDimen.h64.toInt(), AppDimen.h128.toInt())
                    .toDouble(),
              ),
            ),
            const DottedDivider(
              height: 2,
              width: 3,
              color: AppColor.disable,
            ),
            MediaQuery.removePadding(
              context: context,
              removeTop: true,
              child: ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: randomRange(3, 7),
                itemBuilder: (context, index) {
                  return ProductItemWidget(
                    type: ProductItemWidgetType.loading,
                    product: Product(),
                    onPressed: () {},
                  );
                },
              ),
            )
          ],
        );
      },
    );
  }
}
