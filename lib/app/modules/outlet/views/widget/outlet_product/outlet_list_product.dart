import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/outlet/controllers/outlet_controller.dart';
import 'package:mobile_crm/app/modules/outlet/views/widget/outlet_product/outlet_product_item.dart';
import 'package:mobile_crm/app/utils/logger.dart';
import 'package:mobile_crm/app/widget/app_page_empty.dart';
import 'package:mobile_crm/app/widget/cached_image_widget.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/values.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';

import '../../../../../../data/models/available_model.dart';
import '../../../../../../data/models/cart_model.dart';
import '../../../../../../data/models/product_group_model.dart';
import '../../../../../../data/providers/db/database.dart';
import '../../../../../utils/app_alert.dart';
import '../../../../../utils/toast.dart';
import '../../../../../widget/app_dotted_separator.dart';
import '../../../../../widget/button_widget/add_order_button_widget.dart';
import '../../../../../widget/button_widget/minus_plus_button_widget.dart';
import '../outlet_modal_bottom_sheet/outlet_cart_link_menu.dart';
import '../outlet_modal_bottom_sheet/outlet_link_menu.dart';
import '../outlet_modal_bottom_sheet/outlet_product_detail_modal_bottom_sheet.dart';
import '../outlet_modal_bottom_sheet/outlet_unit_conversion.dart';
import 'outlet_list_loading.dart';

class OutletListProduct extends StatelessWidget {
  const OutletListProduct({Key? key, required this.controller})
      : super(key: key);
  final OutletController controller;

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    return FutureBuilder(
      builder: (context, snapshot) {
        if (snapshot.hasData) {
          if ((snapshot.data?.length ?? 0) == 0) {
            return SizedBox(
              height: size.height * 0.6,
              child: AppPageEmpty(
                func: () {
                  Get.back();
                },
                buttonText: Strings.back.tr,
                reason: Strings.valueNotFound.trParams({'value': 'Product'}),
              ),
            );
          }
          return ScrollablePositionedList.builder(
            shrinkWrap: true,
            itemScrollController: controller.listProductItemController,
            scrollOffsetListener: controller.scrollOffsetListener,
            itemCount: snapshot.data?.length ?? 0,
            itemBuilder: (context, index) {
              ProductGroupModel category = snapshot.data![index];
              return Column(
                children: [
                  Container(
                    margin: EdgeInsets.only(top: (index == 0) ? 0 : 15),
                    height: AppDimen.h18,
                    child: Align(
                      alignment: Alignment.centerLeft,
                      child: Text(
                        category.categoryName,
                        style: AppFont.paragraphMediumBold,
                      ),
                    )),
                    AppDimen.h4.height,
                    const DottedDivider(
                      height: 2,
                      width: 3,
                      color: AppColor.black30,
                    ),
                    AppDimen.h4.height,
                    MediaQuery.removePadding(
                      context: context,
                      removeTop: true,
                      child: category.products?.firstOrNull?.subcategoryConfig
                                  ?.viewType ==
                              'grid'
                          ? MasonryGridView.count(
                              shrinkWrap: true,
                              physics: const NeverScrollableScrollPhysics(),
                              crossAxisCount: 2,
                              mainAxisSpacing: AppDimen.h12,
                              itemCount: category.products?.length ?? 0,
                              crossAxisSpacing: AppDimen.h8,
                              itemBuilder: (context, index) {
                                Product product = category.products![index];
                                AvailableModel? available = controller
                                    .isProductAvailableOnThisOutlet(product);
                                bool isProductAvailable =
                                    available?.enableOrder?.status == "enable";
                                return Obx(() {
                                  CartModel? cart =
                                      controller.isProductOnCart(product);
                                  bool isProductOnCart = cart == null;
                                  return InkWell(
                                    onTap: () =>
                                        OutletProductDetailModalBottomSheet(
                                                product: product, cart: cart)
                                            .toModalBottomSheet
                                            .of(context),
                                    child: Column(
                                      mainAxisSize: MainAxisSize.min,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Column(
                                          mainAxisSize: MainAxisSize.min,
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          mainAxisAlignment:
                                              MainAxisAlignment.start,
                                          children: [
                                            ClipRRect(
                                              borderRadius: BorderRadius.all(
                                                  Radius.circular(AppDimen.h8)),
                                              child: CachedImageWidget(
                                                  width: (MediaQuery.of(context)
                                                              .size
                                                              .width /
                                                          2) -
                                                      AppDimen.h24,
                                                  height:
                                                      (MediaQuery.of(context)
                                                                  .size
                                                                  .width /
                                                              2) -
                                                          AppDimen.h24,
                                                  imageUrl: product.photo),
                                            ),
                                            AppDimen.h8.height,
                                            Flexible(
                                              child: Text(
                                                product.name ?? '',
                                                style:
                                                    AppFont.componentSmallBold,
                                                overflow: TextOverflow.ellipsis,
                                                maxLines: 2,
                                              ),
                                            ),
                                            AppDimen.h8.height,
                                            Text(
                                              !isProductAvailable
                                                  ? "Rp${(available?.priceSell ?? product.price).toCurrency}"
                                                  : Strings
                                                      .productNotAvailable.tr,
                                              style: AppFont.componentSmallBold
                                                  .copyWith(
                                                      color: !isProductAvailable
                                                          ? Colors.black
                                                          : AppColor
                                                              .utilityDanger),
                                            ),
                                            AppDimen.h12.height,
                                          ],
                                        ),
                                        isProductOnCart
                                            ? AddOrderButtonWidget(
                                                onTap: () {
                                                  if (!isProductAvailable) {
                                                    if (product.variant
                                                                ?.isNotEmpty ==
                                                            true &&
                                                        product.variant !=
                                                            null) {
                                                      OutletProductDetailModalBottomSheet(
                                                              product: product,
                                                              cart: cart)
                                                          .toModalBottomSheet
                                                          .of(context);
                                                      return;
                                                    } else {
                                                      var search = controller
                                                          .getLinkMenuBy(
                                                              product.productId ??
                                                                  0,
                                                              available
                                                                  ?.productDetailId);
                                                      if (search.isNotEmpty) {
                                                        search.sort((a, b) =>
                                                            (a.orderNo ?? 0)
                                                                .compareTo(
                                                                    b.orderNo ??
                                                                        0));
                                                        OutletLinkMenu(
                                                          listLinkMenuMaster:
                                                              search,
                                                          noteController:
                                                              TextEditingController(),
                                                          product: product,
                                                        )
                                                            .toModalBottomSheet
                                                            .of(context);
                                                      } else {
                                                        controller.addToCart(
                                                            available
                                                                    ?.productDetailId ??
                                                                0,
                                                            product: product);
                                                      }
                                                    }
                                                  } else {
                                                    Toast.show(
                                                        Strings
                                                            .productNotAvailable,
                                                        duration: 3,
                                                        type: ToastType.dark);
                                                  }
                                                },
                                                text: Strings.add.tr,
                                                disable: isProductAvailable,
                                              ).fadeIn(
                                                duration: const Duration(
                                                    milliseconds: 300))
                                            : (controller.newOrder.value.orderList?.where((element) => element.product_detail_fkid == available?.productDetailId).where((element) => element.linkMenu?.isNotEmpty == true).length ??
                                                        0) >
                                                    0
                                                ? AddOrderButtonWidget(
                                                    onTap: () {
                                                      if (product.variant !=
                                                          null) {
                                                        OutletProductDetailModalBottomSheet(
                                                                product:
                                                                    product,
                                                                cart: cart)
                                                            .toModalBottomSheet
                                                            .of(context);
                                                      } else {
                                                        OutletCartLinkMenu(
                                                          product: product,
                                                          orderList: controller
                                                                  .newOrder
                                                                  .value
                                                                  .orderList
                                                                  ?.where((element) =>
                                                                      element
                                                                          .product_detail_fkid ==
                                                                      available
                                                                          ?.productDetailId)
                                                                  .where((element) =>
                                                                      element
                                                                          .linkMenu
                                                                          ?.isNotEmpty ==
                                                                      true)
                                                                  .toList() ??
                                                              [],
                                                        )
                                                            .toModalBottomSheetNoMinHeight
                                                            .of(context);
                                                      }
                                                    },
                                                    text:
                                                        '${controller.newOrder.value.sumQtyWhereProductId(product.productId ?? 0)} Items',
                                                    disable: isProductAvailable,
                                                  ).fadeIn(
                                                    duration: const Duration(
                                                        milliseconds: 300))
                                                : MinusPlusButtonWidget(
                                                    textEditingController:
                                                        TextEditingController(
                                                            text:
                                                                '${cart.qty ?? 0}'),
                                                    listUnit: controller
                                                        .listUnitConversion
                                                        .where((element) =>
                                                            element.productFkId ==
                                                            product.productId)
                                                        .toList(),
                                                    showTools:
                                                        product.variant == null,
                                                    onTapTools: () {
                                                      OutletUnitConversion(
                                                        teController:
                                                            TextEditingController(),
                                                        product: product,
                                                        conversion: (value) {
                                                          cart.qty = value;
                                                          controller
                                                              .updateCart(cart);
                                                          Get.back();
                                                        },
                                                        listUnit: controller
                                                            .listUnitConversion
                                                            .where((element) =>
                                                                element
                                                                    .productFkId ==
                                                                product
                                                                    .productId)
                                                            .toList(),
                                                      )
                                                          .toModalBottomSheetNoMinHeight
                                                          .of(context);
                                                    },
                                                    onTapMinus: () {
                                                      if (product.variant
                                                                  ?.isNotEmpty ==
                                                              true &&
                                                          product.variant !=
                                                              null) {
                                                        OutletProductDetailModalBottomSheet(
                                                          product: product,
                                                        )
                                                            .toModalBottomSheet
                                                            .of(context);
                                                        return;
                                                      } else {
                                                        if (cart.qty != null &&
                                                            (cart.qty ?? 1) >
                                                                1) {
                                                          cart.qty =
                                                              (cart.qty ?? 1) -
                                                                  1;
                                                          controller
                                                              .updateCart(cart);
                                                        } else {
                                                          AppAlert
                                                              .showConfirmDeleteDialog(
                                                            context,
                                                            "${product.name}",
                                                            () {
                                                              controller
                                                                  .deleteCart(
                                                                      cart);
                                                              Get.back();
                                                            },
                                                          );
                                                        }
                                                      }
                                                    },
                                                    onChanged: (value) {
                                                      if (product.variant
                                                                  ?.isNotEmpty ==
                                                              true &&
                                                          product.variant !=
                                                              null) {
                                                        OutletProductDetailModalBottomSheet(
                                                          product: product,
                                                        )
                                                            .toModalBottomSheet
                                                            .of(context);
                                                        return;
                                                      }
                                                      if (int.tryParse(value) !=
                                                          null) {
                                                        int convert =
                                                            int.tryParse(
                                                                    value) ??
                                                                1;
                                                        if (cart.qty != null &&
                                                            convert > 0) {
                                                          if ((cart.qty ?? 0) !=
                                                              convert) {
                                                            cart.qty = convert;
                                                            controller
                                                                .updateCart(
                                                                    cart);
                                                          }
                                                        } else {
                                                          AppAlert
                                                              .showConfirmDeleteDialog(
                                                            context,
                                                            "${cart.product?.name}",
                                                            () {
                                                              controller
                                                                  .deleteCart(
                                                                      cart);
                                                              Get.back();
                                                            },
                                                          );
                                                        }
                                                      }
                                                    },
                                                    onTapPlus: () {
                                                      if (product.variant
                                                                  ?.isNotEmpty ==
                                                              true &&
                                                          product.variant !=
                                                              null) {
                                                        OutletProductDetailModalBottomSheet(
                                                          product: product,
                                                        )
                                                            .toModalBottomSheet
                                                            .of(context);
                                                      } else {
                                                        if (cart.qty != null) {
                                                          cart.qty =
                                                              (cart.qty ?? 1) +
                                                                  1;
                                                          controller
                                                              .updateCart(cart);
                                                        }
                                                      }
                                                    },
                                                    middleText: product.variant !=
                                                                null &&
                                                            (product.variant
                                                                    ?.isNotEmpty ==
                                                                true)
                                                        ? '${controller.newOrder.value.sumQtyWhereProductId(product.productId ?? 0)}'
                                                        : '${cart.qty ?? 0}')
                                      ],
                                    ),
                                  );
                                });
                              },
                            )
                          : ListView.builder(
                              shrinkWrap: true,
                              physics: const NeverScrollableScrollPhysics(),
                              itemCount: category.products?.length ?? 0,
                              itemBuilder: (context, index) {
                                Product product = category.products![index];
                                return Obx(() {
                                  CartModel? cart =
                                      controller.isProductOnCart(product);
                                  return OutletProductItem(
                                    product: product,
                                    cart: cart,
                                    onTap: () =>
                                        OutletProductDetailModalBottomSheet(
                                                product: product, cart: cart)
                                            .toModalBottomSheet
                                            .of(context),
                                    // onTap: () =>
                                    //     ShowCustomModalBottom.showModalProductOutlet(
                                    //         context, product, controller),
                                  );
                                });
                              },
                            ),
                    )
                  ],
                );
              },
            );
          }

        if (snapshot.hasError) {
          return AppPageEmpty(
            func: () {
              Get.back();
            },
            hideButton: true,
            buttonText: Strings.back.tr,
            reason: Strings.errorReason.tr,
          );
        }

        return const OutletListProductLoading();
      },
      future: controller.getProductByCategory(),
    );
  }
}
