import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/screen_wrapper/controller/wrapper_controller.dart';
import 'package:mobile_crm/core/theme/themes.dart';

import '../../../../../../data/services/analytics_service.dart';
import '../../../controllers/outlet_controller.dart';

class OutletFAB extends StatelessWidget {
  const OutletFAB({Key? key, required this.controller}) : super(key: key);
  final OutletController controller;

  @override
  Widget build(BuildContext context) {
    final wrapperController = Get.find<WrapperController>();
    return SizedBox(
      child: Obx(() {
        return FloatingActionButton(
          onPressed: () => _offsetPopup(),
          tooltip: "Menu",
          backgroundColor: wrapperController.getPrimaryColor(),
          child: _offsetPopup(),
        );
      }),
    );
  }

  Widget _offsetPopup() => PopupMenuButton<int>(
        itemBuilder: (context) => controller.listCategoryForFAB
            .map(
              (e) => PopupMenuItem(
                  onTap: () {
                    var listData = controller.listCategoryForFAB;
                    var index = listData.indexOf(e);

                    controller.listProductItemController.scrollTo(index: index, duration: const Duration(milliseconds: 400));

                    AnalyticsService.observer.analytics
                        .logEvent(name: "select_category", parameters: {
                      "name": e.categoryName,
                      "position": e.categoryPosition,
                      "outlet": controller.outletLocalDetail.value.name
                    });
                  },
                  height: 0,
                  padding: const EdgeInsets.symmetric(horizontal: 4),
                  value: controller.listCategoryForFAB.indexOf(e),
                  textStyle: AppFont.componentSmallBold,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(e.categoryName),
                          Text(
                            "${e.products?.length ?? 0}",
                            style: AppFont.componentSmall
                                .copyWith(color: AppColor.black70),
                          ),
                        ],
                      ),
                      const Divider()
                    ],
                  )),
            )
            .toList(),
        offset: const Offset(0, -100),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            const Icon(
              Icons.book,
              color: AppColor.white,
            ),
            Text(
              'Menu',
              style: AppFont.componentSmallBold
                  .copyWith(color: AppColor.white, fontSize: 12),
            ),
          ],
        ),
      );
}
