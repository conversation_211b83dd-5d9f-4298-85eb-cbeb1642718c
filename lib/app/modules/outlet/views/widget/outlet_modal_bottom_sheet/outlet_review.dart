import 'package:flutter/material.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/outlet/controllers/outlet_controller.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/data/models/transaction/feedback/rating_review_model.dart';
import 'package:mobile_crm/data/providers/db/database.dart';

import '../../../../../../data/models/transaction/feedback/rating_summary_model.dart';
import '../../../../../animation/progress_animation.dart';
import '../../../../../widget/app_bar_widget/app_bar_widget.dart';
import '../outlet_review_item.dart';

class OutletReview extends GetView<OutletController> {
  const OutletReview({super.key, required this.outlet, this.ratingSummary});

  final Outlet outlet;
  final RatingSummaryModel? ratingSummary;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const AppBarWidget(
        title: 'Review and ratings',
        centerTitle: false,
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: AppDimen.h16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              AppDimen.h6.height,
              Flexible(
                child: Row(mainAxisSize: MainAxisSize.min, children: [
                  Flexible(
                    child: ListView.builder(
                      itemCount: 5,
                      reverse: true,
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemBuilder: (context, index) {
                        var item = ratingSummary?.countDetail?.firstWhereOrNull(
                          (element) => element.rating == (index + 1),
                        );
                        return Row(
                          children: [
                            Text(
                              "${index + 1}",
                              style: AppFont.componentSmall,
                            ),
                            AppDimen.h8.width,
                            Flexible(
                              child: Stack(
                                children: [
                                  Container(
                                    width: (MediaQuery.of(context).size.width -
                                            AppDimen.h10) *
                                        0.8,
                                    height: AppDimen.h6,
                                    decoration: BoxDecoration(
                                        color:
                                            AppColor.utilityDark.withAlpha(100),
                                        borderRadius: BorderRadius.all(
                                            Radius.circular(AppDimen.h10))),
                                  ),
                                  ProgressBarAnimation(
                                      width:
                                          (MediaQuery.of(context).size.width -
                                                  AppDimen.h10) *
                                              0.8,
                                      currentValue:
                                          (item?.count ?? 0).toDouble(),
                                      maxValue: (ratingSummary?.count ?? 0)
                                          .toDouble(),
                                      duration: const Duration(seconds: 3)),
                                ],
                              ),
                            )
                          ],
                        );
                      },
                    ),
                  ),
                  AppDimen.h10.width,
                  Column(
                    children: [
                      Text(
                        "${ratingSummary?.rating ?? 0.0}",
                        style: AppFont.componentLarge
                            .copyWith(color: AppColor.black90),
                      ),
                      AppDimen.h4.height,
                      RatingBar.builder(
                        initialRating: ratingSummary?.rating ?? 0,
                        glow: false,
                        direction: Axis.horizontal,
                        allowHalfRating: true,
                        itemCount: 5,
                        ignoreGestures: true,
                        unratedColor: AppColor.utilityDark.withAlpha(100),
                        itemSize: AppDimen.h12,
                        itemPadding: EdgeInsets.zero,
                        itemBuilder: (context, _) => const Icon(
                          Icons.star,
                          color: Colors.amber,
                        ),
                        onRatingUpdate: (double value) {},
                      ),
                      AppDimen.h4.height,
                      Text(
                        "${ratingSummary?.count ?? 0.0} reviews",
                        style: AppFont.componentSmall
                            .copyWith(color: AppColor.black90),
                      ),
                    ],
                  )
                ]),
              ),
              AppDimen.h10.height,
              Text(
                "All reviews",
                style: AppFont.componentSmallBold
                    .copyWith(fontSize: 13.sp)
                    .copyWith(color: AppColor.black90),
              ),
              AppDimen.h6.height,
              FutureBuilder(
                future: controller.repoTrans
                    .getReview(outletId: outlet.outlet_id ?? 0),
                builder: (context, snapshot) {
                  if (snapshot.hasData) {
                    return ListView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: snapshot.data?.data?.length ?? 0,
                      itemBuilder: (context, index) {
                        var detail = snapshot.data?.data![index];
                        return OutletReviewItem(
                          review: detail ?? RatingReviewModel(),
                        );
                      },
                    );
                  }

                  return const SizedBox();
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
