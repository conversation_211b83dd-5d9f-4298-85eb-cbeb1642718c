import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/outlet/controllers/outlet_controller.dart';
import 'package:mobile_crm/app/widget/app_dotted_separator.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';  
import 'package:mobile_crm/core/theme/app_color.dart';
import 'package:mobile_crm/core/theme/app_dimen.dart';
import 'package:mobile_crm/core/theme/app_font.dart';
import 'package:mobile_crm/data/models/unit_conversion_model.dart';
import 'package:mobile_crm/data/providers/db/database.dart';

import '../../../../../../core/values/app_strings.dart';
import '../../../../../../data/models/available_model.dart';
import '../../../../../widget/button_widget/primary_button.dart';

class OutletUnitConversion extends GetView<OutletController> {
  const OutletUnitConversion(
      {super.key,
      required this.listUnit,
      required this.teController,
      required this.product,
      this.conversion});

  final List<UnitConversionModel> listUnit;
  final TextEditingController teController;
  final Product product;
  final ValueChanged<int>? conversion;

  @override
  Widget build(BuildContext context) {
    AvailableModel? available =
        controller.isProductAvailableOnThisOutlet(product);
    var isChoose = 0.obs;
    var result = 0.obs;
    return Padding(
      padding:
          EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: EdgeInsets.symmetric(horizontal: AppDimen.h16),
            child: Wrap(
              crossAxisAlignment: WrapCrossAlignment.center,
              alignment: WrapAlignment.center,
              spacing: AppDimen.h2,
              runSpacing: AppDimen.h2,
              children: List.generate(listUnit.length, (index) {
                var item = listUnit[index];
                return InkWell(
                  onTap: () {
                    isChoose.value = index;
                    result.value = (listUnit[index].qty ?? 1) *
                        (int.tryParse(teController.text) ?? 1);
                  },
                  child: Obx(() {
                    return Container(
                      decoration: BoxDecoration(
                          color: isChoose.value == index
                              ? Colors.white
                              : AppColor.black90,
                          border: Border.all(color: AppColor.black90),
                          borderRadius: index == 0
                              ? BorderRadius.only(
                                  topLeft: Radius.circular(AppDimen.h4),
                                  bottomLeft: Radius.circular(AppDimen.h4))
                              : (index == listUnit.length - 1)
                                  ? BorderRadius.only(
                                      topRight: Radius.circular(AppDimen.h4),
                                      bottomRight: Radius.circular(AppDimen.h4))
                                  : BorderRadius.zero),
                      padding: EdgeInsets.all(AppDimen.h4),
                      child: Obx(() {
                        return Text(item.name ?? '-',
                            style: AppFont.componentSmallBold.copyWith(
                                color: isChoose.value == index
                                    ? AppColor.black90
                                    : AppColor.white));
                      }),
                    );
                  }),
                );
              }),
            ),
          ),
          AppDimen.h8.height,
          Padding(
            padding: EdgeInsets.symmetric(horizontal: AppDimen.h16),
            child: TextFormField(
              keyboardType: TextInputType.number,
              controller: teController,
              style: AppFont.componentSmallBold,
              onEditingComplete: () {
                result.value = result.value;
              },
              onFieldSubmitted: (value) {
                (listUnit[isChoose.value].qty ?? 1) *
                    (int.tryParse(value) ?? 1);
              },
              onSaved: (value) {
                result.value = (listUnit[isChoose.value].qty ?? 1) *
                    (int.tryParse(value ?? '1') ?? 1);
              },
              onChanged: (value) {
                result.value = (listUnit[isChoose.value].qty ?? 1) *
                    (int.tryParse(value) ?? 1);
              },
              decoration: const InputDecoration(
                  hintText: '1', border: InputBorder.none),
            ),
          ),
          AppDimen.h10.height,
          Padding(
            padding: EdgeInsets.symmetric(horizontal: AppDimen.h16),
            child: const DottedDivider(
              color: AppColor.black10,
              width: 4,
              height: 2,
            ),
          ),
          AppDimen.h4.height,
          Obx(() {
            return Padding(
              padding: EdgeInsets.symmetric(horizontal: AppDimen.h16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text("${result.value.toCurrency} ${product.unit ?? '-'}",
                      style: AppFont.componentMediumBold),
                  Flexible(
                    child: Text(
                      "Rp${(result.value * (available?.priceSell ?? 0)).toCurrency}",
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: AppFont.componentSmallBold,
                    ),
                  ),
                ],
              ),
            );
          }),
          AppDimen.h10.height,
          Padding(
            padding: EdgeInsets.all(AppDimen.h16),
            child: PrimaryButton(
              onPressed: () {
                conversion!(result.value);
              },
              text: Strings.saveConversion.tr,
              width: double.infinity,
              type: PrimaryButtonType.type2,
            ),
          )
        ],
      ),
    );
  }
}
