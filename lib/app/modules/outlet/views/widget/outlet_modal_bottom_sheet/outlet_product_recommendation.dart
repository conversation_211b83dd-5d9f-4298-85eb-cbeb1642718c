import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/outlet/controllers/outlet_controller.dart';
import 'package:mobile_crm/app/modules/outlet/views/widget/outlet_modal_bottom_sheet/outlet_product_detail_modal_bottom_sheet.dart';
import 'package:mobile_crm/app/modules/outlet/views/widget/outlet_product/outlet_product_item.dart';
import 'package:mobile_crm/core/extensions/widget_extensions.dart';
import 'package:mobile_crm/core/theme/app_font.dart';
import 'package:mobile_crm/data/models/cart_model.dart';
import 'package:mobile_crm/data/providers/db/database.dart';

class OutletProductRecommendationModalBottomSheet
    extends GetView<OutletController> {
  const OutletProductRecommendationModalBottomSheet(
      {super.key, required this.products});

  final List<Product> products;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Column(
        children: [
          Text(
            "Product Recommendation",
            style: AppFont.paragraphMediumBold,
          ),
          Expanded(
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: products.length,
              itemBuilder: (context, index) {
                Product product = products[index];
                return Obx(() {
                  CartModel? cart = controller.isProductOnCart(product);
                  return OutletProductItem(
                    product: product,
                    cart: cart,
                    onTap: () => OutletProductDetailModalBottomSheet(
                            product: product, cart: cart)
                        .toModalBottomSheet
                        .of(context),
                  );
                });
              },
            ),
          )
        ],
      ),
    );
  }
}
