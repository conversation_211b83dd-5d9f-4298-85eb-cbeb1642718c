import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/outlet/controllers/outlet_controller.dart';
import 'package:mobile_crm/app/widget/button_widget/add_order_button_widget.dart';
import 'package:mobile_crm/app/widget/button_widget/button_widget.dart';
import 'package:mobile_crm/app/widget/button_widget/minus_plus_button_widget.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/values.dart';
import 'package:mobile_crm/data/providers/db/database.dart';

import '../../../../../../data/models/available_model.dart';
import '../../../../../../data/models/cart_model.dart';
import '../../../../../../data/models/variant_model.dart';
import '../../../../../utils/app_alert.dart';
import 'outlet_link_menu.dart';
import 'outlet_update_link_menu.dart';

class OutletCartLinkMenu extends GetView<OutletController> {
  const OutletCartLinkMenu(
      {super.key,
      required this.orderList,
      required this.product,
      this.variant});

  final List<CartModel> orderList;
  final Product product;
  final VariantModel? variant;

  @override
  Widget build(BuildContext context) {
    AvailableModel? available = variant != null
        ? controller.isProductVariantAvailable(variant ?? VariantModel())
        : controller.isProductAvailableOnThisOutlet(product);
    return Padding(
      padding:
          EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.symmetric(horizontal: AppDimen.h16),
            child: Text(
              variant != null
                  ? "${product.name} (${variant?.name ?? '-'})"
                  : "${product.name}",
              style: AppFont.componentMediumBold,
            ),
          ),
          AppDimen.h6.height,
          const Divider(
            color: AppColor.black10,
          ),
          AppDimen.h6.height,
          Flexible(
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: AppDimen.h16),
              child: Obx(() {
                var newOrderList = controller.newOrder.value.orderList
                    ?.where((element) =>
                        element.product_detail_fkid ==
                        available?.productDetailId)
                    .where((element) => element.linkMenu?.isNotEmpty == true)
                    .toList();
                return ListView.builder(
                  shrinkWrap: true,
                  itemCount: newOrderList?.length ?? 0,
                  itemBuilder: (context, index) {
                    var item = newOrderList![index].obs;
                    return Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Flexible(
                          child: ListView.builder(
                            shrinkWrap: true,
                            itemCount: item.value.linkMenu?.length ?? 0,
                            itemBuilder: (context, indexChild) {
                              var itemChild = item.value.linkMenu![indexChild];
                              return Row(
                                mainAxisAlignment: MainAxisAlignment.start,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    "${itemChild.name ?? ''}: ",
                                    style: AppFont.componentSmall
                                        .copyWith(fontSize: 11.sp),
                                  ),
                                  Flexible(
                                    child: Text(
                                      "${itemChild.linkMenuDetail?.where((element) => element.isChosen == true).fold('', (previousValue, element) => previousValue.isEmpty ? (element.name ?? '') : previousValue + (', ') + (element.name ?? ''))}",
                                      style: AppFont.componentSmallBold
                                          .copyWith(fontSize: 11.sp),
                                    ),
                                  ),
                                ],
                              );
                            },
                          ),
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              "Rp${(((available?.priceSell ?? product.price ?? 0) * (item.value.qty ?? 0)) + (item.value.linkMenu?.fold(0, (previousValue, element) => (previousValue ?? 0) + element.sumAdditionalPrice(item.value.qty ?? 0)) ?? 0)).toInt().toCurrency}",
                              style: AppFont.componentSmallBold,
                            ),
                            Row(
                              children: [
                                AddOrderButtonWidget(
                                    onTap: () {
                                      Get.back();
                                      OutletUpdateLinkMenu(
                                        cart: item.value,
                                        product: product,
                                        variant: variant,
                                        noteController: TextEditingController(
                                            text: item.value.note),
                                      ).toModalBottomSheet.of(context);
                                    },
                                    text: "Edit"),
                                AppDimen.h10.width,
                                Obx(() {
                                  return MinusPlusButtonWidget(
                                      textEditingController:
                                          TextEditingController(
                                              text: '${item.value.qty ?? 0}'),
                                      onTapPlus: () {
                                        item.value.qty =
                                            (item.value.qty ?? 1) + 1;
                                        controller.updateCart(item.value);
                                        item.refresh();
                                      },
                                      onTapMinus: () {
                                        item.value.qty =
                                            (item.value.qty ?? 1) - 1;
                                        if ((item.value.qty ?? 0) < 1) {
                                          AppAlert.showConfirmDeleteDialog(
                                            context,
                                            "${product.name}",
                                            () {
                                              controller.deleteCart(item.value);
                                              Get.back();
                                            },
                                          );
                                        }
                                        controller.updateCart(item.value);
                                        item.refresh();
                                      },
                                      middleText: "${item.value.qty ?? 0}");
                                })
                              ],
                            ),
                          ],
                        ),
                        AppDimen.h6.height,
                        const Divider(
                          color: AppColor.black5,
                        ),
                      ],
                    );
                  },
                );
              }),
            ),
          ),
          Padding(
            padding: EdgeInsets.all(AppDimen.h16),
            child: PrimaryButton(
              onPressed: () async {
                var search = controller.getLinkMenuBy(
                    product.productId ?? 0, available?.productDetailId);

                if (search.isNotEmpty) {
                  search.sort(
                      (a, b) => (a.orderNo ?? 0).compareTo(b.orderNo ?? 0));
                  OutletLinkMenu(
                    listLinkMenuMaster: search,
                    product: product,
                    variant: variant,
                    noteController: TextEditingController(),
                  ).toModalBottomSheet.of(context);
                }
              },
              width: double.infinity,
              type: PrimaryButtonType.type4,
              child: SizedBox(
                width: double.infinity,
                child: Align(
                  alignment: Alignment.center,
                  child: Text(
                    Strings.addAnother.tr,
                    style:
                        AppFont.componentSmall.copyWith(color: AppColor.white),
                  ),
                ),
              ),
            ),
          )
        ],
      ),
    );
  }
}
