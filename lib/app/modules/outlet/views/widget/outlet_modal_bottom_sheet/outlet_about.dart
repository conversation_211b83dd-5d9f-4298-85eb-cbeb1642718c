import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/outlet/controllers/outlet_controller.dart';
import 'package:mobile_crm/app/utils/toast.dart';
import 'package:mobile_crm/app/utils/url_helper.dart';
import 'package:mobile_crm/app/widget/order_type/order_type_widget.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/values.dart';
import 'package:mobile_crm/data/models/order_model.dart';
import 'package:mobile_crm/data/providers/db/database.dart';

import '../../../../../widget/app_dotted_separator.dart';
import 'outlet_review.dart';

class OutletAbout extends GetView<OutletController> {
  const OutletAbout({super.key, required this.outlet});

  final Outlet outlet;

  @override
  Widget build(BuildContext context) {
    int milis = 900;
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: EdgeInsets.symmetric(horizontal: AppDimen.h16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Flexible(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        outlet.name ?? '',
                        style: AppFont.componentSmallBold,
                      ),
                      AppDimen.h2.height,
                      Text(
                        outlet.address ?? '',
                        style: AppFont.componentSmall
                            .copyWith(color: AppColor.black90, fontSize: 11.sp),
                      )
                    ],
                  ),
                ),
                AppDimen.h10.width,
                Row(
                  children: [
                    IconButton(
                      onPressed: () {
                        if (outlet.latitude == null &&
                            outlet.longitude == null) {
                          Toast.show("Directions not found",
                              type: ToastType.dark);
                          return;
                        }
                        openMap(outlet.latitude ?? 0, outlet.longitude ?? 0);
                      },
                      icon: Icon(
                        Icons.directions,
                        color: AppColor.black90,
                        size: AppDimen.h20,
                      ),
                    ),
                    IconButton(
                      onPressed: () {
                        openNumber(outlet.phone ?? "0");
                      },
                      icon: Icon(
                        Icons.call,
                        color: AppColor.black90,
                        size: AppDimen.h20,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          AppDimen.h10.height,
          Padding(
            padding: EdgeInsets.symmetric(horizontal: AppDimen.h16),
            child: Align(
                alignment: Alignment.centerLeft,
                child: OrderTypeWidget(
                    orderTypeModel: outlet.order_type ?? OrderTypeModel())),
          ),
          AppDimen.h4.height,
          const DottedDivider(
            color: AppColor.black10,
            width: 3,
            height: 2,
          ),
          AppDimen.h10.height,
          Padding(
            padding: EdgeInsets.symmetric(horizontal: AppDimen.h16),
            child: FutureBuilder(
              future: controller.repoTrans
                  .getRatingSummary(outletId: outlet.outlet_id ?? 0),
              builder: (context, rating) {
                if (rating.hasData) {
                  return Visibility(
                    visible: (rating.data?.data?.count ?? 0) > 0,
                    child: InkWell(
                      onTap: () {
                        OutletReview(outlet: outlet, ratingSummary: rating.data?.data,)
                            .toFullDialog
                            .of(context);
                      },
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            "Rating",
                            style: AppFont.componentSmallBold,
                          ),
                          AppDimen.h10.height,
                          Row(
                            children: [
                              Icon(
                                Icons.star,
                                color: Colors.orangeAccent,
                                size: AppDimen.h18,
                              ),
                              AppDimen.h4.width,
                              Text(
                                "${rating.data?.data?.rating ?? 0.0}",
                                style: AppFont.componentMediumBold
                                    .copyWith(fontSize: 16.sp),
                              ),
                            ],
                          ),
                          AppDimen.h2.height,
                          Text(
                            "${rating.data?.data?.count ?? 0} Ratings",
                            style: AppFont.componentSmall,
                          ),
                        ],
                      ).fadeIn(),
                    ),
                  );
                }
                return const SizedBox();
              },
            ),
          ),
          AppDimen.h16.height,
          Padding(
            padding: EdgeInsets.symmetric(horizontal: AppDimen.h16),
            child: Align(
              alignment: Alignment.centerLeft,
              child: Text(
                Strings.openingHours.tr,
                style: AppFont.componentSmallBold,
              ),
            ),
          ),
          AppDimen.h10.height,
          Padding(
            padding: EdgeInsets.symmetric(horizontal: AppDimen.h16),
            child: ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: outlet.business_hour?.length ??
                  (outlet.workingHour?.length ?? 0),
              itemBuilder: (context, index) {
                var detail = outlet.business_hour != null
                    ? outlet.business_hour![index]
                    : outlet.workingHour![index];
                milis = milis - 100;
                return Container(
                  margin: const EdgeInsets.only(bottom: 10),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.calendar_today,
                        color: (detail.day ?? '').dayIsToday
                            ? AppColor.black90
                            : AppColor.black50,
                      ),
                      AppDimen.h8.width,
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            "${detail.day}".capitalizeFirst?.tr.toUpperCase() ??
                                '',
                            style: (detail.day ?? '').dayIsToday
                                ? AppFont.componentSmallBold
                                : AppFont.componentSmallBold
                                    .copyWith(color: AppColor.black50),
                          ),
                          Text(
                            "${detail.time_open?.substring(0, 5)} - ${detail.time_close?.substring(0, 5)}",
                            style: (detail.day ?? '').dayIsToday
                                ? AppFont.componentSmall
                                    .copyWith(color: AppColor.black90)
                                : AppFont.componentSmall
                                    .copyWith(color: AppColor.black30),
                          ),
                        ],
                      ),
                    ],
                  ),
                ).fadeIn(duration: Duration(milliseconds: milis));
              },
            ),
          )
        ],
      ),
    );
  }
}
