import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/helper/product_helper.dart';
import 'package:mobile_crm/app/modules/outlet/controllers/outlet_controller.dart';
import 'package:mobile_crm/app/modules/outlet/views/widget/outlet_modal_bottom_sheet/outlet_link_menu.dart';
import 'package:mobile_crm/app/modules/outlet/views/widget/outlet_modal_bottom_sheet/outlet_unit_conversion.dart';
import 'package:mobile_crm/app/widget/cached_image_widget.dart';
import 'package:mobile_crm/app/widget/html_widget.dart';
import 'package:mobile_crm/app/widget/product_item/product_photo_widget.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/theme/app_color.dart';
import 'package:mobile_crm/core/theme/app_dimen.dart';
import 'package:mobile_crm/core/theme/app_font.dart';
import 'package:mobile_crm/core/values/app_constants.dart';
import 'package:mobile_crm/core/values/app_strings.dart';

import '../../../../../../data/models/available_model.dart';
import '../../../../../../data/models/variant_model.dart';
import '../../../../../../data/providers/db/database.dart';
import '../../../../../../data/services/analytics_service.dart';
import '../../../../../utils/app_alert.dart';
import '../../../../../utils/logger.dart';
import '../../../../../utils/share_helper.dart';
import '../../../../../utils/toast.dart';
import '../../../../../widget/button_widget/add_order_button_widget.dart';
import '../../../../../widget/button_widget/minus_plus_button_widget.dart';
import '../../../../../widget/button_widget/primary_button.dart';
import '../../../../../widget/custom_circular_progress_widget.dart';
import '../../../../../widget/modal_bottom_sheet/wishlist_bottom_sheet.dart';
import '../../../../screen_wrapper/controller/wrapper_controller.dart';
import 'outlet_cart_link_menu.dart';

class OutletProductDetailModalBottomSheet extends GetView<OutletController> {
  const OutletProductDetailModalBottomSheet(
      {super.key, required this.product, this.cart});

  final Product product;
  final Cart? cart;

  @override
  Widget build(BuildContext context) {
    final wrapperController = Get.find<WrapperController>();
    AvailableModel? available =
        controller.isProductAvailableOnThisOutlet(product);
    var lowHighPrice = controller.getLowHighPrice(product);
    var isShow = false.obs;
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: Constants.defaultMaxWidth,
          height: AppDimen.h128 + AppDimen.h128,
          child: Stack(
            children: [
              CachedImageWidget(
                imageUrl: product.photo,
                width: Constants.defaultMaxWidth,
                height: AppDimen.h128 + AppDimen.h128,
                disable: available?.enableOrder?.status == 'disable',
                fit: BoxFit.cover,
              ),
              ClipRRect(
                child: BackdropFilter(
                  filter: ImageFilter.blur(sigmaY: 40, sigmaX: 40),
                  child: Container(
                    alignment: Alignment.center,
                    child: ProductPhotoWidget(
                      imageUrl: product.photo,
                      disable: available?.enableOrder?.status == 'disable',
                      size: Size.square(AppDimen.h128 + AppDimen.h64),
                    ).fadeIn(),
                  ),
                ),
              )
            ],
          ),
        ),
        AppDimen.h8.height,
        Expanded(
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: AppDimen.h16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          product.name ?? '',
                          style: AppFont.componentMediumBold,
                        ),
                        Text(
                          Strings.unit.trParams({'unit': product.unit ?? '-'}),
                          style: AppFont.componentSmall,
                        ),
                      ],
                    ),
                    Row(
                      children: [
                        IconButton(
                          onPressed: () {
                            shareProduct(product);
                          },
                          icon: const Icon(
                            Icons.share,
                            color: AppColor.black70,
                          ),
                          padding: const EdgeInsets.all(0),
                        ),
                        Obx(
                          () => controller.isLoadingAddRemoveWishlist.value
                              ? IconButton(
                                  onPressed: () {},
                                  icon: const CustomCircularProgressIndicator(
                                    backgroundColor: Colors.transparent,
                                    valueColor: AppColor.black90,
                                  ))
                              : product.is_in_wishlist ?? false
                                  ? IconButton(
                                      onPressed: () async {
                                        await controller
                                            .removeWishlist(product);
                                      },
                                      icon: const Icon(
                                        Icons.bookmark_added_rounded,
                                        color: AppColor.black70,
                                      ),
                                      padding: const EdgeInsets.all(0),
                                    )
                                  : IconButton(
                                      onPressed: () async {
                                        var result = await controller
                                            .addWishlist(product);
                                        if (result.status) {
                                          Get.back();
                                          WishlistBottomSheet
                                              .showWishlistOption(
                                                  context: context,
                                                  repo: controller.repoWishlist,
                                                  wishlist: result.data,
                                                  product: product);
                                        } else {
                                          Get.back();
                                        }
                                      },
                                      icon: const Icon(
                                        Icons.bookmark_add_rounded,
                                        color: AppColor.black70,
                                      ),
                                      padding: const EdgeInsets.all(0),
                                    ),
                        )
                      ],
                    ),
                  ],
                ),
                AppDimen.h4.height,
                Text(
                  product.subcategory ?? '',
                  style: AppFont.componentSmallBold
                      .copyWith(color: AppColor.black90),
                ),
                AppDimen.h2.height,
                Text(
                  (lowHighPrice.$1 != null && lowHighPrice.$2 != null)
                      ? "Rp${lowHighPrice.$1.toCurrency} - ${lowHighPrice.$2.toCurrency}"
                      : "Rp${(available?.priceSell ?? (product.price ?? 0)).toCurrency}",
                  style: AppFont.componentMediumBold,
                ),
                const Divider(color: AppColor.black5),
                Visibility(
                  visible: (product.description ?? '').isNotEmpty,
                  child: InkWell(
                    splashFactory: NoSplash.splashFactory,
                    onTap: () {
                      isShow.value = !isShow.value;
                    },
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(Strings.description.tr,
                            style: AppFont.componentSmallBold),
                        Obx(() {
                          return Icon(
                            isShow.value
                                ? Icons.keyboard_arrow_up_rounded
                                : Icons.keyboard_arrow_down_rounded,
                            size: AppDimen.h20,
                            color: AppColor.black90,
                          ).fadeIn();
                        })
                      ],
                    ),
                  ),
                ),
                Obx(() {
                  return isShow.value
                      ? HtmlWidget(
                          data: product.description ?? '',
                          fontSize: 12,
                          maxLines: 8,
                        ).fadeIn()
                      : const SizedBox();
                }),
                AppDimen.h4.height,
                (product.variant != null &&
                        (product.variant?.isNotEmpty == true))
                    ? Expanded(
                        child: Obx(() {
                          controller.newOrder.value;
                          var listVariant =
                              controller.filterProductVariant(product.variant);
                          listVariant.sort(
                              (a, b) => (a.name ?? '').compareTo(b.name ?? ''));

                          var listUnit = controller.listUnitConversion
                              .where((element) =>
                                  element.productFkId == product.productId)
                              .toList();
                          return Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Text(Strings.chooseVariant.tr,
                                  style: AppFont.componentSmallBold),
                              AppDimen.h4.height,
                              Expanded(
                                child: ListView.builder(
                                    shrinkWrap: true,
                                    itemCount: listVariant.length,
                                    itemBuilder: (context, indxx) {
                                      VariantModel? variant =
                                          listVariant[indxx];
                                      Cart? cart =
                                          controller.isVariantOnCart(variant);
                                      AvailableModel? availableVariant =
                                          controller.isProductVariantAvailable(
                                              variant);
                                      return Column(
                                        mainAxisAlignment:
                                            MainAxisAlignment.start,
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Row(
                                            mainAxisSize: MainAxisSize.min,
                                            mainAxisAlignment:
                                                MainAxisAlignment.start,
                                            crossAxisAlignment:
                                                CrossAxisAlignment.center,
                                            children: [
                                              Expanded(
                                                child: Column(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.start,
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  mainAxisSize:
                                                      MainAxisSize.min,
                                                  children: [
                                                    Text(
                                                      '${variant.name}',
                                                      style: AppFont
                                                          .componentSmallBold,
                                                    ),
                                                    Text(
                                                      "Rp${availableVariant?.priceSell.toCurrency}",
                                                      style: AppFont
                                                          .componentSmall,
                                                    ),
                                                  ],
                                                ),
                                              ),
                                              ProductHelper.isProductAvailableOnHours(
                                                      product)
                                                  ? cart == null
                                                      ? availableVariant
                                                                  ?.enableOrder?.status ==
                                                              "enable"
                                                          ? AddOrderButtonWidget(
                                                              onTap: () async {
                                                                var search = controller.getLinkMenuBy(
                                                                    product.productId ??
                                                                        0,
                                                                    availableVariant
                                                                        ?.productDetailId);
                                                                if (search
                                                                    .isNotEmpty) {
                                                                  search.sort((a,
                                                                          b) =>
                                                                      (a.orderNo ??
                                                                              0)
                                                                          .compareTo(b.orderNo ??
                                                                              0));
                                                                  OutletLinkMenu(
                                                                    listLinkMenuMaster:
                                                                        search,
                                                                    noteController:
                                                                        TextEditingController(),
                                                                    variant:
                                                                        variant,
                                                                    product:
                                                                        product,
                                                                  ).toModalBottomSheet.of(
                                                                      context);
                                                                } else {
                                                                  await controller.addToCart(
                                                                      availableVariant
                                                                              ?.productDetailId ??
                                                                          0,
                                                                      product:
                                                                          product);
                                                                  Get.back();
                                                                }
                                                              },
                                                              text: 'Add',
                                                              disable: availableVariant
                                                                      ?.enableOrder?.status == 'disable',
                                                            ).fadeIn(
                                                              duration:
                                                                  const Duration(
                                                                      milliseconds:
                                                                          300))
                                                          : AddOrderButtonWidget(
                                                              onTap: () {
                                                                controller
                                                                    .repoProduct
                                                                    .addNotifyProduct(
                                                                        productDetailId:
                                                                            availableVariant?.productDetailId ??
                                                                                0,
                                                                        qty: 1);
                                                              },
                                                              text: Strings
                                                                  .notifyMe.tr,
                                                              disable: true,
                                                            ).fadeIn(
                                                              duration:
                                                                  const Duration(
                                                                      milliseconds:
                                                                          300))
                                                      : (controller
                                                                      .newOrder
                                                                      .value
                                                                      .orderList
                                                                      ?.where((element) =>
                                                                          element
                                                                              .product_detail_fkid ==
                                                                          availableVariant
                                                                              ?.productDetailId)
                                                                      .where((element) =>
                                                                          element
                                                                              .linkMenu
                                                                              ?.isNotEmpty ==
                                                                          true)
                                                                      .length ??
                                                                  0) >
                                                              0
                                                          ? AddOrderButtonWidget(
                                                              onTap: () {
                                                                OutletCartLinkMenu(
                                                                  product:
                                                                      product,
                                                                  variant:
                                                                      variant,
                                                                  orderList: controller
                                                                          .newOrder
                                                                          .value
                                                                          .orderList
                                                                          ?.where((element) =>
                                                                              element.product_detail_fkid ==
                                                                              availableVariant
                                                                                  ?.productDetailId)
                                                                          .where((element) =>
                                                                              element.linkMenu?.isNotEmpty ==
                                                                              true)
                                                                          .toList() ??
                                                                      [],
                                                                )
                                                                    .toModalBottomSheetNoMinHeight
                                                                    .of(context);
                                                              },
                                                              text: '${controller.newOrder.value.sumQtyWhereProductDetailId(availableVariant?.productDetailId ?? 0)} Items')
                                                          : MinusPlusButtonWidget(
                                                              onTapMinus: () {
                                                                if (cart.qty !=
                                                                        null &&
                                                                    (cart.qty ??
                                                                            1) >
                                                                        1) {
                                                                  cart.qty =
                                                                      (cart.qty ??
                                                                              1) -
                                                                          1;
                                                                  controller
                                                                      .updateCart(
                                                                          cart);
                                                                  AnalyticsService
                                                                      .observer
                                                                      .analytics
                                                                      .logEvent(
                                                                          name:
                                                                              "update_cart",
                                                                          parameters: {
                                                                        "product_name": cart
                                                                            .product
                                                                            ?.name,
                                                                        "product_qty":
                                                                            cart.qty
                                                                      });
                                                                } else {
                                                                  AppAlert
                                                                      .showConfirmDeleteDialog(
                                                                    context,
                                                                    "${product.name}",
                                                                    () {
                                                                      controller
                                                                          .deleteCart(
                                                                              cart);
                                                                      Get.back();
                                                                    },
                                                                  );
                                                                }
                                                              },
                                                              onTapTools: () {
                                                                OutletUnitConversion(
                                                                  teController:
                                                                      TextEditingController(),
                                                                  product:
                                                                      product,
                                                                  conversion:
                                                                      (value) {
                                                                    cart.qty =
                                                                        value;
                                                                    controller
                                                                        .updateCart(
                                                                            cart);
                                                                    Get.back();
                                                                  },
                                                                  listUnit: controller
                                                                      .listUnitConversion
                                                                      .where((element) =>
                                                                          element
                                                                              .productFkId ==
                                                                          product
                                                                              .productId)
                                                                      .toList(),
                                                                )
                                                                    .toModalBottomSheetNoMinHeight
                                                                    .of(context);
                                                              },
                                                              onChanged: (value) {},
                                                              textEditingController: TextEditingController(text: '${cart.qty ?? 0}'),
                                                              onTapPlus: () {
                                                                if (cart.qty !=
                                                                    null) {
                                                                  cart.qty =
                                                                      (cart.qty ??
                                                                              1) +
                                                                          1;
                                                                  controller
                                                                      .updateCart(
                                                                          cart);
                                                                }
                                                              },
                                                              listUnit: listUnit,
                                                              showTools: true,
                                                              middleText: '${cart.qty ?? 0}')
                                                  : AddOrderButtonWidget(
                                                      onTap: () {
                                                        Toast.show(
                                                            Strings
                                                                .productNotAvailable,
                                                            duration: 3,
                                                            type:
                                                                ToastType.dark);
                                                      },
                                                      text: Strings
                                                          .productNotAvailable
                                                          .tr,
                                                      disable: true,
                                                    ).fadeIn(duration: const Duration(milliseconds: 300))
                                            ],
                                          ),
                                          const Divider(
                                              thickness: 2,
                                              color: AppColor.black5),
                                        ],
                                      );
                                    }),
                              ),
                            ],
                          );
                        }),
                      )
                    : const SizedBox(),
                product.variant != null && (product.variant?.isNotEmpty == true)
                    ? const SizedBox()
                    : const Spacer(),
                (product.variant?.isNotEmpty == true && product.variant != null)
                    ? Container()
                    : Column(
                        children: [
                          15.0.height,
                          Obx(() {
                            Cart? cart = controller.isProductOnCart(product);

                            if (cart == null) {
                              if (ProductHelper.isProductAvailableOnHours(
                                  product)) {
                                if (available?.enableOrder?.status == "enable") {
                                  return PrimaryButton(
                                    onPressed: () async {
                                      var search = controller.getLinkMenuBy(
                                          product.productId ?? 0,
                                          available?.productDetailId);
                                      if (search.isNotEmpty) {
                                        search.sort((a, b) => (a.orderNo ?? 0)
                                            .compareTo(b.orderNo ?? 0));
                                        OutletLinkMenu(
                                          listLinkMenuMaster: search,
                                          noteController:
                                              TextEditingController(),
                                          product: product,
                                        ).toModalBottomSheet.of(context);
                                      } else {
                                        await controller.addToCart(
                                            available?.productDetailId ?? 0,
                                            product: product);
                                        Get.back();
                                      }
                                      // controller.addToCart(
                                      //     available?.productDetailId ?? 0,
                                      //     product: product);
                                    },
                                    text: "text",
                                    width: double.infinity,
                                    type: PrimaryButtonType.type4,
                                    child: SizedBox(
                                      width: double.infinity,
                                      child: Align(
                                          alignment: Alignment.center,
                                          child: Text(
                                            Strings.addToCart.tr,
                                            style: AppFont.componentSmall
                                                .copyWith(
                                                    color: AppColor.white),
                                          )),
                                    ),
                                  );
                                } else {
                                  return PrimaryButton(
                                    onPressed: () {
                                      controller.repoProduct.addNotifyProduct(
                                          productDetailId:
                                              available?.productDetailId ?? 0,
                                          qty: 1);
                                    },
                                    text: Strings.notifyMe.tr,
                                    width: double.infinity,
                                    type: PrimaryButtonType.type2,
                                  );
                                }
                              } else {
                                return PrimaryButton(
                                  onPressed: () {
                                    Toast.show(Strings.productNotAvailable,
                                        duration: 3, type: ToastType.dark);
                                  },
                                  text: Strings.productNotAvailable.tr,
                                  width: double.infinity,
                                  type: PrimaryButtonType.type2,
                                );
                              }
                            } else {
                              return (controller.newOrder.value.orderList
                                              ?.where((element) =>
                                                  element.product_detail_fkid ==
                                                  available?.productDetailId)
                                              .where((element) =>
                                                  element
                                                      .linkMenu?.isNotEmpty ==
                                                  true)
                                              .length ??
                                          0) >
                                      0
                                  ? PrimaryButton(
                                      onPressed: () {
                                        OutletCartLinkMenu(
                                          product: product,
                                          orderList: controller
                                                  .newOrder.value.orderList
                                                  ?.where((element) =>
                                                      element
                                                          .product_detail_fkid ==
                                                      available
                                                          ?.productDetailId)
                                                  .where((element) =>
                                                      element.linkMenu
                                                          ?.isNotEmpty ==
                                                      true)
                                                  .toList() ??
                                              [],
                                        )
                                            .toModalBottomSheetNoMinHeight
                                            .of(context);
                                      },
                                      text:
                                          '${controller.newOrder.value.sumQtyWhereProductId(product.productId ?? 0)} Items',
                                      width: double.infinity,
                                      type: PrimaryButtonType.type4,
                                      child: SizedBox(
                                        width: double.infinity,
                                        child: Align(
                                          alignment: Alignment.center,
                                          child: Text(
                                            '${controller.newOrder.value.sumQtyWhereProductId(product.productId ?? 0)} Items',
                                            style: AppFont.componentSmall
                                                .copyWith(
                                                    color: AppColor.white),
                                          ),
                                        ),
                                      ),
                                    ).fadeIn(
                                      duration:
                                          const Duration(milliseconds: 300))
                                  : Column(
                                      mainAxisAlignment:
                                          MainAxisAlignment.start,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Visibility(
                                          visible: controller.listUnitConversion
                                              .where((element) =>
                                                  element.productFkId ==
                                                  product.productId)
                                              .toList()
                                              .isNotEmpty,
                                          child: Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              InkWell(
                                                onTap: () {
                                                  OutletUnitConversion(
                                                    teController:
                                                        TextEditingController(),
                                                    product: product,
                                                    conversion: (value) {
                                                      cart.qty = value;
                                                      controller
                                                          .updateCart(cart);
                                                      Get.back();
                                                    },
                                                    listUnit: controller
                                                        .listUnitConversion
                                                        .where((element) =>
                                                            element
                                                                .productFkId ==
                                                            product.productId)
                                                        .toList(),
                                                  )
                                                      .toModalBottomSheetNoMinHeight
                                                      .of(context);
                                                },
                                                child: Text(
                                                  'Unit Conversion',
                                                  style: AppFont
                                                      .componentSmallBold
                                                      .copyWith(
                                                          color: wrapperController
                                                              .getPrimaryColor()),
                                                ),
                                              ).fadeIn(),
                                              const VerticalDivider(
                                                  color: AppColor.black10,
                                                  width: 2,
                                                  thickness: 2,
                                                  indent: 2),
                                              MinusPlusButtonWidget(
                                                  onTapMinus: () {
                                                    if (cart.qty != null &&
                                                        (cart.qty ?? 1) > 1) {
                                                      cart.qty =
                                                          (cart.qty ?? 1) - 1;
                                                      controller
                                                          .updateCart(cart);
                                                    } else {
                                                      AppAlert
                                                          .showConfirmDeleteDialog(
                                                        context,
                                                        "${cart.product?.name}",
                                                        () {
                                                          controller
                                                              .deleteCart(cart);
                                                          Get.back();
                                                        },
                                                      );
                                                    }
                                                  },
                                                  onTapPlus: () {
                                                    if (cart.qty != null) {
                                                      cart.qty =
                                                          (cart.qty ?? 1) + 1;
                                                      controller
                                                          .updateCart(cart);
                                                    }
                                                  },
                                                  textEditingController:
                                                      TextEditingController(),
                                                  middleText: "${cart.qty}"),
                                            ],
                                          ),
                                        ),
                                        AppDimen.h8.height,
                                        PrimaryButton(
                                          onPressed: () async {
                                            controller.deleteCart(cart);

                                            try {
                                              AnalyticsService
                                                  .observer.analytics
                                                  .logEvent(
                                                      name: "remove_from_cart",
                                                      parameters: {
                                                    "product_name":
                                                        product.name,
                                                    "product_detail_id": (product
                                                                .product_detail_id ??
                                                            product
                                                                .productDetailFkId)
                                                        .toString(),
                                                  });
                                            } catch (e, s) {
                                              errorLogger(
                                                  pos: "add cart",
                                                  error: e,
                                                  stackTrace: s);
                                            }
                                          },
                                          text: "text",
                                          width: double.infinity,
                                          type: PrimaryButtonType.type4,
                                          child: SizedBox(
                                            width: double.infinity,
                                            child: Align(
                                              alignment: Alignment.center,
                                              child: Text(
                                                Strings.removeFromCart.tr,
                                                style: AppFont.componentSmall
                                                    .copyWith(
                                                        color: AppColor.white),
                                              ),
                                            ),
                                          ),
                                        ),
                                      ],
                                    );
                            }
                          }),
                        ],
                      ),
                AppDimen.h10.height,
              ],
            ),
          ),
        ),
      ],
    );
  }
}
