import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/helper/product_helper.dart';
import 'package:mobile_crm/app/modules/outlet/controllers/outlet_controller.dart';
import 'package:mobile_crm/app/widget/button_widget/button_widget.dart';
import 'package:mobile_crm/app/widget/button_widget/minus_plus_button_widget.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/values.dart';
import 'package:mobile_crm/data/models/variant_model.dart';
import 'package:mobile_crm/data/providers/db/database.dart';

import '../../../../../../data/models/available_model.dart';
import '../../../../../../data/models/product/link_menu_detail_model.dart';
import '../../../../../../data/models/product/link_menu_model.dart';
import '../../../../../widget/app_dotted_separator.dart';
import '../../../../../widget/check_radio_box/labeled_checkbox.dart';
import '../../../../../widget/check_radio_box/labeled_radio.dart';

class OutletLinkMenu extends GetView<OutletController> {
  const OutletLinkMenu(
      {super.key,
      required this.listLinkMenuMaster,
      required this.product,
      required this.noteController,
      this.variant});

  final List<LinkMenuModel> listLinkMenuMaster;
  final Product product;
  final TextEditingController noteController;
  final VariantModel? variant;

  @override
  Widget build(BuildContext context) {
    var productQty = 1.obs;
    AvailableModel? available = variant != null
        ? controller.isProductVariantAvailable(variant ?? VariantModel())
        : controller.isProductAvailableOnThisOutlet(product);
    List<LinkMenuModel> listLinkMenu = listLinkMenuMaster.toList();

    var sumItemsPrice =
        (((available?.priceSell ?? product.price ?? 0) * productQty.value) +
                listLinkMenu.fold(
                    0,
                    (previousValue, element) =>
                        previousValue +
                        element.sumAdditionalPrice(productQty.value)))
            .toInt()
            .obs;
    LinkMenuDetailModel? selectedLinkMenuDetail;
    return Padding(
      padding:
          EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.symmetric(horizontal: AppDimen.h16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  variant != null
                      ? "${product.name} (${variant?.name ?? '-'})"
                      : "${product.name}",
                  style: AppFont.componentMediumBold,
                ),
                Text(
                  (available?.priceSell ?? product.price).toCurrency,
                  style: AppFont.componentMediumBold,
                ),
              ],
            ),
          ),
          AppDimen.h6.height,
          const Divider(
            color: AppColor.black10,
          ),
          Flexible(
            child: SingleChildScrollView(
              child: MediaQuery.removePadding(
                context: context,
                removeTop: true,
                child: ListView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: listLinkMenu.length,
                  itemBuilder: (context, index) {
                    var linkMenu = listLinkMenu[index].obs;
                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding:
                              EdgeInsets.symmetric(horizontal: AppDimen.h16),
                          child: Text(
                            linkMenu.value.name ?? '',
                            style: AppFont.componentSmallBold,
                          ),
                        ),
                        Padding(
                          padding:
                              EdgeInsets.symmetric(horizontal: AppDimen.h16),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                linkMenu.value.isMultipleChoice == 0
                                    ? Strings.selectOne.tr
                                    : Strings.selectMultiple.tr,
                                style: AppFont.componentSmall.copyWith(
                                    color: AppColor.black70, fontSize: 11.sp),
                              ),
                              Text(
                                linkMenu.value.description ?? '-',
                                style: AppFont.componentSmall
                                    .copyWith(color: AppColor.black70),
                              ),
                            ],
                          ),
                        ),
                        AppDimen.h2.height,
                        Padding(
                          padding:
                              EdgeInsets.symmetric(horizontal: AppDimen.h16),
                          child: const DottedDivider(
                              width: 3, height: 2, color: AppColor.black5),
                        ),
                        AppDimen.h2.height,
                        Padding(
                          padding:
                              EdgeInsets.symmetric(horizontal: AppDimen.h16),
                          child: Obx(() {
                            return ListView.builder(
                              shrinkWrap: true,
                              physics: const NeverScrollableScrollPhysics(),
                              itemCount:
                                  linkMenu.value.linkMenuDetail?.length ?? 0,
                              itemBuilder: (context, indexChild) {
                                var linkMenuDetail =
                                    linkMenu.value.linkMenuDetail![indexChild];
                                var product =
                                    ProductHelper.getProductByDetailId(
                                        products: controller.listProduct,
                                        productDetailId:
                                            linkMenuDetail.productDetailFkId ??
                                                0);
                                return Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    linkMenu.value.isMultipleChoice == 0
                                        ? LabeledRadio<LinkMenuDetailModel?>(
                                            label: Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment
                                                        .spaceBetween,
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                mainAxisSize: MainAxisSize.min,
                                                children: [
                                                  Text(
                                                    product
                                                                ?.variant
                                                                ?.firstOrNull
                                                                ?.name !=
                                                            null
                                                        ? "${product?.name ?? '-'} (${product?.variant?.firstOrNull?.name})"
                                                        : product?.name ?? '-',
                                                    style:
                                                        AppFont.componentSmall,
                                                  ),
                                                  Text(
                                                    "+${(linkMenuDetail.priceAdd ?? 0).toCurrency}",
                                                    style: AppFont
                                                        .componentSmall
                                                        .copyWith(
                                                            fontSize: 11.sp),
                                                  )
                                                ]),
                                            onChanged: (value) {
                                              selectedLinkMenuDetail = value;
                                              linkMenu.value.setChosenMenuDetail(
                                                  true,
                                                  linkMenuDetail
                                                          .linkMenuDetailId ??
                                                      0,
                                                  productName: (product
                                                              ?.variant !=
                                                          null)
                                                      ? "${product?.name} (${product?.variant?.firstOrNull?.name ?? '-'})"
                                                      : product?.name ?? '-');
                                              linkMenu.refresh();
                                              sumItemsPrice.value = (((available
                                                              ?.priceSell ??
                                                          product?.price ??
                                                          0) *
                                                      productQty.value) +
                                                  listLinkMenu.fold(
                                                      0,
                                                      (previousValue,
                                                              element) =>
                                                          (previousValue) +
                                                          element
                                                              .sumAdditionalPrice(
                                                                  productQty
                                                                      .value)));
                                            },
                                            defaultValue: linkMenuDetail,
                                            groupValue: selectedLinkMenuDetail,
                                            padding: EdgeInsets.zero)
                                        : LabeledCheckbox(
                                            label: Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment
                                                        .spaceBetween,
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                mainAxisSize: MainAxisSize.min,
                                                children: [
                                                  Text(
                                                    product
                                                                ?.variant
                                                                ?.firstOrNull
                                                                ?.name !=
                                                            null
                                                        ? "${product?.name ?? '-'} (${product?.variant?.firstOrNull?.name})"
                                                        : product?.name ?? '-',
                                                    style:
                                                        AppFont.componentSmall,
                                                  ),
                                                  Text(
                                                    "+${(linkMenuDetail.priceAdd ?? 0).toCurrency}",
                                                    style: AppFont
                                                        .componentSmall
                                                        .copyWith(
                                                            fontSize: 11.sp),
                                                  )
                                                ]),
                                            padding: EdgeInsets.zero,
                                            value: linkMenuDetail.isChosen ??
                                                false,
                                            onChanged: (value) {
                                              linkMenu.value.setChosenMenuDetail(
                                                  value,
                                                  linkMenuDetail
                                                          .linkMenuDetailId ??
                                                      0,
                                                  productName: (product
                                                              ?.variant !=
                                                          null)
                                                      ? "${product?.name} (${product?.variant?.firstOrNull?.name ?? '-'})"
                                                      : product?.name ?? '-');
                                              linkMenu.refresh();
                                              sumItemsPrice.value = (((available
                                                              ?.priceSell ??
                                                          product?.price ??
                                                          0) *
                                                      productQty.value) +
                                                  listLinkMenu.fold(
                                                      0,
                                                      (previousValue,
                                                              element) =>
                                                          (previousValue) +
                                                          element
                                                              .sumAdditionalPrice(
                                                                  productQty
                                                                      .value)));
                                            },
                                          ),
                                    indexChild ==
                                            ((linkMenu.value.linkMenuDetail
                                                        ?.length ??
                                                    1) -
                                                1)
                                        ? const SizedBox()
                                        : const Divider(
                                            color: AppColor.black5,
                                          )
                                  ],
                                );
                              },
                            );
                          }),
                        ),
                        AppDimen.h4.height,
                        Divider(
                            color: AppColor.black5,
                            height: AppDimen.h6,
                            thickness: AppDimen.h6),
                        AppDimen.h10.height,
                      ],
                    );
                  },
                ),
              ),
            ),
          ),
          const Divider(color: AppColor.black5),
          AppDimen.h4.height,
          Padding(
            padding: EdgeInsets.symmetric(horizontal: AppDimen.h16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Text(
                  Strings.notes.tr,
                  style: AppFont.componentSmallBold,
                ),
                Text(
                  Strings.optional.tr,
                  style: AppFont.componentSmall,
                ),
                Row(
                  children: [
                    const Icon(
                      Icons.description,
                      color: AppColor.black90,
                    ),
                    AppDimen.h8.width,
                    Flexible(
                      child: TextFormField(
                          key: key,
                          keyboardType: TextInputType.text,
                          controller: noteController,
                          style: AppFont.componentSmall,
                          minLines: 1,
                          autofocus: false,
                          decoration: InputDecoration(
                              hintText: Strings.exampleNoteTo.tr,
                              border: InputBorder.none)),
                    ),
                  ],
                ),
              ],
            ),
          ),
          AppDimen.h8.height,
          const Divider(
            color: AppColor.black5,
            height: 4,
            thickness: 4,
          ),
          AppDimen.h6.height,
          Padding(
            padding: EdgeInsets.symmetric(horizontal: AppDimen.h16),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      Strings.productQuantity.tr,
                      style: AppFont.componentSmallBold,
                    ),
                    Obx(() {
                      return MinusPlusButtonWidget(
                          textEditingController: TextEditingController(
                              text: '${productQty.value}'),
                          onTapMinus: () {
                            if (productQty.value > 1) {
                              productQty.value -= 1;
                              sumItemsPrice.value = (((available?.priceSell ??
                                          product.price ??
                                          0) *
                                      productQty.value) +
                                  listLinkMenu.fold(
                                      0,
                                      (previousValue, element) =>
                                          (previousValue) +
                                          element.sumAdditionalPrice(
                                              productQty.value)));
                            }
                          },
                          onTapPlus: () {
                            productQty.value += 1;
                            sumItemsPrice.value =
                                (((available?.priceSell ?? product.price ?? 0) *
                                        productQty.value) +
                                    listLinkMenu.fold(
                                        0,
                                        (previousValue, element) =>
                                            (previousValue) +
                                            element.sumAdditionalPrice(
                                                productQty.value)));
                          },
                          middleText: "${productQty.value}");
                    })
                  ],
                ),
                AppDimen.h10.height,
                PrimaryButton(
                  onPressed: () {
                    controller.addToCart(available?.productDetailId ?? 0,
                        product: product,
                        links: listLinkMenu,
                        qty: productQty.value,
                        note: noteController.text);
                    Get.back();
                  },
                  text: "text",
                  width: double.infinity,
                  type: PrimaryButtonType.type4,
                  child: SizedBox(
                    width: double.infinity,
                    child: Align(
                        alignment: Alignment.center,
                        child: Obx(() {
                          return Text(
                            "${Strings.addToCart.tr} - ${sumItemsPrice.value.toCurrency}",
                            style: AppFont.componentSmallBold
                                .copyWith(color: AppColor.white),
                          );
                        })),
                  ),
                ),
              ],
            ),
          ),
          AppDimen.h10.height,
        ],
      ),
    );
  }
}
