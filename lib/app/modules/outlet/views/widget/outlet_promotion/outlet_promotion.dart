import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/outlet/controllers/outlet_controller.dart';
import 'package:mobile_crm/app/modules/screen_wrapper/controller/wrapper_controller.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/values.dart';

import '../../../../../widget/modal_bottom_sheet/promotion_modal_bottom_sheet.dart';
import 'outlet_promotion_loading.dart';

class OutletPromotionWidget extends StatelessWidget {
  const OutletPromotionWidget(
      {Key? key, required this.controller, required this.wrapperController})
      : super(key: key);
  final OutletController controller;
  final WrapperController wrapperController;

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
      future: controller.getAllPromos(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const OutletPromotionLoading();
        }
        if (snapshot.hasData) {
          if (snapshot.data?.isEmpty == true) {
            return const SizedBox();
          }
          return InkWell(
            onTapDown: (details) => PromotionModalBottomSheet.show(
                context: context, listPromo: snapshot.data ?? []),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: AppColor.white,
                  border: Border.all(color: AppColor.black5),
                  borderRadius: BorderRadius.circular(10.r)),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Container(
                          width: AppDimen.h16,
                          height: AppDimen.h16,
                          padding: const EdgeInsets.all(4),
                          decoration: const BoxDecoration(
                              color: AppColor.couponBGYellow,
                              shape: BoxShape.circle),
                          child: SizedBox(
                              width: AppDimen.h14,
                              height: AppDimen.h14,
                              child: Image.asset(ImgStrings.coupon)),
                        ),
                        10.0.width,
                        Text(
                          Strings.sumPromosAreAvailable.trParams(
                              {'sum': "${snapshot.data?.length ?? 0}"}),
                          style: AppFont.componentSmallBold,
                        ),
                        const Spacer(),
                        Icon(
                          Icons.arrow_circle_right_rounded,
                          color: wrapperController.getPrimaryColor(),
                        )
                      ],
                    ),
                  ),
                  const Divider(
                    color: AppColor.black5,
                  ),
                  MediaQuery.removePadding(
                    context: context,
                    removeTop: true,
                    child: ListView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: (snapshot.data?.length ?? 0) > 2
                          ? 2
                          : snapshot.data?.length ?? 0,
                      itemBuilder: (context, index) {
                        var detail = snapshot.data![index];
                        return Container(
                          margin: const EdgeInsets.only(bottom: 4),
                          child: Row(
                            children: [
                              SizedBox(
                                width: AppDimen.h14,
                                height: AppDimen.h14,
                                child: Image.asset(detail.promotionTypeName
                                            ?.contains('free') ==
                                        true
                                    ? ImgStrings.couponFree
                                    : detail.promotionTypeName
                                                ?.contains('discount') ==
                                            true
                                        ? ImgStrings.couponDiscount
                                        : ImgStrings.couponSpecial),
                              ),
                              10.0.width,
                              Text(
                                "${detail.name}",
                                style: AppFont.componentSmallBold
                                    .copyWith(color: AppColor.black90),
                              ),
                            ],
                          ),
                        );
                      },
                    ),
                  )
                ],
              ),
            ),
          );
        }

        if (snapshot.hasError) {
          return const SizedBox();
        }

        return const OutletPromotionLoading();
      },
    );
  }
}
