import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mobile_crm/app/utils/utils.dart';
import 'package:mobile_crm/app/widget/shimmer_loading_widget.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';

class OutletPromotionLoading extends StatelessWidget {
  const OutletPromotionLoading({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: AppColor.white,
          border: Border.all(color: AppColor.black5),
          borderRadius: BorderRadius.circular(10.r)),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 8.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                ShimmerWidget(
                    height: AppDimen.h14,
                    width: AppDimen.h14,
                    radius: AppDimen.h18),
                10.0.width,
                ShimmerWidget(
                    height: AppDimen.h12,
                    width:
                        randomRange(AppDimen.h64.toInt(), AppDimen.h128.toInt())
                            .toDouble(),
                    radius: AppDimen.h18),
                const Spacer(),
                ShimmerWidget(
                    height: AppDimen.h14,
                    width: AppDimen.h14,
                    radius: AppDimen.h18),
              ],
            ),
          ),
          const Divider(
            color: AppColor.black5,
          ),
          MediaQuery.removePadding(
            context: context,
            removeTop: true,
            child: ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: 2,
              itemBuilder: (context, index) {
                return Container(
                  margin: const EdgeInsets.only(bottom: 4),
                  child: Row(
                    children: [
                      ShimmerWidget(
                          height: AppDimen.h12,
                          width: AppDimen.h12,
                          radius: AppDimen.h12),
                      10.0.width,
                      ShimmerWidget(
                          height: AppDimen.h12,
                          width: randomRange(
                                  AppDimen.h64.toInt(), AppDimen.h128.toInt())
                              .toDouble(),
                          radius: AppDimen.h18),
                    ],
                  ),
                );
              },
            ),
          )
        ],
      ),
    );
  }
}
