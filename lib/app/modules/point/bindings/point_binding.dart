import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/point/controllers/point_controller.dart';
import 'package:mobile_crm/data/repository/membership_repository.dart';
import 'package:mobile_crm/data/repository/user_repository.dart';

class PointBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => PointController());
    Get.lazyPut(() => MembershipRepositoryIml());
    Get.lazyPut(() => UserRepositoryIml());
  }
}
