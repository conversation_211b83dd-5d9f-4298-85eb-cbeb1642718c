import 'package:get/get.dart';
import 'package:mobile_crm/app/helper/membership_helper.dart';
import 'package:mobile_crm/app/utils/logger.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/data/models/user_model.dart';
import 'package:mobile_crm/data/providers/db/database.dart';
import 'package:mobile_crm/data/repository/membership_repository.dart';
import 'package:mobile_crm/data/repository/user_repository.dart';
import 'package:mobile_crm/data/services/local_storage.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

import '../../../../data/services/analytics_service.dart';

class PointController extends GetxController {
  final _repoMembership = Get.find<MembershipRepositoryIml>();
  final _repoUser = Get.find<UserRepositoryIml>();
  final store = LocalStorageService();

  var membership = Future.value(<MembershipData>[]).obs;
  var nextMemberShip = MembershipData().obs;
  var user = UserModel().obs;

  @override
  void onInit() {
    fetchData();
    Sentry.addBreadcrumb(Breadcrumb(
        type: 'debug',
        category: 'user.activity.point.init',
        level: SentryLevel.debug));
    super.onInit();
  }

  void fetchData() {
    fetchMembershipLevel();
    fetchUser();
  }

  void fetchMembershipLevel() async {
    membership.value = _repoMembership.getMembershipLocal().then((value) {
      nextMemberShip.value = getNextPoint(value) ?? MembershipData();
      return MembershipHelper.filterMembership(
          user: user.value, listMembership: value);
    });
  }

  void fetchUser() async {
    user.value = await _repoUser.getUser() ?? UserModel();
    AnalyticsService.observer.analytics
        .logEvent(name: "user_spend_point", parameters: {
      'total_spent': user.value.totalSpent,
      'point_remaining': user.value.totalPoint,
    });
  }

  MembershipData? getNextPoint(List<MembershipData> value) {
    value.sort((a, b) => (a.point_target ?? 0).compareTo(b.point_target ?? 0));
    // return value.last;
    var currentPoint =
        user.value.getCurrentTotalPoint() - (user.value.totalPointLost ?? 0);
    if(currentPoint.isNegative){
      currentPoint = 0;
    }

    for (var ms in value) {
      var index = value.indexOf(ms);
      var prevMs = index - 1;
      var nextMs = index + 1;

      if ((nextMs > (value.length - 1))) {
        nextMs = index;
      }

      if ((prevMs < 0)) {
        prevMs = index;
      }

      var targetPoint = ms.point_target ?? 0;
      var prevPoint = value[prevMs].point_target ?? 0;
      var nextPoint = value[nextMs].point_target ?? 0;

      infoLogger("pos 0",
          "${currentPoint.toCurrency} ${user.value.totalPoint} | ${prevPoint.toCurrency} | ${ms.point_target.toCurrency} ${ms.point_target} | ${nextPoint.toCurrency} | ${ms.name} | ${user.value.getCurrentTotalSpent().toCurrency} ${user.value.totalPointLost.toCurrency}");
      if (index == 0 &&
          currentPoint >= (targetPoint) &&
          currentPoint < nextPoint) {
        infoLogger(
            "pos 1", "$currentPoint | $prevPoint | $nextPoint | ${ms.name}");
        return value[nextMs];
      } else if (index == value.length - 1 && currentPoint >= prevPoint) {
        infoLogger(
            "pos 2", "$currentPoint | $prevPoint | $nextPoint | ${ms.name}");
        return ms;
      } else if (currentPoint >= prevPoint &&
          currentPoint < targetPoint &&
          currentPoint < nextPoint) {
        infoLogger(
            "pos 3", "$currentPoint | $prevPoint | $nextPoint | ${ms.name}");
        return ms;
      }
    }
    return null;
  }
}
