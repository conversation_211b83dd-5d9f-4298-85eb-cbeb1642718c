import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/animation/progress_animation.dart';
import 'package:mobile_crm/app/modules/screen_wrapper/views/app_page_wrapper.dart';
import 'package:mobile_crm/app/widget/app_bar_widget/app_bar_widget.dart';
import 'package:mobile_crm/app/widget/app_dotted_separator.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/values.dart';
import 'package:mobile_crm/data/services/analytics_service.dart';

import '../../../../routes/app_pages.dart';
import '../../screen_wrapper/controller/wrapper_controller.dart';
import '../controllers/point_controller.dart';
import 'widget/bottom_sheet/point_detail_bottom_sheet.dart';

class PointScreen extends GetView<PointController> {
  const PointScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final wrapperController = Get.find<WrapperController>();
    AnalyticsService.observer.analytics
        .logScreenView(screenName: "Point Screen", screenClass: "PointScreen");
    return PageWrapper(
      child: Column(children: [
        AppBarWidget(title: "Point", actions: [
          IconButton(
              onPressed: () {
                Get.toNamed(Routes.POINTHISTORY);
              },
              icon: Icon(
                Icons.receipt_long_rounded,
                color: AppColor.white,
                size: AppDimen.h16,
              ))
        ]),
        Flexible(
          child: SingleChildScrollView(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                AppDimen.h10.height,
                InkWell(
                  splashFactory: NoSplash.splashFactory,
                  onTap: () {
                    Get.toNamed(Routes.POINTHISTORY);
                  },
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: AppDimen.h16),
                    child: Container(
                      padding: EdgeInsets.all(AppDimen.h8),
                      decoration: BoxDecoration(
                          color: wrapperController.getPrimaryColor(),
                          boxShadow: const [
                            BoxShadow(
                                color: AppColor.black10,
                                offset: Offset(0, 0),
                                spreadRadius: 2,
                                blurRadius: 2)
                          ],
                          borderRadius:
                              BorderRadius.all(Radius.circular(AppDimen.h6))),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceAround,
                            children: [
                              Column(
                                children: [
                                  Text(
                                    "TIER".toUpperCase(),
                                    style: AppFont.componentSmallBold.copyWith(
                                        color: wrapperController
                                            .getPrimaryColor()
                                            .changeColorBasedOnBackgroundColor()
                                            .$1),
                                  ),
                                  Obx(() {
                                    return Text(
                                        (controller.user.value.memberType ??
                                                '-')
                                            .toUpperCase(),
                                        style: AppFont.componentSmallBold.copyWith(
                                            color: wrapperController
                                                .getPrimaryColor()
                                                .changeColorBasedOnBackgroundColor()
                                                .$1));
                                  }),
                                ],
                              ).fadeIn(
                                  delay: const Duration(milliseconds: 300),
                                  duration: const Duration(milliseconds: 800)),
                              Column(
                                children: [
                                  Obx(() {
                                    return Text(
                                        "${wrapperController.configApp.value.language?.point ?? ''} Point"
                                            .toUpperCase(),
                                        style: AppFont.componentSmallBold.copyWith(
                                            color: wrapperController
                                                .getPrimaryColor()
                                                .changeColorBasedOnBackgroundColor()
                                                .$1));
                                  }),
                                  Obx(() {
                                    return Text(
                                        (controller.user.value
                                                .getCurrentTotalPoint())
                                            .toCurrency
                                            .toUpperCase(),
                                        style: AppFont.componentSmallBold.copyWith(
                                            color: wrapperController
                                                .getPrimaryColor()
                                                .changeColorBasedOnBackgroundColor()
                                                .$1));
                                  }),
                                ],
                              ).fadeIn(
                                  delay: const Duration(milliseconds: 600),
                                  duration: const Duration(milliseconds: 800)),
                              Column(
                                children: [
                                  Text(Strings.spending.tr.toUpperCase(),
                                      style: AppFont.componentSmallBold.copyWith(
                                          color: wrapperController
                                              .getPrimaryColor()
                                              .changeColorBasedOnBackgroundColor()
                                              .$1)),
                                  Obx(() {
                                    return Text(
                                        "Rp${(controller.user.value.totalSpent ?? 0).toCurrency}"
                                            .toUpperCase(),
                                        style: AppFont.componentSmallBold.copyWith(
                                            color: wrapperController
                                                .getPrimaryColor()
                                                .changeColorBasedOnBackgroundColor()
                                                .$1));
                                  }),
                                ],
                              ).fadeIn(
                                  delay: const Duration(milliseconds: 1000),
                                  duration: const Duration(milliseconds: 800)),
                            ],
                          ),
                          AppDimen.h8.height,
                          DottedDivider(
                            color: wrapperController
                                .getPrimaryColor()
                                .changeColorBasedOnBackgroundColor()
                                .$1,
                            width: 3,
                            height: 2,
                          ),
                          AppDimen.h10.height,
                          Obx(() {
                            return Text(
                                "${wrapperController.configApp.value.language?.point ?? ''} Progress",
                                style: AppFont.componentSmallBold.copyWith(
                                    color: wrapperController
                                        .getPrimaryColor()
                                        .changeColorBasedOnBackgroundColor()
                                        .$1));
                          }),
                          AppDimen.h6.height,
                          Stack(
                            children: [
                              Container(
                                width: MediaQuery.of(context).size.width,
                                height: AppDimen.h6,
                                decoration: BoxDecoration(
                                    color:
                                        wrapperController.getPrimaryDarkColor(),
                                    borderRadius: BorderRadius.all(
                                        Radius.circular(AppDimen.h10))),
                              ),
                              Obx(() {
                                var currentNextPoint =
                                    controller.nextMemberShip.value;
                                return controller.user.value.totalSpent !=
                                            null &&
                                        controller.nextMemberShip.value
                                                .point_target !=
                                            null
                                    ? ProgressBarAnimation(
                                        currentValue: (controller.user.value
                                                    .getCurrentTotalPoint() -
                                                (controller.user.value
                                                        .totalPointLost ??
                                                    0))
                                            .toDouble(),
                                        maxValue:
                                            (currentNextPoint.point_target ?? 1)
                                                .toDouble(),
                                        duration: const Duration(seconds: 3))
                                    : Container(
                                        width:
                                            MediaQuery.of(context).size.width,
                                        height: AppDimen.h6,
                                        decoration: BoxDecoration(
                                            color: wrapperController
                                                .getSecondaryColor(),
                                            borderRadius: BorderRadius.all(
                                                Radius.circular(AppDimen.h10))),
                                      );
                              }),
                            ],
                          ),
                          AppDimen.h6.height,
                          Obx(() {
                            var currentNextPoint =
                                controller.nextMemberShip.value;
                            var currentProgressPoint =
                                (currentNextPoint.point_target ?? 0) -
                                    (controller.user.value
                                            .getCurrentTotalPoint() -
                                        (controller.user.value.totalPointLost ??
                                            0));
                            return Text(
                                    currentProgressPoint.isLowerThan(0)
                                        ? Strings.keepEnjoyBenefit.trParams({
                                            'value': currentNextPoint.name ?? ''
                                          })
                                        : Strings.collectPointToReachValue
                                            .trParams({
                                            'point': (currentProgressPoint)
                                                .toCurrency,
                                            'value':
                                                '${(currentNextPoint.name ?? '').capitalizeFirst}'
                                          }),
                                    style: AppFont.componentSmallBold.copyWith(
                                        color: wrapperController
                                            .getPrimaryColor()
                                            .changeColorBasedOnBackgroundColor()
                                            .$1))
                                .fadeIn(
                                    delay: const Duration(milliseconds: 1500),
                                    duration:
                                        const Duration(milliseconds: 800));
                          }),
                        ],
                      ),
                    ),
                  ),
                ),
                AppDimen.h10.height,
                Flexible(
                  child: FutureBuilder(
                    future: controller.membership.value,
                    builder: (context, snapshot) {
                      if (snapshot.hasData) {
                        return MediaQuery.removePadding(
                          context: context,
                          removeTop: true,
                          child: ListView.builder(
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                            itemCount: snapshot.data?.length ?? 0,
                            itemBuilder: (context, index) {
                              var item = snapshot.data![index];
                              return Column(
                                children: [
                                  InkWell(
                                    splashFactory: NoSplash.splashFactory,
                                    onTap: () {
                                      const PointDetailBottomSheet()
                                          .toModalBottomSheet
                                          .of(context);
                                    },
                                    child: Container(
                                      padding: EdgeInsets.symmetric(
                                          horizontal: AppDimen.h16),
                                      margin:
                                          EdgeInsets.only(bottom: AppDimen.h10),
                                      child: Container(
                                        padding: EdgeInsets.all(AppDimen.h8),
                                        decoration: BoxDecoration(
                                            color: AppColor.white,
                                            border: item.current == 2
                                                ? Border.all(
                                                    color: wrapperController
                                                        .getPrimaryColor(),
                                                    width: 4)
                                                : null,
                                            borderRadius: BorderRadius.all(
                                                Radius.circular(AppDimen.h8))),
                                        child: Row(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          mainAxisAlignment:
                                              MainAxisAlignment.start,
                                          children: [
                                            Icon(
                                              item.current == 2
                                                  ? Icons
                                                      .workspace_premium_rounded
                                                  : Icons.emoji_events,
                                              color: item.current == 3
                                                  ? AppColor.black5
                                                  : item.current == 2
                                                      ? wrapperController
                                                          .getPrimaryColor()
                                                      : wrapperController
                                                          .getPrimaryColor()
                                                          .withAlpha(200),
                                              size: AppDimen.h60,
                                            ),
                                            AppDimen.h10.width,
                                            Expanded(
                                              child: Column(
                                                mainAxisAlignment:
                                                    MainAxisAlignment.start,
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                mainAxisSize: MainAxisSize.min,
                                                children: [
                                                  Text(
                                                    "${item.name}",
                                                    style: AppFont
                                                        .componentMediumBold,
                                                  ),
                                                  Flexible(
                                                      child: item.current == 1
                                                          ? Text("Unlocked",
                                                              style: AppFont
                                                                  .componentSmall)
                                                          : item.current == 3
                                                              ? Text("Locked",
                                                                  style: AppFont
                                                                      .componentSmall)
                                                              : Text(
                                                                  "Collect More",
                                                                  style: AppFont
                                                                      .componentSmall)),
                                                ],
                                              ),
                                            ),
                                            Align(
                                              alignment: Alignment.center,
                                              child: item.current == 1
                                                  ? Row(
                                                      children: [
                                                        Transform.rotate(
                                                          angle: 50,
                                                          child: Icon(
                                                            Icons.lock_open,
                                                            color: AppColor
                                                                .black90,
                                                            size: AppDimen.h12,
                                                          ),
                                                        ),
                                                        Icon(
                                                          Icons
                                                              .keyboard_arrow_right_rounded,
                                                          size: AppDimen.h12,
                                                          color:
                                                              AppColor.black90,
                                                        )
                                                      ],
                                                    )
                                                  : item.current == 2
                                                      ? Icon(
                                                          Icons
                                                              .arrow_circle_right_rounded,
                                                          size: AppDimen.h14,
                                                          color:
                                                              AppColor.black90,
                                                        )
                                                      : Icon(
                                                          Icons.lock,
                                                          color: AppColor
                                                              .utilityDanger,
                                                          size: AppDimen.h14,
                                                        ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                  Visibility(
                                      visible: item.current == 3 &&
                                          snapshot
                                                  .data![(index + 1) >=
                                                          (snapshot.data
                                                                  ?.length ??
                                                              0)
                                                      ? index
                                                      : (index + 1)]
                                                  .current !=
                                              3,
                                      child: Column(
                                        children: [
                                          Padding(
                                            padding: EdgeInsets.symmetric(
                                                horizontal: AppDimen.h16),
                                            child: const DottedDivider(
                                              width: 4,
                                              height: 2,
                                              color: AppColor.black10,
                                            ),
                                          ),
                                          AppDimen.h8.height
                                        ],
                                      ))
                                ],
                              ).fadeIn(
                                  delay: Duration(
                                      milliseconds: 200 * (index + 1)));
                            },
                          ),
                        );
                      }

                      return const SizedBox();
                    },
                  ),
                )
              ],
            ),
          ),
        ),
        // const PointUser(),
        // const PointCustom(),
      ]),
    );
  }
}
