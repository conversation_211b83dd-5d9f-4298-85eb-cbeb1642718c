import 'package:flutter/material.dart';
import 'package:mobile_crm/core/theme/themes.dart';

class SliderWidget extends StatelessWidget {
  const SliderWidget(
      {super.key, required this.currentValue, required this.max});

  final double currentValue;
  final double max;

  @override
  Widget build(BuildContext context) {
    return SliderTheme(
      data: SliderTheme.of(context).copyWith(
        trackHeight: 10.0,
        activeTrackColor: AppColor.utilityDanger,
        inactiveTrackColor: AppColor.disable,
        thumbColor: Colors.transparent,
        disabledThumbColor: Colors.transparent,
        thumbShape: SliderComponentShape.noThumb,
        overlayColor: Colors.transparent,
        trackShape: CustomTrackShape(),
      ),
      child: Slider(
        value: currentValue,
        min: 0,
        max: max >= currentValue ? max : currentValue,
        label: currentValue.toString(),
        onChanged: (value) {},
      ),
    );
  }
}

class CustomTrackShape extends RoundedRectSliderTrackShape {
  @override
  Rect getPreferredRect({
    required RenderBox parentBox,
    Offset offset = Offset.zero,
    required SliderThemeData sliderTheme,
    bool isEnabled = false,
    bool isDiscrete = false,
  }) {
    final double trackHeight = sliderTheme.trackHeight!;
    final double trackLeft = offset.dx;
    final double trackTop =
        offset.dy + (parentBox.size.height - trackHeight) / 2;
    final double trackWidth = parentBox.size.width;
    return Rect.fromLTWH(trackLeft, trackTop, trackWidth, trackHeight);
  }
}
