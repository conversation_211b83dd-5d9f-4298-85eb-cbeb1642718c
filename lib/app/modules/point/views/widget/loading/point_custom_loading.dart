import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:mobile_crm/core/extensions/double_extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';

import '../../../../../widget/shimmer_loading_widget.dart';

class PointCustomLoading extends StatelessWidget {
  const PointCustomLoading({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        CarouselSlider(
          items: [1]
              .map((e) => Container(
                    margin: const EdgeInsets.all(10),
                    decoration: BoxDecoration(
                        color: AppColor.white,
                        borderRadius: BorderRadius.circular(10),
                        boxShadow: const [
                          BoxShadow(
                              color: AppColor.black5,
                              spreadRadius: 1,
                              blurRadius: 1,
                              offset: Offset(0, 1))
                        ]),
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Row(
                                children: [
                                  ShimmerWidget(
                                    width: AppDimen.h24,
                                    height: AppDimen.h14,
                                    borderRadius: BorderRadius.circular(5),
                                  ),
                                  5.0.width,
                                  ShimmerWidget(
                                    width: AppDimen.h64,
                                  ),
                                ],
                              ),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 8, vertical: 4),
                                child: ShimmerWidget(
                                  width: AppDimen.h40,
                                  height: AppDimen.h14,
                                ),
                              )
                            ],
                          ),
                          AppDimen.h8.height,
                          const ShimmerWidget(
                            width: double.infinity,
                          ),
                          AppDimen.h6.height,
                          ShimmerWidget(
                            width: AppDimen.h192,
                          ),
                          const Spacer(),
                          const Divider(),
                          ShimmerWidget(
                            width: AppDimen.h128 + AppDimen.h32,
                          ),
                          AppDimen.h6.height,
                          ShimmerWidget(
                            width: AppDimen.h64,
                          ),
                        ],
                      ),
                    ),
                  ))
              .toList(),
          options: CarouselOptions(
              aspectRatio: 2 / 1,
              viewportFraction: 1,
              enableInfiniteScroll: false,
              reverse: false,
              autoPlay: false,
              autoPlayInterval: const Duration(seconds: 5),
              autoPlayAnimationDuration: const Duration(milliseconds: 500),
              autoPlayCurve: Curves.fastLinearToSlowEaseIn,
              enlargeCenterPage: true,
              scrollDirection: Axis.horizontal),
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [1].map((e) {
            return Container(
              width: 10,
              height: 5,
              margin: const EdgeInsets.symmetric(vertical: 5, horizontal: 2),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                shape: BoxShape.rectangle,
                // color: currentIndex == index
                //     ? AppColor.black
                //     : AppColor.disable,
              ),
            );
          }).toList(),
        ),
      ],
    );
  }
}
