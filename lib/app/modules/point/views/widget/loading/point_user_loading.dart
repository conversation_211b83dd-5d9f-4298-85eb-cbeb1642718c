import 'package:flutter/material.dart';
import 'package:mobile_crm/app/widget/shimmer_loading_widget.dart';
import 'package:mobile_crm/core/extensions/double_extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';

class PointUserLoading extends StatelessWidget {
  const PointUserLoading({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
        width: MediaQuery.of(context).size.width,
        padding: const EdgeInsets.all(15),
        margin: const EdgeInsets.all(10),
        decoration: BoxDecoration(
            color: AppColor.white,
            borderRadius: BorderRadius.circular(10),
            boxShadow: const [
              BoxShadow(
                  color: AppColor.black5,
                  spreadRadius: 1,
                  blurRadius: 1,
                  offset: Offset(0, 1))
            ]),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ShimmerWidget(
              width: AppDimen.h128 + AppDimen.h40,
            ),
            AppDimen.h6.height,
            ShimmerWidget(
              width: AppDimen.h64,
            ),
            AppDimen.h14.height,
            ShimmerWidget(
              width: AppDimen.h128,
            ),
            AppDimen.h6.height,
            Row(
              children: [
                ShimmerWidget(
                  width: AppDimen.h40,
                ),
                10.0.width,
                ShimmerWidget(
                  width: AppDimen.h12,
                ),
              ],
            ),
          ],
        ));
  }
}
