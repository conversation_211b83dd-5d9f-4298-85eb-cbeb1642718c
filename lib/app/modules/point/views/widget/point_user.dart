import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/point/controllers/point_controller.dart';
import 'package:mobile_crm/app/modules/screen_wrapper/controller/wrapper_controller.dart';
import 'package:mobile_crm/core/extensions/double_extensions.dart';
import 'package:mobile_crm/core/extensions/int_extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/values.dart';

import 'loading/point_user_loading.dart';

class PointUser extends StatelessWidget {
  const PointUser({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<PointController>();
    final c = Get.find<WrapperController>();
    return Obx(() {
      return FutureBuilder(
        future: controller.membership.value,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.done) {
            return Container(
                width: MediaQuery.of(context).size.width,
                padding: const EdgeInsets.all(15),
                margin: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                    color: AppColor.white,
                    borderRadius: BorderRadius.circular(10),
                    boxShadow: const [
                      BoxShadow(
                          color: AppColor.black5,
                          spreadRadius: 1,
                          blurRadius: 1,
                          offset: Offset(0, 1))
                    ]),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      Strings.currentTotalSpending.tr,
                      style: AppFont.paragraphSmall
                          .copyWith(color: AppColor.black70),
                    ),
                    Text(
                      'Rp ${controller.user.value.totalSpent.toCurrency}',
                      style: AppFont.componentLarge
                          .copyWith(color: AppColor.black90),
                    ),
                    const Divider(),
                    Text(
                      Strings.remainingPoint.trParams(
                          {"name": c.configApp.value.language?.point ?? ''}),
                      style: AppFont.paragraphSmall
                          .copyWith(color: AppColor.black70),
                    ),
                    Row(
                      children: [
                        Text(
                          controller.user.value
                              .totalPoint
                              .toCurrency,
                          style: AppFont.componentLarge
                              .copyWith(color: AppColor.black90),
                        ),
                        10.0.width,
                        // SvgIcon.svgIcon(
                        //   ImgStrings.circleArrowRight,
                        //   color: c.getPrimaryColor(),
                        //   onPressed: () {
                        //     Get.toNamed(Routes.POINTHISTORY);
                        //   },
                        // ),
                      ],
                    ),
                  ],
                ));
          }

          return const Center(child: PointUserLoading());
        },
      );
    });
  }
}
