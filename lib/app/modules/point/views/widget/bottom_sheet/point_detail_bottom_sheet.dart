import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';
import 'package:mobile_crm/core/values/app_img_strings.dart';

class PointDetailBottomSheet extends StatelessWidget {
  const PointDetailBottomSheet({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Lottie.asset(ImgStrings.lottieComingSoon, animate: true, repeat: false)
      ],
    );
  }
}
