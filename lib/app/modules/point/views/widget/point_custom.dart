import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/point/controllers/point_controller.dart';
import 'package:mobile_crm/core/extensions/double_extensions.dart';
import 'package:mobile_crm/core/extensions/int_extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';

import '../../../../../core/values/app_strings.dart';
import 'loading/point_custom_loading.dart';
import 'slider_widget.dart';

class PointCustom extends StatelessWidget {
  const PointCustom({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<PointController>();
    return Obx(() {
      return FutureBuilder(
        future: controller.membership.value,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.done) {
            return Column(
              children: [
                CarouselSlider(
                  items: snapshot.data
                      ?.map((e) => Container(
                            margin: const EdgeInsets.all(10),
                            decoration: BoxDecoration(
                                color: AppColor.white,
                                borderRadius: BorderRadius.circular(10),
                                boxShadow: const [
                                  BoxShadow(
                                      color: AppColor.black5,
                                      spreadRadius: 1,
                                      blurRadius: 1,
                                      offset: Offset(0, 1))
                                ]),
                            child: Padding(
                              padding: const EdgeInsets.all(16.0),
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Row(
                                        children: [
                                          const Icon(
                                            Icons.wallet_giftcard_rounded,
                                            color: AppColor.black70,
                                          ),
                                          5.0.width,
                                          Text(
                                            "${e.name}",
                                            style: AppFont.componentLarge
                                                .copyWith(
                                                    color: AppColor.black90),
                                          ),
                                        ],
                                      ),
                                      Container(
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: 8, vertical: 4),
                                        decoration: BoxDecoration(
                                            color: e.current == 1
                                                ? AppColor.utilityDark
                                                : e.current == 2
                                                    ? AppColor.utilityWarning
                                                    : AppColor.utilityDanger,
                                            borderRadius:
                                                BorderRadius.circular(15)),
                                        child: Text(
                                          e.current == 1
                                              ? "Pass"
                                              : e.current == 2
                                                  ? "Current"
                                                  : "Locked",
                                          style: AppFont.componentSmall
                                              .copyWith(color: AppColor.white),
                                        ),
                                      )
                                    ],
                                  ),
                                  SliderWidget(
                                      currentValue: (e.current == 1
                                              ? (e.point_target == 0)
                                                  ? 999
                                                  : e.point_target ?? 0
                                              : e.current == 2
                                                  ? controller.user.value
                                                      .getCurrentTotalPoint()
                                                  : e.current == 3
                                                      ? (controller.user.value
                                                          .getCurrentTotalPoint())
                                                      : 0)
                                          .toDouble(),
                                      max: (e.point_target == 0
                                              ? 999
                                              : e.point_target ?? 0)
                                          .toDouble()),
                                  Text(
                                    e.current == 1
                                        ? Strings.passedThisLevel.tr
                                        : e.current == 2
                                            ? Strings.youAchievedHigherLevel
                                                .trParams(
                                                    {'value': e.name ?? ''})
                                            : Strings.spendMoreToLevelUp
                                                .trParams({
                                                'value': ((e.spent_target ??
                                                            0) -
                                                        (controller.user.value
                                                            .getCurrentTotalSpent()))
                                                    .abs()
                                                    .toCurrency,
                                                'point': (e.point_target ?? 0)
                                                    .toString()
                                              }),
                                    style: AppFont.componentSmall,
                                  ),
                                  const Spacer(),
                                  const Divider(),
                                  Flexible(
                                    child: e.current == 1
                                        ? Text(
                                            Strings.keepEnjoyBenefit.trParams(
                                                {'value': e.name ?? ''}),
                                            style: AppFont.paragraphSmall
                                                .copyWith(
                                                    color: AppColor.ink02))
                                        : e.current == 2
                                            ? Text(
                                                Strings
                                                    .keepYourLevelEveryPurchase
                                                    .tr,
                                                style: AppFont.paragraphSmall
                                                    .copyWith(
                                                        color: AppColor.ink02))
                                            : Text(
                                                Strings
                                                    .reachNewLevelMakingPurchase
                                                    .tr,
                                                style: AppFont.paragraphSmall
                                                    .copyWith(
                                                        color: AppColor.ink02)),
                                  )
                                ],
                              ),
                            ),
                          ))
                      .toList(),
                  options: CarouselOptions(
                    aspectRatio: 2 / 1,
                    viewportFraction: 1,
                    enableInfiniteScroll: false,
                    reverse: false,
                    autoPlay: false,
                    autoPlayInterval: const Duration(seconds: 5),
                    autoPlayAnimationDuration:
                        const Duration(milliseconds: 500),
                    autoPlayCurve: Curves.fastLinearToSlowEaseIn,
                    enlargeCenterPage: true,
                    scrollDirection: Axis.horizontal,
                  ),
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: snapshot.data?.map((e) {
                        return Container(
                          width: 10,
                          height: 5,
                          margin: const EdgeInsets.symmetric(
                              vertical: 5, horizontal: 2),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8),
                            shape: BoxShape.rectangle,
                            // color: currentIndex == index
                            //     ? AppColor.black
                            //     : AppColor.disable,
                          ),
                        );
                      }).toList() ??
                      [],
                ),
              ],
            );
          }
          return const PointCustomLoading();
        },
      );
    });
  }
}
