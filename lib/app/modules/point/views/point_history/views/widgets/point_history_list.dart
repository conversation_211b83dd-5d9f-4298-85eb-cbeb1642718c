import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/point/point_export.dart';
import 'package:mobile_crm/app/widget/app_page_empty.dart';
import 'package:mobile_crm/app/widget/custom_circular_progress_widget.dart';
import 'package:mobile_crm/core/theme/app_color.dart';
import 'package:mobile_crm/data/providers/db/database.dart';

import '../../../../../../../data/services/analytics_service.dart';
import 'point_history_item.dart';

class PointHistoryList extends StatelessWidget {
  const PointHistoryList({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<PointHistoryController>();
    return Obx(() {
      return FutureBuilder(
        future: controller.pointHistory.value,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.done) {
            if ((snapshot.data?.length ?? 0) <= 0) {
              return AppPageEmpty(
                func: () {
                  Get.back();
                },
                reason: "You have not made a transaction",
                buttonText: "Back",
              );
            } else {
              AnalyticsService.observer.analytics.logViewItemList(
                  itemListName: 'point_history',
                  items: snapshot.data
                      ?.map((e) => AnalyticsEventItem(
                          itemName: e.source,
                          itemCategory: e.type,
                          quantity: e.point))
                      .toList());
              return ListView.builder(
                itemCount: snapshot.data?.length ?? 0,
                itemBuilder: (context, index) {
                  PointData point = snapshot.data![index];
                  return PointItemWidget(index: index, point: point);
                },
              );
            }
          }
          return const Center(
              child: CustomCircularProgressIndicator(
            valueColor: AppColor.black,
          ));
        },
      );
    });
  }
}
