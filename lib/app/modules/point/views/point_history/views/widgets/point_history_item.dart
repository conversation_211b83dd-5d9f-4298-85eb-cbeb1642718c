// ignore_for_file: must_be_immutable

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/app_constants.dart';
import 'package:mobile_crm/data/providers/db/database.dart';

class PointItemWidget extends StatelessWidget {
  // ignore: prefer_typing_uninitialized_variables
  final PointData point;
  final int index;

  const PointItemWidget({Key? key, required this.point, required this.index})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (point.isHeader ?? false) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          index != 0 ? 10.0.height : 0.0.height,
          Text(
            point.time_created.toDayAgo,
            style: AppFont.paragraphSmallBold.copyWith(color: AppColor.ink02),
          ),
        ],
      );
    } else {
      return InkWell(
        onTap: () {},
        child: Container(
          margin: EdgeInsets.symmetric(
              vertical: AppDimen.h6, horizontal: AppDimen.h4),
          padding: EdgeInsets.all(Constants.defaultPadding),
          decoration: BoxDecoration(
              color: AppColor.white,
              borderRadius: BorderRadius.all(Radius.circular(10.r)),
              boxShadow: const [
                BoxShadow(
                    color: AppColor.black5,
                    offset: Offset(0, 1),
                    blurRadius: 1,
                    spreadRadius: 1),
              ]),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    "${point.type}".toUpperCase(),
                    style:
                        AppFont.componentSmall.copyWith(color: AppColor.black),
                  ),
                  Text(
                    point.time_created.toDate,
                    style: AppFont.paragraphSmall
                        .copyWith(color: AppColor.black70),
                  ),
                ],
              ),
              const Divider(),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Flexible(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "${point.source}",
                          style: AppFont.componentSmallBold
                              .copyWith(color: AppColor.black),
                        ),
                        5.0.height,
                        Text(
                          "${point.source_desc}",
                          style: AppFont.paragraphSmall
                              .copyWith(color: AppColor.black90),
                        ),
                      ],
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8.0),
                    child: Text("${point.point}",
                        style: AppFont.componentSmallBold.copyWith(
                            color: point.point.toString().contains("-")
                                ? AppColor.utilityDanger
                                : AppColor.utilitySuccess)),
                  )
                ],
              ),
            ],
          ),
        ),
      );
    }
  }
}
