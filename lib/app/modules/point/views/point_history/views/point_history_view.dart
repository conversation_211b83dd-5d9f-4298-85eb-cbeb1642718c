import 'package:flutter/material.dart';
import 'package:mobile_crm/app/modules/screen_wrapper/views/app_page_wrapper.dart';
import 'package:mobile_crm/app/widget/app_bar_widget/app_bar_widget.dart';

import '../../../../../../data/services/analytics_service.dart';
import 'widgets/point_history_wdiget.dart';

class PointHistoryScreen extends StatelessWidget {
  const PointHistoryScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    AnalyticsService.observer.analytics.logScreenView(
        screenName: "Point History Screen", screenClass: "PointHistoryScreen");
    final Size size = MediaQuery.of(context).size;
    return PageWrapper(
      child: SizedBox(
        height: size.height,
        child: Column(
          children: [
            const AppBarWidget(title: "Point History"),
            Flexible(
                child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: MediaQuery.removePadding(
                  removeTop: true,
                  context: context,
                  child: const PointHistoryList()),
            ))
          ],
        ),
      ),
    );
  }
}
