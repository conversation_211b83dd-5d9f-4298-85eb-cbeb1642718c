import 'package:get/get.dart';
import 'package:mobile_crm/data/providers/db/database.dart';
import 'package:mobile_crm/data/repository/user_repository.dart';
import 'package:mobile_crm/domain/repository/user_repository.dart';

class PointHistoryController extends GetxController {
  final UserRepository _userRepo = Get.find<UserRepositoryIml>();

  var pointHistory = Future.value(<PointData>[]).obs;

  @override
  void onInit() {
    fetchPointHistory();
    super.onInit();
  }

  void fetchPointHistory() {
    pointHistory.value = _userRepo.getPointHistory();
  }
}
