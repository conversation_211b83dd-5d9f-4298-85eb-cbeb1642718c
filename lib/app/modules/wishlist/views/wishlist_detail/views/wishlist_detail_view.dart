import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/screen_wrapper/views/app_page_wrapper.dart';
import 'package:mobile_crm/app/modules/wishlist/views/wishlist_detail/views/widgets/wishlist_dialog.dart';
import 'package:mobile_crm/app/widget/app_bar_widget/app_bar_widget.dart';
import 'package:mobile_crm/app/widget/cached_image_widget.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/values.dart';
import 'package:mobile_crm/data/providers/db/database.dart';

import '../controllers/wishlist_detail_controller.dart';

class WishlistDetailPage extends StatelessWidget {
  WishlistDetailPage({Key? key}) : super(key: key);

  final controller = Get.find<WishlistDetailController>();

  @override
  Widget build(BuildContext context) {
    return PageWrapper(
        child: Column(
      children: [
        AppBarWidget(title: controller.id == 3 ? 'Wishlist' : controller.title),
        AppDimen.h4.height,
        controller.id != 3
            ? const SizedBox()
            : Container(
                margin: const EdgeInsets.symmetric(horizontal: 10),
                alignment: Alignment.centerLeft,
                width: double.infinity,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Text(
                      controller.title,
                      style: AppFont.componentSmallBold,
                    ),
                    Text(
                      Strings.valueSavedWishlist.trParams(
                          {'value': "${controller.listWishlist.length}"}),
                      style: AppFont.componentSmall.copyWith(
                          fontSize: AppDimen.h8, color: AppColor.black70),
                    ),
                  ],
                ),
              ),
        AppDimen.h4.height,
        Expanded(
          child: MediaQuery.removePadding(
            context: context,
            removeTop: true,
            child: GridView.builder(
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisSpacing: AppDimen.h2,
                  crossAxisCount: 2,
                  mainAxisSpacing: AppDimen.h2),
              shrinkWrap: true,
              padding: EdgeInsets.symmetric(horizontal: AppDimen.h2),
              itemCount: controller.listWishlist.length,
              itemBuilder: (context, index) {
                var item = controller.listWishlist[index];
                return InkWell(
                  onTapDown: (details) async {
                    await controller
                        .getProductByProductId(item.productFkId ?? 0)
                        .then((value) => WishlistDialog.showProduct(
                            context: context,
                            wishlistData: item,
                            product: value ?? item.product ?? Product()));
                  },
                  child: Stack(
                    children: [
                      Container(
                        alignment: Alignment.center,
                        decoration:
                            const BoxDecoration(color: AppColor.disable),
                        child: CachedImageWidget(
                          width: double.infinity,
                          height: double.infinity,
                          fit: BoxFit.cover,
                          imageUrl: item.product?.photo ?? '',
                        ),
                      ),
                      Container(
                        width: double.infinity,
                        decoration: const BoxDecoration(
                            gradient: LinearGradient(
                                colors: [
                              AppColor.black50,
                              AppColor.black10,
                              AppColor.black5
                            ],
                                begin: Alignment.bottomCenter,
                                end: Alignment.topCenter)),
                        child: Container(
                          margin: EdgeInsets.only(
                              left: AppDimen.h4, bottom: AppDimen.h4),
                          child: Align(
                            alignment: Alignment.bottomLeft,
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  item.product?.name ?? '',
                                  style: AppFont.componentSmallBold
                                      .copyWith(color: AppColor.white),
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                ),
                                (item.product?.priceSellStart == null)
                                    ? const SizedBox()
                                    : Text(
                                        item.product?.priceSellStart ==
                                                item.product?.priceSellEnd
                                            ? "Rp.${(item.product?.priceSellStart ?? 0).toCurrency}"
                                            : "Rp.${(item.product?.priceSellStart ?? 0).toCurrency} - ${(item.product?.priceSellEnd ?? 0).toCurrency}",
                                        style: AppFont.componentSmallBold
                                            .copyWith(
                                                color: AppColor.white,
                                                fontSize: 10.sp),
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                      ),
                              ],
                            ),
                          ),
                        ),
                      ),
                      item.total != null
                          ? Align(
                              alignment: Alignment.topRight,
                              child: Container(
                                padding: EdgeInsets.symmetric(
                                    horizontal: AppDimen.h6),
                                decoration: const BoxDecoration(
                                    borderRadius: BorderRadius.only(
                                        bottomLeft: Radius.circular(10)),
                                    color: AppColor.black70),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Text(
                                      "${item.total ?? 0}",
                                      style: AppFont.componentSmallBold
                                          .copyWith(color: AppColor.white),
                                    ),
                                    AppDimen.h2.width,
                                    Icon(Icons.bookmark_rounded,
                                        size: AppDimen.h12),
                                  ],
                                ),
                              ))
                          : const SizedBox()
                    ],
                  ),
                );
              },
            ),
          ),
        )
      ],
    ));
  }
}
