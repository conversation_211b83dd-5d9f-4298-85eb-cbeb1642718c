import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/screen_wrapper/controller/wrapper_controller.dart';
import 'package:mobile_crm/app/widget/button_widget/button_widget.dart';
import 'package:mobile_crm/app/widget/cached_image_widget.dart';
import 'package:mobile_crm/app/widget/html_widget.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/values.dart';
import 'package:mobile_crm/data/models/available_model.dart';
import 'package:mobile_crm/data/models/variant_model.dart';
import 'package:mobile_crm/data/providers/db/database.dart';
import 'package:mobile_crm/data/services/analytics_service.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

import '../../../../../../../routes/app_pages.dart';

class WishlistDialog {
  static void showProduct(
      {required BuildContext context,
      required WishlistData wishlistData,
      required Product product}) {
    final wrapperController = Get.find<WrapperController>();
    var tabIndex = 0.obs;
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.8,
          maxWidth: Constants.defaultMaxWidth,
          minWidth: Constants.defaultMaxWidth),
      builder: (context) => Container(
        constraints: BoxConstraints(
            maxWidth: Constants.defaultMaxWidth,
            maxHeight: MediaQuery.of(context).size.height * 0.8),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: EdgeInsets.symmetric(
                  horizontal: AppDimen.h6, vertical: AppDimen.h8),
              width: double.infinity,
              decoration: BoxDecoration(
                  color: AppColor.disable,
                  borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(AppDimen.h10),
                      topRight: Radius.circular(AppDimen.h10))),
              child: const Icon(
                Icons.drag_handle_rounded,
                color: AppColor.black90,
              ),
            ),
            AppDimen.h6.height,
            Align(
              alignment: Alignment.center,
              child: SizedBox(
                height: 200.h,
                child: ClipRRect(
                    borderRadius: BorderRadius.circular(AppDimen.h10),
                    child: CachedImageWidget(
                        width: 300,
                        height: 200,
                        fit: BoxFit.cover,
                        imageUrl: product.photo.toString())),
              ),
            ),
            AppDimen.h6.height,
            Flexible(
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: AppDimen.h6),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      wishlistData.product?.name ?? '',
                      style: AppFont.componentSmallBold,
                    ),
                    Text(
                      product.subcategory ?? '',
                      style: AppFont.componentSmall.copyWith(fontSize: 10.sp),
                    ),
                    Flexible(
                      child: HtmlWidget(
                        data: product.description ?? '',
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            AppDimen.h6.height,
            Container(
              padding: EdgeInsets.symmetric(horizontal: AppDimen.h6),
              height: AppDimen.h24,
              child: (product.variant?.isEmpty == true ||
                      product.variant == null)
                  ? FittedBox(
                      alignment: Alignment.centerLeft,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 16, vertical: 8),
                        decoration: BoxDecoration(
                            color: AppColor.black,
                            borderRadius: BorderRadius.circular(30)),
                        child: Text(
                          Strings.all.tr,
                          textAlign: TextAlign.center,
                          style: AppFont.componentSmall
                              .copyWith(color: AppColor.white),
                        ),
                      ),
                    )
                  : ListView.builder(
                      scrollDirection: Axis.horizontal,
                      shrinkWrap: true,
                      itemCount: product.variant?.length ?? 0,
                      itemBuilder: (BuildContext context, int idx) {
                        VariantModel variant = product.variant![idx];
                        return Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 2),
                          child: InkWell(
                            child: Obx(() => FittedBox(
                                  alignment: Alignment.centerLeft,
                                  child: Container(
                                    padding: const EdgeInsets.all(8),
                                    decoration: BoxDecoration(
                                        border:
                                            Border.all(color: AppColor.disable),
                                        color: (tabIndex.value == idx)
                                            ? AppColor.black
                                            : AppColor.white,
                                        borderRadius:
                                            BorderRadius.circular(30)),
                                    child: Text(
                                      '${variant.name}',
                                      style: AppFont.componentSmall.copyWith(
                                          color: (tabIndex.value == idx)
                                              ? AppColor.white
                                              : AppColor.black),
                                    ),
                                  ),
                                )),
                            onTap: () {
                              tabIndex.value = idx;
                            },
                          ),
                        );
                      },
                    ),
            ),
            const Divider(
              thickness: 2,
              color: AppColor.black10,
            ),
            Container(
              padding: EdgeInsets.symmetric(horizontal: AppDimen.h6),
              margin: const EdgeInsets.only(bottom: 5),
              width: MediaQuery.of(context).size.width,
              child: Obx(() {
                return Text(
                    Strings.availableAt.trParams({
                      "outlet":
                          wrapperController.configApp.value.language?.outlet ??
                              "Outlet"
                    }),
                    textAlign: TextAlign.left,
                    style: AppFont.componentSmall);
              }),
            ),
            (product.variant?.isEmpty == true || product.variant == null)
                ? Flexible(
                    child: Padding(
                      padding: EdgeInsets.symmetric(horizontal: AppDimen.h6),
                      child: ListView.builder(
                          scrollDirection: Axis.vertical,
                          shrinkWrap: true,
                          itemCount: product.available?.length ?? 0,
                          itemBuilder: (BuildContext context, int i) {
                            AvailableModel available = product.available![i];
                            return Column(
                              children: [
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Column(
                                      mainAxisAlignment:
                                          MainAxisAlignment.start,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          available.name ?? '',
                                          style: AppFont.componentSmallBold,
                                        ),
                                        Text(
                                          "RP ${available.priceSell.toCurrency}",
                                          style: AppFont.componentSmall,
                                        ),
                                      ],
                                    ),
                                    available.enableOrder?.status == 'disable'
                                        ? Text(
                                            Strings.productNotAvailable.tr,
                                            style: AppFont.componentSmall
                                                .copyWith(
                                                    color:
                                                        AppColor.utilityDanger),
                                          )
                                        : PrimaryButton(
                                            onPressed: () {
                                              Sentry.addBreadcrumb(Breadcrumb(
                                                  type: 'debug',
                                                  category:
                                                      'user.activity.wishlist.order',
                                                  data: {
                                                    'product_id':
                                                        product.productId,
                                                    'product_name':
                                                        product.name,
                                                    'product_price':
                                                        product.price
                                                  },
                                                  level: SentryLevel.debug));
                                              int outletId =
                                                  available.outletId ?? 0;
                                              AnalyticsService
                                                  .observer.analytics
                                                  .logSelectItem(
                                                      itemListId: available
                                                          .productDetailId
                                                          .toString(),
                                                      itemListName:
                                                          product.name);
                                              Get.toNamed(
                                                  Routes.OUTLET(
                                                      outletId.toString()),
                                                  arguments: product);
                                            },
                                            text: Strings.order2.tr,
                                            type: PrimaryButtonType.type4,
                                            child: Text(Strings.order2.tr,
                                                style: AppFont.componentSmall
                                                    .copyWith(
                                                        color:
                                                            AppColor.white))),
                                  ],
                                ),
                                const Divider()
                              ],
                            );
                          }),
                    ),
                  )
                : Flexible(
                    child: Padding(
                      padding: EdgeInsets.symmetric(horizontal: AppDimen.h6),
                      child: Obx(
                        () => ListView.builder(
                          shrinkWrap: true,
                          itemCount: product
                                  .variant?[tabIndex.value].available?.length ??
                              0,
                          itemBuilder: (BuildContext context, int i) {
                            AvailableModel? available =
                                product.variant?[tabIndex.value].available![i];
                            return Column(
                              children: [
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Column(
                                      mainAxisAlignment:
                                          MainAxisAlignment.start,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(available?.name ?? '',
                                            style: AppFont.componentSmallBold),
                                        Text(
                                            "RP ${available?.priceSell?.toCurrency}",
                                            style: AppFont.componentSmall),
                                      ],
                                    ),
                                    available?.enableOrder?.status == 'disable'
                                        ? Text(
                                            Strings.productNotAvailable.tr,
                                            style: AppFont.componentSmall
                                                .copyWith(
                                                    color:
                                                        AppColor.utilityDanger),
                                          )
                                        : PrimaryButton(
                                            onPressed: () {
                                              Sentry.addBreadcrumb(Breadcrumb(
                                                  type: 'debug',
                                                  category:
                                                      'user.activity.wishlist.order',
                                                  data: {
                                                    'product_id':
                                                        product.productId,
                                                    'product_name':
                                                        product.name,
                                                    'product_price':
                                                        product.price,
                                                    'product_variant':
                                                        available?.name,
                                                    'product_variant_price':
                                                        available?.priceSell
                                                  },
                                                  level: SentryLevel.debug));
                                              int outletId =
                                                  available?.outletId ?? 0;
                                              Get.toNamed(
                                                  Routes.OUTLET(
                                                      outletId.toString()),
                                                  arguments: product);
                                            },
                                            text: Strings.order2.tr,
                                            type: PrimaryButtonType.type4,
                                            child: Text(Strings.order2.tr,
                                                style: AppFont.componentSmall
                                                    .copyWith(
                                                        color:
                                                            AppColor.white))),
                                  ],
                                ),
                                const Divider()
                              ],
                            );
                          },
                        ),
                      ),
                    ),
                  ),
            AppDimen.h10.height
          ],
        ),
      ),
    );
  }
}
