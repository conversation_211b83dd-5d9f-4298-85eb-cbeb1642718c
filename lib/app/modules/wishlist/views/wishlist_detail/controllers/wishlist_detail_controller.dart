import 'package:get/get.dart';
import 'package:mobile_crm/data/providers/db/database.dart';
import 'package:mobile_crm/data/repository/product_repository.dart';

class WishlistDetailController extends GetxController {
  final productRepo = Get.find<ProductRepositoryIml>();
  List<WishlistData> listWishlist = [];
  String title = '';
  int id = 1;

  @override
  void onInit() {
    if (Get.arguments['wishlist'].runtimeType == listWishlist.runtimeType) {
      listWishlist = Get.arguments['wishlist'];
    }

    if (Get.arguments['title'].runtimeType == String) {
      title = Get.arguments['title'];
    }

    if (Get.arguments['id'].runtimeType == int) {
      id = Get.arguments['id'];
    }
    super.onInit();
  }

  Future<Product?> getProductByProductId(int productId) async =>
      await productRepo.getProductById(productId.toString());
}
