import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/enum/wishlist_enum.dart';
import 'package:mobile_crm/app/modules/screen_wrapper/views/app_page_wrapper.dart';
import 'package:mobile_crm/app/widget/app_bar_widget/app_bar_widget.dart';
import 'package:mobile_crm/app/widget/cached_image_widget.dart';
import 'package:mobile_crm/app/widget/custom_circular_progress_widget.dart';
import 'package:mobile_crm/core/theme/app_color.dart';
import 'package:mobile_crm/core/theme/app_dimen.dart';
import 'package:mobile_crm/core/theme/app_font.dart';
import 'package:mobile_crm/core/values/values.dart';
import 'package:mobile_crm/data/providers/db/database.dart';
import 'package:mobile_crm/routes/app_pages.dart';

import '../../../../data/services/analytics_service.dart';
import '../controllers/wishlist_controller.dart';

class WishlistPage extends StatelessWidget {
  WishlistPage({Key? key}) : super(key: key);

  final controller = Get.find<WishlistController>();

  @override
  Widget build(BuildContext context) {
    return PageWrapper(
        child: Column(
      mainAxisSize: MainAxisSize.max,
      children: [
        AppBarWidget(title: Strings.valueSavedWishlist.trParams({'value': ''})),
        Flexible(
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                    margin: const EdgeInsets.symmetric(vertical: 10),
                    child: MediaQuery.removePadding(
                      context: context,
                      removeTop: true,
                      child: GridView.builder(
                        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisSpacing: AppDimen.h16,
                            crossAxisCount: 2,
                            mainAxisSpacing: 10),
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        padding: EdgeInsets.symmetric(horizontal: AppDimen.h12),
                        itemCount: 2,
                        itemBuilder: (context, index) {
                          return ClipRRect(
                            borderRadius: BorderRadius.circular(5.r),
                            child: index == 0
                                ? FutureBuilder(
                                    future: controller.getAllWishlist(),
                                    builder: (BuildContext context, snapshot) {
                                      if (snapshot.connectionState ==
                                          ConnectionState.waiting) {
                                        return const CustomCircularProgressIndicator();
                                      }
                                      if (snapshot.hasData) {
                                        if (snapshot.data != null) {
                                          var listData = snapshot.data ?? [];
                                          return ClipRRect(
                                            borderRadius:
                                                BorderRadius.circular(5.r),
                                            child: InkWell(
                                              onTapDown: (details) {
                                                AnalyticsService
                                                    .observer.analytics
                                                    .logEvent(
                                                        name:
                                                            "go_to_all_wishlist",
                                                        parameters: {
                                                      "wishlist_count":
                                                          listData.length
                                                    });
                                                Get.toNamed(
                                                    Routes.WISHLISTDETAIL(
                                                        'all'),
                                                    arguments: {
                                                      'wishlist': listData,
                                                      'title': 'Semua Wishlist',
                                                      'id': 1,
                                                    });
                                              },
                                              child: AllWishlist(
                                                  listWishlist: listData,
                                                  categoryName:
                                                      'Semua Wishlist'),
                                            ),
                                          );
                                        } else {
                                          return const SizedBox();
                                        }
                                      }
                                      return const CustomCircularProgressIndicator();
                                    })
                                : FutureBuilder(
                                    future: controller.wishlistRepo
                                        .getWishListRemote(
                                            format: WishlistEnum.top_item),
                                    builder: (BuildContext context, snapshot) {
                                      if (snapshot.connectionState ==
                                          ConnectionState.waiting) {
                                        return const CustomCircularProgressIndicator();
                                      }
                                      if (snapshot.hasData) {
                                        if (snapshot.data != null) {
                                          var listData =
                                              snapshot.data?.data ?? [];
                                          return ClipRRect(
                                            borderRadius:
                                                BorderRadius.circular(5.r),
                                            child: InkWell(
                                              onTapDown: (details) {
                                                AnalyticsService
                                                    .observer.analytics
                                                    .logEvent(
                                                        name:
                                                            "go_to_top_wishlist",
                                                        parameters: {
                                                      "wishlist_count":
                                                          listData.length
                                                    });
                                                Get.toNamed(
                                                    Routes.WISHLISTDETAIL(
                                                        'top'),
                                                    arguments: {
                                                      'wishlist': listData,
                                                      'title': 'Top Wishlist',
                                                      'id': 2,
                                                    });
                                              },
                                              child: AllWishlist(
                                                  listWishlist: listData,
                                                  categoryName: 'Top Wishlist'),
                                            ),
                                          );
                                        } else {
                                          return const SizedBox();
                                        }
                                      }
                                      return const CustomCircularProgressIndicator();
                                    }),
                          );
                        },
                      ),
                    )),
                Flexible(
                  child: Container(
                    margin: const EdgeInsets.symmetric(vertical: 10),
                    width: MediaQuery.of(context).size.width,
                    height: MediaQuery.of(context).size.height,
                    child: FutureBuilder(
                        future: controller.getAllCategory(),
                        builder: (BuildContext context, snapshot) {
                          if (snapshot.connectionState ==
                              ConnectionState.waiting) {
                            return const CustomCircularProgressIndicator();
                          }
                          if (snapshot.hasData) {
                            if (snapshot.data != null) {
                              return MediaQuery.removePadding(
                                context: context,
                                removeTop: true,
                                child: GridView.builder(
                                  gridDelegate:
                                      SliverGridDelegateWithFixedCrossAxisCount(
                                          crossAxisSpacing: AppDimen.h10,
                                          crossAxisCount: 3,
                                          mainAxisSpacing: 10),
                                  padding: EdgeInsets.symmetric(
                                      horizontal: AppDimen.h12),
                                  physics: const NeverScrollableScrollPhysics(),
                                  shrinkWrap: true,
                                  itemCount: snapshot.data?.length ?? 0,
                                  itemBuilder: (context, index) {
                                    var category = snapshot.data![index];
                                    return ClipRRect(
                                      borderRadius: BorderRadius.circular(5.r),
                                      child: FutureBuilder(
                                          future: controller
                                              .getAllWishlistByCategoryId(
                                                  category.id ?? 0),
                                          builder:
                                              (BuildContext context, snapshot) {
                                            var listData = snapshot.data ?? [];
                                            if (snapshot.hasData) {
                                              return InkWell(
                                                onTapDown: (details) {
                                                  AnalyticsService
                                                      .observer.analytics
                                                      .logEvent(
                                                          name:
                                                              "go_to_category_wishlist",
                                                          parameters: {
                                                        "wishlist_count":
                                                            listData.length,
                                                        "category_name":
                                                            category.name
                                                      });
                                                  Get.toNamed(
                                                      Routes.WISHLISTDETAIL(
                                                          "${category.id}"),
                                                      arguments: {
                                                        'wishlist': listData,
                                                        'title': category.name,
                                                        'id': 3,
                                                      });
                                                },
                                                child: AllWishlist(
                                                    listWishlist: listData,
                                                    showOne: true,
                                                    categoryName:
                                                        '${category.name}'),
                                              );
                                            }
                                            return const CustomCircularProgressIndicator();
                                          }),
                                    );
                                  },
                                ),
                              );
                            } else {
                              return const SizedBox();
                            }
                          }
                          return const CustomCircularProgressIndicator();
                        }),
                  ),
                ),
              ],
            ),
          ),
        )
      ],
    ));
  }
}

class AllWishlist extends StatelessWidget {
  const AllWishlist(
      {super.key,
      required this.listWishlist,
      required this.categoryName,
      this.showOne = false});

  final List<WishlistData> listWishlist;
  final String categoryName;
  final bool showOne;

  @override
  Widget build(BuildContext context) {
    int wishlistCount = listWishlist.length;
    return Stack(
      children: [
        showOne
            ? wishlistItem(wishlistCount, 0)
            : Column(
                children: [
                  Expanded(
                    child: Row(
                      children: [
                        Expanded(child: wishlistItem(wishlistCount, 0)),
                        Expanded(child: wishlistItem(wishlistCount, 1)),
                      ],
                    ),
                  ),
                  Expanded(
                    child: Row(
                      children: [
                        Expanded(child: wishlistItem(wishlistCount, 2)),
                        Expanded(child: wishlistItem(wishlistCount, 3)),
                      ],
                    ),
                  )
                ],
              ),
        Container(
          width: double.infinity,
          decoration: const BoxDecoration(
              gradient: LinearGradient(
                  colors: [AppColor.black50, AppColor.black10, AppColor.black5],
                  begin: Alignment.bottomCenter,
                  end: Alignment.topCenter)),
          child: Container(
            margin: EdgeInsets.only(left: AppDimen.h8, bottom: AppDimen.h12),
            child: Align(
                alignment: Alignment.bottomLeft,
                child: Text(
                  categoryName,
                  style: AppFont.componentMediumBold
                      .copyWith(color: AppColor.white),
                )),
          ),
        )
      ],
    );
  }

  Widget wishlistItem(int wishlistCount, int index) {
    double spacing = 2;
    return Container(
        margin: EdgeInsets.only(
            right: (index == 0 || index == 2) ? spacing : 0,
            bottom: (index == 0 || index == 1) ? spacing : 0,
            left: (index == 1 || index == 3) ? spacing : 0,
            top: (index == 2 || index == 3) ? spacing : 0),
        height: double.infinity,
        decoration: const BoxDecoration(color: AppColor.disable),
        child: wishlistCount >= index + 1
            ? CachedImageWidget(
                imageUrl: listWishlist[index].product?.photo ?? '',
                width: double.infinity,
                height: double.infinity,
                fit: BoxFit.fill,
              )
            : null);
  }
}
