import 'package:get/get.dart';
import 'package:mobile_crm/data/providers/db/database.dart';
import 'package:mobile_crm/data/repository/wishlist_repository.dart';

class WishlistController extends GetxController {
  final wishlistRepo = Get.find<WishlistRepositoryIml>();

  Future<List<WishlistData>> getAllWishlist() async =>
      await wishlistRepo.getAllWishlist();

  Future<List<WishlistCategoryData>> getAllCategory() async =>
      await wishlistRepo.getWishlistCategory();

  Future<List<WishlistData>> getAllWishlistByCategoryId(int categoryId) async =>
      await wishlistRepo.getAllWishlistByCategoryId(categoryId);
}
