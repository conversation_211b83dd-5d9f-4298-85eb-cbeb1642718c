import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/address/address_export.dart';
import 'package:mobile_crm/app/utils/utils.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/app_strings.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:flutter_svg/flutter_svg.dart';

class EditAddressForm extends GetView<EditAddressController> {
  const EditAddressForm({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Form(
          key: controller.validateFormKey,
          autovalidateMode: AutovalidateMode.always,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _inputItem(
                Strings.addressLabel.tr,
                "e.g My House / My Office",
                TextInputType.text,
                controller.labelController,
                validator: (value) {
                  if (value == '') {
                    return 'Label cannot be empty';
                  }

                  if (Utils.isEmoji(value ?? '')) {
                    return 'Don\'t use emoji';
                  }
                  return null;
                },
              ),
              Obx(
                () => _inputItem(
                  Strings.address.tr,
                  Strings.exAddress.tr,
                  TextInputType.multiline,
                  controller.addressController.value,
                  validator: (value) {
                    if (value == '') {
                      return Strings.valueCannotEmpty
                          .trParams({'value': Strings.address.tr});
                    }
                    if (Utils.isEmoji(value ?? '')) {
                      return 'Don\'t use emoji';
                    }
                    return null;
                  },
                ),
              ),
              SizedBox(
                height: 200,
                width: MediaQuery.of(context).size.width,
                child: Stack(
                  children: [
                    Obx(
                      () => GoogleMap(
                        mapType: MapType.terrain,
                        initialCameraPosition: CameraPosition(
                          target: LatLng(
                              controller
                                      .latitudeController.value.text.isNotEmpty
                                  ? double.tryParse(
                                      controller.latitudeController.value.text)!
                                  : 0,
                              controller
                                      .longitudeController.value.text.isNotEmpty
                                  ? double.tryParse(controller
                                      .longitudeController.value.text)!
                                  : 0),
                          zoom: 18,
                        ),
                        onMapCreated: (GoogleMapController ctrl) {
                          controller.mapController.complete(ctrl);
                        },
                        onCameraIdle: () async {
                          GoogleMapController ctrl =
                              await controller.mapController.future;
                          CameraUpdate cameraUpdate =
                              CameraUpdate.newCameraPosition(CameraPosition(
                            target: LatLng(
                                controller.latitudeController.value.text
                                        .isNotEmpty
                                    ? double.tryParse(controller
                                        .latitudeController.value.text)!
                                    : 0,
                                controller.longitudeController.value.text
                                        .isNotEmpty
                                    ? double.tryParse(controller
                                        .longitudeController.value.text)!
                                    : 0),
                            zoom: 18,
                          ));
                          ctrl.moveCamera(cameraUpdate);
                        },
                        zoomControlsEnabled: false,
                        zoomGesturesEnabled: false,
                        onTap: (_) async {
                          var result = await controller.goToMap();
                          controller.setDataFromMap(result);
                        },
                      ),
                    ),
                    Center(
                      child: SvgPicture.asset(
                        "assets/map-pin-3-fill.svg",
                        color: Colors.red,
                      ),
                    ),
                  ],
                ),
              ),
              Obx(
                () => _inputItem(
                  "Latitude",
                  "e.g -7.723312",
                  TextInputType.text,
                  controller.latitudeController.value,
                ),
              ),
              Obx(
                () => _inputItem(
                  "Longitude",
                  "e.g 110.361805",
                  TextInputType.text,
                  controller.longitudeController.value,
                ),
              ),

              _inputItem(
                Strings.receiptPhoneNumber.tr,
                "e.g 08xxxxxxxxxxx",
                TextInputType.phone,
                controller.phoneController,
                validator: (value) {
                  if (value == '') {
                    return Strings.valueCannotEmpty
                        .trParams({'value': Strings.phoneNumber.tr});
                  }
                  return controller.isPhoneNumber(value ?? '0')
                      ? Strings.invalidValue
                          .trParams({'value': Strings.phoneNumber.tr})
                      : null;
                },
              ),
              _inputItem(
                Strings.province.tr,
                "e.g Special Region of Yogyakarta",
                TextInputType.text,
                controller.provinceController,
                validator: (value) {
                  if (Utils.isEmoji(value ?? '')) {
                    return 'Don\'t use emoji';
                  }
                  return null;
                },
              ),
              _inputItem(
                Strings.district.tr,
                "e.g Yogyakarta City",
                TextInputType.text,
                controller.cityController,
                validator: (value) {
                  if (Utils.isEmoji(value ?? '')) {
                    return 'Don\'t use emoji';
                  }
                  return null;
                },
              ),

              // _inputItem("Postal Code", "e.g 12345", TextInputType.number,
              //     controller.postalController),
            ],
          ),
        ),
      ),
    );
  }

  Widget _inputItem(String title, String label, TextInputType textInputType,
      TextEditingController controller,
      {FormFieldValidator<String>? validator, Key? key}) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: AppFont.paragraphSmallBold,
        ),
        5.0.height,
        TextFormField(
            key: key,
            keyboardType: textInputType,
            validator: validator,
            controller: controller,
            style: AppFont.componentSmall,
            minLines: 1,
            maxLines: textInputType == TextInputType.multiline ? 10 : 1,
            autofocus: false,
            decoration: AppInput.defaultTheme.copyWith(hintText: label)),
        10.0.height
      ],
    );
  }
}
