import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/screen_wrapper/views/app_page_wrapper.dart';
import 'package:mobile_crm/app/widget/app_bar_widget/app_bar_widget.dart';
import 'package:mobile_crm/app/widget/button_widget/button_widget.dart';
import 'package:mobile_crm/app/widget/custom_circular_progress_widget.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/values.dart';

import '../controllers/edit_address_controller.dart';
import 'widgets/edit_address_form.dart';

class EditAddressScreen extends GetView<EditAddressController> {
  const EditAddressScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return PageWrapper(
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        backgroundColor: Colors.transparent,
        bottomNavigationBar: _bottomNavBar(context),
        body: Column(
          children: [
            AppBarWidget(
              title: "Address Detail",
              actions: [
                controller.addressId != -1
                    ? IconButton(
                        onPressed: () => controller.deleteAddress(),
                        icon: Icon(
                          Icons.delete,
                          size: Constants.iconSize(context),
                        ),
                      )
                    : const SizedBox()
              ],
            ),
            const Flexible(
              child: EditAddressForm(),
            )
          ],
        ),
      ),
    );
  }

  Widget _bottomNavBar(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: PrimaryButton(
        onPressed: () {
          if (controller.validateFormKey.currentState?.validate() != null) {
            if (controller.validateFormKey.currentState!.validate()) {
              controller.addressId != -1
                  ? controller.updateAddress()
                  : controller.addAddress();
            }
          }
        },
        text: Strings.save.tr,
        type: PrimaryButtonType.type4,
        child: Obx(
          () => controller.isLoading.value
              ? const CustomCircularProgressIndicator()
              : Text(
                  Strings.save.tr,
                  style: AppFont.componentSmallBold
                      .copyWith(color: AppColor.white),
                ),
        ),
      ),
    );
  }
}
