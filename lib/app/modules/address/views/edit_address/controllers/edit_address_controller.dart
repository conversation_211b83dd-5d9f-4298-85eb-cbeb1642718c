import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:geocoding/geocoding.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:mobile_crm/app/modules/address/address_export.dart';
import 'package:mobile_crm/app/utils/location_helper.dart';
import 'package:mobile_crm/app/utils/logger.dart';
import 'package:mobile_crm/app/utils/toast.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/data/models/address_model.dart';
import 'package:mobile_crm/data/repository/user_repository.dart';
import 'package:mobile_crm/data/services/local_storage.dart';
import 'package:mobile_crm/routes/app_pages.dart';

class EditAddressController extends GetxController {
  final userRepo = Get.find<UserRepositoryIml>();
  final addrController = Get.find<AddressController>();
  final TextEditingController labelController = TextEditingController();
  final addressController = TextEditingController().obs;
  final TextEditingController phoneController = TextEditingController();
  final TextEditingController provinceController = TextEditingController();
  final TextEditingController cityController = TextEditingController();
  final TextEditingController postalController = TextEditingController();
  final latitudeController = TextEditingController().obs;
  final longitudeController = TextEditingController().obs;
  final store = LocalStorageService();
  final validateFormKey = GlobalKey<FormState>();

  /// Google Map
  Completer<GoogleMapController> mapController = Completer();

  var address = AddressModel().obs;
  var placeMark = Placemark().obs;
  var isLoading = false.obs;
  int addressId = -1;

  @override
  void onInit() {
    getAddressFromArgument();
    super.onInit();
  }

  void getAddressFromArgument() {
    if (Get.arguments != null) {
      infoLogger(
          "EAC", "Get Args \n${Get.arguments.runtimeType}\n${Get.arguments}");
      if (Get.arguments['placemark'] != null &&
          Get.arguments['placemark'].runtimeType == address.value.runtimeType) {
        address.value = Get.arguments['placemark'];
        String completeAddress =
            "${address.value.address} ${address.value.city} ${address.value.province} ${address.value.postalCode}";
        setAddress(completeAddress);
        setLabel(address.value.label);
        setPhoneNumber(address.value.phone);
        setProvince(address.value.province);
        setCity(address.value.city);
        setProvince(address.value.postalCode);
        setLatitude(address.value.latitude);
        setLongitude(address.value.longitude);
        addressId = address.value.membersAddressId ?? -1;
        infoLogger('pos', "address id $addressId \n${address.value.toJson()}");
      } else if (Get.arguments['placemark'] != null &&
          Get.arguments['placemark'].runtimeType ==
              placeMark.value.runtimeType) {
        placeMark.value = Get.arguments['placemark'];
        var latLng = Get.arguments['latlng'];
        String completeAddress =
            "${placeMark.value.street ?? ''} ${placeMark.value.postalCode} ${placeMark.value.subLocality} ${placeMark.value.locality} ${placeMark.value.subAdministrativeArea} ${placeMark.value.administrativeArea}";
        setAddress(completeAddress);
        setLatitude(latLng['latitude']);
        setLongitude(latLng['longitude']);
        setProvince(placeMark.value.administrativeArea);
        setCity(placeMark.value.subAdministrativeArea);
        // setPhoneNumber(store.user?.phone ?? "");
        // setPostalCode(placeMark.value.postalCode);
      }
    }
  }

  void setDataFromMap(Map<String, dynamic> data) {
    if (data['placemark'] != null &&
        data['placemark'].runtimeType == placeMark.value.runtimeType) {
      placeMark.value = data['placemark'];
      var latLng = data['latlng'];
      infoLogger("", latLng);
      String completeAddress =
          "${placeMark.value.street ?? ''} ${placeMark.value.postalCode} ${placeMark.value.subLocality} ${placeMark.value.locality} ${placeMark.value.subAdministrativeArea} ${placeMark.value.administrativeArea}";
      setAddress(completeAddress);
      setLatitude(latLng['latitude']);
      setLongitude(latLng['longitude']);
      setProvince(placeMark.value.administrativeArea);
      setCity(placeMark.value.subAdministrativeArea);
    }
  }

  Future<dynamic> goToMap() async {
    Map<String, double> latLong = await LocationHelper.instance.getLatling();
    addrController.assignInitialCamPosition(
      LatLng(
          latitudeController.value.text.isNotEmpty
              ? double.tryParse(latitudeController.value.text)!
              : latLong['latitude']!,
          longitudeController.value.text.isNotEmpty
              ? double.tryParse(longitudeController.value.text)!
              : latLong['longitude']!),
    );
    return await Get.toNamed(Routes.MAPVIEW);
  }

  void setLoading(bool loading) => isLoading.value = loading;

  void setAddress(String address) {
    addressController.value.text = address;
  }

  void setLabel(String? label) {
    labelController.text = label ?? '';
  }

  void setPhoneNumber(String? number) {
    if (GetUtils.isPhoneNumber(number ?? '')) {
      phoneController.text = (number ?? '').toLocal;
    }
  }

  bool isPhoneNumber(String number) {
    if (GetUtils.isPhoneNumber(number)) {
      return false;
    }
    return true;
  }

  void setProvince(String? province) {
    provinceController.text = province ?? '';
  }

  void setLatitude(double? latitude) {
    latitudeController.value.text = latitude.toString();
  }

  void setLongitude(double? longitude) {
    longitudeController.value.text = longitude.toString();
  }

  void setCity(String? city) {
    cityController.text = city ?? '';
  }

  void setPostalCode(String? postal) {
    postalController.text = postal ?? '';
  }

  Future deleteAddress() async {
    setLoading(true);
    await userRepo.deleteAddress(addressId.toString()).then((value) async {
      Toast.show(value.message.toString(),
          type: value.status ? ToastType.dark : ToastType.error);
      if (value.status) {
        Get.back();
      }
    }).whenComplete(() => setLoading(false));
  }

  Future updateAddress() async {
    setLoading(true);
    AddressModel addressModel = AddressModel();
    addressModel.label = labelController.text;
    addressModel.province = provinceController.text;
    addressModel.city = cityController.text;
    addressModel.latitude = double.tryParse(latitudeController.value.text);
    addressModel.longitude = double.tryParse(longitudeController.value.text);
    addressModel.address = addressController.value.text;
    addressModel.postalCode = postalController.text;
    addressModel.membersAddressId = addressId;
    addressModel.phone =
        phoneController.text == '' ? store.user?.phone : phoneController.text;
    infoLogger("EAC", "Update Address ${addressModel.toJson()}");
    await userRepo.updateAddress(addressModel).then((value) async {
      Toast.show(value.message.toString(),
          type: value.status ? ToastType.dark : ToastType.error);
      if (value.status) {
        Get.back();
        Get.toNamed(Routes.ADDRESS);
        await addrController.getUserAddressList();
      }
    }).whenComplete(() => setLoading(false));
  }

  Future addAddress() async {
    setLoading(true);
    AddressModel addressModel = AddressModel();
    addressModel.label = labelController.text;
    addressModel.province = provinceController.text;
    addressModel.city = cityController.text;
    addressModel.latitude = double.parse(latitudeController.value.text);
    addressModel.longitude = double.parse(longitudeController.value.text);
    addressModel.address = addressController.value.text;
    addressModel.postalCode = postalController.text;
    addressModel.phone =
        phoneController.text == '' ? store.user?.phone : phoneController.text;
    infoLogger("EAC", "Add Address ${addressModel.toJson()}");
    await userRepo.addAddress(addressModel).then((value) async {
      Toast.show(value.message.toString(),
          type: value.status ? ToastType.dark : ToastType.error);
      if (value.status) {
        Get.toNamed(Routes.ADDRESS);
        await addrController.getUserAddressList();
      }
    }).whenComplete(() => setLoading(false));
  }
}
