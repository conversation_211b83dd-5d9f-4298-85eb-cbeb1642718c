import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/address/address_export.dart';
import 'package:mobile_crm/app/modules/screen_wrapper/controller/wrapper_controller.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/values.dart';
import 'package:mobile_crm/data/models/address_model.dart';
import 'package:mobile_crm/routes/app_pages.dart';
import 'package:flutter_slidable/flutter_slidable.dart';

import 'addres_loading.dart';

class AddressSaved extends GetView<AddressController> {
  const AddressSaved({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: AppDimen.h16),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            'Saved places',
            style: AppFont.componentMediumBold,
          ),
          AppDimen.h8.height,
          Obx(() {
            return FutureBuilder(
              future: controller.address.value,
              builder: (BuildContext context, snapshot) {
                if (snapshot.connectionState != ConnectionState.done) {
                  return const AddressLoading();
                }
                if (snapshot.hasData) {
                  return _listAddress(context, snapshot);
                } else if (snapshot.hasError) {
                  return Icon(Icons.error_outline,
                      size: Constants.iconSize(context));
                } else {
                  return const AddressLoading();
                }
              },
            );
          })
        ],
      ),
    );
  }

  Widget _listAddress(
      BuildContext context, AsyncSnapshot<List<AddressModel>> snapshot) {
    return MediaQuery.removePadding(
      context: context,
      removeTop: true,
      child: (snapshot.data?.length ?? 0) < 3
          ? Column(
              mainAxisSize: MainAxisSize.min,
              children: [_itemListAddress(snapshot), _addNewAddress(context)],
            )
          : _itemListAddress(snapshot),
    );
  }

  Widget _itemListAddress(AsyncSnapshot<List<AddressModel>> snapshot) {
    final controller = Get.find<AddressController>();
    final wrapperController = Get.find<WrapperController>();
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: snapshot.data?.length ?? 0,
      itemBuilder: (context, index) {
        AddressModel address = snapshot.data![index];
        String completeAddress =
            "${address.address} ${address.city} ${address.province} ${address.postalCode}";
        return Slidable(
          endActionPane: index < snapshot.data!.length - 1
              ? ActionPane(
                  motion: const ScrollMotion(),
                  children: [
                    SlidableAction(
                      flex: 1,
                      onPressed: (_) {
                        controller.useCurrent.value = false;
                        Get.toNamed(Routes.ADDRESSEDIT, arguments: {
                          "placemark": address,
                        });
                      },
                      backgroundColor: const Color(0xFF0392CF),
                      foregroundColor: Colors.white,
                      icon: Icons.edit,
                      label: 'Edit',
                    ),
                    SlidableAction(
                      flex: 1,
                      onPressed: (_) {
                        controller
                            .deleteAddress(address.membersAddressId.toString());
                      },
                      backgroundColor: const Color.fromARGB(255, 207, 3, 3),
                      foregroundColor: Colors.white,
                      icon: Icons.delete,
                      label: 'Delete',
                    ),
                  ],
                )
              : null,
          child: InkWell(
            onTap: () {
              controller.store.mainAddress = address;
              Get.back(result: address);
            },
            child: Container(
              margin: EdgeInsets.only(bottom: AppDimen.h4),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Container(
                      padding: EdgeInsets.all(AppDimen.h4),
                      decoration: BoxDecoration(
                          color: wrapperController
                              .getPrimaryColor()
                              .withAlpha(150),
                          borderRadius:
                              BorderRadius.all(Radius.circular(AppDimen.h6))),
                      child: Icon(
                        Icons.bookmark,
                        size: AppDimen.h14,
                        color: wrapperController
                            .getPrimaryColor()
                            .changeColorBasedOnBackgroundColor()
                            .$1,
                      )),
                  AppDimen.h10.width,
                  Flexible(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          ((address.label?.isEmpty == true)
                                      ? '-'
                                      : address.label)
                                  ?.capitalizeFirst ??
                              '-',
                          style: AppFont.componentSmallBold,
                        ),
                        AppDimen.h2.height,
                        Flexible(
                          child: Text(
                            completeAddress.replaceAll("null ", ""),
                            style: AppFont.componentSmall
                                .copyWith(color: AppColor.black70),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  )
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _addNewAddress(BuildContext context) {
    return GestureDetector(
      onTap: () => Get.toNamed(Routes.ADDRESSEDIT),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(5), color: AppColor.ink05),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(Icons.add,
                color: Colors.blueGrey, size: Constants.iconSize(context)),
            5.0.width,
            Text(
              Strings.addNewAddress.tr,
              style: AppFont.componentSmall,
            ),
          ],
        ),
      ),
    );
  }
}
