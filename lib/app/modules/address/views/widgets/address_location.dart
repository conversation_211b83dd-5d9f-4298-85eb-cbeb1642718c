import 'package:flutter/material.dart';
import 'package:geocoding/geocoding.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/widget/button_widget/button_widget.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/values.dart';
import 'package:mobile_crm/routes/app_pages.dart';

import '../../controllers/address_controller.dart';
import 'addres_loading.dart';
import 'address_item_widget.dart';

class AddressLocation extends StatelessWidget {
  const AddressLocation({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<AddressController>();
    return SliverToBoxAdapter(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          GestureDetector(
            child: Container(
              padding: const EdgeInsets.all(8),
              margin: const EdgeInsets.only(bottom: 5),
              child: PrimaryButton(
                type: PrimaryButtonType.type4,
                onPressed: () => controller.getAddressByGPS(),
                text: Strings.getLocation.tr,
                width: MediaQuery.of(context).size.width,
                child:
                    Row(mainAxisAlignment: MainAxisAlignment.center, children: [
                  Icon(Icons.my_location,
                      color: AppColor.white, size: Constants.iconSize(context)),
                  5.0.width,
                  Text(
                    Strings.getLocation.tr,
                    style: AppFont.componentSmallBold
                        .copyWith(color: AppColor.white),
                  ),
                ]),
              ),
            ),
          ),
          Flexible(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8.0),
              child: Obx(() {
                return FutureBuilder(
                    future: controller.placeMarks.value,
                    builder: (BuildContext context, snapshot) {
                      if (snapshot.connectionState != ConnectionState.done) {
                        return const AddressLoading();
                      }

                      if (snapshot.hasData) {
                        return MediaQuery.removePadding(
                          removeTop: true,
                          context: context,
                          child: ListView.builder(
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                            itemCount: snapshot.data?.length ?? 0,
                            itemBuilder: (context, index) {
                              Placemark placemark = snapshot.data![index];
                              String address =
                                  "${placemark.street ?? ''} ${placemark.postalCode} ${placemark.subLocality} ${placemark.locality} ${placemark.subAdministrativeArea} ${placemark.administrativeArea}";
                              return AddressItemWidget(
                                  placemark: placemark,
                                  onTap: () => Get.toNamed(Routes.ADDRESSEDIT,
                                      arguments: placemark),
                                  address: address,
                                  type: ItemType.location);
                            },
                          ),
                        );
                      } else if (snapshot.hasError) {
                        return Icon(Icons.error_outline,
                            size: Constants.iconSize(context));
                      } else {
                        return const AddressLoading();
                      }
                    });
              }),
            ),
          )
        ],
      ),
    );
  }
}
