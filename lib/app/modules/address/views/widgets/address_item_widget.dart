import 'package:flutter/material.dart';
import 'package:geocoding/geocoding.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/core/extensions/double_extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/values.dart';
import 'package:mobile_crm/data/models/address_model.dart';
import 'package:mobile_crm/routes/app_pages.dart';

class AddressItemWidget extends StatelessWidget {
  const AddressItemWidget(
      {Key? key,
      required this.address,
      this.label,
      this.onTap,
      this.type = ItemType.location,
      this.addressModel,
      this.placemark})
      : super(key: key);
  final String? address;
  final String? label;
  final VoidCallback? onTap;
  final AddressModel? addressModel;
  final Placemark? placemark;
  final ItemType type;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
        onTap: onTap,
        child: type == ItemType.saved
            ? _itemSaved(context)
            : _itemLocation(context));
  }

  Widget _itemSaved(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Row(
          children: [
            Icon(Icons.stars_rounded,
                color: Colors.black, size: Constants.iconSize(context)),
            5.0.width,
            label == ''
                ? const SizedBox()
                : Text(
                    "$label",
                    style: AppFont.paragraphSmallBold,
                  ),
          ],
        ),
        5.0.height,
        Row(
          children: [
            Expanded(
              child: Text(
                "$address",
                style: AppFont.paragraphSmall,
              ),
            ),
            GestureDetector(
                onTap: () =>
                    Get.toNamed(Routes.ADDRESSEDIT, arguments: addressModel),
                child: Icon(Icons.edit_location_outlined,
                    color: Colors.blueGrey, size: Constants.iconSize(context))),
          ],
        ),
        const Divider()
      ],
    );
  }

  Widget _itemLocation(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Row(
          children: [
            Icon(Icons.location_pin,
                color: Colors.redAccent, size: Constants.iconSize(context)),
            10.0.width,
            Expanded(
              child: Text(
                "$address",
                style: AppFont.paragraphSmall,
              ),
            ),
          ],
        ),
        const Divider()
      ],
    );
  }
}

enum ItemType { saved, location }
