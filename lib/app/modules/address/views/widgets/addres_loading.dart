import 'package:flutter/material.dart';
import 'package:mobile_crm/app/widget/shimmer_loading_widget.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';

class AddressLoading extends StatelessWidget {
  const AddressLoading({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MediaQuery.removePadding(
      removeTop: true,
      context: context,
      child: ListView.separated(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: 3,
        separatorBuilder: (context, index) => const Divider(),
        itemBuilder: (context, index) => Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Row(
              children: [
                const ShimmerWidget(width: 20, height: 20),
                5.0.width,
                const ShimmerWidget(
                  width: 50,
                  height: 20,
                  radius: 5,
                ),
              ],
            ),
            5.0.height,
            const ShimmerWidget(
              width: 150,
              height: 20,
              radius: 5,
            ),
            const Divider()
          ],
        ),
      ),
    );
  }
}
