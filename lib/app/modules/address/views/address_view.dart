import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/screen_wrapper/views/app_page_wrapper.dart';
import 'package:mobile_crm/app/utils/location_helper.dart';
import 'package:mobile_crm/app/widget/app_bar_widget/app_bar_widget.dart';
import 'package:mobile_crm/app/widget/custom_circular_progress_widget.dart';
import 'package:mobile_crm/app/widget/shimmer_loading_widget.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/values.dart';
import 'package:mobile_crm/data/models/address_model.dart';

import '../../../../data/services/analytics_service.dart';
import '../../screen_wrapper/controller/wrapper_controller.dart';
import '../controllers/address_controller.dart';
import 'widgets/address_saved_profile.dart';

class AddressScreen extends GetView<AddressController> {
  const AddressScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    AnalyticsService.observer.analytics.logScreenView(
        screenName: "Address Screen", screenClass: "AddressScreen");
    final wrapperController = Get.find<WrapperController>();
    return PageWrapper(
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        backgroundColor: Colors.transparent,
        body: SizedBox(
          height: double.maxFinite,
          child: Column(
            children: [
              const AppBarWidget(
                title: "Address",
              ),
              FutureBuilder(
                  future: controller.placeMarks.value,
                  builder: (BuildContext context, snapshot) {
                    if (snapshot.connectionState != ConnectionState.done) {
                      return Container(
                        width: MediaQuery.of(context).size.width,
                        padding: EdgeInsets.symmetric(
                            horizontal: AppDimen.h16, vertical: AppDimen.h4),
                        decoration: const BoxDecoration(color: AppColor.white),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Stack(
                              children: [
                                Container(
                                  padding: EdgeInsets.all(AppDimen.h4),
                                  decoration: BoxDecoration(
                                    color: wrapperController
                                        .getPrimaryColor()
                                        .withAlpha(150),
                                    borderRadius: BorderRadius.all(
                                      Radius.circular(AppDimen.h6),
                                    ),
                                  ),
                                  child: SizedBox(
                                    width: AppDimen.h14,
                                    height: AppDimen.h14,
                                  ),
                                ),
                                Positioned(
                                  left: 6,
                                  right: 1,
                                  top: 1,
                                  bottom: 6,
                                  child: Transform.rotate(
                                    angle: -0.8,
                                    child: Icon(
                                      Icons.send,
                                      size: AppDimen.h14,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            AppDimen.h10.width,
                            Flexible(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisAlignment: MainAxisAlignment.start,
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Text(
                                    "Use current location",
                                    style: AppFont.componentSmallBold,
                                  ),
                                  AppDimen.h2.height,
                                  ShimmerWidget(
                                    height: AppDimen.h14,
                                    width: AppDimen.h192,
                                  )
                                ],
                              ),
                            )
                          ],
                        ),
                      );
                    }

                    if (snapshot.hasData) {
                      var item = snapshot.data?.firstOrNull;
                      return InkWell(
                        onTap: () async {
                          Map<String, double> latlng =
                              await LocationHelper.instance.getLatling();
                          AddressModel address = AddressModel(
                            city: item!.locality!,
                            postalCode: item.postalCode,
                            province: item.administrativeArea,
                            district: item.subLocality,
                            address:
                                "${item.street}, ${item.subLocality}, ${item.locality}, ${item.subAdministrativeArea}, ${item.administrativeArea}",
                            latitude: latlng['latitude'],
                            longitude: latlng['longitude'],
                          );
                          controller.store.mainAddress = address;
                          Get.back(result: address);
                        },
                        child: Container(
                          width: MediaQuery.of(context).size.width,
                          padding: EdgeInsets.symmetric(
                              horizontal: AppDimen.h16, vertical: AppDimen.h4),
                          decoration:
                              const BoxDecoration(color: AppColor.white),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Stack(
                                children: [
                                  Container(
                                    padding: EdgeInsets.all(AppDimen.h4),
                                    decoration: BoxDecoration(
                                      color: wrapperController
                                          .getPrimaryColor()
                                          .withAlpha(150),
                                      borderRadius: BorderRadius.all(
                                        Radius.circular(AppDimen.h6),
                                      ),
                                    ),
                                    child: SizedBox(
                                      width: AppDimen.h14,
                                      height: AppDimen.h14,
                                    ),
                                  ),
                                  Positioned(
                                    left: 6,
                                    right: 1,
                                    top: 1,
                                    bottom: 6,
                                    child: Transform.rotate(
                                      angle: -0.8,
                                      child: Icon(
                                        Icons.send,
                                        size: AppDimen.h14,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              AppDimen.h10.width,
                              Flexible(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Text(
                                      "Use current location",
                                      style: AppFont.componentSmallBold,
                                    ),
                                    AppDimen.h2.height,
                                    Flexible(
                                      child: Text(
                                        "${item?.street}, ${item?.subLocality}, ${item?.locality}, ${item?.subAdministrativeArea}, ${item?.administrativeArea}",
                                        style: AppFont.componentSmall
                                            .copyWith(color: AppColor.black70),
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ),
                                  ],
                                ),
                              )
                            ],
                          ),
                        ),
                      );
                    } else if (snapshot.hasError) {
                      return Icon(Icons.error_outline,
                          size: Constants.iconSize(context));
                    } else {
                      return const CustomCircularProgressIndicator();
                    }
                  }),
              AppDimen.h10.height,
              const Flexible(
                child: AddressSaved(),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
