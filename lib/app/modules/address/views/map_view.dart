import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:mobile_crm/app/modules/address/controllers/address_controller.dart';
import 'package:mobile_crm/app/modules/screen_wrapper/views/app_page_wrapper.dart';
import 'package:mobile_crm/app/widget/app_bar_widget/app_bar_widget.dart';
import 'package:mobile_crm/app/widget/button_widget/button_widget.dart';
import 'package:mobile_crm/core/theme/app_color.dart';
import 'package:mobile_crm/core/theme/app_font.dart';
import 'package:mobile_crm/core/values/app_strings.dart';
import 'package:flutter_svg/flutter_svg.dart';

class MapScreen extends GetView<AddressController> {
  const MapScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return PageWrapper(
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        backgroundColor: Colors.transparent,
        bottomNavigationBar: SizedBox(
          width: MediaQuery.of(context).size.width,
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: PrimaryButton(
              text: Strings.save.tr,
              onPressed: () {
                var item = controller.placeMarker.value.lastOrNull;
                Map<String, dynamic> itemWithLatLng = {
                  "placemark": item,
                  "latlng": controller.latLng.value,
                };

                Get.back(result: itemWithLatLng);
              },
              type: PrimaryButtonType.type_2_1,
              child: Text(
                Strings.save.tr,
                style: AppFont.componentSmall.copyWith(
                  color: AppColor.white,
                ),
              ),
            ),
          ),
        ),
        body: Column(
          children: [
            const AppBarWidget(title: "Map"),
            Obx(() {
              var item = controller.placeMarker.value.firstOrNull;
              return Expanded(
                child: Stack(
                  alignment: Alignment.topCenter,
                  children: [
                    Obx(
                      () => GoogleMap(
                        mapType: MapType.terrain,
                        initialCameraPosition:
                            controller.inititalCameraPosition.value!,
                        onMapCreated: (GoogleMapController ctrl) {
                          controller.mapController.complete(ctrl);
                        },
                        onCameraIdle: () {
                          LatLng latLng = controller.locationList.value.last;
                          controller.latLng.value = {
                            "latitude": latLng.latitude,
                            "longitude": latLng.longitude,
                          };
                          controller.setMarker(
                            LatLng(latLng.latitude, latLng.longitude),
                          );
                        },
                        onCameraMove: (position) {
                          LatLng latLng = LatLng(position.target.latitude,
                              position.target.longitude);
                          controller.addLocationList(latLng);
                        },
                        onCameraMoveStarted: () {},
                        // markers: controller.marker.value,
                        myLocationButtonEnabled: true,
                        myLocationEnabled: true,
                      ),
                    ),
                    Center(
                      child: Padding(
                        padding: const EdgeInsets.only(bottom: 25),
                        child: SvgPicture.asset(
                          "assets/map-pin-3-fill.svg",
                          color: Colors.red,
                        ),
                      ),
                    ),
                    Positioned(
                      top: 10,
                      child: Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          color: Colors.white,
                        ),
                        width: MediaQuery.of(context).size.width * 0.95,
                        // height: 100,
                        padding: const EdgeInsets.symmetric(
                          vertical: 8,
                          horizontal: 8,
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              Strings.address.tr,
                              style: AppFont.buttonMedium,
                            ),
                            SizedBox(
                              width: MediaQuery.of(context).size.width,
                              child: Text(
                                "${item?.street}, ${item?.subLocality}, ${item?.locality}",
                                style: AppFont.componentSmall
                                    .copyWith(color: AppColor.black70),
                                textAlign: TextAlign.left,
                              ),
                            )
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              );
            }),
          ],
        ),
      ),
    );
  }
}
