import 'dart:async';

import 'package:flutter/material.dart';
import 'package:geocoding/geocoding.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/utils/location_helper.dart';
import 'package:mobile_crm/app/utils/logger.dart';
import 'package:mobile_crm/app/utils/toast.dart';
import 'package:mobile_crm/core/extensions/future_extensions.dart';
import 'package:mobile_crm/data/models/address_model.dart';
import 'package:mobile_crm/data/repository/user_repository.dart';
import 'package:mobile_crm/data/services/local_storage.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class AddressController extends GetxController {
  final userRepo = Get.find<UserRepositoryIml>();
  final store = LocalStorageService();

  var placeMarks = Future.value(<Placemark>[]).obs;
  var placeMarker = Rx<List<Placemark>>([]);
  var address = Future.value(<AddressModel>[]).obs;
  var isLoading = true.obs;

  final validateFormOnMapKey = GlobalKey<FormState>();

  final TextEditingController labelController = TextEditingController();
  final TextEditingController addressController = TextEditingController();
  final TextEditingController phoneController = TextEditingController();
  final TextEditingController provinceController = TextEditingController();
  final TextEditingController cityController = TextEditingController();
  final TextEditingController postalController = TextEditingController();
  final TextEditingController latitudeController = TextEditingController();
  final TextEditingController longitudeController = TextEditingController();

  /// Google map
  Completer<GoogleMapController> mapController = Completer();
  var inititalCameraPosition = Rx<CameraPosition?>(null);
  var marker = Rx<Set<Marker>>({});
  var latLng = Rx<Map<String, double>?>(null);

  var locationList = Rx<List<LatLng>>([]);
  var mode = Rx<String>("add");
  var useCurrent = Rx<bool>(false);

  @override
  onInit() async {
    await getAddressByGPS();
    await getUserAddressList();
    await assignInitialCamPosition(null);

    super.onInit();
  }

  Future<void> fetchAddress() async {
    await getAddressByGPS();
    await getUserAddressList();
  }

  void addLocationList(LatLng latLng) {
    locationList.value.add(latLng);
  }

  Future deleteAddress(String addressId) async {
    await userRepo.deleteAddress(addressId).then((value) async {
      Toast.show(
        value.message.toString(),
        type: value.status ? ToastType.dark : ToastType.error,
      );
      if (value.status) {
        await getUserAddressList();
      }
    });
  }

  bool isPhoneNumber(String number) {
    if (GetUtils.isPhoneNumber(number)) {
      return false;
    }
    return true;
  }

  void setLoading(bool loading) => isLoading.value = loading;

  Future<void> getAddressByGPS() async {
    placeMarks.value = LocationHelper.instance.getPlacemarks();
  }

  Future<void> assignInitialCamPosition(LatLng? latLong) async {
    if (latLong == null) {
      latLng.value = await LocationHelper.instance.getLatling();
      inititalCameraPosition.value = CameraPosition(
        target: LatLng(latLng.value!['latitude']!, latLng.value!['longitude']!),
        zoom: 18,
      );
      marker.value.add(
        Marker(
          markerId: const MarkerId("current_location1"),
          position:
              LatLng(latLng.value!['latitude']!, latLng.value!['longitude']!),
        ),
      );
      placeMarker.value = await LocationHelper.instance.getMarkerPosition(
          latLng.value!['latitude']!, latLng.value!['longitude']!);
    } else {
      latLng.value = {
        "latitude": latLong.latitude,
        "longitude": latLong.longitude
      };
      inititalCameraPosition.value = CameraPosition(
        target: LatLng(latLng.value!['latitude']!, latLng.value!['longitude']!),
        zoom: 18,
      );
      marker.value.add(
        Marker(
          markerId: const MarkerId("current_location1"),
          position:
              LatLng(latLng.value!['latitude']!, latLng.value!['longitude']!),
        ),
      );
      placeMarker.value = await LocationHelper.instance.getMarkerPosition(
          latLng.value!['latitude']!, latLng.value!['longitude']!);
    }
  }

  void setMarker(LatLng latlng) async {
    Set<Marker> newMark = {};
    newMark.add(
      Marker(
        markerId: const MarkerId("current_location1"),
        position: LatLng(latlng.latitude, latlng.longitude),
      ),
    );
    latLng.value = {
      "latitude": latlng.latitude,
      "longitude": latlng.longitude,
    };
    marker.value = newMark;
    placeMarker.value = await LocationHelper.instance
        .getMarkerPosition(latlng.latitude, latlng.longitude);
  }

  Future<List<AddressModel>> getUserAddressList() async =>
      address.value = userRepo.getAddress.withRetries(3);

  Future addAddress() async {
    setLoading(true);
    AddressModel addressModel = AddressModel();
    addressModel.label = labelController.text;
    addressModel.province = provinceController.text;
    addressModel.city = cityController.text;
    addressModel.latitude = latLng.value!['latitude'];
    addressModel.longitude = latLng.value!['longitude'];
    addressModel.address = addressController.text;
    // addressModel.postalCode =
    addressModel.phone = phoneController.text;
    infoLogger(addressModel.toJson().toString());
    setLoading(false);
  }
}
