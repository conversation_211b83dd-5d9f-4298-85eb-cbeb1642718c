import 'package:get/get.dart';
import 'package:mobile_crm/data/repository/cart_repository.dart';
import 'package:mobile_crm/data/repository/transaction_repository.dart';

import '../controllers/order_controller.dart';

class OrderBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => OrderController());
    Get.lazyPut(() => CartRepositoryIml());
    Get.lazyPut(() => TransactionRepositoryIml());
  }
}
