import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:mobile_crm/app/utils/app_alert.dart';
import 'package:mobile_crm/app/utils/logger.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/values.dart';
import 'package:mobile_crm/data/models/feedback_model.dart';
import 'package:mobile_crm/data/models/order_detail_model.dart';
import 'package:mobile_crm/data/models/payment_method_model.dart';
import 'package:mobile_crm/data/models/payments_model.dart';
import 'package:mobile_crm/data/models/server_response.dart';
import 'package:mobile_crm/data/models/tax_model.dart';
import 'package:mobile_crm/data/models/transaction_new_model.dart';
import 'package:mobile_crm/data/repository/cart_repository.dart';
import 'package:mobile_crm/data/repository/transaction_repository.dart';
import 'package:mobile_crm/data/services/in_app_review_service.dart';
import 'package:mobile_crm/data/services/local_storage.dart';
import 'package:mobile_crm/domain/repository/cart_repository.dart';
import 'package:mobile_crm/domain/repository/transaction_repository.dart';
import 'package:mobile_crm/routes/app_pages.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

import '../../../enum/order_status_enum.dart';

class OrderController extends GetxController {
  final CartRepository _repoCart = Get.find<CartRepositoryIml>();
  final TransactionRepository repoTransaction =
      Get.find<TransactionRepositoryIml>();
  TextEditingController phoneNumberController = TextEditingController();

  final StreamController<TransactionNewModel> _streamTransactionData =
      StreamController<TransactionNewModel>.broadcast();

  Stream<TransactionNewModel> get streamTransactionData =>
      _streamTransactionData.stream;

  final store = LocalStorageService();

  var newOrder = TransactionNewModel().obs;
  var listTax = <TaxModel>[].obs;
  var paymentData = PaymentDataModel().obs;
  var payment = PaymentModel().obs;
  var filledFeedback = false.obs;
  var selfOrderStatus = "".obs;
  var infoTimer = "".obs;

  Rx<XFile?> xFile = XFile("").obs;

  var loading = false.obs;
  var loadingProof = false.obs;

  late Timer timer;
  Timer? timerPayment;

  @override
  void onInit() async {
    await getArgumentsData();
    super.onInit();
    infoLogger('-- orderController...');
  }

  Future<void> getArgumentsData() async {
    if (Get.arguments.runtimeType == TransactionNewModel) {
      newOrder.value = Get.arguments;
      if (newOrder.value.path != null) {
        xFile.value = XFile(newOrder.value.path ?? '');
      }
      if (newOrder.value.selfOrder != null) {
        Future.delayed(const Duration(seconds: 1), () async {
          timer = Timer.periodic(
            const Duration(seconds: 3),
            (it) async {
              if (newOrder.value.status != OrderStatusEnum.expired.name) {
                await getSelfOrderStatus();
              } else if (newOrder.value.status !=
                  OrderStatusEnum.already_paid.name) {
                await getSelfOrderStatus();
              } else {
                it.cancel();
                InAppReviewService.instance.requestReview();
              }
            },
          );
        });
      } else {
        infoLogger('checking....');
        await check();
        Future.delayed(const Duration(seconds: 1), () async {
          timer = Timer.periodic(
            const Duration(seconds: 4),
            (it) async {
              if (newOrder.value.status?.toLowerCase() ==
                      OrderStatusEnum.arrived.name.toLowerCase() ||
                  newOrder.value.status?.toLowerCase() ==
                      OrderStatusEnum.received.name.toLowerCase() ||
                  newOrder.value.status?.toLowerCase() ==
                      OrderStatusEnum.cancel.name.toLowerCase() ||
                  newOrder.value.status?.toLowerCase() ==
                      OrderStatusEnum.ready.name.toLowerCase() ||
                  newOrder.value.status?.toLowerCase() ==
                      OrderStatusEnum.reject.name.toLowerCase() ||
                  newOrder.value.status?.toLowerCase() ==
                      OrderStatusEnum.payment_reject.name.toLowerCase()) {
                it.cancel();
                await InAppReviewService.requestReview();
                await _repoCart.deleteTransactionOrder(newOrder.value,
                    isOnOrder: true);
              } else {
                await check();
              }
            },
          );
        });
      }
    } else {
      newOrder.value.orderSalesId = (Get.parameters['id'] ?? '');
      newOrder.value = await _repoCart.getTransactionOrderByOutletId(0,
              isOnOrder: true, transactionNewModel: newOrder.value) ??
          TransactionNewModel();
      newOrder.refresh();
      if (newOrder.value.outletId != null) {
        if (newOrder.value.path != null) {
          xFile.value = XFile(newOrder.value.path ?? '');
        }
        if (newOrder.value.selfOrder != null) {
          Future.delayed(const Duration(seconds: 1), () async {
            timer = Timer.periodic(
              const Duration(seconds: 3),
              (it) async {
                if (newOrder.value.status != OrderStatusEnum.expired.name) {
                  await getSelfOrderStatus();
                } else if (newOrder.value.status !=
                    OrderStatusEnum.already_paid.name) {
                  await getSelfOrderStatus();
                } else {
                  it.cancel();
                  InAppReviewService.instance.requestReview();
                }
              },
            );
          });
        } else {
          infoLogger('checkin order status....');
          await check();
          Future.delayed(const Duration(seconds: 1), () async {
            timer = Timer.periodic(
              const Duration(seconds: 3),
              (it) async {
                if (newOrder.value.status?.toLowerCase() ==
                        OrderStatusEnum.arrived.name.toLowerCase() ||
                    newOrder.value.status?.toLowerCase() ==
                        OrderStatusEnum.received.name.toLowerCase() ||
                    newOrder.value.status?.toLowerCase() ==
                        OrderStatusEnum.cancel.name.toLowerCase() ||
                    newOrder.value.status?.toLowerCase() ==
                        OrderStatusEnum.ready.name.toLowerCase() ||
                    newOrder.value.status?.toLowerCase() ==
                        OrderStatusEnum.reject.name.toLowerCase() ||
                    newOrder.value.status?.toLowerCase() ==
                        OrderStatusEnum.payment_reject.name.toLowerCase()) {
                  it.cancel();
                  await InAppReviewService.requestReview();
                  await _repoCart.deleteTransactionOrder(newOrder.value,
                      isOnOrder: true);
                } else {
                  await check();
                }
              },
            );
          });
        }
      }
    }
  }

  Future<void> check() async {
    infoLogger('checking order');
    var result = await repoTransaction.getOrderDetail2(
        newOrder.value.orderSalesId ?? (Get.parameters['id'] ?? ''));
    infoLogger('order --> ${result}', result.data);
    if (result.status) {
      //updating order data based on api return
      newOrder.value.status = result.data?.status;
      newOrder.value.message = result.data?.message;
      newOrder.value.paymentTimeout = result.data?.paymentTimeout;
      await _repoCart.updateTransactionOrder(newOrder.value, isOnOrder: true);
      _streamTransactionData.sink.add(newOrder.value);
      newOrder.refresh();

      if (newOrder.value.status == "accept") {
        runPaymentTimer();
      }
    }
  }

  Future<void> runPaymentTimer() async {
    if (timerPayment != null) {
      infoLogger('timer already running...');
      return;
    }

    timerPayment = Timer.periodic(const Duration(seconds: 1), (timer) {
      infoTimer.value = "";
      if (getRemainingSeconds() > 0) {
        int remainingSeconds = getRemainingSeconds();
        if (remainingSeconds > 0) {
          int minutes = remainingSeconds ~/ 60;
          int seconds = remainingSeconds % 60;
          infoTimer.value =
              'Complete payment in ${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
        }
      } else {
        timerPayment?.cancel();
        timerPayment = null;
        infoLogger('stop timer payment');
        AppAlert.showInfoDialog(
            Get.context!, "Melebihi batas waktu pembayaran");
      }
      infoTimer.refresh();
    });
  }

  int getRemainingSeconds() {
    int currentTimeMillis = DateTime.now().millisecondsSinceEpoch;
    int elapsedSeconds =
        (currentTimeMillis - (newOrder.value.timeOrder ?? 0)) ~/ 1000;
    int totalTimeoutSeconds = (newOrder.value.paymentTimeout ?? 60) * 60;
    infoLogger('getRemainingSeconds',
        "currentTimeMillis $currentTimeMillis, timeOrder ${newOrder.value.timeOrder}, paymentTimeout ${newOrder.value.paymentTimeout}");
    return totalTimeoutSeconds - elapsedSeconds;
  }

  Future<ServerResponse> removeOrderTransaction() async {
    Sentry.addBreadcrumb(Breadcrumb(
        type: 'debug',
        category: 'user.activity.order.controller.removeOrderTransaction',
        data: {
          'orderCode': newOrder.value.selfOrder?.orderCode,
          'orderInv': newOrder.value.orderSalesId,
        },
        level: SentryLevel.debug));
    ServerResponse resp;
    loading.value = true;
    await _repoCart.deleteTransactionOrder(newOrder.value, isOnOrder: true);
    try {
      if (store.token != null) {
        // resp =
        //     await _repoCart.removeSelfOrder(orderList.value.order_list ?? []);
        Future.delayed(const Duration(seconds: 1), () async {
          loading.value = false;
        });
        return ServerResponse();
      }
      Future.delayed(const Duration(seconds: 1), () async {
        loading.value = false;
      });
      return ServerResponse();
    } catch (e, s) {
      errorLogger(
          pos: 'OC - Remove Order Transaction', error: e, stackTrace: s);
      loading.value = false;
      resp = ServerResponse();
      return resp;
    }
  }

  Future<ServerResponse<PaymentDataModel>> getPaymentDetail() async {
    loading.value = true;
    try {
      paymentData.value.type = payment.value.type;
      ServerResponse<PaymentDataModel> resp =
          await repoTransaction.getPaymentDetailOnline(
              newOrder.value.orderSalesId ?? "", paymentData.value);
      paymentData.value = resp.data ?? PaymentDataModel();
      loading.value = false;
      return resp;
    } catch (e, s) {
      loading.value = false;
      errorLogger(pos: "pos", error: e, stackTrace: s);
      return Future.error(e, s);
    }
  }

  Future<List<PaymentMethodModel>?> getPaymentMethodd() async {
    loading.value = true;
    return await repoTransaction
        .getPaymentMethod(
            orderType: newOrder.value.orderType,
            outletId: newOrder.value.outletId.toString())
        .whenComplete(() => loading.value = false);
  }

  Future<ServerResponse<PaymentMethodDetail>> createTransactionPayment(
          OrderDetailModel orderDetailModel,
          PaymentMethodDetail paymentMethodDetail) async =>
      await repoTransaction.createTransactionPayment(
          newOrder.value, paymentMethodDetail);

  Future<void> alwaysUpdateOrderType() async {
    Future.delayed(const Duration(seconds: 1), () async {
      timer = Timer.periodic(
        const Duration(seconds: 3),
        (it) async {
          if ((newOrder.value.status?.toLowerCase() ==
                  OrderStatusEnum.arrived.name.toLowerCase()) ||
              (newOrder.value.status?.toLowerCase() ==
                      OrderStatusEnum.payment_reject.name.toLowerCase() ||
                  (newOrder.value.status?.toLowerCase() ==
                      OrderStatusEnum.cancel.name.toLowerCase()) ||
                  (newOrder.value.status?.toLowerCase() ==
                      OrderStatusEnum.received.name.toLowerCase()))) {
            await _repoCart.deleteTransactionOrder(newOrder.value,
                isOnOrder: true);
            it.cancel();
            await InAppReviewService.requestReview();
          } else {
            await check();
          }
        },
      );
    });
  }

  Future<void> isSelfOrderExist() async {
    Future.delayed(const Duration(seconds: 1), () async {
      timer = Timer.periodic(
        const Duration(seconds: 3),
        (it) async {
          if (selfOrderStatus.value == "") {
            await getSelfOrderStatus();
          } else {
            it.cancel();
            InAppReviewService.instance.requestReview();
          }
        },
      );
    });
  }

  Future<void> getSelfOrderStatus() async {
    var resp = await repoTransaction.getStatusSelfOrder(
        transactionNewModel: newOrder.value);
    if (!resp.status) {
      if (resp.message.toLowerCase() == 'not found' ||
          resp.message.toLowerCase() == 'pesanan ini sudah expired') {
        selfOrderStatus.value = OrderStatusEnum.expired.name;
        newOrder.value.status = OrderStatusEnum.expired.name;
        return;
      }

      if (resp.message.toLowerCase() == 'pesanan ini sudah digunakan') {
        selfOrderStatus.value = OrderStatusEnum.already_paid.name;
        newOrder.value.status = OrderStatusEnum.already_paid.name;
        await removeOrderTransaction();
        await _repoCart.deleteTransactionOrder(newOrder.value, isOnOrder: true);
        Future.delayed(const Duration(milliseconds: 1500), () async {
          // await deleteActiveOrderTransaction();
          AppAlert.showThanksDialog(
            context: Get.context!,
            message: Strings.orderIsProcessed.tr,
            actions: [
              TextButton(
                  onPressed: () {
                    Get.offAllNamed(Routes.HOME);
                  },
                  child: Text(
                    "Ok",
                    style:
                        AppFont.componentSmall.copyWith(color: AppColor.white),
                  ))
            ],
          );
        });
        return;
      }
    }
  }

  Future<ServerResponse<OrderDetailModel>> updateOrderDetailStatus(
      OrderStatusEnum status) async {
    loading.value = true;
    if (status == OrderStatusEnum.cancel) {
      await _repoCart.deleteTransactionOrder(newOrder.value, isOnOrder: true);
    }
    try {
      var resp = await repoTransaction
          .updateOrderDetail(newOrder.value.orderSalesId ?? "",
              status.name.toString(), xFile.value)
          .whenComplete(() => check());
      if (resp.status && status == OrderStatusEnum.payment_verification) {
        newOrder.value.path = xFile.value?.path;
        await _repoCart.updateTransactionOrder(newOrder.value, isOnOrder: true);
      }

      if (resp.status && (status == OrderStatusEnum.received)) {
        await _repoCart.deleteTransactionOrder(newOrder.value, isOnOrder: true);
      }
      loading.value = false;
      return resp;
    } catch (e, s) {
      errorLogger(pos: "pos", error: e, stackTrace: s);
      loading.value = false;
      return Future.error(e, s);
    }
  }

  Future<bool> sendFeedback(String comment, int star) async {
    loading.value = true;
    FeedbackModel feedbackModel = FeedbackModel(
        comment: comment, salesId: newOrder.value.orderSalesId, stars: star);
    ServerResponse resp = await repoTransaction
        .postGiveFeedback(feedbackModel)
        .whenComplete(() => loading.value = false);
    Sentry.addBreadcrumb(Breadcrumb(
        type: 'debug',
        category: 'user.activity.order.controller.sendFeedback',
        data: {
          'feedBack': comment,
          'orderCode': newOrder.value.selfOrder?.orderCode,
          'orderInv': newOrder.value.orderSalesId,
        },
        level: SentryLevel.debug));
    return resp.status;
  }

  @override
  void dispose() {
    timer.cancel();
    timerPayment?.cancel();
    super.dispose();
  }

  @override
  void onClose() {
    timer.cancel();
    timerPayment?.cancel();
    dispose();
    super.onClose();
  }
}
