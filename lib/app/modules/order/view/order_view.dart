// ignore_for_file: invalid_use_of_protected_member

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/screen_wrapper/views/app_page_wrapper.dart';
import 'package:mobile_crm/app/utils/toast.dart';
import 'package:mobile_crm/core/values/app_strings.dart';

import '../../../../routes/app_pages.dart';
import '../controllers/order_controller.dart';
import 'widgets/pickup_delivery_order.dart';
import 'widgets/self_order.dart';

class OrderScreen extends GetView<OrderController> {
  const OrderScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    DateTime currentBackPressTime = DateTime.now();
    return PageWrapper(
      onWillPop: () {
        DateTime now = DateTime.now();
        if (now.difference(currentBackPressTime) > const Duration(seconds: 2)) {
          currentBackPressTime = now;
          Toast.show(Strings.tapAgainToReturnValue.trParams({'value': 'Home'}),
              type: ToastType.dark, duration: 1);
          return Future.value(false);
        }
        controller.timer.cancel();
        Get.offAllNamed(Routes.HOME);
        return Future.value(true);
      },  
      child: Obx(
        () => ["pickup", "delivery", "internal_delivery", "dine_in"].contains(controller.newOrder.value.orderType?.toLowerCase())
            ? PickUpDeliveryOrderWidget(
                transactionNewModel: controller.newOrder.value,
              )
            : const SelfOrderWidget(),
      ),
    );
  }
}
