import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/order/view/widgets/order_timer.dart';
import 'package:mobile_crm/app/widget/app_bar_widget/app_bar_widget.dart';
import 'package:mobile_crm/app/widget/custom_refresh_indicator_widget.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/values.dart';
import 'package:mobile_crm/data/models/transaction_new_model.dart';

import '../../controllers/order_controller.dart';
import 'order_bottom_button.dart';
import 'order_message.dart';
import 'order_note.dart';
import 'order_proof.dart';
import 'order_stepper.dart';
import 'order_summary.dart';
import 'order_type.dart';

class PickUpDeliveryOrderWidget extends StatelessWidget {
  const PickUpDeliveryOrderWidget({Key? key, required this.transactionNewModel})
      : super(key: key);
  final TransactionNewModel transactionNewModel;

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<OrderController>();
    return Scaffold(
      backgroundColor: AppColor.white,
      bottomNavigationBar: const OrderBottomButton(),
      appBar: AppBarWidget(
          title: Strings.orderStatus.tr,
          backgroundColor: AppColor.white,
          leading: Visibility(
            visible: GetPlatform.isIOS,
            child: IconButton(
              icon: Icon(Icons.arrow_back,
                  size: Constants.iconSizeSmall(context)),
              onPressed: () {
                Get.back();
              },
            ),
          )),
      body: CustomRefreshIndicator(
        onRefresh: () => controller.check(),
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              AppDimen.h10.height,
              const OrderStepper(),
              const OrderMessage(),
              AppDimen.h10.height,
              Obx(() {
                if (controller.newOrder.value.status == "accept") {
                  return const OrderTimer();
                } else {
                  return const SizedBox();
                }
              }),
              AppDimen.h10.height,
              Divider(
                color: AppColor.black5,
                height: AppDimen.h6,
                thickness: AppDimen.h6,
              ),
              const OrderProofFile(),
              const OrderType(),
              const OrderNote(),
              Divider(
                  color: AppColor.black5,
                  height: AppDimen.h6,
                  thickness: AppDimen.h6),
              const OrderSummary(),
              Divider(
                  color: AppColor.black5,
                  height: AppDimen.h6,
                  thickness: AppDimen.h6),
            ],
          ),
        ),
      ),
    );
  }
}
