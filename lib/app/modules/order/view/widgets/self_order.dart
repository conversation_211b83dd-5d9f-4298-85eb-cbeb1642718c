import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/screen_wrapper/controller/wrapper_controller.dart';
import 'package:mobile_crm/app/utils/utils.dart';
import 'package:mobile_crm/app/widget/app_bar_widget/app_bar_widget.dart';
import 'package:mobile_crm/app/widget/cached_image_widget.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/values.dart';

import '../../controllers/order_controller.dart';
import 'self_order_bottom_sheet.dart';

class SelfOrderWidget extends StatelessWidget {
  const SelfOrderWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<OrderController>();
    final c = Get.find<WrapperController>();
    return Scaffold(
      backgroundColor: Colors.transparent,
      bottomSheet: GestureDetector(
          onVerticalDragDown: (details) =>
              const SelfOrderBottomSheet().toModalBottomSheet.of(context),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            width: double.infinity,
            decoration: const BoxDecoration(
                color: AppColor.white,
                boxShadow: [
                  BoxShadow(
                      color: AppColor.black10,
                      offset: Offset(0, 1),
                      blurRadius: 5,
                      spreadRadius: 5)
                ],
                borderRadius: BorderRadiusDirectional.only(
                    topStart: Radius.circular(10),
                    topEnd: Radius.circular(10))),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Center(
                  child: Icon(
                    Icons.drag_handle_rounded,
                    size: Constants.iconSizeSmall(context),
                    color: AppColor.black70,
                  ),
                ),
                SizedBox(
                  width: MediaQuery.of(context).size.width,
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Obx(() {
                            return Text(
                              Strings.outletName.trParams({
                                "outlet": c.configApp.value.language?.outlet ??
                                    "Outlet"
                              }),
                              style: AppFont.paragraphSmall,
                            );
                          }),
                          Obx(
                            () => Text(
                              controller.newOrder.value.outlet?.name ?? '',
                              style: AppFont.paragraphMediumBold,
                            ),
                          ),
                          Text(Strings.paymentSummary.tr,
                              style: AppFont.paragraphMedium),
                        ],
                      ),
                      Obx(
                        () => (controller.newOrder.value.diningTable == null ||
                                controller.newOrder.value.diningTable?.trim() ==
                                    '')
                            ? const SizedBox()
                            : Column(
                                mainAxisSize: MainAxisSize.min,
                                crossAxisAlignment: CrossAxisAlignment.end,
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                  Text(Strings.tableNumber.tr,
                                      style: AppFont.paragraphSmall),
                                  Text(
                                      controller.newOrder.value.diningTable ??
                                          '',
                                      style: AppFont.paragraphLargeBold),
                                ],
                              ),
                      )
                    ],
                  ),
                ),
                const Divider()
              ],
            ),
          )),
      appBar: AppBarWidget(
          title: Strings.order.tr,
          leading: Visibility(
            visible: GetPlatform.isIOS,
            child: IconButton(
              icon: const Icon(Icons.arrow_back),
              onPressed: () {
                Get.back();
              },
            ),
          )),
      body: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
              padding: const EdgeInsets.all(8),
              alignment: Alignment.center,
              width: double.infinity,
              decoration: BoxDecoration(color: c.getPrimaryColor()),
              child: Obx(
                () => controller.newOrder.value.status != null
                    ? Text(
                        translationOrderStatus(
                            controller.newOrder.value.status ?? ''),
                        style: AppFont.paragraphSmallBold.copyWith(
                            color: c
                                .getPrimaryColor()
                                .changeColorBasedOnBackgroundColor()
                                .$1),
                      )
                    : Text(
                        Strings.pleaseMakePayment.trParams({
                          'date': controller.newOrder.value.selfOrder?.expired
                                  .toDateAndTime ??
                              ''
                        }),
                        style: AppFont.paragraphSmallBold.copyWith(
                            color: c
                                .getPrimaryColor()
                                .changeColorBasedOnBackgroundColor()
                                .$1),
                      ),
              )),
          const Spacer(),
          Container(
            height: Constants.barcodeSize(context),
            decoration: BoxDecoration(boxShadow: const [
              BoxShadow(
                  color: AppColor.black5,
                  blurRadius: 3,
                  spreadRadius: 2,
                  offset: Offset(0, 0))
            ], borderRadius: BorderRadius.all(Radius.circular(10.r))),
            child: ClipRRect(
              borderRadius: BorderRadius.all(Radius.circular(10.r)),
              child: CachedImageWidget(
                  imageUrl: controller.newOrder.value.selfOrder?.qrCode ??
                      "https://chart.googleapis.com/chart?chs=300x300&cht=qr&chl=${controller.newOrder.value.selfOrder?.orderCode ?? 0}&chld=M|1",
                  width: Constants.barcodeSize(context),
                  height: Constants.barcodeSize(context)),
            ),
          ),
          10.0.height,
          Flexible(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(Strings.orderCode.tr, style: AppFont.paragraphSmall),
                Text("${controller.newOrder.value.selfOrder?.orderCode ?? 0}",
                    style: AppFont.paragraphLargeBold),
              ],
            ),
          ),
          10.0.height,
          Flexible(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8.0),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    Strings.orderMessage.tr,
                    style: AppFont.paragraphSmall,
                    textAlign: TextAlign.center,
                  ),
                  Text(Strings.orderMessage2.tr, style: AppFont.paragraphSmall),
                ],
              ),
            ),
          ),
          const Spacer(),
          const Spacer(),
        ],
      ),
    );
  }
}
