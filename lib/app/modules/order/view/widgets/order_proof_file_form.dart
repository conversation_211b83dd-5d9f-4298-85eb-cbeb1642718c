import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/order/order_export.dart';
import 'package:mobile_crm/app/utils/app_alert.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/values.dart';

import '../../../../enum/order_status_enum.dart';

class OrderProofFileForm extends StatelessWidget {
  const OrderProofFileForm({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<OrderController>();
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          Strings.proofPayment.tr,
          style: AppFont.componentMediumBold,
        ),
        AppDimen.h10.height,
        GestureDetector(
          onTapDown: (details) {
            AppAlert.showPreviewImage(
                path: controller.xFile.value?.path ?? "",
                context: context,
                canChange: controller.newOrder.value.status ==
                        OrderStatusEnum.accept.name ||
                    controller.newOrder.value.status ==
                        OrderStatusEnum.payment_reject.name);
          },
          child: Container(
            width: MediaQuery.of(context).size.width,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
                color: AppColor.ink05, borderRadius: BorderRadius.circular(5)),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Flexible(
                  child: Obx(() {
                    return Text(
                      "${controller.xFile.value?.name}",
                      style: AppFont.paragraphSmallBold,
                    );
                  }),
                ),
                10.0.width,
                Icon(
                  Icons.open_in_new_rounded,
                  size: Constants.iconSizeSmall(context),
                  color: AppColor.black70,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
