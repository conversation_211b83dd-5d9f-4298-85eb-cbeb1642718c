import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/values.dart';

import '../../controllers/order_controller.dart';

class OrderTimer extends StatelessWidget {
  const OrderTimer({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<OrderController>();
    return Obx(() {
      return Visibility(
        visible: controller.infoTimer.value.isNotEmpty,
        child: Column(
          children: [
            AppDimen.h8.height,
            Container(
              width: Constants.defaultMaxWidth,
              decoration: BoxDecoration(
                color: AppColor.accentBlue.withOpacity(0.4),
                borderRadius: BorderRadius.all(
                  Radius.circular(AppDimen.h6),
                ),
              ),
              margin: EdgeInsets.symmetric(horizontal: AppDimen.h16),
              padding: EdgeInsets.symmetric(
                  horizontal: AppDimen.h12, vertical: AppDimen.h8),
              child: Obx(() {
                return Text(
                  controller.infoTimer.value,
                  textAlign: TextAlign.center,
                  style: AppFont.componentSmallBold.copyWith(
                    color: const Color.fromARGB(255, 155, 6, 6),
                  ),
                );
              }),
            ),
          ],
        ),
      );
    });
  }
}
