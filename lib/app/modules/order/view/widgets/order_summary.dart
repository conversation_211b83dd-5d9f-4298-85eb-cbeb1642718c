import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/screen_wrapper/controller/wrapper_controller.dart';
import 'package:mobile_crm/app/widget/app_dotted_separator.dart';
import 'package:mobile_crm/core/extensions/double_extensions.dart';
import 'package:mobile_crm/core/extensions/int_extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/values.dart';

import '../../../../../data/providers/db/database.dart';
import '../../../../utils/toast.dart';
import '../../controllers/order_controller.dart';

class OrderSummary extends StatelessWidget {
  const OrderSummary({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<OrderController>();
    final wrapperController = Get.find<WrapperController>();
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: AppDimen.h16, vertical: 16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            Strings.yourOrder.tr,
            style: AppFont.componentMediumBold,
          ),
          10.0.height,
          Obx(() {
            return Text(
                controller
                        .newOrder.value.orderList?.firstOrNull?.outlet?.name ??
                    '',
                style: AppFont.componentSmallBold);
          }),
          GestureDetector(
            onTapDown: (details) => Clipboard.setData(ClipboardData(
                    text: controller.newOrder.value.orderSalesId ?? ''))
                .then(
              (value) {
                Toast.show(Strings.textCopied.tr, type: ToastType.dark);
              },
            ),
            child: Row(
              children: [
                Text("${controller.newOrder.value.orderSalesId}",
                    style: AppFont.componentSmallBold),
                AppDimen.h10.width,
                Icon(
                  Icons.copy,
                  size: Constants.iconSizeSmall(context),
                  color: AppColor.black90,
                ),
              ],
            ),
          ),
          10.0.height,
          Flexible(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Flexible(
                  child: Obx(() {
                    return ListView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount:
                          controller.newOrder.value.orderList?.length ?? 0,
                      itemBuilder: (context, index) {
                        controller.newOrder.value.orderList?.sort(
                          (a, b) => (b.product?.priceSellPromo ?? 0)
                              .compareTo(a.product?.priceSellPromo ?? 0),
                        );
                        Cart cart = controller.newOrder.value.orderList![index];
                        return Container(
                          margin: EdgeInsets.symmetric(vertical: AppDimen.h4),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      SizedBox(
                                        width: AppDimen.h24,
                                        child: Text("${cart.qty}x",
                                            textAlign: TextAlign.center,
                                            style: AppFont.componentSmall),
                                      ),
                                      AppDimen.h6.width,
                                    ],
                                  ),
                                  Expanded(
                                    child: Text(
                                      cart.product?.name ?? '',
                                      style: AppFont.componentSmall,
                                    ),
                                  ),
                                  Row(
                                    children: [
                                      AppDimen.h10.width,
                                      (cart.product?.priceSell == null &&
                                              cart.product?.priceSellPromo ==
                                                  null)
                                          ? Text("Rp${cart.price.toCurrency}",
                                              style: AppFont.componentSmallBold)
                                          : cart.product?.priceSellPromo == null
                                              ? Text(
                                                  "Rp${cart.product?.priceSell.toCurrency}",
                                                  style: AppFont
                                                      .componentSmallBold)
                                              : Row(
                                                  children: [
                                                    Text(
                                                        "Rp${cart.product?.priceSell.toCurrency ?? ''}",
                                                        style: AppFont
                                                            .componentSmallBold
                                                            .copyWith(
                                                                decoration:
                                                                    TextDecoration
                                                                        .combine([
                                                                  TextDecoration
                                                                      .lineThrough
                                                                ]),
                                                                color: AppColor
                                                                    .black50)),
                                                    5.0.width,
                                                    Text(
                                                        "Rp${cart.product?.priceSellPromo.toCurrency ?? ''}",
                                                        style: AppFont
                                                            .componentSmallBold),
                                                  ],
                                                ),
                                    ],
                                  ),
                                ],
                              ),
                              AppDimen.h4.height,
                              ListView.builder(
                                shrinkWrap: true,
                                physics: const NeverScrollableScrollPhysics(),
                                itemCount: cart.linkMenu?.length ?? 0,
                                itemBuilder: (context, index) {
                                  var item = cart.linkMenu![index];
                                  return Visibility(
                                    visible:
                                        item.linkMenuDetail?.isNotEmpty == true,
                                    child: Column(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Row(
                                          children: [
                                            AppDimen.h30.width,
                                            Text(item.name ?? '-',
                                                style: AppFont.componentSmall),
                                          ],
                                        ),
                                        ListView.builder(
                                          shrinkWrap: true,
                                          physics:
                                              const NeverScrollableScrollPhysics(),
                                          itemCount:
                                              item.linkMenuDetail?.length ?? 0,
                                          itemBuilder: (context, index) {
                                            var itemChild =
                                                item.linkMenuDetail![index];
                                            return Column(
                                              children: [
                                                Row(
                                                  children: [
                                                    AppDimen.h34.width,
                                                    Expanded(
                                                      child: Row(
                                                        mainAxisAlignment:
                                                            MainAxisAlignment
                                                                .spaceBetween,
                                                        children: [
                                                          Text(
                                                              itemChild.name ??
                                                                  '-',
                                                              style: AppFont
                                                                  .paragraphSmall
                                                                  .copyWith(
                                                                      fontSize:
                                                                          11.sp)),
                                                          Text(
                                                              "+Rp${(itemChild.priceAdd ?? 0).toCurrency}",
                                                              style: AppFont
                                                                  .paragraphSmallBold),
                                                        ],
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ],
                                            );
                                          },
                                        ),
                                        AppDimen.h6.height
                                      ],
                                    ),
                                  );
                                },
                              ),
                              AppDimen.h4.height,
                              const Divider(
                                color: AppColor.black5,
                              )
                            ],
                          ),
                        );
                      },
                    );
                  }),
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(Strings.subTotal.tr, style: AppFont.componentSmall),
                    Obx(() => Container(
                          margin: EdgeInsets.symmetric(vertical: AppDimen.h4),
                          child: Text(
                              "Rp${controller.newOrder.value.subTotal.toCurrency}",
                              style: AppFont.componentSmallBold),
                        )),
                  ],
                ),
                Obx(
                  () => Visibility(
                    visible: controller.newOrder.value.sumTax() != 0,
                    child: Container(
                      margin: EdgeInsets.symmetric(vertical: AppDimen.h4),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(Strings.totalTaxes.tr,
                              style: AppFont.componentSmall),
                          Text(
                              "Rp${controller.newOrder.value.sumTax().toCurrency}",
                              style: AppFont.componentSmallBold),
                        ],
                      ),
                    ),
                  ),
                ),
                Obx(() {
                  return controller.newOrder.value.shipment?.shippingCharge ==
                          null
                      ? const SizedBox()
                      : Container(
                          margin: EdgeInsets.symmetric(vertical: AppDimen.h4),
                          child: Column(
                            children: [
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    Strings.shippingCosts.tr,
                                    style: AppFont.componentSmall,
                                  ),
                                  Obx(() {
                                    return Text(
                                      "Rp${controller.newOrder.value.shipment?.shippingCharge.toCurrency}",
                                      style: AppFont.componentSmallBold,
                                    );
                                  }),
                                ],
                              ),
                            ],
                          ),
                        );
                }),
                Obx(
                  () => Visibility(
                    visible: controller.newOrder.value.deal?.name != null,
                    child: Container(
                      margin: EdgeInsets.symmetric(vertical: AppDimen.h4),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(controller.newOrder.value.deal?.name ?? '',
                              style: AppFont.componentSmall
                                  .copyWith(color: AppColor.navy)),
                          Obx(() => Text(
                              "-Rp${controller.newOrder.value.totalDiscount.toCurrency}",
                              style: AppFont.componentSmallBold
                                  .copyWith(color: AppColor.navy))),
                        ],
                      ),
                    ),
                  ),
                ),
                AppDimen.h4.height,
                const DottedDivider(
                  color: AppColor.black10,
                  width: 6,
                  height: 2,
                ),
                Container(
                  margin: EdgeInsets.symmetric(vertical: AppDimen.h4),
                  child: Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            Strings.totalBill.tr,
                            style: AppFont.componentMediumBold,
                          ),
                          Obx(() {
                            return Text(
                              "Rp${controller.newOrder.value.sumTotalBill().toCurrency}",
                              style: AppFont.componentMediumBold,
                            );
                          }),
                        ],
                      ),
                      AppDimen.h4.height,
                      Obx(() {
                        return Visibility(
                          visible: controller.newOrder.value.point != 0,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                  Strings.valueEarned.trParams({
                                    'value':
                                        wrapperController.getPointLanguage()
                                  }),
                                  style: AppFont.componentSmall
                                      .copyWith(color: AppColor.black70)),
                              Container(
                                padding: EdgeInsets.symmetric(
                                    horizontal: AppDimen.h6,
                                    vertical: AppDimen.h2),
                                decoration: BoxDecoration(
                                    color: Colors.red,
                                    borderRadius: BorderRadius.circular(15)),
                                child: Row(
                                  children: [
                                    Obx(() {
                                      return Text(
                                          "+${controller.newOrder.value.point}",
                                          style: AppFont.componentSmall
                                              .copyWith(color: AppColor.white));
                                    }),
                                    Icon(Icons.local_fire_department,
                                        color: Colors.orangeAccent,
                                        size: Constants.iconSize(context))
                                  ],
                                ),
                              ),
                            ],
                          ),
                        );
                      }),
                    ],
                  ),
                ),
              ],
            ),
          )
        ],
      ),
    );
  }
}
