import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/core/theme/themes.dart';

import '../../../../enum/order_status_enum.dart';
import '../../../../utils/utils.dart';
import '../../../../widget/custom_stepper/custom_stepper_progress.dart';
import '../../controllers/order_controller.dart';

class OrderStepper extends StatelessWidget {
  const OrderStepper({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<OrderController>();
    return Obx(
      () => ["delivery", "internal_delivery"]
              .contains(controller.newOrder.value.orderType)
          ? Padding(
              padding: EdgeInsets.symmetric(horizontal: AppDimen.h16),
              child: Obx(() {
                return Row(
                  children: [
                    CustomStepperProgress(
                      setIndex: controller.newOrder.value.status ==
                              OrderStatusEnum.pending.name
                          ? 0
                          : (controller.newOrder.value.status ==
                                      OrderStatusEnum.accept.name ||
                                  controller.newOrder.value.status ==
                                      OrderStatusEnum.payment_verification.name)
                              ? 1
                              : (controller.newOrder.value.status ==
                                          OrderStatusEnum
                                              .payment_verified.name &&
                                      controller.newOrder.value.shipment
                                              ?.shippingId ==
                                          null)
                                  ? 3
                                  : (controller.newOrder.value.status ==
                                              OrderStatusEnum
                                                  .payment_verified.name &&
                                          controller.newOrder.value.shipment
                                                  ?.shippingId !=
                                              null)
                                      ? 4
                                      : (controller.newOrder.value.status ==
                                                  OrderStatusEnum
                                                      .received.name ||
                                              controller.newOrder.value.status ==
                                                  OrderStatusEnum.arrived.name)
                                          ? 5
                                          : (controller.newOrder.value.status ==
                                                  OrderStatusEnum.ready.name)
                                              ? 4
                                              : 1,
                      steps: const [
                        Icons.app_shortcut,
                        Icons.storefront_outlined,
                        Icons.payment,
                        Icons.takeout_dining,
                        Icons.delivery_dining_rounded,
                        Icons.done_all_rounded,
                      ],
                      messages: translationOrderStatus(
                          controller.newOrder.value.status ?? ''),
                    ),
                  ],
                );
              }),
            )
          : controller.newOrder.value.orderType?.toLowerCase() == 'pickup'
              ? Padding(
                  padding: EdgeInsets.symmetric(horizontal: AppDimen.h16),
                  child: Obx(() {
                    return CustomStepperProgress(
                      setIndex: controller.newOrder.value.status ==
                              OrderStatusEnum.pending.name
                          ? 0
                          : (controller.newOrder.value.status ==
                                      OrderStatusEnum.accept.name ||
                                  controller.newOrder.value.status ==
                                      OrderStatusEnum.payment_verification.name)
                              ? 1
                              : (controller.newOrder.value.status ==
                                      OrderStatusEnum.payment_verified.name)
                                  ? 3
                                  : (controller.newOrder.value.status ==
                                              OrderStatusEnum.received.name ||
                                          controller.newOrder.value.status ==
                                              OrderStatusEnum.arrived.name)
                                      ? 4
                                      : 1,
                      steps: const [
                        Icons.app_shortcut,
                        Icons.storefront_outlined,
                        Icons.payment,
                        Icons.takeout_dining,
                        Icons.done_all_rounded,
                      ],
                      messages: translationOrderStatus(
                          controller.newOrder.value.status ?? ''),
                    );
                  }),
                )
              : Padding(
                  padding: EdgeInsets.symmetric(horizontal: AppDimen.h16),
                  child: Obx(() {
                    return CustomStepperProgress(
                      setIndex: controller.newOrder.value.status ==
                              OrderStatusEnum.pending.name
                          ? 0
                          : (controller.newOrder.value.status ==
                                      OrderStatusEnum.accept.name ||
                                  controller.newOrder.value.status ==
                                      OrderStatusEnum.payment_verification.name)
                              ? 1
                              : (controller.newOrder.value.status ==
                                      OrderStatusEnum.payment_verified.name)
                                  ? 2
                                  : (controller.newOrder.value.status ==
                                          OrderStatusEnum.ready.name)
                                      ? 3
                                      : (controller.newOrder.value.status ==
                                                  OrderStatusEnum
                                                      .received.name ||
                                              controller
                                                      .newOrder.value.status ==
                                                  OrderStatusEnum.arrived.name)
                                          ? 4
                                          : 1,
                      steps: const [
                        Icons.app_shortcut,
                        Icons.storefront_outlined,
                        Icons.payment,
                        Icons.takeout_dining,
                        Icons.done_all_rounded,
                      ],
                      messages: translationOrderStatus(
                          controller.newOrder.value.status ?? ''),
                    );
                  }),
                ),
    );
  }
}
