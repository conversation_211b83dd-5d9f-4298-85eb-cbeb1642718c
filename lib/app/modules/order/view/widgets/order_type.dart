import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/utils/app_alert.dart';
import 'package:mobile_crm/app/utils/toast.dart';
import 'package:mobile_crm/app/widget/app_dotted_separator.dart';
import 'package:mobile_crm/core/extensions/double_extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/values.dart';

import '../../controllers/order_controller.dart';

class OrderType extends StatelessWidget {
  const OrderType({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<OrderController>();
    return Container(
      padding: EdgeInsets.symmetric(horizontal: AppDimen.h16, vertical: 16),
      child: Obx(
        () => controller.newOrder.value.orderType == 'pickup'
            ? SizedBox(
                width: MediaQuery.of(context).size.width,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      Strings.pickupTime.tr,
                      style: AppFont.componentMediumBold,
                    ),
                    10.0.height,
                    Obx(() {
                      return Text(
                        controller.newOrder.value.pickup == null
                            ? ""
                            : "${controller.newOrder.value.pickup?.pickupTime}",
                        style: AppFont.componentSmall,
                      );
                    }),
                  ],
                ),
              )
            : (["delivery", "internal delivery", "internal_delivery"]
                    .contains(controller.newOrder.value.orderType))
                ? Column(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        Strings.destination.tr,
                        style: AppFont.componentMediumBold,
                      ),
                      AppDimen.h10.height,
                      Obx(() {
                        // infoLogger(
                        //     'order detail: ', controller.newOrder.value);
                        return Text(
                          controller.newOrder.value.shipment?.shippingAddress ??
                              '',
                          style: AppFont.componentSmall,
                        );
                      }),
                      Obx(() => controller
                                  .newOrder.value.shipment?.shippingId ==
                              null
                          ? const SizedBox()
                          : Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                AppDimen.h4.height,
                                const DottedDivider(
                                    color: AppColor.black10,
                                    width: 6,
                                    height: 2),
                                AppDimen.h4.height,
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      Strings.shipmentCourier.tr,
                                      style: AppFont.componentSmall,
                                    ),
                                    Text(
                                      "${controller.newOrder.value.shipment?.shippingCourier}",
                                      style: AppFont.componentSmallBold,
                                    ),
                                  ],
                                ),
                                5.0.height,
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      Strings.shipmentReceiptNumber.tr,
                                      style: AppFont.componentSmall,
                                    ),
                                    GestureDetector(
                                      onTapDown: (details) => Clipboard.setData(
                                              ClipboardData(
                                                  text: controller
                                                          .newOrder
                                                          .value
                                                          .shipment
                                                          ?.shippingId ??
                                                      ''))
                                          .then((value) {
                                        Toast.show(Strings.textCopied.tr,
                                            type: ToastType.dark);
                                      }),
                                      child: Row(
                                        children: [
                                          Text(
                                            "${controller.newOrder.value.shipment?.shippingId}",
                                            style: AppFont.componentSmallBold,
                                          ),
                                          5.0.width,
                                          Icon(
                                            Icons.copy,
                                            size: Constants.iconSizeSmall(
                                                context),
                                            color: AppColor.black70,
                                          )
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                                5.0.height,
                                GestureDetector(
                                  onTapDown: (details) {
                                    AppAlert.showPreviewImageNetwork(
                                        url: controller.newOrder.value.shipment
                                                ?.shippingReceipt ??
                                            "",
                                        context: context);
                                  },
                                  child: Container(
                                    width: MediaQuery.of(context).size.width,
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 16, vertical: 8),
                                    decoration: BoxDecoration(
                                        color: AppColor.ink05,
                                        borderRadius: BorderRadius.circular(5)),
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Flexible(
                                            child: Text(
                                          Strings.profOfDeliveryReceipt.tr,
                                          style: AppFont.paragraphSmallBold,
                                        )),
                                        10.0.width,
                                        Icon(
                                          Icons.open_in_new_rounded,
                                          size:
                                              Constants.iconSizeSmall(context),
                                          color: AppColor.black70,
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ],
                            )),
                    ],
                  )
                : Column(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        Strings.tableNumber.tr,
                        style: AppFont.componentMediumBold,
                      ),
                      Text(
                        controller.newOrder.value.diningTable != null
                            ? (controller.newOrder.value.diningTable != ''
                                ? controller.newOrder.value.diningTable ?? '-'
                                : '-')
                            : '-',
                        style: AppFont.componentSmall,
                      ),
                    ],
                  ),
      ),
    );
  }
}
