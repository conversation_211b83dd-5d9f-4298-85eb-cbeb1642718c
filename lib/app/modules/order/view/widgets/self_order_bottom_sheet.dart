import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/screen_wrapper/controller/wrapper_controller.dart';
import 'package:mobile_crm/app/utils/app_alert.dart';
import 'package:mobile_crm/app/widget/app_dotted_separator.dart';
import 'package:mobile_crm/app/widget/button_widget/button_widget.dart';
import 'package:mobile_crm/app/widget/custom_circular_progress_widget.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/values.dart';
import 'package:mobile_crm/data/models/tax_model.dart';
import 'package:mobile_crm/data/providers/db/database.dart';
import 'package:mobile_crm/routes/app_pages.dart';

import '../../controllers/order_controller.dart';

class SelfOrderBottomSheet extends GetView<OrderController> {
  const SelfOrderBottomSheet({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final c = Get.find<WrapperController>();
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: AppDimen.h16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              Expanded(
                child: Obx(
                  () => (controller.newOrder.value.customerName == null ||
                          controller.newOrder.value.customerName == '')
                      ? (controller.newOrder.value.receiptReceiver == null ||
                              controller.newOrder.value.receiptReceiver
                                      ?.trim() ==
                                  '')
                          ? Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  Strings.customerName.tr,
                                  style: AppFont.paragraphSmall,
                                ),
                                Text(
                                  Strings.guest.tr,
                                  style: AppFont.paragraphMediumBold,
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                )
                              ],
                            )
                          : Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  Strings.order.tr,
                                  style: AppFont.paragraphSmall,
                                ),
                                Text(
                                  '${controller.newOrder.value.receiptReceiver}',
                                  style: AppFont.paragraphMediumBold,
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                )
                              ],
                            )
                      : Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              Strings.customerName.tr,
                              style: AppFont.paragraphSmall,
                            ),
                            Text(
                              '${controller.newOrder.value.customerName}',
                              style: AppFont.paragraphMediumBold,
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            )
                          ],
                        ),
                ),
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Obx(() {
                      return Text(
                        Strings.outletName.trParams({
                          "outlet":
                              c.configApp.value.language?.outlet ?? "Outlet"
                        }),
                        style: AppFont.paragraphSmall,
                      );
                    }),
                    Obx(
                      () => Text(
                        controller.newOrder.value.outlet?.name ?? '',
                        style: AppFont.paragraphMediumBold,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          Text(Strings.paymentSummary.tr, style: AppFont.paragraphMedium),
          AppDimen.h4.height,
          const DottedDivider(width: 3, height: 2, color: AppColor.disable),
          AppDimen.h4.height,
          Flexible(
            child: MediaQuery.removePadding(
              removeTop: true,
              context: context,
              child: Obx(() {
                return ListView.builder(
                  shrinkWrap: true,
                  itemCount: controller.newOrder.value.orderList?.length ?? 0,
                  itemBuilder: (context, index) {
                    controller.newOrder.value.orderList?.sort(
                      (a, b) => (b.product?.priceSellPromo ?? 0)
                          .compareTo(a.product?.priceSellPromo ?? 0),
                    );
                    Cart item = controller.newOrder.value.orderList![index];
                    return Container(
                      margin: EdgeInsets.symmetric(vertical: AppDimen.h4),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Row(
                                mainAxisAlignment: MainAxisAlignment.start,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  SizedBox(
                                    width: AppDimen.h24,
                                    child: Text("${item.qty}x",
                                        textAlign: TextAlign.center,
                                        style: AppFont.componentSmall),
                                  ),
                                  AppDimen.h6.width,
                                ],
                              ),
                              Expanded(
                                child: Text(
                                  "${item.product?.name}",
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                  style: AppFont.componentSmall,
                                ),
                              ),
                              Row(
                                children: [
                                  AppDimen.h10.width,
                                  (item.product?.priceSell == null &&
                                          item.product?.priceSellPromo == null)
                                      ? Text("Rp${item.price.toCurrency}",
                                          style: AppFont.componentSmallBold)
                                      : item.product?.priceSellPromo == null
                                          ? Text(
                                              "Rp${item.product?.priceSell.toCurrency}",
                                              style: AppFont.componentSmallBold)
                                          : Row(
                                              children: [
                                                Text(
                                                    "Rp${item.product?.priceSell.toCurrency ?? ''}",
                                                    style: AppFont
                                                        .componentSmallBold
                                                        .copyWith(
                                                            decoration:
                                                                TextDecoration
                                                                    .combine([
                                                              TextDecoration
                                                                  .lineThrough
                                                            ]),
                                                            color: AppColor
                                                                .black50)),
                                                5.0.width,
                                                Text(
                                                    "Rp${item.product?.priceSellPromo.toCurrency ?? ''}",
                                                    style: AppFont
                                                        .componentSmallBold),
                                              ],
                                            ),
                                ],
                              ),
                            ],
                          ),
                          AppDimen.h4.height,
                          ListView.builder(
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                            itemCount: item.linkMenu?.length ?? 0,
                            itemBuilder: (context, indexs) {
                              var lm = item.linkMenu![indexs];
                              return Visibility(
                                visible: lm.linkMenuDetail
                                        ?.where((element) =>
                                            element.isChosen == true)
                                        .isNotEmpty ==
                                    true,
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Row(
                                      children: [
                                        AppDimen.h30.width,
                                        Text(lm.name ?? '-',
                                            style: AppFont.componentSmall),
                                      ],
                                    ),
                                    ListView.builder(
                                      shrinkWrap: true,
                                      physics:
                                          const NeverScrollableScrollPhysics(),
                                      itemCount: lm.linkMenuDetail
                                              ?.where((element) =>
                                                  element.isChosen == true)
                                              .length ??
                                          0,
                                      itemBuilder: (context, indexChild) {
                                        var itemChild =
                                            lm.linkMenuDetail![indexChild];
                                        return Column(
                                          children: [
                                            Row(
                                              children: [
                                                AppDimen.h34.width,
                                                Expanded(
                                                  child: Row(
                                                    mainAxisAlignment:
                                                        MainAxisAlignment
                                                            .spaceBetween,
                                                    children: [
                                                      Text(
                                                          itemChild.name ?? '-',
                                                          style: AppFont
                                                              .paragraphSmall
                                                              .copyWith(
                                                                  fontSize:
                                                                      11.sp)),
                                                      Text(
                                                          "+Rp${(itemChild.priceAdd ?? 0).toCurrency}",
                                                          style: AppFont
                                                              .paragraphSmallBold),
                                                    ],
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ],
                                        );
                                      },
                                    ),
                                    AppDimen.h6.height
                                  ],
                                ),
                              );
                            },
                          ),
                          AppDimen.h4.height,
                          Visibility(
                            visible: index !=
                                (controller.newOrder.value.orderList?.length ??
                                        1) -
                                    1,
                            child: const Divider(
                              color: AppColor.black5,
                            ),
                          )
                        ],
                      ),
                    );
                  },
                );
              }),
            ),
          ),
          const Divider(),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(Strings.productQuantity.tr, style: AppFont.componentSmall),
              Obx(
                () => Text("${controller.newOrder.value.sumQTY()}",
                    style: AppFont.componentSmallBold),
              ),
            ],
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(Strings.productPrice.tr, style: AppFont.componentSmall),
              controller.newOrder.value.sumTotalDiscount() == 0
                  ? Text("Rp${controller.newOrder.value.sumPrice().toCurrency}",
                      style: AppFont.componentSmallBold)
                  : Row(
                      children: [
                        Obx(() {
                          return Text(
                              "Rp${controller.newOrder.value.sumPrice().toCurrency}",
                              style: AppFont.componentSmall.copyWith(
                                  decoration: TextDecoration.combine(
                                      [TextDecoration.lineThrough]),
                                  color: AppColor.black50));
                        }),
                        5.0.width,
                        Obx(() {
                          return Text(
                              controller.newOrder.value
                                  .sumTotalDiscount()
                                  .toCurrency,
                              style: AppFont.componentSmallBold);
                        }),
                      ],
                    )
            ],
          ),
          Visibility(
            visible: controller.newOrder.value.sumTax() != 0,
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(Strings.totalTaxes.tr, style: AppFont.componentSmall),
                    Obx(
                      () => Text(
                          "Rp${controller.newOrder.value.sumTax().toCurrency}",
                          style: AppFont.componentSmallBold),
                    ),
                  ],
                ),
                MediaQuery.removePadding(
                  removeTop: true,
                  context: context,
                  child: Obx(
                    () => ListView.builder(
                        itemCount:
                            controller.newOrder.value.getTaxDetail().length,
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        itemBuilder: (context, index) {
                          TaxModel tax =
                              controller.newOrder.value.getTaxDetail()[index];
                          return Visibility(
                            visible: tax.currentTax != 0,
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(" - ${tax.name}",
                                    style: AppFont.componentSmall),
                                Text("Rp${tax.totalTax.toCurrency}",
                                    style: AppFont.componentSmall),
                              ],
                            ),
                          );
                        }),
                  ),
                ),
              ],
            ),
          ),
          controller.newOrder.value.deal?.name == null
              ? const SizedBox()
              : Column(
                  children: [
                    const Divider(),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text("Voucher", style: AppFont.componentSmallBold),
                        Obx(
                          () => Text("${controller.newOrder.value.deal?.name}",
                              style: AppFont.componentSmallBold),
                        ),
                      ],
                    ),
                  ],
                ),
          Obx(() {
            return Visibility(
              visible: (controller.newOrder.value.point != 0 &&
                  controller.store.token != null),
              child: Column(
                children: [
                  const Divider(),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                          Strings.valueEarned
                              .trParams({'value': c.getPointLanguage()}),
                          style: AppFont.componentSmall),
                      Container(
                        padding: const EdgeInsets.only(left: 8, right: 2),
                        decoration: BoxDecoration(
                            color: Colors.red,
                            borderRadius: BorderRadius.circular(15)),
                        child: Row(
                          children: [
                            Text("+${controller.newOrder.value.point}",
                                style: AppFont.componentSmall
                                    .copyWith(color: AppColor.white)),
                            const Icon(
                              Icons.local_fire_department,
                              color: Colors.orangeAccent,
                            )
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            );
          }),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Divider(),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(Strings.totalBill.tr,
                      style: AppFont.paragraphMediumBold),
                  Obx(
                    () => Text(
                        "Rp${controller.newOrder.value.sumTotalBill().toCurrency}",
                        style: AppFont.paragraphMediumBold),
                  ),
                ],
              ),
            ],
          ),
          Obx(
            () => (controller.newOrder.value.diningTable == null ||
                    controller.newOrder.value.diningTable == '')
                ? 10.0.height
                : Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Divider(),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(Strings.tableNumber.tr,
                              style: AppFont.componentSmall),
                          Text(controller.newOrder.value.diningTable ?? '',
                              style: AppFont.componentSmall),
                        ],
                      ),
                      AppDimen.h4.height
                    ],
                  ),
          ),
          Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              PrimaryButton(
                onPressed: () {
                  AppAlert.showConfirmWarningDialog(
                      context: context,
                      message:
                          'Make sure the qrcode has been scanned by the cashier'
                              .tr,
                      actions: [
                        TextButton(
                            onPressed: () async {
                              Get.back();
                              await controller.removeOrderTransaction();
                              Future.delayed(const Duration(milliseconds: 1500),
                                  () {
                                AppAlert.showThanksDialog(
                                  context: context,
                                  message: Strings.orderIsProcessed.tr,
                                  actions: [
                                    TextButton(
                                        onPressed: () {
                                          Get.offAllNamed(Routes.HOME);
                                        },
                                        child: Text(
                                          "Ok",
                                          style: AppFont.componentSmall
                                              .copyWith(color: AppColor.white),
                                        ))
                                  ],
                                );
                              });
                            },
                            child: Text("Confirm",
                                style: AppFont.componentSmall
                                    .copyWith(color: AppColor.white))),
                      ]);
                },
                text: Strings.done.tr,
                type: PrimaryButtonType.type4,
                child: SizedBox(
                  width: double.infinity,
                  child: Align(
                    alignment: Alignment.center,
                    child: Obx(() => controller.loading.value
                        ? const CustomCircularProgressIndicator()
                        : Text(
                            Strings.done.tr,
                            style: AppFont.componentSmall
                                .copyWith(color: AppColor.white),
                          )),
                  ),
                ),
              ),
              TextButtonWidget(
                onPressed: () {
                  AppAlert.showConfirmWarningDialog(
                      context: context,
                      message: Strings.cancelTransaction.tr,
                      actions: [
                        TextButton(
                            onPressed: () async {
                              await controller.removeOrderTransaction();
                              Get.back();
                              Get.offAllNamed(Routes.HOME);
                            },
                            child: Text("Sure",
                                style: AppFont.componentSmall
                                    .copyWith(color: AppColor.white))),
                      ]);
                },
                text: Strings.cancel.tr,
                width: double.infinity,
                type: TextButtonType.type2,
                child: Text(
                  Strings.cancel.tr,
                  textAlign: TextAlign.center,
                  style: AppFont.componentMediumBold
                      .copyWith(color: AppColor.utilityDanger),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
