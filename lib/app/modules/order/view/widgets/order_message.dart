import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/values.dart';

import '../../controllers/order_controller.dart';

class OrderMessage extends StatelessWidget {
  const OrderMessage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<OrderController>();
    return Obx(() {
      return Visibility(
        visible: controller.newOrder.value.message != null,
        child: Column(
          children: [
            AppDimen.h8.height,
            Container(
              width: Constants.defaultMaxWidth,
              decoration: BoxDecoration(
                color: AppColor.accentBlue.withOpacity(0.4),
                borderRadius: BorderRadius.all(
                  Radius.circular(AppDimen.h6),
                ),
              ),
              margin: EdgeInsets.symmetric(horizontal: AppDimen.h16),
              padding: EdgeInsets.symmetric(
                  horizontal: AppDimen.h12, vertical: AppDimen.h8),
              child: Obx(() {
                return Text(
                  controller.newOrder.value.message ?? '',
                  style: AppFont.componentSmallBold.copyWith(
                    color: AppColor.black70,
                  ),
                );
              }),
            ),
          ],
        ),
      );
    });
  }
}
