import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/core/theme/themes.dart';

import '../../controllers/order_controller.dart';
import 'order_proof_file_form.dart';

class OrderProofFile extends StatelessWidget {
  const OrderProofFile({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<OrderController>();
    return Obx(() => controller.xFile.value?.name == ""
        ? const SizedBox()
        : Column(
            children: [
              Padding(
                  padding: EdgeInsets.symmetric(
                      horizontal: AppDimen.h16, vertical: 16),
                  child: const OrderProofFileForm()),
              Divider(
                  color: AppColor.black5,
                  height: AppDimen.h6,
                  thickness: AppDimen.h6),
            ],
          ));
  }
}
