import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/order/order_export.dart';
import 'package:mobile_crm/app/utils/share_helper.dart';
import 'package:mobile_crm/app/utils/toast.dart';
import 'package:mobile_crm/app/widget/cached_image_widget.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/values.dart';

import '../../../../../data/models/payment_method_model.dart';

class PaymentQR extends StatelessWidget {
  const PaymentQR(
      {Key? key, required this.controller, required this.paymentMethodDetail})
      : super(key: key);
  final OrderController controller;
  final PaymentMethodDetail paymentMethodDetail;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8.0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(Icons.drag_handle,
              color: AppColor.black70, size: Constants.iconSizeSmall(context)),
          Text(
            "${paymentMethodDetail.name}",
            style: AppFont.paragraphMediumBold,
          ),
          10.0.height,
          Flexible(
            child: SingleChildScrollView(
              physics: const BouncingScrollPhysics(),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  ClipRRect(
                    borderRadius: const BorderRadius.all(
                      Radius.circular(12.0),
                    ),
                    child: CachedImageWidget(
                      imageUrl: paymentMethodDetail.paymentInfo?.value ?? "",
                    ),
                  ),
                  10.0.height,
                  IconButton(
                      onPressed: () async {
                        shareQR(
                            paymentMethodDetail.paymentInfo?.value ?? '',
                            "payment_${paymentMethodDetail.name}_${controller.newOrder.value.orderSalesId}",
                            "No.Invoice: ${controller.newOrder.value.orderSalesId}\nOutlet: ${controller.newOrder.value.outlet?.name ?? ''}\nTotal Bill: Rp ${controller.newOrder.value.sumTotalBill()}");
                      },
                      icon: Icon(Icons.share_outlined,
                          color: AppColor.black70,
                          size: Constants.iconSize(context))),
                  10.0.height,
                ],
              ),
            ),
          ),
          const Divider(),
          Align(
            alignment: Alignment.centerLeft,
            child: Text(
              Strings.payAmount.tr,
              style: AppFont.componentSmallBold,
            ),
          ),
          5.0.height,
          GestureDetector(
            onTapDown: (details) => Clipboard.setData(
                    ClipboardData(text: controller.newOrder.value.sumTotalBill().toString()))
                .then(
              (value) {
                Toast.show(Strings.textCopied.tr, type: ToastType.dark);
              },
            ),
            child: Container(
              width: MediaQuery.of(context).size.width,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                  color: AppColor.ink05,
                  borderRadius: BorderRadius.circular(10)),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    "Rp ${controller.newOrder.value.sumTotalBill().toCurrency}",
                    style: AppFont.paragraphSmallBold,
                  ),
                  Icon(
                    Icons.copy,
                    size: Constants.iconSizeSmall(context),
                    color: AppColor.black70,
                  ),
                ],
              ),
            ),
          ),
          20.0.height,
        ],
      ),
    );
  }
}
