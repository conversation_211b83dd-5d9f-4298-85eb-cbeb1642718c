import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/order/order_export.dart';
import 'package:mobile_crm/app/utils/logger.dart';
import 'package:mobile_crm/app/utils/toast.dart';
import 'package:mobile_crm/app/utils/utils.dart';
import 'package:mobile_crm/app/widget/button_widget/button_widget.dart';
import 'package:mobile_crm/app/widget/custom_circular_progress_widget.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/values.dart';

import '../../../../../data/models/payment_method_model.dart';
import '../../../../enum/order_status_enum.dart';
import '../components/order_modal_bottom_sheet.dart';
import 'order_proof_file_form.dart';

class PaymentTransfer extends StatelessWidget {
  const PaymentTransfer(
      {Key? key, required this.controller, required this.paymentMethodDetail})
      : super(key: key);
  final OrderController controller;
  final PaymentMethodDetail paymentMethodDetail;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8.0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Center(
              child: Icon(
            Icons.drag_handle,
            size: Constants.iconSizeSmall(context),
            color: AppColor.black70,
          )),
          Text(
            paymentMethodDetail.type?.replaceAll("_", " ").capitalizeFirst ??
                '',
            style: AppFont.paragraphMediumBold,
          ),
          5.0.height,
          Text(
            "${paymentMethodDetail.name}",
            style: AppFont.componentMediumBold,
          ),
          Text(
            "${paymentMethodDetail.accountName}",
            style: AppFont.componentSmallBold,
          ),
          10.0.height,
          GestureDetector(
            onTapDown: (details) => Clipboard.setData(ClipboardData(
                    text: paymentMethodDetail.paymentInfo?.value ?? ''))
                .then(
              (value) {
                Toast.show(Strings.textCopied.tr, type: ToastType.dark);
              },
            ),
            child: Container(
              width: MediaQuery.of(context).size.width,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                  color: AppColor.ink05,
                  borderRadius: BorderRadius.circular(10)),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    "${paymentMethodDetail.accountNumber}",
                    style: AppFont.paragraphSmallBold,
                  ),
                  Icon(
                    Icons.copy,
                    size: Constants.iconSizeSmall(context),
                    color: AppColor.black70,
                  ),
                ],
              ),
            ),
          ),
          10.0.height,
          Text(
            Strings.transferAmount.tr,
            style: AppFont.componentSmallBold,
          ),
          5.0.height,
          GestureDetector(
            onTapDown: (details) => Clipboard.setData(ClipboardData(
                    text: controller.newOrder.value.sumTotalBill().toString()))
                .then(
              (value) {
                Toast.show(Strings.textCopied.tr, type: ToastType.dark);
              },
            ),
            child: Container(
              width: MediaQuery.of(context).size.width,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                  color: AppColor.ink05,
                  borderRadius: BorderRadius.circular(10)),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    "Rp ${controller.newOrder.value.sumTotalBill().toCurrency}",
                    style: AppFont.paragraphSmallBold,
                  ),
                  Icon(
                    Icons.copy,
                    size: Constants.iconSizeSmall(context),
                    color: AppColor.black70,
                  ),
                ],
              ),
            ),
          ),
          Obx(
            () => controller.xFile.value?.path == ""
                ? const SizedBox()
                : Column(
                    children: [
                      20.0.height,
                      const Divider(),
                      const OrderProofFileForm(),
                    ],
                  ),
          ),
          20.0.height,
          Obx(
            () => controller.xFile.value?.path == ""
                ? PrimaryButton(
                    onPressed: () {
                      OrderModalBottomSheet.modalBottomChooseImage(
                          context: context);
                    },
                    text: "",
                    width: MediaQuery.of(context).size.width,
                    type: PrimaryButtonType.type4,
                    child: SizedBox(
                      width: MediaQuery.of(context).size.width,
                      child: Text(
                        Strings.selectProofOfPayment.tr,
                        textAlign: TextAlign.center,
                        style: AppFont.componentSmallBold
                            .copyWith(color: AppColor.white),
                      ),
                    ),
                  )
                : controller.xFile.value?.path != "" &&
                        controller.newOrder.value.status !=
                            OrderStatusEnum.payment_verification.name &&
                        controller.newOrder.value.status !=
                            OrderStatusEnum.payment_verified.name &&
                        controller.newOrder.value.status !=
                            OrderStatusEnum.received.name &&
                        controller.newOrder.value.status !=
                            OrderStatusEnum.arrived.name
                    ? PrimaryButton(
                        onPressed: () async {
                          infoLogger("Update Order Status",
                              "${await controller.updateOrderDetailStatus(OrderStatusEnum.payment_verification).then((value) => value.data)}");
                        },
                        text: "",
                        width: MediaQuery.of(context).size.width,
                        type: PrimaryButtonType.type4,
                        child: SizedBox(
                          width: MediaQuery.of(context).size.width,
                          child: Obx(() {
                            return controller.loading.value
                                ? const CustomCircularProgressIndicator()
                                : Align(
                                    alignment: Alignment.center,
                                    child: Text(
                                      Strings.sendProofOfPayment.tr,
                                      style: AppFont.componentSmallBold
                                          .copyWith(color: AppColor.white),
                                    ),
                                  );
                          }),
                        ),
                      )
                    : Center(
                        child: Column(
                          children: [
                            Text(
                              translationOrderStatus(
                                  controller.newOrder.value.status ?? ""),
                              style: AppFont.paragraphMediumBold,
                            ),
                            10.0.height
                          ],
                        ),
                      ),
          ),
          10.0.height,
        ],
      ),
    );
  }
}
