import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/values.dart';

import '../../controllers/order_controller.dart';

class OrderNote extends StatelessWidget {
  const OrderNote({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<OrderController>();
    return Visibility(
      visible: (controller.newOrder.value.orderNote != null ? controller.newOrder.value.orderNote != '' : false),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Divider(
              color: AppColor.black5,
              height: AppDimen.h6,
              thickness: AppDimen.h6),
          Container(
              padding: EdgeInsets.symmetric(
                  horizontal: AppDimen.h16, vertical: 16),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    Strings.notes.tr,
                    style: AppFont.componentMediumBold,
                  ),
                  Text(
                    "${controller.newOrder.value.orderNote}",
                    style: AppFont.componentSmall,
                  )
                ],
              )),
        ],
      ),
    );
  }
}
