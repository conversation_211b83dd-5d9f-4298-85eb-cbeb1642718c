import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/utils/image_picker_helper.dart';
import 'package:mobile_crm/app/widget/button_widget/button_widget.dart';
import 'package:mobile_crm/core/extensions/double_extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/values.dart';

import '../../controllers/order_controller.dart';

class PaymentProof extends StatelessWidget {
  const PaymentProof({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<OrderController>();
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(Icons.drag_handle_rounded,
              color: AppColor.black70, size: Constants.iconSizeSmall(context)),
          <PERSON><PERSON>(
            alignment: Alignment.centerLeft,
            child: Text(
              Strings.selectImageFrom.tr,
              style: AppFont.paragraphMedium,
            ),
          ),
          const Divider(),
          10.0.height,
          PrimaryButton(
            onPressed: () async {
              Get.back();
              controller.xFile.value =
                  await ImagePickerHelper.getImageFromCamera();
            },
            text: Strings.camera.tr,
            type: PrimaryButtonType.type4,
            child: SizedBox(
              width: MediaQuery.of(context).size.width,
              child: Text(
                Strings.camera.tr,
                textAlign: TextAlign.center,
                style:
                    AppFont.componentSmallBold.copyWith(color: AppColor.white),
              ),
            ),
          ),
          10.0.height,
          PrimaryButton(
              onPressed: () async {
                Get.back();
                controller.xFile.value =
                    await ImagePickerHelper.getImageFromGallery();
              },
              text: Strings.gallery.tr,
              width: MediaQuery.of(context).size.width,
              type: PrimaryButtonType.type4,
              child: SizedBox(
                width: MediaQuery.of(context).size.width,
                child: Text(
                  Strings.gallery.tr,
                  style: AppFont.componentSmallBold
                      .copyWith(color: AppColor.white),
                  textAlign: TextAlign.center,
                ),
              )),
          20.0.height,
        ],
      ),
    );
  }
}
