// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/utils/app_alert.dart';
import 'package:mobile_crm/app/utils/toast.dart';
import 'package:mobile_crm/app/widget/button_widget/button_widget.dart';
import 'package:mobile_crm/app/widget/custom_circular_progress_widget.dart';
import 'package:mobile_crm/app/widget/modal_bottom_sheet/pay_deal_modal_sheet.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/app_strings.dart';
import 'package:mobile_crm/routes/app_pages.dart';

import '../../../../../data/services/analytics_service.dart';
import '../../../../enum/order_status_enum.dart';
import '../../../../widget/full_screen_dialog/feedback/feedback_fdialog.dart';
import '../../controllers/order_controller.dart';

class OrderBottomButton extends StatelessWidget {
  const OrderBottomButton({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<OrderController>();
    return Obx(
      () => controller.newOrder.value.status ==
                  OrderStatusEnum.payment_verified.name ||
              controller.newOrder.value.status ==
                  OrderStatusEnum.arrived.name ||
              controller.newOrder.value.status ==
                  OrderStatusEnum.received.name ||
              controller.newOrder.value.status == OrderStatusEnum.ready.name
          ? Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Obx(
                  () {
                    return controller.filledFeedback.value
                        ? const SizedBox()
                        : GestureDetector(
                            onTapDown: (details) {
                              FeedbackFullScreenDialog().show(
                                  context: context,
                                  transRepo: controller.repoTransaction,
                                  salesId:
                                      controller.newOrder.value.orderSalesId);
                            },
                            child: Container(
                              height: 40,
                              width: double.infinity,
                              decoration: BoxDecoration(
                                  color: AppColor.white,
                                  border: Border.all(color: AppColor.black10),
                                  boxShadow: const [
                                    BoxShadow(
                                        color: AppColor.black10,
                                        spreadRadius: 1,
                                        blurRadius: 10,
                                        offset: Offset(0, 1))
                                  ]),
                              child: Center(
                                  child: Text(
                                "Feed Back",
                                style: AppFont.paragraphSmallBold,
                              )),
                            ));
                  },
                ),
                Container(
                  color: AppColor.white,
                  padding: const EdgeInsets.all(8.0),
                  child: (controller.newOrder.value.shipment?.shippingId ==
                              null &&
                          controller.newOrder.value.orderType == "delivery")
                      ? PrimaryButton(
                          onPressed: () {
                            controller.check();
                          },
                          width: double.infinity,
                          text: "",
                          type: PrimaryButtonType.type4,
                          child: SizedBox(
                            width: double.infinity,
                            child: Obx(() {
                              return controller.loadingProof.value
                                  ? const CustomCircularProgressIndicator()
                                  : Align(
                                      alignment: Alignment.center,
                                      child: Text(
                                        Strings.waitingForValue.trParams({
                                          'value':
                                              Strings.profOfDeliveryReceipt.tr
                                        }),
                                        style: AppFont.paragraphSmallBold
                                            .copyWith(color: AppColor.white),
                                      ),
                                    );
                            }),
                          ),
                        )
                      : PrimaryButton(
                          text: Strings.done.tr,
                          onPressed: () async {
                            AppAlert.showConfirmWarningDialog(
                              context: context,
                              message: Strings.makeSureTheOrderIsCorrect.tr,
                              actions: [
                                TextButton(
                                    onPressed: () async {
                                      AnalyticsService.observer.analytics
                                          .logEvent(
                                              name: "done_order",
                                              parameters: {
                                            "invoice": controller
                                                .newOrder.value.orderSalesId,
                                            "total_price": controller
                                                .newOrder.value
                                                .sumTotalBill(),
                                          });
                                      controller.updateOrderDetailStatus(
                                        OrderStatusEnum.received,
                                      );
                                      AppAlert.showThanksDialog(
                                        context: context,
                                        actions: [
                                          TextButton(
                                            onPressed: () =>
                                                Get.offAllNamed(Routes.HOME),
                                            child: Text(
                                              "Ok",
                                              style: AppFont.componentSmall
                                                  .copyWith(
                                                      color: AppColor.white),
                                            ),
                                          )
                                        ],
                                      );
                                    },
                                    child: Text(
                                      Strings.done.tr,
                                      style: AppFont.componentSmall
                                          .copyWith(color: AppColor.white),
                                    )),
                              ],
                            );
                          },
                          type: PrimaryButtonType.type4,
                          child: SizedBox(
                            width: double.infinity,
                            child: Align(
                              alignment: Alignment.center,
                              child: Obx(
                                () => controller.loading.value
                                    ? const CustomCircularProgressIndicator()
                                    : Text(
                                        Strings.done.tr,
                                        style: AppFont.paragraphSmallBold
                                            .copyWith(color: AppColor.white),
                                      ),
                              ),
                            ),
                          ),
                        ),
                ),
              ],
            )
          : controller.newOrder.value.status == OrderStatusEnum.pending.name
              ? Container(
                  color: AppColor.white,
                  padding: const EdgeInsets.all(8.0),
                  child: PrimaryButton(
                    text: Strings.cancel.tr,
                    onPressed: () async {
                      AppAlert.showConfirmWarningDialog(
                        message: Strings.cancelTransaction.tr,
                        context: context,
                        actions: [
                          TextButton(
                            onPressed: () async {
                              AnalyticsService.observer.analytics
                                  .logEvent(name: "cancel_order", parameters: {
                                "invoice":
                                    controller.newOrder.value.orderSalesId,
                                "total_price":
                                    controller.newOrder.value.sumTotalBill(),
                              });
                              Get.back();
                              var resp =
                                  await controller.updateOrderDetailStatus(
                                      OrderStatusEnum.cancel);
                              if (resp.status) {
                                Toast.show(
                                    "Order ${controller.newOrder.value.orderSalesId} Canceled",
                                    type: ToastType.dark);
                                Future.delayed(const Duration(seconds: 2), () {
                                  Get.offAllNamed(Routes.HOME);
                                });
                              }
                            },
                            child: Text(
                              Strings.cancel.tr,
                              style: AppFont.componentSmall
                                  .copyWith(color: AppColor.white),
                            ),
                          ),
                        ],
                      );
                    },
                    width: double.infinity,
                    type: PrimaryButtonType.type4,
                    child: Obx(
                      () => controller.loading.value
                          ? const CustomCircularProgressIndicator()
                          : Text(Strings.cancel.tr,
                              style: AppFont.paragraphSmallBold
                                  .copyWith(color: AppColor.white)),
                    ),
                  ),
                )
              : controller.newOrder.value.status ==
                          OrderStatusEnum.reject.name ||
                      controller.newOrder.value.status ==
                          OrderStatusEnum.cancel.name
                  ? Container(
                      color: AppColor.white,
                      padding: const EdgeInsets.all(8.0),
                      child: PrimaryButton(
                        text: "Back",
                        onPressed: () async {
                          AnalyticsService.observer.analytics.logEvent(
                              name: "cancel_or_reject_order",
                              parameters: {
                                "invoice":
                                    controller.newOrder.value.orderSalesId,
                                "total_price":
                                    controller.newOrder.value.totalBill,
                              });
                          Get.offAllNamed(Routes.HOME);
                        },
                        width: double.infinity,
                        type: PrimaryButtonType.type4,
                        child: Obx(
                          () => controller.loading.value
                              ? const CustomCircularProgressIndicator()
                              : Text(Strings.back.tr,
                                  style: AppFont.paragraphSmallBold
                                      .copyWith(color: AppColor.white)),
                        ),
                      ),
                    )
                  : controller.newOrder.value.status ==
                          OrderStatusEnum.payment_verification.name
                      ? const SizedBox()
                      : Container(
                          width: double.infinity,
                          color: AppColor.white,
                          padding: const EdgeInsets.all(8.0),
                          child: PrimaryButton(
                            text: Strings.choosePayment.tr,
                            onPressed: () async => PayBottomSheet.showOrder(
                              context: context,
                              paymentMethods:
                                  await controller.getPaymentMethodd() ?? [],
                            ),
                            width: double.infinity,
                            type: PrimaryButtonType.type4,
                            child: Obx(
                              () => controller.loading.value
                                  ? const CustomCircularProgressIndicator()
                                  : Text(
                                      Strings.choosePayment.tr,
                                      style: AppFont.paragraphSmallBold
                                          .copyWith(color: AppColor.white),
                                    ),
                            ),
                          ),
                        ),
    );
  }
}
