import 'package:flutter/material.dart';
import 'package:mobile_crm/app/modules/order/controllers/order_controller.dart';
import 'package:mobile_crm/app/modules/order/view/widgets/payment_qr.dart';
import 'package:mobile_crm/app/modules/order/view/widgets/payment_transfer.dart';

import '../../../../../data/models/payment_method_model.dart';
import '../widgets/payment_proof.dart';

class OrderModalBottomSheet {
  static void modalBottomChooseImage({required BuildContext context}) {
    showModalBottomSheet(
        constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * .8,
            minWidth: MediaQuery.of(context).size.width),
        context: context,
        shape: const RoundedRectangleBorder(
            borderRadius: BorderRadiusDirectional.only(
                topStart: Radius.circular(15), topEnd: Radius.circular(15))),
        isScrollControlled: true,
        builder: (context) => const PaymentProof());
  }

  static void payBankTransfer(
      {required BuildContext context,
        required OrderController controller,
        required PaymentMethodDetail paymentMethodDetail}) {
    showModalBottomSheet(
        constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * .8,
            minWidth: MediaQuery.of(context).size.width),
        context: context,
        shape: const RoundedRectangleBorder(
            borderRadius: BorderRadiusDirectional.only(
                topStart: Radius.circular(15), topEnd: Radius.circular(15))),
        isScrollControlled: true,
        builder: (context) => paymentMethodDetail.paymentInfo?.type != "image"
            ? PaymentTransfer(
          controller: controller,
          paymentMethodDetail: paymentMethodDetail,
        )
            : PaymentQR(
          controller: controller,
          paymentMethodDetail: paymentMethodDetail,
        ));
  }
}
