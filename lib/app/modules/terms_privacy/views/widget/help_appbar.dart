import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/screen_wrapper/controller/wrapper_controller.dart';
import 'package:mobile_crm/app/widget/cached_image_widget.dart';

PreferredSizeWidget helpAppBar(
  BuildContext context,
  String title,
  WrapperController c,
) {
  return AppBar(
    title: Text(title),
    centerTitle: true,
    backgroundColor: c.getPrimaryColor(),
    flexibleSpace: Obx(
      () => (c.configApp.value.asset == null)
          ? const Text("")
          : CachedImageWidget(
              height: kToolbarHeight + MediaQuery.of(context).padding.top,
              width: Get.size.width,
              fit: BoxFit.cover,
              imageUrl:
                  c.configApp.value.asset?.toolbar_background.toString() ?? "",
              errorWidget: (context, url, error) => const SizedBox(),
            ),
    ),
  );
}
