import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/terms_privacy/controllers/terms_privacy_controller.dart';
import 'package:mobile_crm/app/modules/terms_privacy/views/widget/faq_list_tile.dart';
import 'package:mobile_crm/data/providers/db/database.dart';

class TabViewHelp extends StatelessWidget {
  const TabViewHelp({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<TermsPrivacyController>();

    return Obx(
      () => DefaultTabController(
        length: controller.tabs.length,
        child: Column(
          children: [
            TabBar(
              isScrollable: true,
              tabs: controller.tabs
                  .map(
                    (it) => Tab(
                      text: it,
                    ),
                  )
                  .toList(),
            ),
            Expanded(
              child: TabBarView(
                children: List.generate(controller.tabs.length, (index) {
                  List<FaqData> faqs = [];
                  if (index == 0) {
                    faqs = controller.wrapperController.faqs;
                  } else {
                    faqs = controller.faqs[controller.tabs[index]]!;
                  }
                  return ListView(
                    // ignore: invalid_use_of_protected_member
                    children: faqs
                        .map(
                          (it) => generateFaqListTile(
                            context,
                            it,
                          ),
                        )
                        .toList(),
                  );
                }),
              ),
            )
          ],
        ),
      ),
    );
  }
}
