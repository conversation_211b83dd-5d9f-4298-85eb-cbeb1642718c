import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mobile_crm/core/theme/app_color.dart';
import 'package:mobile_crm/core/values/app_constants.dart';
import 'package:mobile_crm/data/providers/db/database.dart';

ListTile generateFaqListTile(BuildContext context, FaqData faq) {
  return ListTile(
    onTap: () {
      showModalBottomSheet(
        context: context,
        builder: (context) => Container(
          padding:
              EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
          constraints: BoxConstraints(
            maxWidth: Constants.defaultBoxConstraints(context),
            minHeight: Constants.defaultBoxConstraints(context),
          ),
          decoration: const BoxDecoration(
            color: AppColor.black10,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(25),
              topRight: Radius.circular(25),
            ),
          ),
          child: Padding(
            padding: EdgeInsets.all(15.h),
            child: SingleChildScrollView(
              child: Html(
                data: faq.answer,
              ),
            ),
          ),
        ),
      );
    },
    title: Text("${faq.title}"),
    leading: const Icon(
      Icons.help_outline,
      color: AppColor.couponDiscount,
    ),
    trailing: const Icon(
      Icons.keyboard_arrow_down_outlined,
      color: AppColor.ink02,
    ),
  );
}
