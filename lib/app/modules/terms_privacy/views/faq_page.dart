import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/screen_wrapper/controller/wrapper_controller.dart';
import 'package:mobile_crm/app/modules/screen_wrapper/views/app_page_wrapper.dart';
import 'package:mobile_crm/app/modules/terms_privacy/views/widget/faq_list_tile.dart';
import 'package:mobile_crm/app/modules/terms_privacy/views/widget/help_appbar.dart';
import 'package:mobile_crm/app/modules/terms_privacy/views/widget/tab_view_help.dart';
import 'package:mobile_crm/core/theme/app_color.dart';
import 'package:mobile_crm/data/providers/db/database.dart';

class FaqPage extends StatelessWidget {
  const FaqPage({super.key});

  @override
  Widget build(BuildContext context) {
    final wController = Get.find<WrapperController>();

    return PageWrapper(
      appBar: helpAppBar(context, "Frequently Asked Questions", wController),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: SearchAnchor(
                isFullScreen: true,
                viewElevation: 0.0,
                viewLeading: IconButton(
                  onPressed: () {
                    Get.back();
                  },
                  icon: const Icon(
                    Icons.arrow_back,
                    color: AppColor.black,
                  ),
                ),
                builder: (context, controller) {
                  return SearchBar(
                    hintText: "search FAQs...",
                    elevation: const WidgetStatePropertyAll(0.0),
                    controller: controller,
                    padding: const WidgetStatePropertyAll<EdgeInsets>(
                      EdgeInsets.symmetric(horizontal: 16.0),
                    ),
                    onTap: () {
                      controller.openView();
                    },
                    onChanged: (val) {
                      controller.openView();
                    },
                    leading: const Icon(
                      Icons.search,
                      color: AppColor.black,
                    ),
                  );
                },
                suggestionsBuilder: (context, controller) {
                  List<FaqData> filtered = wController.faqs
                      .where((it) => it.title!
                          .toLowerCase()
                          .contains(controller.text.toLowerCase()))
                      .toList();

                  return List<ListTile>.generate(filtered.length, (int index) {
                    FaqData faq = filtered[index];
                    return generateFaqListTile(
                      context,
                      faq,
                    );
                  });
                }),
          ),
          const Expanded(
            child: TabViewHelp(),
          )
        ],
      ),
    );
  }
}
