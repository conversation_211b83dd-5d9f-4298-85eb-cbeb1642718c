import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/screen_wrapper/controller/wrapper_controller.dart';
import 'package:mobile_crm/app/modules/screen_wrapper/views/app_page_wrapper.dart';
import 'package:mobile_crm/app/modules/terms_privacy/views/widget/help_appbar.dart';
import 'package:mobile_crm/core/values/app_strings.dart';

class PrivacyPage extends StatelessWidget {
  const PrivacyPage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<WrapperController>();

    return PageWrapper(
      child: Scaffold(
        appBar: helpAppBar(context, Strings.privacyPolicy.tr, controller),
        backgroundColor: Colors.transparent,
        body: SingleChildScrollView(
          child: Html(data: "${controller.configApp.value.privacyPolicy}"),
        ),
      ),
    );
  }
}
