import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/screen_wrapper/controller/wrapper_controller.dart';
import 'package:collection/collection.dart';
import 'package:mobile_crm/data/providers/db/database.dart';

class TermsPrivacyController extends GetxController {
  late final WrapperController wrapperController;
  var tabs = <String>[].obs;
  var faqs = <String?, List<FaqData>>{}.obs;
  late final SearchController? searchController;

  @override
  void onInit() {
    super.onInit();
    wrapperController = Get.find<WrapperController>();
    searchController = SearchController();
    groupFaqByCategory();
  }

  void groupFaqByCategory() {
    List<String> t = ["All"];
    var byCategory = groupBy(wrapperController.faqs, (it) => it.category);

    byCategory.forEach((k, v) => t.add(k!));
    faqs.value = byCategory;
    tabs.value = t;
  }

  void onChanged(val) {
    print(searchController?.text);
  }
}
