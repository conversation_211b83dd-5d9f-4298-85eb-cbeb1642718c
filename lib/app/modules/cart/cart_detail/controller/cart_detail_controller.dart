// ignore_for_file: invalid_use_of_protected_member

import 'package:drift/drift.dart' as drift;
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:geocoding/geocoding.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:mobile_crm/app/helper/transaction_helper.dart';
import 'package:mobile_crm/app/modules/screen_wrapper/controller/wrapper_controller.dart';
import 'package:mobile_crm/app/utils/app_alert.dart';
import 'package:mobile_crm/app/utils/firebase_remote_helper.dart';
import 'package:mobile_crm/app/utils/location_helper.dart';
import 'package:mobile_crm/app/utils/logger.dart';
import 'package:mobile_crm/app/utils/toast.dart';
import 'package:mobile_crm/app/utils/utils.dart';
import 'package:mobile_crm/core/theme/app_color.dart';
import 'package:mobile_crm/core/theme/app_font.dart';
import 'package:mobile_crm/core/values/values.dart';
import 'package:mobile_crm/data/models/address_model.dart';
import 'package:mobile_crm/data/models/business_hour_model.dart';
import 'package:mobile_crm/data/models/cart_model.dart';
import 'package:mobile_crm/data/models/delivery_price_model.dart';
import 'package:mobile_crm/data/models/order_model.dart';
import 'package:mobile_crm/data/models/point_collection_model.dart';
import 'package:mobile_crm/data/models/self_order_model.dart';
import 'package:mobile_crm/data/models/shipment_model.dart';
import 'package:mobile_crm/data/models/variant_model.dart';
import 'package:mobile_crm/data/providers/db/database.dart';
import 'package:mobile_crm/data/repository/cart_repository.dart';
import 'package:mobile_crm/data/repository/deal_repository.dart';
import 'package:mobile_crm/data/repository/outlet_repository.dart';
import 'package:mobile_crm/data/repository/point_collection_repository.dart';
import 'package:mobile_crm/data/repository/product_repository.dart';
import 'package:mobile_crm/data/repository/transaction_repository.dart';
import 'package:mobile_crm/data/services/local_storage.dart';
import 'package:mobile_crm/domain/repository/cart_repository.dart';
import 'package:mobile_crm/domain/repository/deal_repository.dart';
import 'package:mobile_crm/domain/repository/outlet_repository.dart';
import 'package:mobile_crm/domain/repository/point_collection_repository.dart';
import 'package:mobile_crm/domain/repository/product_repository.dart';
import 'package:mobile_crm/domain/repository/transaction_repository.dart';
import 'package:mobile_crm/routes/app_pages.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

import '../../../../../data/models/outlet_feature_model.dart';
import '../../../../../data/models/transaction_new_model.dart';
import '../../../../../data/models/unit_conversion_model.dart';

class CartDetailController extends GetxController {
  final TransactionRepository repoTransaction =
      Get.find<TransactionRepositoryIml>();
  final CartRepository _repoCart = Get.find<CartRepositoryIml>();
  final ProductRepository _repoProduct = Get.find<ProductRepositoryIml>();
  final OutletRepository _repoOutlet = Get.find<OutletRepositoryIml>();
  final DealRepository repoDeal = Get.find<DealRepositoryIml>();
  final PointCollectionRepository _repoPointCollection =
      Get.find<PointCollectionRepositoryIml>();
  final wrapperController = Get.find<WrapperController>();
  final store = LocalStorageService();
  final firebaseRemote = FirebaseRemoteHelper.instance;

  TextEditingController teNoteController = TextEditingController();
  TextEditingController noteOrderController = TextEditingController();
  TextEditingController dinningTableOrderController = TextEditingController();
  TextEditingController addressOrderController = TextEditingController();
  TextEditingController qtyEditingController = TextEditingController();
  TextEditingController receiveReceiptsOrderController =
      TextEditingController();
  // SelfOrder orderList = SelfOrder();

  var newTransactionModel = TransactionNewModel().obs;
  var listTransaction = <TransactionDataData>[].obs;
  var voucher = DealData().obs;
  var orderTypeString = (<String>["self order"]).obs;
  var address = AddressModel().obs;

  var valueIndex = 0.obs;
  var pointCollection = 0.0.obs;

  int? deliveryTime;
  String? timePickup;

  int outletId = 0;
  bool isUserLogin = false;
  List<UnitConversionModel> listUnitConversion = [];
  Outlet? outlet = null;

  var isLoading = false.obs;
  var isCreateTransactionLoading = false.obs;
  var useVoucherLoading = false.obs;
  var isOpenThisOutlet = false.obs;
  var isDeliveryPriceLoading = false.obs;

  var outletDeliveryPrice = Rx<DeliveryPrice?>(null);

  @override
  void onInit() async {
    isUserLogin = store.token != null;
    getAllUnitConversion();
    outletId = int.tryParse(Get.parameters['id'] ?? '0') ?? 0;
    newTransactionModel.value = await _repoCart
            .getTransactionOrderByOutletId(outletId, isOnOrder: false)
        // .whenComplete(() => getOrderTypeSetting())
        ??
        TransactionNewModel();
    newTransactionModel.value.outletId = outletId;
    newTransactionModel.value.setDeals(null);
    if (isUserLogin) {
      newTransactionModel.value.customerName = store.user?.name;
      newTransactionModel.value.receiptReceiver = store.user?.phone;
    }

    if (kDebugMode) {
      print(store.token);
    }

    Sentry.addBreadcrumb(Breadcrumb(
        type: 'debug',
        category: 'user.activity.cart_detail.controller.onInit',
        data: {'outletID': outletId},
        level: SentryLevel.debug));
    getAllTransaction();
    isOutletOpen();
    await getOrderTypeSetting();
    await getPointCollection();
    super.onInit();
  }

  Future<void> getAllUnitConversion() async {
    listUnitConversion = await _repoProduct.getUnitConversion();
  }

  Future<void> getOutletDeliveryPrice(Map<String, dynamic> latlng) async {
    if (latlng['latitude'] == null || latlng['longitude'] == null) {
      Toast.show('can not determine location, please change the address',
          type: ToastType.error);
      return;
    }
    isDeliveryPriceLoading.value = true;
    infoLogger("get delivery price....", latlng);
    var data = await _repoOutlet.getOutletDeliveryPrice(latlng, outletId);
    if (data == null) {
      Toast.show('failed getting shipping price, please try again later',
          type: ToastType.warning);
      return;
    }

    outletDeliveryPrice.value = data;
    newTransactionModel.value.shipment = ShipmentModel(
      shippingAddress: address.value.address,
      memberAddressId: address.value.membersAddressId ?? 0,
      shippingCharge: data.price!,
    );
    newTransactionModel.refresh();
    isDeliveryPriceLoading.value = false;
    infoLogger('shipment data, address: ${address.value.address} ',
        newTransactionModel.value.shipment);
  }

  void resetShipment() {
    newTransactionModel.value.shipment = null;
    newTransactionModel.refresh();
  }

  void getQueryUrl() {
    if (Get.arguments.runtimeType.toString() == "_Map<String, String?>") {
      String? table = Get.arguments['tableNumber'];
      newTransactionModel.value.diningTable = table;
      dinningTableOrderController.text = table ?? '';
    }
  }

  bool getFirebaseRemoteABPoint() => firebaseRemote.getBool('testing');

  setLoadingCreateTransactionButton(bool loading) =>
      isCreateTransactionLoading.value = loading;

  void getArgument() {
    if (Get.arguments.runtimeType.toString() == "_Map<String, String?>") {
      dinningTableOrderController.text = Get.arguments['tableNumber'];
    }
  }

  void updateCartRemote({required int outletId}) {
    if (isUserLogin) {
      _repoCart.updateBatchCarts(outletId: outletId);
    }
  }

  Future<OutletFeatureModel?> getOutletFeature() async => await _repoOutlet
      .getOutletById(outletId)
      .then((value) => value?.feature)
      .whenComplete(() => getArgument());

  Future getOrderTypeSetting() async {
    infoLogger(
        'getOrderTypeSetting, outlet: ', newTransactionModel.value.outlet);
    // printLongString(newTransactionModel.value.outlet!.toJsonString());
    if (newTransactionModel.value.outlet?.order_type == null) {
      outlet = await _repoOutlet.getOutletById(outletId);
      newTransactionModel.value.outlet?.order_type =
          outlet?.order_type ?? OrderTypeModel();
    }

    if (newTransactionModel.value.outlet?.order_type != null) {
      orderTypeString.value = TransactionHelper.filterOrderType(
        orderTypeModel: newTransactionModel.value.outlet?.order_type,
      );
      infoLogger('orderTypeString value: ', orderTypeString.value);
    } else {
      infoLogger('can not load order type, outlet order_type is null',
          newTransactionModel.value.outlet?.order_type);
      errorLogger(
          pos: 'can not load order type',
          error: Exception('order type is null'));
    }

    if (orderTypeString.value.isEmpty) {
      orderTypeString.value = ['self order'];
    }

    // get last order local data
    LastTypeEntityData? lastOrderType =
        await _repoOutlet.getLastOrderType(outletId);
    if (lastOrderType != null) {
      int indexType = orderTypeString.indexOf(lastOrderType.orderType);
      valueIndex.value = indexType;
    }
  }

  void handleSelectOrderType(int index) {
    String orderType = orderTypeString[index];
    _repoOutlet.insertLastOrderType(outletId, orderType);
  }

  Future<List<TransactionDataData>> getAllTransaction() async =>
      listTransaction.value =
          await repoTransaction.getAllTransaction(dealRepo: repoDeal);

  Future<VariantModel?> getProductByVariantId(
      String productId, int productDetailId) async {
    var product = await _repoProduct.getProductById(productId);

    if (product == null || product.variant == null) {
      return null;
    }

    var data = product.variant?.firstWhereOrNull((element) => element.available
                ?.firstWhereOrNull(
                    (el) => el.productDetailId == productDetailId) !=
            null
        ? true
        : false);
    return data;
  }

  List<String> getVoucherDayActive(DealData deal) {
    List<String> weekdayAvailable = [];
    List nums = [
      deal.sunday,
      deal.monday,
      deal.tuesday,
      deal.wednesday,
      deal.thursday,
      deal.friday,
      deal.saturday
    ];

    if (nums[0] == 1) weekdayAvailable.add("Sunday");
    if (nums[1] == 1) weekdayAvailable.add("Monday");
    if (nums[2] == 1) weekdayAvailable.add("Tuesday");
    if (nums[3] == 1) weekdayAvailable.add("Wednesday");
    if (nums[4] == 1) weekdayAvailable.add("Thursday");
    if (nums[5] == 1) weekdayAvailable.add("Friday");
    if (nums[6] == 1) weekdayAvailable.add("Saturday");

    return weekdayAvailable;
  }

  bool isVoucherCanBeUse(DealData voucher) {
    bool isDayMeetReq =
        isDayMeetRequirement(weekday: getVoucherDayActive(voucher));
    if (!isDayMeetReq) {
      return false;
    }
    bool isDateMeetReq = isDateMeetRequirement(
        dateStart: voucher.startPromotionDate,
        dateEnd: voucher.endPromotionDate);
    if (!isDateMeetReq) {
      return false;
    }

    bool isTimeMeetReq = isTimeMeetRequirement(
        timeStart: voucher.startPromotionTime,
        timeEnd: voucher.endPromotionTime);
    if (!isTimeMeetReq) {
      return false;
    }

    return true;
  }

  Future<List<DealData>?> getVoucher() async {
    return await repoDeal.getMyVoucher().then((value) => filterPromos(value));
  }

  Future<List<PointCollectionModel>?> getPointCollection() async {
    var res = await _repoPointCollection.getPointCollection(
        active: true,
        parentPointType: ParentPointType.transaction,
        memberTypeId: store.user?.typefkid);
    if (res.status) {
      var data = res.data ?? [];
      pointCollection.value = await filterProductPoint(
          carts: newTransactionModel.value.orderList ?? [], points: data);
      newTransactionModel.value.point = pointCollection.value.toInt();
      newTransactionModel.refresh();
      return data;
    }
    return null;
  }

  Future<double> filterProductPoint(
      {List<PointCollectionModel>? points,
      required List<CartModel> carts}) async {
    if (points == null) {
      return Future.value(0);
    }
    var totalPointCollect = 0.0;
    for (var point in points) {
      bool isDayMeetReq =
          isDayMeetRequirement(weekday: point.timeActive?.dayActive);
      if (!isDayMeetReq) {
        continue;
      }

      bool isDateMeetReq = isDateMeetRequirement(
          dateStart: point.timeActive?.dateStart,
          dateEnd: point.timeActive?.dateEnd);
      if (!isDateMeetReq) {
        continue;
      }

      bool isTimeMeetReq = isTimeMeetRequirement(
          timeStart: point.timeActive?.timeStart,
          timeEnd: point.timeActive?.timeEnd);
      if (!isTimeMeetReq) {
        continue;
      }

      if ((newTransactionModel.value.sumTotalBill()) <=
          (point.minTransaction ?? 0)) {
        totalPointCollect += 0;
        continue;
      }

      if (point.pointType?.toLowerCase() == PointCollectionType.product.name) {
        for (var cart in carts) {
          var varian = await getProductByVariantId(
              (cart.product?.product_fkid ?? 0).toString(),
              cart.product?.product_detail_id ?? 0);
          for (var product in point.pointTypeProduct ?? []) {
            var isRepeat = product.repeatPoint == 1 ? true : false;
            if (product.variantId != null &&
                product.variantId == varian?.variantId) {
              if (isRepeat) {
                totalPointCollect += (product.point ?? 0) * (cart.qty ?? 1);
              }

              if (!isRepeat) {
                totalPointCollect += (product.point ?? 0) * 1;
              }
              break;
            } else if (cart.product?.product_fkid == product.productId &&
                product.variantId == null) {
              if (isRepeat) {
                totalPointCollect += (product.point ?? 0) * (cart.qty ?? 1);
              }

              if (!isRepeat) {
                totalPointCollect += (product.point ?? 0) * 1;
              }
              break;
            }
          }
        }
      }

      if (point.pointType?.toLowerCase() == PointCollectionType.nominal.name) {
        var isRepeat = point.pointTypeNominal?.repeatPoint == 1 ? true : false;
        if (isRepeat) {
          var multiple = newTransactionModel.value.sumTotalBill() /
              (point.pointTypeNominal?.transaction ?? 0);
          var pointTotal = multiple.isInfinite
              ? 0
              : (multiple.truncate()) * (point.point ?? 1);
          totalPointCollect += pointTotal;
          continue;
        }

        if (!isRepeat) {
          totalPointCollect += point.point ?? 0;
          continue;
        }
      }
    }
    return Future.value(totalPointCollect);
  }

  Future<void> applyDeal(DealData? deal) async {
    if (deal != null) {
      var result = await repoDeal.getDetailMyVoucher(
          promotionId: "${deal.promotionBuyId}");
      voucher.value = result ?? deal;
      newTransactionModel.value.setDeals(result);
    } else {
      newTransactionModel.value.setDeals(null);
    }
    await getPointCollection();
    await _repoCart.updateTransactionOrder(newTransactionModel.value);
    newTransactionModel.refresh();
  }

  List<DealData> filterPromos(List<DealData> deals) {
    List<DealData>? vouchers = deals;
    List<DealData> dealModel = <DealData>[];
    vouchers = vouchers.where((element) {
      bool canUse = false;
      canUse = (element.outlet
                  ?.where((el) => (((el.outlet_id ?? 0) == (outletId)) &&
                      isVoucherCanBeUse(element)))
                  .toList()
                  .length ??
              0) >
          0;
      return canUse;
    }).toList();
    vouchers.sort(
        (a, b) => (a.promotionFkId ?? 0).compareTo((b.promotionFkId ?? 0)));
    vouchers.asMap().forEach((index, deal) {
      if (index == 0 ||
          deal.promotionFkId != vouchers?[index - 1].promotionFkId) {
        var item = deal.copyWith(isHeader: const drift.Value(true));
        dealModel.add(item);
      }
      var item = deal.copyWith(isHeader: const drift.Value(false));
      dealModel.add(item);
    });

    dealModel.sort(
      (a, b) => (a.endPromotionDate ?? 0).compareTo(b.endPromotionDate ?? 0),
    );
    return dealModel;
  }

  Future<List<Placemark>> getAddress() async {
    return await LocationHelper.instance.getPlacemarks();
  }

  void updateCart(Cart cart) async {
    newTransactionModel.value.updateOrder(cart);
    await getPointCollection();
    await _repoCart.updateTransactionOrder(newTransactionModel.value);
    newTransactionModel.refresh();
  }

  void deleteCart(Cart cart) async {
    isLoading.value = true;
    try {
      newTransactionModel.value.removeOrder(cart);
      await getPointCollection();
      await _repoCart.updateTransactionOrder(newTransactionModel.value);
      if (newTransactionModel.value.orderList?.isEmpty == true) {
        Get.back();
      } else {
        newTransactionModel.refresh();
      }
    } catch (e, s) {
      isLoading.value = false;
      errorLogger(pos: 'CDC', error: e, stackTrace: s);
    }
  }

  Future<void> createTransaction() async {
    infoLogger('-- create transaction --');
    try {
      if (!await isOutletOpen()) {
        AppAlert.showInfoDialog(Get.context!,
            "Sorry, ${wrapperController.configApp.value.language?.outlet ?? "Outlet"} is close");
        return;
      }
      setLoadingCreateTransactionButton(true);
      String typeOrder = orderTypeString[valueIndex.value].toLowerCase();
      newTransactionModel.value.orderType = typeOrder;
      newTransactionModel.value.orderNote = noteOrderController.text;
      newTransactionModel.value.point = pointCollection.value.toInt();
      // getCartByOutletId();
      updateCartRemote(outletId: outletId);
      switch (typeOrder) {
        case 'delivery':
          {
            try {
              if (isUserLogin) {
                if (store.mainAddress == null) {
                  setLoadingCreateTransactionButton(false);
                  return Toast.show(Strings.whereOrderBeDelivered.tr,
                      type: ToastType.dark, onVisible: () async {
                    var result = await Get.toNamed(Routes.ADDRESS);
                    if (result.runtimeType == address.value.runtimeType) {
                      address.value =
                          result ?? (store.mainAddress ?? AddressModel());
                      store.mainAddress = result;
                    }
                  });
                }
              } else {
                address.value =
                    AddressModel(address: addressOrderController.text);
                store.mainAddress = address.value;
              }
              newTransactionModel.value.shipment = ShipmentModel(
                  shippingAddress: address.value.address,
                  memberAddressId: address.value.membersAddressId ?? 0);

              var result = await repoTransaction
                  .createTransaction2(newTransactionModel.value);
              if (result.status) {
                newTransactionModel.value.status = 'pending';
                addTransactionToLocal(newTransactionModel.value);
                Future.delayed(
                  const Duration(milliseconds: 200),
                  () {
                    setLoadingCreateTransactionButton(false);
                    Get.offAndToNamed(
                        Routes.TRANSACTION(result.data?.orderSalesId ?? ''),
                        arguments: result.data);
                  },
                );
              } else {
                setLoadingCreateTransactionButton(false);
              }
            } catch (e, s) {
              setLoadingCreateTransactionButton(false);
              errorLogger(pos: "CDC ~ Delivery", error: e, stackTrace: s);
            }
          }
          break;
        case 'pickup':
          {
            try {
              newTransactionModel.value.pickup =
                  PickupModel(pickupTime: timePickup);
              var result = await repoTransaction
                  .createTransaction2(newTransactionModel.value);
              if (result.status) {
                newTransactionModel.value.status = 'pending';
                addTransactionToLocal(newTransactionModel.value);
                Future.delayed(
                  const Duration(milliseconds: 200),
                  () {
                    setLoadingCreateTransactionButton(false);
                    Get.offAndToNamed(
                        Routes.TRANSACTION(result.data?.orderSalesId ?? ''),
                        arguments: result.data);
                  },
                );
              } else {
                setLoadingCreateTransactionButton(false);
              }
            } catch (e, s) {
              setLoadingCreateTransactionButton(false);
              errorLogger(pos: "CDC ~ PickUp", error: e, stackTrace: s);
            }
          }
          break;
        case 'self order':
          {
            await addTransaction();
          }
          break;
        case 'dine in':
          {
            newTransactionModel.value.orderType = 'dine_in';
            getQueryUrl();
            newTransactionModel.value.diningTable =
                dinningTableOrderController.text;
            var result = await repoTransaction
                .createTransaction2(newTransactionModel.value);
            if (result.status) {
              newTransactionModel.value.status = 'pending';
              addTransactionToLocal(newTransactionModel.value);
              Future.delayed(
                const Duration(milliseconds: 200),
                () {
                  setLoadingCreateTransactionButton(false);
                  Get.offAndToNamed(
                      Routes.TRANSACTION(result.data?.orderSalesId ?? ''),
                      arguments: result.data);
                },
              );
            } else {
              setLoadingCreateTransactionButton(false);
            }
          }
          break;
        case "internal delivery":
          {
            try {
              if (isUserLogin) {
                if (store.mainAddress == null) {
                  setLoadingCreateTransactionButton(false);

                  AppAlert.showConfirmWarningDialog(
                      context: Get.context!,
                      message: Strings.whereOrderBeDelivered.tr,
                      actions: [
                        TextButton(
                            onPressed: () {
                              Get.back();
                              chooseAdress();
                            },
                            child: Text(
                              Strings.chooseAddress,
                              style: AppFont.componentSmall
                                  .copyWith(color: AppColor.white),
                            ))
                      ]);
                  return Toast.show(Strings.whereOrderBeDelivered.tr,
                      type: ToastType.dark, onVisible: () async {
                    var result = await Get.toNamed(Routes.ADDRESS);
                    if (result.runtimeType == address.value.runtimeType) {
                      address.value =
                          result ?? (store.mainAddress ?? AddressModel());
                      store.mainAddress = result;
                    }
                  });
                }
              } else {
                address.value =
                    AddressModel(address: addressOrderController.text);
                store.mainAddress = address.value;
              }
              newTransactionModel.value.shipment!.deliveryTime = deliveryTime;
              newTransactionModel.value.orderType = "internal_delivery";

              var result = await repoTransaction
                  .createTransaction2(newTransactionModel.value);
              infoLogger('create transaction result:', result);

              if (result.status) {
                newTransactionModel.value.status = 'pending';
                addTransactionToLocal(newTransactionModel.value);
                Future.delayed(
                  const Duration(milliseconds: 200),
                  () {
                    setLoadingCreateTransactionButton(false);
                    Get.offAndToNamed(
                      Routes.TRANSACTION(result.data?.orderSalesId ?? ''),
                      arguments: result.data,
                    );
                  },
                );
              } else {
                if (result.code == 80) {
                  Toast.show('stock tidak mencukupi', type: ToastType.error);
                  AppAlert.showInfoDialog(
                      Get.context!, 'maaf, stok tidak mencukupi!');
                } else {
                  Toast.show('can not create transaction',
                      type: ToastType.error);
                }
                setLoadingCreateTransactionButton(false);
              }
            } catch (e, s) {
              Toast.show('failed creating internal delivery order',
                  type: ToastType.error);
              setLoadingCreateTransactionButton(false);
              errorLogger(
                  pos: "CDC ~ Internal Delivery", error: e, stackTrace: s);
            }
          }
          break;
      }
    } catch (e, s) {
      Sentry.captureException(e, stackTrace: s);
      setLoadingCreateTransactionButton(false);
      errorLogger(pos: "pos", error: e, stackTrace: s);
      Toast.show('failed creating order!');
    }
  }

  Future<TransactionNewModel> addTransactionToLocal(
      TransactionNewModel transactionNewModel) async {
    transactionNewModel.timeOrder =
        transactionNewModel.timeOrder ?? DateTime.now().millisecondsSinceEpoch;
    transactionNewModel.isOnOrder = true;
    await _repoCart.updateTransactionOrder(transactionNewModel,
        isOnOrder: false);
    return transactionNewModel;
  }

  Future<void> addTransaction() async {
    setLoadingCreateTransactionButton(true);
    voucher.value = DealData();
    getQueryUrl();
    newTransactionModel.value.receiptReceiver =
        receiveReceiptsOrderController.text.trim();
    newTransactionModel.value.receiptReceiver =
        dinningTableOrderController.text;
    var resp = await repoTransaction
        .createTransactionSelfOrderV2(newTransactionModel.value);
    if (resp.status) {
      addTransactionToLocal(newTransactionModel.value);
      Future.delayed(
        const Duration(milliseconds: 200),
        () {
          setLoadingCreateTransactionButton(false);
          Get.offAndToNamed(Routes.ORDER, arguments: resp.data);
        },
      );
    } else {
      setLoadingCreateTransactionButton(false);
    }
    return;
  }

  int totalVoucherGroup({List<DealData>? deals, required DealData deal}) =>
      deals
          ?.where((element) =>
              element.promotionFkId == deal.promotionFkId &&
              element.isHeader == false)
          .length ??
      0;

  int voucherComparator(DealData a, DealData b) {
    var promoid = voucher.value.promotionFkId;
    if ((a.promotionFkId == promoid) == (b.promotionFkId == promoid)) {
      return 0;
    }
    if (a.promotionFkId == promoid) {
      return -1;
    }
    return 1;
  }

  Future<bool> isOutletOpen() async {
    var outlet = await _repoOutlet.getOutletById(outletId);
    List<BusinessHourModel> workHours =
        outlet?.workingHour ?? (outlet?.business_hour ?? []);
    if (workHours.isEmpty) {
      isOpenThisOutlet.value = false;
      return false;
    }

    List<String> weekDay = [];
    for (var day in workHours) {
      weekDay.add(day.day ?? '');
    }

    var isMeetDay = isDayMeetRequirement(weekday: weekDay);

    if (!isMeetDay) {
      isOpenThisOutlet.value = false;
      return false;
    }

    for (var day in workHours) {
      if (day.day?.toLowerCase() ==
          DateFormat(DateFormat.WEEKDAY).format(DateTime.now()).toLowerCase()) {
        var result = isTimeMeetRequirement(
            timeStart: day.time_open, timeEnd: day.time_close);
        isOpenThisOutlet.value = result;
        return result;
      }
    }
    isOpenThisOutlet.value = false;
    return false;
  }

  String getShippingAddressTitle() {
    return store.mainAddress?.label == null
        ? 'Default'
        : (store.mainAddress?.label ?? '') == ''
            ? 'Default'
            : (store.mainAddress?.label ?? '');
  }

  String getShippingAddress() {
    String completeAddress =
        "${store.user?.address} ${store.user?.city} ${store.user?.province}";
    infoLogger(
        '-- address, mainAddress: ${store.mainAddress?.address} | address: ${address.value.address} ',
        store.mainAddress.toString());
    String result;

    if (store.mainAddress?.address == null) {
      if (completeAddress.contains('null')) {
        result = 'Choose Address';
      } else {
        result = completeAddress;
      }
    } else {
      result = address.value.address ?? '${store.mainAddress?.address}';
      if (address.value.address == null && store.mainAddress?.address != null) {
        address.value = AddressModel(
            address: store.mainAddress?.address,
            membersAddressId: store.mainAddress?.membersAddressId);
        infoLogger('address value use from stored', address.value);
      }
    }
    return result;
  }

  Future<void> chooseAdress() async {
    var result = await Get.toNamed(Routes.ADDRESS);
    if (result.runtimeType == address.value.runtimeType) {
      address.value = await result;
      store.mainAddress = await result;

      Map<String, dynamic> latLng = {
        "latitude": result.latitude,
        "longitude": result.longitude,
      };
      getOutletDeliveryPrice(latLng);
    }
  }
}
