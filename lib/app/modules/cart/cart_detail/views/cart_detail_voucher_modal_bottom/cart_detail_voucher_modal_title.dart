import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/app_strings.dart';

class CartDetailVoucherModalTitle extends StatelessWidget {
  const CartDetailVoucherModalTitle({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: AppDimen.h16),
      child: Text(
        Strings.availableDeals.tr,
        style: AppFont.componentMediumBold,
      ),
    );
  }
}
