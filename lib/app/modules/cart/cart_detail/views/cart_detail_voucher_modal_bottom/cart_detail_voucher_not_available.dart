import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/screen_wrapper/controller/wrapper_controller.dart';
import 'package:mobile_crm/core/theme/themes.dart';

import '../../../../../../data/providers/db/database.dart';

class CartDetailNotAvailableVoucher extends StatelessWidget {
  const CartDetailNotAvailableVoucher({Key? key, required this.snapshot})
      : super(key: key);
  final AsyncSnapshot<List<DealData>?> snapshot;

  @override
  Widget build(BuildContext context) {
    final wrapperC = Get.find<WrapperController>();
    return Obx(() {
      return Text(
          "You do not have a voucher to use at this ${wrapperC.configApp.value.language?.outlet ?? "Outlet"}",
          style: AppFont.componentSmall);
    });
  }
}
