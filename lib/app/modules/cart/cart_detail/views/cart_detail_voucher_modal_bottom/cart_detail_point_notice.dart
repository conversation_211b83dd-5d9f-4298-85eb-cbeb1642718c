import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/widget/button_widget/button_widget.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/values.dart';
import 'package:mobile_crm/data/services/analytics_service.dart';
import 'package:mobile_crm/routes/app_pages.dart';

import '../../controller/cart_detail_controller.dart';

class CartDetailPointNotice extends StatelessWidget {
  const CartDetailPointNotice({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<CartDetailController>();
    return SingleChildScrollView(
      padding: EdgeInsets.all(AppDimen.h8),
      child: Column(mainAxisSize: MainAxisSize.min, children: [
        (controller.pointCollection.value > 0 && !controller.isUserLogin)
            ? Container(
                padding: EdgeInsets.all(Constants.defaultPadding),
                decoration: const BoxDecoration(
                    color: AppColor.whiteGrey,
                    borderRadius: BorderRadius.all(Radius.circular(10)),
                    boxShadow: [
                      BoxShadow(
                          color: Color.fromARGB(50, 168, 168, 168),
                          offset: Offset(0.0, 5.0), //(x,y)
                          blurRadius: 10.0),
                    ]),
                child: Row(
                  children: [
                    const Icon(
                      Icons.info_outline_rounded,
                      color: AppColor.black90,
                    ),
                    10.0.width,
                    Flexible(
                      child: Text(
                        Strings.dontMissOpportunityToEarnValuePoint.trParams({
                          'value': controller.pointCollection.value
                              .toInt()
                              .toString()
                        }),
                        style: AppFont.componentSmall
                            .copyWith(color: AppColor.black90),
                      ),
                    )
                  ],
                ),
              )
            : const SizedBox(),
        AppDimen.h8.height,
        PrimaryButton(
          onPressed: () async {
            Get.back();
            await controller.createTransaction();
          },
          text: Strings.proceedToOrder.tr,
          width: MediaQuery.of(context).size.width,
          type: PrimaryButtonType.type5,
        ),
        AppDimen.h4.height,
        InkWell(
            onTap: () {
              AnalyticsService.instance.logEvent(
                  name: "point_notice",
                  parameters: {'type': 'bottom sheet point notice'});
              Get.toNamed(Routes.LOGIN, arguments: controller.outletId);
            },
            child: Text(Strings.orSignIn.tr,
                style: AppFont.componentSmall.copyWith(
                    color: AppColor.black70,
                    decoration: TextDecoration.underline))),
      ]),
    );
  }
}
