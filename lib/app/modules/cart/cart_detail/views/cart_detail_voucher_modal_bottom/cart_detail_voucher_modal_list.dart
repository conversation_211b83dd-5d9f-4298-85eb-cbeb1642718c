import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/widget/app_dotted_separator.dart';
import 'package:mobile_crm/app/widget/button_widget/add_order_button_widget.dart';
import 'package:mobile_crm/app/widget/button_widget/button_widget.dart';
import 'package:mobile_crm/app/widget/cached_image_widget.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/values.dart';
import 'package:mobile_crm/data/models/deal_model.dart';
import 'package:mobile_crm/routes/app_pages.dart';

import '../../../../../../data/providers/db/database.dart';
import '../../controller/cart_detail_controller.dart';

class CartDetailVoucherModalList extends StatelessWidget {
  const CartDetailVoucherModalList({Key? key, required this.snapshot})
      : super(key: key);
  final AsyncSnapshot<List<DealData>?> snapshot;

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<CartDetailController>();
    return Flexible(
      child: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: snapshot.data
                  ?.where((element) => element.isHeader == true)
                  .length,
              itemBuilder: (context, index) {
                List<DealData> vouchers = snapshot.data!
                    .where((element) => element.isHeader == true)
                    .toList();

                vouchers.sort((a, b) => controller.voucherComparator(a, b));

                DealData voucher = vouchers[index];
                int totalVoucher = controller.totalVoucherGroup(
                    deals: snapshot.data, deal: voucher);
                return InkWell(
                  onTap: () async {
                    var result = await Get.toNamed(
                        Routes.VOUCHERDETAIL(voucher.promotionBuyId.toString()),
                        arguments: voucher);
                    if (result.runtimeType == DealData().runtimeType) {
                      controller.voucher.value = result;
                    }
                  },
                  child: Stack(
                    children: [
                      Container(
                        margin: EdgeInsets.only(
                            left: AppDimen.h16,
                            right: AppDimen.h16,
                            bottom: AppDimen.h10),
                        decoration: BoxDecoration(
                            color: controller.isVoucherCanBeUse(voucher)
                                ? AppColor.white
                                : AppColor.disable,
                            boxShadow: const [
                              BoxShadow(
                                  color: AppColor.black10,
                                  blurRadius: 1,
                                  offset: Offset(0, 0),
                                  spreadRadius: 1)
                            ],
                            borderRadius:
                                const BorderRadius.all(Radius.circular(10))),
                        child: Column(
                          children: [
                            Padding(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 8.0, vertical: 8),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.start,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Container(
                                    decoration: const BoxDecoration(boxShadow: [
                                      BoxShadow(
                                          color: AppColor.black10,
                                          blurRadius: 5,
                                          offset: Offset(
                                            0,
                                            1,
                                          ),
                                          spreadRadius: 2)
                                    ]),
                                    child: ClipRRect(
                                      borderRadius: BorderRadius.circular(10),
                                      child: CachedImageWidget(
                                        fit: BoxFit.cover,
                                        imageUrl: voucher.photo ?? "",
                                        width: (voucher.photo == null ||
                                                voucher.photo?.isEmpty == true)
                                            ? AppDimen.h128
                                            : null,
                                        height: 72,
                                      ),
                                    ),
                                  ),
                                  5.0.width,
                                  Flexible(
                                    child: Column(
                                      mainAxisSize: MainAxisSize.min,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      mainAxisAlignment:
                                          MainAxisAlignment.start,
                                      children: [
                                        Flexible(
                                            child: Text(
                                          voucher.name
                                                  .toString()
                                                  .capitalizeFirst ??
                                              '',
                                          style: AppFont.componentSmallBold,
                                          softWrap: false,
                                          overflow: TextOverflow.fade,
                                          maxLines: 1,
                                        )),
                                        TermItem(
                                          voucher: voucher,
                                          child: voucher.minOrder == 0
                                              ? Text(
                                                  "No Minimum Order",
                                                  style: AppFont.paragraphSmall,
                                                )
                                              : Text(
                                                  "${Strings.minimumOrder.tr} ${voucher.minOrder.toCurrency}",
                                                  style: AppFont.paragraphSmall,
                                                ),
                                        ),
                                        TermItem(
                                          voucher: voucher,
                                          child: voucher.promoType ==
                                                  DealPromoType.discount.name
                                              ? voucher.promoDiscountType ==
                                                      DealDiscountType
                                                          .percent.name
                                                  ? Row(
                                                      children: [
                                                        Text(
                                                          "${voucher.promoType.toString().replaceAll('_', ' ').capitalizeFirst} ${voucher.promoNominal}%",
                                                          style: AppFont
                                                              .paragraphSmall,
                                                        ),
                                                        voucher.promoDiscountMaximum ==
                                                                null
                                                            ? Text(
                                                                "",
                                                                style: AppFont
                                                                    .paragraphSmall,
                                                              )
                                                            : Text(
                                                                "(Maks. ${voucher.promoDiscountMaximum.toCurrency})",
                                                                style: AppFont
                                                                    .paragraphSmall,
                                                              )
                                                      ],
                                                    )
                                                  : Text(
                                                      "${voucher.promoType.toString().replaceAll('_', ' ').capitalizeFirst} ${voucher.promoNominal.toCurrency}",
                                                      style: AppFont
                                                          .paragraphSmall,
                                                    )
                                              : Text(
                                                  "${voucher.promoType.toString().replaceAll('_', ' ').capitalizeFirst}",
                                                  style: AppFont.paragraphSmall,
                                                ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            const DottedDivider(
                                width: 3, height: 2, color: AppColor.black10),
                            Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        Strings.availableUntil.tr,
                                        style: AppFont.paragraphSmall,
                                      ),
                                      Row(
                                        children: [
                                          Icon(
                                            Icons.access_time_filled_rounded,
                                            color: Colors.grey,
                                            size: Constants.iconSizeSmall(
                                                context),
                                          ),
                                          5.0.width,
                                          Text(
                                            voucher.endPromotionDate
                                                    ?.toDateAndTime ??
                                                '',
                                            style: AppFont.paragraphSmall,
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                  controller.isVoucherCanBeUse(voucher)
                                      ? Obx(() => controller.voucher.value
                                                  .promotionFkId ==
                                              voucher.promotionFkId
                                          ? SecondaryButton(
                                              onPressed: () {
                                                controller.voucher.value =
                                                    DealData();
                                                controller.applyDeal(null);
                                                Get.back();
                                              },
                                              text: 'Remove',
                                              type: SecondaryButtonType.type4,
                                              child: Text("Remove",
                                                  style:
                                                      AppFont.componentSmall),
                                            )
                                          : AddOrderButtonWidget(
                                              onTap: () {
                                                Get.back();
                                                controller.voucher.value =
                                                    voucher;
                                                controller.applyDeal(voucher);
                                              },
                                              text: Strings.useDeals.tr))
                                      : Text(
                                          Strings.notAvailable.tr,
                                          style: AppFont.componentSmall,
                                        ),
                                ],
                              ),
                            )
                          ],
                        ),
                      ),
                      totalVoucher == 1
                          ? const SizedBox()
                          : Align(
                              alignment: Alignment.topRight,
                              child: Container(
                                margin: EdgeInsets.only(right: AppDimen.h16),
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 12.0),
                                decoration: const BoxDecoration(
                                    borderRadius: BorderRadius.only(
                                        topRight: Radius.circular(10),
                                        bottomLeft: Radius.circular(10)),
                                    color: AppColor.black90),
                                child: Text(
                                  "$totalVoucher x",
                                  style: AppFont.componentSmallBold
                                      .copyWith(color: AppColor.white),
                                ),
                              ))
                    ],
                  ),
                );
              },
            )
          ],
        ),
      ),
    );
  }
}

class TermItem extends StatelessWidget {
  const TermItem({
    super.key,
    required this.voucher,
    required this.child,
  });

  final DealData voucher;
  final Widget child;

  @override
  Widget build(BuildContext context) {
    return Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          const Icon(
            Icons.circle,
            size: 6,
            color: AppColor.black70,
          ),
          5.0.width,
          child
        ]);
  }
}
