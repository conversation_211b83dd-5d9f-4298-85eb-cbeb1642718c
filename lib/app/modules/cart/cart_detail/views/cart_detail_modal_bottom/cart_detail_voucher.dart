import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/cart/cart_detail/cart_detail_export.dart';
import 'package:mobile_crm/app/widget/custom_circular_progress_widget.dart';
import 'package:mobile_crm/core/theme/app_color.dart';

import '../cart_detail_voucher_modal_bottom/cart_detail_voucher_modal_list.dart';
import '../cart_detail_voucher_modal_bottom/cart_detail_voucher_modal_title.dart';

class CartDetailVoucherBottomSheet extends GetView<CartDetailController> {
  const CartDetailVoucherBottomSheet({super.key});
  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
      future: controller.getVoucher(),
      builder: (context, snapshot) {
        if (snapshot.hasData) {
          return Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const CartDetailVoucherModalTitle(),
              const Divider(
                color: AppColor.black5,
              ),
              CartDetailVoucherModalList(snapshot: snapshot)
            ],
          );
        }
        return const CustomCircularProgressIndicator(
          valueColor: AppColor.black90,
        );
      },
    );
  }
}
