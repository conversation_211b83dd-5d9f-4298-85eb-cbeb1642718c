import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/cart/cart_export.dart';
// import 'package:mobile_crm/app/utils/logger.dart';
import 'package:mobile_crm/app/widget/button_widget/button_widget.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/theme/app_color.dart';
import 'package:mobile_crm/core/theme/app_dimen.dart';
import 'package:mobile_crm/core/theme/app_font.dart';
import 'package:mobile_crm/core/values/app_strings.dart';

import '../../../../../../data/providers/db/database.dart';
import '../../../../screen_wrapper/controller/wrapper_controller.dart';

class CartDetailOrderType extends GetView<CartDetailController> {
  const CartDetailOrderType({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final wrapperController = Get.find<WrapperController>();
    var chooseType = controller.valueIndex.value.obs;
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Padding(
          padding: EdgeInsets.only(
              left: AppDimen.h16, right: AppDimen.h16, bottom: AppDimen.h8),
          child: Text(
            Strings.orderType.tr,
            style: AppFont.componentSmallBold,
          ),
        ),
        ListView.builder(
          physics: const NeverScrollableScrollPhysics(),
          shrinkWrap: true,
          itemCount: controller.orderTypeString.length,
          itemBuilder: (context, index) {
            var type = controller.orderTypeString[index];
            return InkWell(
              onTap: () {
                chooseType.value = index;
              },
              child: Container(
                width: MediaQuery.of(context).size.width,
                padding: EdgeInsets.symmetric(horizontal: AppDimen.h16),
                decoration: const BoxDecoration(color: AppColor.white),
                child: Column(
                  children: [
                    index == 0 ? AppDimen.h8.height : AppDimen.h4.height,
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              type,
                              style: AppFont.componentSmallBold,
                            ),
                            Text(
                              type.toLowerCase() == 'pickup'
                                  ? Strings.orderPickupDesc.tr
                                  : type.toLowerCase() == 'self order'
                                      ? Strings.orderSelfOrderDesc.tr
                                      : type.toLowerCase() == 'dine in'
                                          ? Strings.orderDineInDesc.tr
                                          : Strings.orderDeliveryDesc,
                              style: AppFont.componentSmall.copyWith(
                                  fontSize: 11.sp, color: AppColor.black70),
                            ),
                          ],
                        ),
                        Obx(() {
                          return Visibility(
                              visible: chooseType.value == index,
                              child: Icon(Icons.done,
                                      color:
                                          wrapperController.getPrimaryColor())
                                  .fadeIn(
                                      duration:
                                          const Duration(milliseconds: 200)));
                        })
                      ],
                    ),
                    AppDimen.h4.height,
                    Divider(
                      color: index == (controller.orderTypeString.length - 1)
                          ? AppColor.white
                          : AppColor.black5,
                    ),
                  ],
                ),
              ),
            ).fadeIn(duration: Duration(milliseconds: 200 + (200 * index)));
          },
        ),
        AppDimen.h10.height,
        Padding(
          padding: EdgeInsets.symmetric(horizontal: AppDimen.h16),
          child: PrimaryButton(
            onPressed: () {
              controller.handleSelectOrderType(chooseType.value);
              String current =
                  controller.orderTypeString[chooseType.value].toLowerCase();
              if (current == 'self order' || current == 'dine in') {
                controller.voucher.value = DealData();
                controller.applyDeal(null);
              }
              if (current != "internal delivery") {
                controller.resetShipment();
              }
              controller.valueIndex.value = chooseType.value;
              var mainAddress = controller.store.mainAddress;
              // infoLogger('main address: ${mainAddress?.address}');
              if (mainAddress != null && mainAddress.latitude != null) {
                Map<String, dynamic> latLng = {
                  "latitude": mainAddress.latitude!,
                  "longitude": mainAddress.longitude!,
                };
                if (current == "internal delivery") {
                  controller.getOutletDeliveryPrice(latLng);
                }
              }
              Get.back();
            },
            type: PrimaryButtonType.type5,
            text: Strings.save.tr,
            width: MediaQuery.of(context).size.width,
            borderRadius: BorderRadius.all(Radius.circular(AppDimen.h6)),
          ),
        ),
        AppDimen.h10.height
      ],
    );
  }
}
