// ignore_for_file: invalid_use_of_protected_member

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/enum/order_type_enum.dart';
import 'package:mobile_crm/app/modules/cart/cart_detail/views/cart_detail_voucher_modal_bottom/cart_detail_point_notice.dart';
import 'package:mobile_crm/app/modules/screen_wrapper/controller/wrapper_controller.dart';
import 'package:mobile_crm/app/modules/screen_wrapper/views/app_page_wrapper.dart';
import 'package:mobile_crm/app/utils/app_alert.dart';
import 'package:mobile_crm/app/utils/logger.dart';
import 'package:mobile_crm/app/utils/utils.dart';
import 'package:mobile_crm/app/widget/app_bar_widget/app_bar_widget.dart';
import 'package:mobile_crm/app/widget/button_widget/button_widget.dart';
import 'package:mobile_crm/app/widget/custom_circular_progress_widget.dart';
import 'package:mobile_crm/core/extensions/double_extensions.dart';
import 'package:mobile_crm/core/extensions/int_extensions.dart';
import 'package:mobile_crm/core/extensions/string_extensions.dart';
import 'package:mobile_crm/core/extensions/widget_extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/values.dart';
import 'package:mobile_crm/routes/app_pages.dart';

import '../../../../helper/deal_helper.dart';
import '../../../../widget/full_screen_dialog/active_transaction/active_transaction_fdialog.dart';
import '../controller/cart_detail_controller.dart';
import 'cart_detail_modal_bottom/cart_detail_voucher.dart';
import 'widget/cart_detail_content.dart';

class CartDetailScreen extends GetView<CartDetailController> {
  const CartDetailScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final wrapperC = Get.find<WrapperController>();
    return PageWrapper(
      resizeToAvoidBottomInset: true,
      bottomNavigationBar: Container(
        constraints: BoxConstraints(maxWidth: Constants.defaultMaxWidth),
        decoration: const BoxDecoration(
          color: AppColor.white,
          boxShadow: <BoxShadow>[
            BoxShadow(color: AppColor.black10, blurRadius: 3),
          ],
        ),
        child: SafeArea(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Obx(() {
                infoLogger('size orderTypes ',
                    controller.orderTypeString.value.length);
                final orderTypes = controller.orderTypeString.value;
                if (orderTypes.isEmpty) {
                  orderTypes.add("self order");
                }
                return Visibility(
                  visible: (orderTypes[controller.valueIndex.value]
                              .toLowerCase() ==
                          'pickup') ||
                      (orderTypes[controller.valueIndex.value].toLowerCase() ==
                          'delivery'),
                  child: InkWell(
                    splashFactory: NoSplash.splashFactory,
                    onTap: () {
                      const CartDetailVoucherBottomSheet()
                          .toModalBottomSheet
                          .of(context);
                    },
                    child: Column(
                      children: [
                        Container(
                          color: AppColor.white,
                          padding: EdgeInsets.only(
                              left: AppDimen.h16,
                              right: AppDimen.h16,
                              top: AppDimen.h6,
                              bottom: AppDimen.h2),
                          width: MediaQuery.of(context).size.width,
                          alignment: Alignment.center,
                          child: Obx(() {
                            return controller.voucher.value.name != null
                                ? Row(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: [
                                      Icon(
                                        Icons.discount,
                                        color: AppColor.black90,
                                        size: AppDimen.h14,
                                      ),
                                      AppDimen.h10.width,
                                      Flexible(
                                        child: Text(
                                          controller.voucher.value.name ?? '-',
                                          style: AppFont.componentSmallBold,
                                        ),
                                      ),
                                      AppDimen.h4.width,
                                      Flexible(
                                          child: Text(
                                        controller.voucher.value.term ?? '-',
                                        style: AppFont.componentSmall.copyWith(
                                            fontSize: 11.sp,
                                            color: AppColor.black70),
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                      )),
                                      Obx(() {
                                        return Visibility(
                                          visible: (!DealHelper
                                                  .isVoucherCanBeUserBasedOnDate(
                                                      controller
                                                          .voucher.value)) ||
                                              (controller
                                                      .newTransactionModel.value
                                                      .sumPrice() <
                                                  (controller.voucher.value
                                                          .minOrder ??
                                                      0)),
                                          child: Row(
                                            children: [
                                              AppDimen.h10.width,
                                              Icon(
                                                Icons.info_outline_rounded,
                                                color: AppColor.utilityDanger,
                                                size: AppDimen.h14,
                                              ),
                                            ],
                                          ).fadeIn(),
                                        );
                                      }),
                                    ],
                                  )
                                : Text(
                                    'ADD DEAL',
                                    style: AppFont.componentSmallBold.copyWith(
                                        color: wrapperC.getPrimaryColor()),
                                  );
                          }),
                        ),
                        const Divider(
                          color: AppColor.black5,
                        ),
                      ],
                    ),
                  ),
                );
              }),
              Container(
                color: AppColor.white,
                padding: EdgeInsets.symmetric(
                    horizontal: AppDimen.h16, vertical: AppDimen.h8),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Flexible(
                        child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text("Total", style: AppFont.paragraphSmall),
                        Obx(
                          () => Text(
                              "Rp ${controller.newTransactionModel.value.sumTotalBill().toCurrency}",
                              style: AppFont.paragraphSmallBold),
                        )
                      ],
                    )),
                    Flexible(
                      flex: 2,
                      child: Obx(
                        () => !controller.isOpenThisOutlet.value
                            ? DisableButton(
                                onPressed: () {
                                  AppAlert.showInfoDialog(
                                      context,
                                      Strings.sorryTheValueIsClosed.trParams({
                                        'value': wrapperC.configApp.value
                                                .language?.outlet ??
                                            'Outlet'
                                      }));
                                },
                                width: AppDimen.h64 + AppDimen.h20,
                                text: Strings.close.tr)
                            : (!controller.isUserLogin &&
                                    controller.orderTypeString[
                                                controller.valueIndex.value]
                                            .toLowerCase() !=
                                        "self order")
                                ? PrimaryButton(
                                    onPressed: () {
                                      Get.toNamed(Routes.LOGIN,
                                          arguments: controller.outletId);
                                    },
                                    text: Strings.login.tr,
                                    type: PrimaryButtonType.type4,
                                    child: Text(
                                      Strings.login.tr,
                                      style: AppFont.paragraphSmallBold
                                          .copyWith(color: AppColor.white),
                                    ),
                                  )
                                : PrimaryButton(
                                    onPressed: controller
                                            .isCreateTransactionLoading.value
                                        ? () {}
                                        : () async {
                                            if (!controller
                                                .isOpenThisOutlet.value) {
                                              return AppAlert.showInfoDialog(
                                                  context,
                                                  Strings.sorryTheValueIsClosed
                                                      .trParams({
                                                    'value': wrapperC
                                                            .configApp
                                                            .value
                                                            .language
                                                            ?.getOutletLanguage ??
                                                        "Outlet"
                                                  }));
                                            }
                                            if (controller.orderTypeString
                                                .contains(
                                                    controller.orderTypeString[
                                                        controller.valueIndex
                                                            .value])) {
                                              if (controller.pointCollection
                                                          .value >
                                                      0 &&
                                                  !controller.isUserLogin &&
                                                  !controller
                                                      .getFirebaseRemoteABPoint()) {
                                                const CartDetailPointNotice()
                                                    .toModalBottomSheetNoMinHeight
                                                    .of(context);
                                                return;
                                              }
                                              await controller
                                                  .createTransaction();
                                            }
                                          },
                                    text: "",
                                    type: PrimaryButtonType.type4,
                                    child: Obx(() {
                                      String text = "";
                                      if (controller
                                                  .orderTypeString[controller
                                                      .valueIndex.value]
                                                  .removeUnderscore
                                                  .toLowerCase() ==
                                              OrderTypeEnum
                                                  .dine_in.name.removeUnderscore
                                                  .toLowerCase() ||
                                          controller
                                                  .orderTypeString[controller
                                                      .valueIndex.value]
                                                  .removeUnderscore
                                                  .toLowerCase() ==
                                              OrderTypeEnum
                                                  .pickup.name.removeUnderscore
                                                  .toLowerCase() ||
                                          controller
                                                  .orderTypeString[controller
                                                      .valueIndex.value]
                                                  .removeUnderscore
                                                  .toLowerCase() ==
                                              OrderTypeEnum.self_order.name
                                                  .removeUnderscore
                                                  .toLowerCase()) {
                                        text = Strings.pickup.tr;
                                      } else {
                                        text = Strings.delivery.tr;
                                      }
                                      return controller.isOpenThisOutlet.value
                                          ? controller
                                                  .isCreateTransactionLoading
                                                  .value
                                              ? SizedBox(
                                                  width: 24.h,
                                                  child:
                                                      const CustomCircularProgressIndicator())
                                              : Text(
                                                  text,
                                                  style: AppFont
                                                      .paragraphSmallBold
                                                      .copyWith(
                                                          color:
                                                              AppColor.white),
                                                )
                                          : Text(
                                              Strings.close,
                                              style: AppFont.paragraphSmallBold
                                                  .copyWith(
                                                      color: AppColor.white),
                                            );
                                    }),
                                  ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
      onWillPop: () {
        if (Get.previousRoute.contains('outlet') ||
            Get.previousRoute.contains(Routes.CART)) {
          Get.back(result: true);
        } else {
          Get.offAllNamed(Routes.HOME);
        }
        return Future.value(true);
      },
      child: Scaffold(
        appBar: AppBarWidget(
          title: "Cart Detail",
          leading: IconButton(
              onPressed: () {
                if (Get.previousRoute.contains('outlet') ||
                    Get.previousRoute.contains(Routes.CART)) {
                  Get.back();
                } else {
                  Get.offAllNamed(Routes.HOME);
                }
              },
              icon: Icon(
                Icons.arrow_back,
                size: Constants.iconSize(context),
              )),
          actions: [
            IconButton(
              onPressed: () {
                ActiveTransactionFullScreenDialog().show(
                  context: context,
                  transRepo: controller.repoTransaction,
                  dealRepo: controller.repoDeal,
                );
              },
              icon: Obx(
                () => Badge(
                  isLabelVisible: controller.listTransaction.value.isNotEmpty,
                  label: Text(convertNumber(controller.listTransaction.length)),
                  child: Icon(
                    Icons.receipt_long_outlined,
                    size: Constants.iconSize(context),
                  ),
                ),
              ),
            ),
          ],
        ),
        backgroundColor: Colors.transparent,
        resizeToAvoidBottomInset: true,
        body: const CartDetailContent(),
      ),
    );
  }
}
