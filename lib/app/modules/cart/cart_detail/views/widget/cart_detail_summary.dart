import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/cart/cart_export.dart';
import 'package:mobile_crm/app/widget/shimmer_loading_widget.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/theme/app_color.dart';
import 'package:mobile_crm/core/theme/app_dimen.dart';
import 'package:mobile_crm/core/theme/app_font.dart';
import 'package:mobile_crm/core/values/values.dart';

import '../../../../../../data/models/cart_model.dart';
import '../../../../../../data/models/tax_model.dart';
import '../../../../../enum/order_type_enum.dart';
import '../../../../../widget/app_dotted_separator.dart';
import '../../../../screen_wrapper/controller/wrapper_controller.dart';
import 'product_item_cart_widget.dart';

class CartDetailSummary extends GetView<CartDetailController> {
  const CartDetailSummary({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final wrapperController = Get.find<WrapperController>();
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          color: AppColor.white,
          padding: EdgeInsets.symmetric(
              horizontal: Constants.defaultPadding2, vertical: AppDimen.h8),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Obx(() {
                return Text(
                  Strings.outletName.trParams({
                    "outlet":
                        wrapperController.configApp.value.language?.outlet ??
                            "Outlet"
                  }),
                  style: AppFont.paragraphMediumBold,
                );
              }),
              Obx(() {
                return Text(
                    controller.newTransactionModel.value.outlet?.name ?? '',
                    style: AppFont.paragraphSmallBold);
              }),
              AppDimen.h4.height,
              const DottedDivider(color: AppColor.black5, width: 5, height: 2),
              AppDimen.h8.height,
              MediaQuery.removePadding(
                removeTop: true,
                context: context,
                child: Obx(
                  () => ListView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: controller
                            .newTransactionModel.value.orderList?.length ??
                        0,
                    itemBuilder: (context, indexChild) {
                      CartModel cart = controller
                          .newTransactionModel.value.orderList![indexChild];
                      return ProductItemCartWidget(
                        cart: cart,
                      );
                    },
                  ),
                ),
              ),
              Column(
                children: [
                  FutureBuilder(
                    future: controller.getOutletFeature(),
                    builder: (context, snapshot) {
                      if (snapshot.hasData) {
                        if (snapshot.data?.tableInput == true) {
                          return Obx(() {
                            return Visibility(
                              visible: (controller
                                          .orderTypeString[
                                              controller.valueIndex.value]
                                          .removeUnderscore
                                          .toLowerCase() ==
                                      OrderTypeEnum
                                          .self_order.name.removeUnderscore
                                          .toLowerCase() ||
                                  controller
                                          .orderTypeString[
                                              controller.valueIndex.value]
                                          .removeUnderscore
                                          .toLowerCase() ==
                                      OrderTypeEnum
                                          .dine_in.name.removeUnderscore
                                          .toLowerCase()),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.start,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    Strings.tableNumber.tr,
                                    style: AppFont.componentSmallBold,
                                  ),
                                  Row(
                                    children: [
                                      const Icon(
                                        Icons.table_restaurant,
                                        color: AppColor.black90,
                                      ),
                                      AppDimen.h8.width,
                                      Flexible(
                                        child: TextFormField(
                                            key: key,
                                            keyboardType: TextInputType.text,
                                            controller: controller
                                                .dinningTableOrderController,
                                            style: AppFont.componentSmall,
                                            minLines: 1,
                                            autofocus: false,
                                            decoration: InputDecoration(
                                                hintText:
                                                    Strings.exTableNumber8.tr,
                                                border: InputBorder.none)),
                                      ),
                                    ],
                                  ),
                                  const DottedDivider(
                                    width: 3,
                                    height: 2,
                                    color: AppColor.black5,
                                  )
                                ],
                              ).fadeIn(),
                            );
                          });
                        } else {
                          return const SizedBox();
                        }
                      }
                      return const SizedBox();
                    },
                  ),
                  Row(
                    children: [
                      const Icon(
                        Icons.description,
                        color: AppColor.black90,
                      ),
                      AppDimen.h8.width,
                      Flexible(
                        child: TextFormField(
                          key: key,
                          keyboardType: TextInputType.text,
                          controller: controller.noteOrderController,
                          style: AppFont.componentSmall,
                          minLines: 1,
                          autofocus: false,
                          decoration: InputDecoration(
                            hintText: Strings.exampleNoteOrder2.tr,
                            border: InputBorder.none,
                          ),
                        ),
                      ),
                    ],
                  ),
                  AppDimen.h4.height,
                  const Divider(
                    color: AppColor.black5,
                  ),
                ],
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(Strings.productQuantity.tr,
                      style: AppFont.componentSmall),
                  Obx(() {
                    return Text(
                      "${controller.newTransactionModel.value.sumQTY()} Items",
                      style: AppFont.componentSmallBold,
                    );
                  })
                ],
              ),
              AppDimen.h6.height,
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(Strings.productPrice.tr, style: AppFont.componentSmall),
                  Obx(() {
                    return Text(
                        "Rp${controller.newTransactionModel.value.sumPrice().toCurrency}",
                        style: AppFont.componentSmallBold);
                  })
                ],
              ),
              Obx(() {
                if (controller.orderTypeString[controller.valueIndex.value]
                        .toLowerCase() ==
                    "internal delivery") {
                  if (!controller.isDeliveryPriceLoading.value) {
                    if (controller.outletDeliveryPrice.value != null) {
                      return Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          RichText(
                            text: TextSpan(
                              text: Strings.deliveryPrice.tr,
                              style: AppFont.componentSmall,
                              children: [
                                TextSpan(
                                  text:
                                      " . ${controller.outletDeliveryPrice.value!.distance!.distance!.toStringAsFixed(1)} ${controller.outletDeliveryPrice.value!.distance!.unit}",
                                  style: AppFont.componentSmall.copyWith(
                                    color: Colors.grey,
                                  ),
                                )
                              ],
                            ),
                          ),
                          Text(
                            "Rp. ${controller.outletDeliveryPrice.value!.price!.toCurrency}",
                            style: AppFont.componentSmall,
                          )
                        ],
                      );
                    }
                  } else {
                    return Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text("Delivery price", style: AppFont.componentSmall),
                        const ShimmerWidget(
                          width: 80,
                          height: 12,
                          radius: 5,
                        ),
                      ],
                    );
                  }
                }
                return const SizedBox();
              }),
              AppDimen.h6.height,
              Obx(() {
                return Visibility(
                  visible: (controller
                          .orderTypeString[controller.valueIndex.value]
                          .removeUnderscore
                          .toLowerCase() ==
                      OrderTypeEnum.self_order.name.removeUnderscore
                          .toLowerCase()),
                  child: Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            Strings.rounding.tr,
                            style: AppFont.componentSmall
                                .copyWith(color: AppColor.couponDiscount),
                          ),
                          Obx(() {
                            return Text(
                              "Rp${(controller.newTransactionModel.value.sumTotalBill() - (controller.newTransactionModel.value.sumPrice() + controller.newTransactionModel.value.sumTax())).toCurrency}",
                              style: AppFont.componentSmall
                                  .copyWith(color: AppColor.couponDiscount),
                            );
                          })
                        ],
                      ),
                      AppDimen.h6.height,
                    ],
                  ),
                );
              }),
              Obx(() {
                return Visibility(
                  visible: controller.newTransactionModel.value.sumTax() != 0,
                  child: Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(Strings.totalTaxes.tr,
                              style: AppFont.componentSmall),
                          Text(
                              "Rp${controller.newTransactionModel.value.sumTax().toCurrency}",
                              style: AppFont.componentSmallBold)
                        ],
                      ),
                      MediaQuery.removePadding(
                        removeTop: true,
                        context: context,
                        child: Obx(
                          () => ListView.builder(
                            itemCount: controller.newTransactionModel.value
                                .getTaxDetail()
                                .length,
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                            itemBuilder: (context, index) {
                              TaxModel tax = controller
                                  .newTransactionModel.value
                                  .getTaxDetail()[index];
                              return Visibility(
                                visible: tax.currentTax != 0,
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text("  - ${tax.name}",
                                        style: AppFont.componentSmall
                                            .copyWith(fontSize: 11.sp)),
                                    Text("Rp${tax.totalTax.toCurrency}",
                                        style: AppFont.componentSmall
                                            .copyWith(fontSize: 11.sp)),
                                  ],
                                ),
                              );
                            },
                          ),
                        ),
                      ),
                      AppDimen.h6.height,
                    ],
                  ),
                );
              }),
              Obx(() {
                return Visibility(
                  visible:
                      controller.newTransactionModel.value.sumTotalDiscount() !=
                          0,
                  child: Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(Strings.discount.tr,
                              style: AppFont.componentSmall),
                          Text(
                              "Rp${controller.newTransactionModel.value.sumTotalDiscount().toCurrency}",
                              style: AppFont.componentSmallBold)
                        ],
                      ).fadeIn(),
                      AppDimen.h6.height,
                    ],
                  ),
                );
              }),
              Obx(() {
                return Visibility(
                  visible: controller.newTransactionModel.value.point != null
                      ? controller.newTransactionModel.value.point != 0
                      : false,
                  child: Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                              Strings.valueEarned.trParams({
                                'value': wrapperController.getPointLanguage()
                              }),
                              style: AppFont.componentSmall),
                          Container(
                            padding: const EdgeInsets.only(left: 8, right: 2),
                            decoration: BoxDecoration(
                                color: Colors.red,
                                borderRadius: BorderRadius.circular(15)),
                            child: Row(
                              children: [
                                Text(
                                    "+${controller.newTransactionModel.value.point?.toInt().toCurrency}",
                                    style: AppFont.componentSmall
                                        .copyWith(color: AppColor.white)),
                                Icon(Icons.local_fire_department,
                                    color: Colors.orangeAccent,
                                    size: Constants.iconSize(context))
                              ],
                            ),
                          ),
                        ],
                      ),
                      AppDimen.h6.height,
                    ],
                  ),
                );
              }),
              const DottedDivider(
                width: 5,
                height: 2,
                color: AppColor.black10,
              ),
              AppDimen.h4.height,
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    Strings.totalBill.tr,
                    style: AppFont.componentMediumBold,
                  ),
                  Obx(() {
                    return Text(
                        "Rp${controller.newTransactionModel.value.sumTotalBill().toCurrency}",
                        style: AppFont.componentMediumBold);
                  })
                ],
              ),
            ],
          ),
        ),
        Divider(
          color: AppColor.black5,
          height: AppDimen.h6,
          thickness: AppDimen.h6,
        ),
      ],
    );
  }
}
