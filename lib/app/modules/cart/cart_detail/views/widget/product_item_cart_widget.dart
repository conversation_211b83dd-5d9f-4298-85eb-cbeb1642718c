import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/cart/cart_detail/controller/cart_detail_controller.dart';
import 'package:mobile_crm/app/widget/button_widget/minus_plus_button_widget.dart';
import 'package:mobile_crm/app/widget/product_item/product_photo_widget.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/theme/app_dimen.dart';
import 'package:mobile_crm/core/theme/app_font.dart';
import 'package:mobile_crm/core/values/app_strings.dart';

import '../../../../../../core/theme/app_color.dart';
import '../../../../../../data/models/cart_model.dart';
import '../../../../../../data/providers/db/database.dart';
import '../../../../../utils/app_alert.dart';
import '../../../../../widget/button_widget/note_text_button_widget.dart';
import '../../../../../widget/modal_bottom_sheet/app_modal_bottom_sheet.dart';
import '../../../../outlet/views/widget/outlet_modal_bottom_sheet/outlet_unit_conversion.dart';

class ProductItemCartWidget extends GetView<CartDetailController> {
  const ProductItemCartWidget({super.key, required this.cart});

  final CartModel cart;
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          mainAxisSize: MainAxisSize.max,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ProductPhotoWidget(
                  imageUrl: cart.product?.photo,
                  size: Size.square(AppDimen.h40),
                ),
                AppDimen.h8.width,
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      cart.product?.name ?? '',
                      style:
                          AppFont.componentSmallBold.copyWith(fontSize: 11.sp),
                    ),
                    AppDimen.h6.height,
                    MinusPlusButtonWidget(
                        textEditingController:
                            TextEditingController(text: '${cart.qty ?? 0}'),
                        onTapMinus: () {
                          if (cart.qty != null && (cart.qty ?? 0) > 1) {
                            cart.qty = (cart.qty ?? 0) - 1;
                            controller.updateCart(cart);
                          } else {
                            AppAlert.showConfirmDeleteDialog(
                              context,
                              "${cart.product?.name}",
                              () {
                                controller.deleteCart(cart);
                                Get.back();
                              },
                            );
                          }
                        },
                        onChanged: (value) {
                          if (int.tryParse(value) != null) {
                            int convert = int.tryParse(value) ?? 1;
                            if (cart.qty != null && convert > 0) {
                              if ((cart.qty ?? 0) != convert) {
                                cart.qty = convert;
                                controller.updateCart(cart);
                              }
                            } else {
                              AppAlert.showConfirmDeleteDialog(
                                context,
                                "${cart.product?.name}",
                                () {
                                  controller.deleteCart(cart);
                                  Get.back();
                                },
                              );
                            }
                          }
                        },
                        onTapPlus: () {
                          if (cart.qty != null) {
                            cart.qty = (cart.qty ?? 0) + 1;
                            controller.updateCart(cart);
                          }
                        },
                        listUnit: controller.listUnitConversion
                            .where((element) =>
                                element.productFkId == cart.product_fkid)
                            .toList(),
                        showTools: true,
                        onTapTools: () {
                          OutletUnitConversion(
                            teController: TextEditingController(),
                            product: Product(
                                unit: cart.product?.unit,
                                price: cart.product?.price,
                                priceSell: cart.product?.priceSell),
                            conversion: (value) {
                              cart.qty = value;
                              controller.updateCart(cart);
                              Get.back();
                            },
                            listUnit: controller.listUnitConversion
                                .where((element) =>
                                    element.productFkId == cart.product_fkid)
                                .toList(),
                          ).toModalBottomSheetNoMinHeight.of(context);
                        },
                        middleText: "${cart.qty}")
                  ],
                ),
              ],
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                ((cart.product?.priceSellPromo == null ||
                            cart.product?.priceSellPromo == 0) &&
                        (cart.qtyPromo == null || cart.qtyPromo == 0))
                    ? Text(
                        "Rp${cart.product?.priceSell.toCurrency}",
                        style: AppFont.componentSmallBold,
                        textAlign: TextAlign.right,
                      )
                    : Row(
                        children: [
                          Text("Rp${cart.product?.priceSell.toCurrency}",
                              style: AppFont.componentSmall.copyWith(
                                  decoration: TextDecoration.combine(
                                      [TextDecoration.lineThrough]),
                                  color: AppColor.black50)),
                          5.0.width,
                          Text("Rp${cart.product?.priceSellPromo.toCurrency}",
                              style: AppFont.componentSmall),
                        ],
                      ),
                AppDimen.h6.height,
                NoteTextButtonWidget(
                    onTap: () {
                      controller.teNoteController.text = cart.note ?? '';
                      ShowCustomModalBottom.addNote(
                        context,
                        cart.product ?? Product(),
                        controller.teNoteController,
                        () {
                          cart.note = controller.teNoteController.text;
                          Get.back();
                        },
                      );
                    },
                    text: Strings.addNote.tr),
              ],
            ),
          ],
        ),
        Visibility(
          visible: cart.linkMenu?.isNotEmpty == true,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              AppDimen.h8.height,
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: cart.linkMenu?.length ?? 0,
                itemBuilder: (context, index) {
                  var item = cart.linkMenu![index].linkMenuDetail
                      ?.where((element) => element.isChosen == true)
                      .toList();
                  return Visibility(
                    visible: item?.isNotEmpty == true,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(cart.linkMenu![index].name ?? '-',
                            style: AppFont.componentSmall),
                        ListView.builder(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          itemCount: item?.length ?? 0,
                          itemBuilder: (context, indexChild) {
                            var itemChild = item![indexChild];
                            return Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Row(
                                  children: [
                                    AppDimen.h10.width,
                                    Text(
                                      itemChild.name ?? '',
                                      style: AppFont.paragraphSmall
                                          .copyWith(fontSize: 11.sp),
                                    ),
                                  ],
                                ),
                                Text(
                                    "+Rp${(itemChild.priceAdd ?? 0).toCurrency}",
                                    style: AppFont.paragraphSmallBold),
                              ],
                            );
                          },
                        ),
                      ],
                    ),
                  );
                },
              ),
            ],
          ),
        ),
        AppDimen.h6.height,
        Visibility(
          visible: cart.note != null && (cart.note?.isNotEmpty == true),
          child: InkWell(
            onTap: () {
              controller.teNoteController.text = cart.note ?? '';
              ShowCustomModalBottom.addNote(
                context,
                cart.product ?? Product(),
                controller.teNoteController,
                () {
                  cart.note = controller.teNoteController.text;
                  Get.back();
                },
              );
            },
            child: Container(
              margin: const EdgeInsets.only(top: 5),
              padding:
                  const EdgeInsets.only(left: 4, top: 5, bottom: 5, right: 12),
              decoration: const BoxDecoration(
                  color: AppColor.black5,
                  borderRadius: BorderRadius.all(Radius.circular(4))),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.mode_edit_outline_outlined,
                    color: AppColor.black70,
                    size: AppDimen.h14,
                  ),
                  AppDimen.h4.width,
                  Flexible(
                    child: Text(
                      (cart.note ?? '').capitalizeFirst ?? '',
                      maxLines: 5,
                      style: AppFont.paragraphSmall.copyWith(fontSize: 11.sp),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        AppDimen.h2.height,
        const Divider(
          color: AppColor.black5,
        ),
        AppDimen.h4.height,
      ],
    );
  }
}
