import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:mobile_crm/app/enum/order_type_enum.dart';
import 'package:mobile_crm/app/modules/cart/cart_export.dart';
import 'package:mobile_crm/app/utils/logger.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/theme/app_color.dart';
import 'package:mobile_crm/core/theme/app_dimen.dart';
import 'package:mobile_crm/core/theme/app_font.dart';
import 'package:mobile_crm/core/values/values.dart';

import '../../../../../../routes/app_pages.dart';
import '../../../../../widget/app_dotted_separator.dart';
import '../../../../screen_wrapper/controller/wrapper_controller.dart';
import '../cart_detail_modal_bottom/cart_detail_order_type.dart';
import 'cart_detail_choose_time.dart';

class CartDetailTypeOrder extends GetView<CartDetailController> {
  const CartDetailTypeOrder({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final wrapperController = Get.find<WrapperController>();
    var timePickup = TimeOfDay.now().obs;
    var datePickup = Rx<DateTime?>(null);
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          color: AppColor.white,
          width: MediaQuery.of(context).size.width,
          padding: EdgeInsets.symmetric(
              horizontal: Constants.defaultPadding2, vertical: AppDimen.h8),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Obx(() {
                          IconData? icon;
                          if (controller
                                  .orderTypeString[controller.valueIndex.value]
                                  .removeUnderscore
                                  .toLowerCase() ==
                              OrderTypeEnum.dine_in.name.removeUnderscore
                                  .toLowerCase()) {
                            icon = Icons.restaurant;
                          } else if (controller.orderTypeString[controller.valueIndex.value].removeUnderscore
                                      .toLowerCase() ==
                                  OrderTypeEnum.delivery.name.removeUnderscore
                                      .toLowerCase() ||
                              controller.orderTypeString[controller.valueIndex.value].removeUnderscore
                                      .toLowerCase() ==
                                  OrderTypeEnum
                                      .internal_delivery.name.removeUnderscore
                                      .toLowerCase()) {
                            icon = Icons.delivery_dining;
                          } else if (controller
                                      .orderTypeString[
                                          controller.valueIndex.value]
                                      .removeUnderscore
                                      .toLowerCase() ==
                                  OrderTypeEnum.pickup.name.removeUnderscore
                                      .toLowerCase() ||
                              controller
                                      .orderTypeString[controller.valueIndex.value]
                                      .removeUnderscore
                                      .toLowerCase() ==
                                  OrderTypeEnum.self_order.name.removeUnderscore.toLowerCase()) {
                            icon = Icons.shopping_bag;
                          }

                          return Chip(
                            elevation: 0,
                            color: WidgetStateProperty.all(
                              Colors.transparent,
                            ),
                            padding: const EdgeInsets.symmetric(
                                horizontal: 10, vertical: 5),
                            labelStyle: AppFont.componentSmallBold.copyWith(
                                color: wrapperController.getPrimaryColor()),
                            label: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Icon(
                                  icon,
                                  color: wrapperController.getPrimaryColor(),
                                  size: 16,
                                ),
                                const SizedBox(
                                  width: 8,
                                ),
                                Text(
                                  controller.orderTypeString[
                                      controller.valueIndex.value],
                                ).fadeIn(
                                  duration: const Duration(milliseconds: 200),
                                )
                              ],
                            ),
                          );
                        }),
                        InkWell(
                          onTap: () {
                            const CartDetailOrderType()
                                .toModalBottomSheetNoMinHeight
                                .of(context);
                          },
                          child: Row(
                            children: [
                              Text(
                                'Change',
                                style: AppFont.componentSmallBold.copyWith(
                                    color: wrapperController.getPrimaryColor()),
                              ),
                              Transform.rotate(
                                angle: 3 * 3.14 / 2,
                                child: Icon(
                                  Icons.chevron_left,
                                  color: wrapperController.getPrimaryColor(),
                                ),
                              ),
                            ],
                          ),
                        )
                      ],
                    ),
                    AppDimen.h10.height,
                    Obx(
                      () => controller
                                  .orderTypeString[controller.valueIndex.value]
                                  .toLowerCase() ==
                              'self order'
                          ? Text(
                              Strings.orderSelfOrderDesc.tr,
                              style: AppFont.componentSmall,
                            )
                          : controller.orderTypeString[
                                          controller.valueIndex.value]
                                      .toLowerCase() ==
                                  'dine in'
                              ? Text(
                                  Strings.orderDineInDesc.tr,
                                  style: AppFont.componentSmall,
                                )
                              : controller.orderTypeString[
                                              controller.valueIndex.value]
                                          .toLowerCase() ==
                                      'pickup'
                                  ? Text(
                                      Strings.orderPickupDesc.tr,
                                      style: AppFont.componentSmall,
                                    )
                                  : Text(
                                      Strings.orderDeliveryDesc.tr,
                                      style: AppFont.componentSmall,
                                    ),
                    ),
                    Obx(
                      () => controller
                                  .orderTypeString[controller.valueIndex.value]
                                  .toLowerCase() ==
                              'pickup'
                          ? Column(
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                AppDimen.h4.height,
                                const DottedDivider(
                                  width: 3,
                                  height: 2,
                                  color: AppColor.black5,
                                ),
                                AppDimen.h4.height,
                                Text(
                                  Strings.chooseTime.tr,
                                  style: AppFont.componentSmallBold
                                      .copyWith(color: AppColor.black90),
                                ),
                                GestureDetector(
                                  onTap: () async {
                                    timePickup.value = await getTime(
                                            context: context) ??
                                        TimeOfDay(
                                            hour: timePickup.value.hour,
                                            minute: timePickup.value.minute);
                                  },
                                  child: Container(
                                    margin:
                                        const EdgeInsets.symmetric(vertical: 5),
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 16, vertical: 4),
                                    decoration: BoxDecoration(
                                        color: AppColor.white,
                                        border: Border.all(
                                            color: AppColor.black, width: 2),
                                        borderRadius: BorderRadius.circular(5)),
                                    child: Obx(() {
                                      final localizations =
                                          MaterialLocalizations.of(context);
                                      final formattedTimeOfDay = localizations
                                          .formatTimeOfDay(timePickup.value,
                                              alwaysUse24HourFormat: true);
                                      controller.timePickup =
                                          formattedTimeOfDay;
                                      return Text(
                                          timePickup.value.format(context),
                                          style: AppFont.componentSmallBold);
                                    }),
                                  ),
                                ),
                              ],
                            )
                          : controller.orderTypeString[
                                          controller.valueIndex.value]
                                      .toLowerCase() ==
                                  'delivery'
                              ? Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    AppDimen.h4.height,
                                    const DottedDivider(
                                        width: 3,
                                        height: 2,
                                        color: AppColor.black5),
                                    AppDimen.h4.height,
                                    Text(
                                      Strings.destination.tr,
                                      style: AppFont.componentSmallBold,
                                    ),
                                    GestureDetector(
                                      onTap: () async {
                                        var result =
                                            await Get.toNamed(Routes.ADDRESS);
                                        if (result.runtimeType ==
                                            controller
                                                .address.value.runtimeType) {
                                          controller.address.value =
                                              await result;
                                          controller.store.mainAddress =
                                              await result;
                                        }
                                      },
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Obx(
                                            () {
                                              controller.address.value;
                                              return Text(
                                                controller.store.mainAddress
                                                            ?.label ==
                                                        null
                                                    ? 'Default'
                                                    : (controller
                                                                    .store
                                                                    .mainAddress
                                                                    ?.label ??
                                                                '') ==
                                                            ''
                                                        ? 'Default'
                                                        : (controller
                                                                .store
                                                                .mainAddress
                                                                ?.label ??
                                                            ''),
                                                style:
                                                    AppFont.paragraphSmallBold,
                                              );
                                            },
                                          ),
                                          5.0.height,
                                          Flexible(
                                            child: Obx(
                                              () {
                                                controller
                                                    .address.value.address;
                                                String completeAddress =
                                                    "${controller.store.user?.address} ${controller.store.user?.city} ${controller.store.user?.province}";
                                                return Text(
                                                  controller.store.mainAddress
                                                              ?.address ==
                                                          null
                                                      ? completeAddress
                                                              .contains('null')
                                                          ? 'Choose Address'
                                                          : completeAddress
                                                      : controller.address.value
                                                              .address ??
                                                          '${controller.store.mainAddress?.address}',
                                                  style: AppFont.paragraphSmall,
                                                );
                                              },
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                )
                              : controller.orderTypeString[
                                              controller.valueIndex.value]
                                          .toLowerCase() ==
                                      'internal delivery'
                                  ? Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      mainAxisAlignment:
                                          MainAxisAlignment.start,
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        AppDimen.h4.height,
                                        const DottedDivider(
                                            width: 3,
                                            height: 2,
                                            color: AppColor.black5),
                                        AppDimen.h4.height,
                                        Text(
                                          Strings.destination.tr,
                                          style: AppFont.componentSmallBold,
                                        ),
                                        GestureDetector(
                                          onTap: () async {
                                            var result = await Get.toNamed(
                                                Routes.ADDRESS);
                                            if (result.runtimeType ==
                                                controller.address.value
                                                    .runtimeType) {
                                              controller.address.value =
                                                  await result;
                                              controller.store.mainAddress =
                                                  await result;

                                              Map<String, dynamic> latLng = {
                                                "latitude": result.latitude,
                                                "longitude": result.longitude,
                                              };
                                              controller.getOutletDeliveryPrice(
                                                  latLng);
                                            }
                                          },
                                          child: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            mainAxisAlignment:
                                                MainAxisAlignment.center,
                                            mainAxisSize: MainAxisSize.min,
                                            children: [
                                              Obx(() {
                                                controller.address.value;
                                                return Text(
                                                  controller
                                                      .getShippingAddressTitle(),
                                                  style: AppFont
                                                      .paragraphSmallBold,
                                                );
                                              }),
                                              5.0.height,
                                              Flexible(
                                                child: Obx(() {
                                                  controller
                                                      .address.value.address;
                                                  return Text(
                                                    controller
                                                        .getShippingAddress(),
                                                    style:
                                                        AppFont.paragraphSmall,
                                                  );
                                                }),
                                              ),
                                            ],
                                          ),
                                        ),
                                        Column(
                                          mainAxisAlignment:
                                              MainAxisAlignment.start,
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            AppDimen.h4.height,
                                            const DottedDivider(
                                              width: 3,
                                              height: 2,
                                              color: AppColor.black5,
                                            ),
                                            AppDimen.h4.height,
                                            Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment
                                                      .spaceBetween,
                                              children: [
                                                Text(
                                                  Strings.scheduleDelivery.tr,
                                                  style: AppFont
                                                      .componentSmallBold
                                                      .copyWith(
                                                          color:
                                                              AppColor.black90),
                                                ),
                                                GestureDetector(
                                                  onTap: () async {
                                                    datePickup.value =
                                                        await getDate(
                                                            context: context);

                                                    if (datePickup.value !=
                                                        null) {
                                                      timePickup
                                                          // ignore: use_build_context_synchronously
                                                          .value = await getTime(
                                                              context:
                                                                  context) ??
                                                          TimeOfDay(
                                                            hour: timePickup
                                                                .value.hour,
                                                            minute: timePickup
                                                                .value.minute,
                                                          );
                                                    }
                                                  },
                                                  child: Container(
                                                    margin: const EdgeInsets
                                                        .symmetric(vertical: 5),
                                                    padding: const EdgeInsets
                                                        .symmetric(
                                                        horizontal: 16,
                                                        vertical: 4),
                                                    decoration: BoxDecoration(
                                                        color: AppColor.white,
                                                        border: Border.all(
                                                            color:
                                                                AppColor.black,
                                                            width: 2),
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(5)),
                                                    child: Obx(
                                                      () {
                                                        final formattedDate =
                                                            DateFormat("dd MMM")
                                                                .format(
                                                          datePickup.value ??
                                                              DateTime.now(),
                                                        );

                                                        try {
                                                          final hour =
                                                              timePickup
                                                                  .value.hour;
                                                          final minute =
                                                              timePickup
                                                                  .value.minute
                                                                  .toString()
                                                                  .padLeft(2,
                                                                      '0'); // Pad
                                                          DateTime parsedDate =
                                                              DateTime.parse(
                                                                  "${DateFormat('yyyy-MM-dd').format(datePickup.value ?? DateTime.now())} $hour:${minute}");

                                                          controller
                                                                  .deliveryTime =
                                                              parsedDate
                                                                  .millisecondsSinceEpoch;
                                                          infoLogger(
                                                              'parsing time success------');
                                                        } catch (e) {
                                                          errorLogger(
                                                              pos:
                                                                  "parse pickup time error",
                                                              error: e);
                                                          infoLogger(
                                                              'parset time error: ',
                                                              e);
                                                        }

                                                        return Text(
                                                          "$formattedDate, ${timePickup.value.format(context)}",
                                                          style: AppFont
                                                              .componentSmallBold,
                                                        );
                                                        // return Text(
                                                        //     'date: $formattedDate');
                                                      },
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ],
                                        )
                                      ],
                                    )
                                  : const SizedBox(),
                    ),
                  ],
                ),
              )
            ],
          ),
        ),
        Divider(
          color: AppColor.black5,
          height: AppDimen.h6,
          thickness: AppDimen.h6,
        ),
      ],
    );
  }
}
