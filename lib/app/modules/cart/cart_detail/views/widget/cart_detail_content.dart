// ignore_for_file: invalid_use_of_protected_member

import 'package:flutter/material.dart';
import '../cart_detail_view.dart';
import 'cart_detail_phone_input.dart';
import 'cart_detail_point_information.dart';
import 'cart_detail_summary.dart';
import 'cart_detail_type_order.dart';

class CartDetailContent extends CartDetailScreen {
  const CartDetailContent({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        children: [
          const CartDetailTypeOrder(),
          const CartDetailPhoneInput(),
          const CartDetailSummary(),
          controller.getFirebaseRemoteABPoint()
              ? const CartDetailPointInformation()
              : const SizedBox(),
        ],
      ),
    );
  }
}
