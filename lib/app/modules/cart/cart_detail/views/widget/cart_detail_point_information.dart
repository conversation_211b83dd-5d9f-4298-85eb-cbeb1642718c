import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/values.dart';

import '../../../../../../data/services/analytics_service.dart';
import '../../../../../../routes/app_pages.dart';
import '../../controller/cart_detail_controller.dart';

class CartDetailPointInformation extends StatelessWidget {
  const CartDetailPointInformation({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<CartDetailController>();
    return Obx(() {
      return (controller.pointCollection.value > 0 && !controller.isUserLogin)
          ? InkWell(
              onTap: () {
                AnalyticsService.instance.logEvent(
                    name: "point_notice",
                    parameters: {'type': 'without bottom sheet point notice'});
                Get.toNamed(Routes.LOGIN, arguments: controller.outletId);
              },
              child: Container(
                margin: const EdgeInsets.symmetric(vertical: 8),
                padding: EdgeInsets.all(Constants.defaultPadding),
                decoration: const BoxDecoration(
                    color: AppColor.whiteGrey,
                    borderRadius: BorderRadius.all(Radius.circular(10)),
                    boxShadow: [
                      BoxShadow(
                          color: Color.fromARGB(50, 168, 168, 168),
                          offset: Offset(0.0, 5.0), //(x,y)
                          blurRadius: 10.0),
                    ]),
                child: Row(
                  children: [
                    const Icon(
                      Icons.info_outline_rounded,
                      color: AppColor.black90,
                    ),
                    10.0.width,
                    Flexible(
                      child: Text(
                        Strings.dontMissOpportunityToEarnPoint.tr,
                        style: AppFont.componentSmall
                            .copyWith(color: AppColor.black90),
                      ),
                    )
                  ],
                ),
              ),
            )
          : const SizedBox();
    });
  }
}
