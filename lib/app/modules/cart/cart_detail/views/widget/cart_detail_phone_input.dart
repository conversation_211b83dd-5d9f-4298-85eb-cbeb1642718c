import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/cart/cart_export.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/theme/app_color.dart';
import 'package:mobile_crm/core/theme/app_dimen.dart';
import 'package:mobile_crm/core/theme/app_font.dart';
import 'package:mobile_crm/core/values/values.dart';

import '../../../../../../routes/app_pages.dart';
import '../../../../../widget/app_dotted_separator.dart';

class CartDetailPhoneInput extends GetView<CartDetailController> {
  const CartDetailPhoneInput({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Visibility(
      visible: !controller.isUserLogin,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
              color: AppColor.white,
              width: MediaQuery.of(context).size.width,
              padding: EdgeInsets.symmetric(
                  horizontal: Constants.defaultPadding2,
                  vertical: AppDimen.h8),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        Strings.phoneNumber.tr,
                        style: AppFont.componentSmallBold,
                      ),
                      InkWell(
                        onTap: () => Get.toNamed(Routes.LOGIN,
                            arguments: controller.outletId),
                        child: Text(
                          Strings.orSignIn.tr,
                          style: AppFont.componentSmall.copyWith(
                              color: AppColor.black70,
                              decoration: TextDecoration.underline),
                        ),
                      )
                    ],
                  ),
                  Row(
                    children: [
                      const Icon(
                        Icons.phone,
                        color: AppColor.black90,
                      ),
                      AppDimen.h8.width,
                      Flexible(
                        child: TextFormField(
                            key: key,
                            keyboardType: TextInputType.number,
                            controller:
                            controller.receiveReceiptsOrderController,
                            style: AppFont.componentSmall,
                            minLines: 1,
                            autofocus: false,
                            decoration: const InputDecoration(
                                hintText: '08123456789',
                                border: InputBorder.none)),
                      ),
                    ],
                  ),
                  const DottedDivider(
                    width: 3,
                    height: 2,
                    color: AppColor.black5,
                  )
                ],
              ).fadeIn()),
          Divider(
              color: AppColor.black5,
              height: AppDimen.h6,
              thickness: AppDimen.h6),
        ],
      ),
    );
  }
}
