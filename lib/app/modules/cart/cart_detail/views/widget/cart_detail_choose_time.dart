import 'package:flutter/material.dart';

Future<TimeOfDay?> getTime({
  required BuildContext context,
  String? title,
  TimeOfDay? initialTime,
  String? cancelText,
  String? confirmText,
}) async {
  TimeOfDay? time = await showTimePicker(
    initialEntryMode: TimePickerEntryMode.dial,
    context: context,
    initialTime: initialTime ?? TimeOfDay.now(),
    cancelText: cancelText ?? "Cancel",
    confirmText: confirmText ?? "Save",
    helpText: title ?? "Select time",
  );

  return time;
}

Future<DateTime?> getDate({
  required BuildContext context,
  String? title,
  DateTime? initialDate,
  DateTime? firstDate,
  DateTime? lastDate,
}) async {
  DateTime? date = await showDatePicker(
    context: context,
    initialDate: initialDate ?? DateTime.now(),
    firstDate: firstDate ?? DateTime.now(),
    lastDate: lastDate ?? DateTime(DateTime.now().year + 1),
    selectableDayPredicate: (date) {
      return date.month > DateTime.now().month - 1 &&
          date.month < DateTime.now().month + 1 &&
          date.day > DateTime.now().day - 1 &&
          date.day < DateTime.now().day + 2;
    },
  );

  return date;
}
