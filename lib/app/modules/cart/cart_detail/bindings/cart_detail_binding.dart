import 'package:get/get.dart';
import 'package:mobile_crm/data/repository/cart_repository.dart';
import 'package:mobile_crm/data/repository/deal_repository.dart';
import 'package:mobile_crm/data/repository/outlet_repository.dart';
import 'package:mobile_crm/data/repository/point_collection_repository.dart';
import 'package:mobile_crm/data/repository/product_repository.dart';
import 'package:mobile_crm/data/repository/transaction_repository.dart';

import '../controller/cart_detail_controller.dart';

class CartDetailBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => CartRepositoryIml());
    Get.lazyPut(() => DealRepositoryIml());
    Get.lazyPut(() => PointCollectionRepositoryIml());
    Get.lazyPut(() => ProductRepositoryIml());
    Get.lazyPut(() => OutletRepositoryIml());
    Get.lazyPut(() => TransactionRepositoryIml());
    Get.lazyPut(() => CartDetailController());
  }
}
