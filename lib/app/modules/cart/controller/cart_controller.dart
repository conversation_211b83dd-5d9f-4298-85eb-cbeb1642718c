// ignore_for_file: invalid_use_of_protected_member

import 'package:get/get.dart';
import 'package:mobile_crm/data/models/transaction_new_model.dart';
import 'package:mobile_crm/data/providers/db/database.dart';
import 'package:mobile_crm/data/repository/cart_repository.dart';
import 'package:mobile_crm/data/repository/deal_repository.dart';
import 'package:mobile_crm/data/repository/transaction_repository.dart';
import 'package:mobile_crm/domain/repository/cart_repository.dart';
import 'package:mobile_crm/domain/repository/deal_repository.dart';

import '../../../../domain/repository/transaction_repository.dart';

class CartController extends GetxController {
  final CartRepository _repoCart = Get.find<CartRepositoryIml>();
  final DealRepository repoDeal = Get.find<DealRepositoryIml>();
  final TransactionRepository repoTransaction =
      Get.find<TransactionRepositoryIml>();

  var listTransaction = <TransactionDataData>[].obs;
  var newOrder = <TransactionNewModel>[].obs;

  @override
  void onInit() async {
    await fetchData();
    super.onInit();
  }

  void updateCart(TransactionNewModel transactionNewModel) async =>
      await _repoCart
          .updateTransactionOrder(transactionNewModel)
          .then((value) async => await getTransactionOrder());

  Future<List<TransactionDataData>> getAllTransaction() async =>
      listTransaction.value =
          await repoTransaction.getAllTransaction(dealRepo: repoDeal);

  Future<List<TransactionNewModel>> getTransactionOrder() async {
    newOrder.value = await _repoCart.getAllTransactionOrder().then((value) =>
        value
            .where((element) => (element.orderList?.isNotEmpty == true && element.isOnOrder == false))
            .toList());
    return newOrder.value;
  }

  Future fetchData() async {
    await getAllTransaction();
    await getTransactionOrder();
  }
}
