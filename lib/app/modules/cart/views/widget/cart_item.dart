// ignore_for_file: must_be_immutable

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/widget/cached_image_widget.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/app_constants.dart';
import 'package:mobile_crm/core/values/app_strings.dart';
import 'package:mobile_crm/data/providers/db/database.dart';

class CartItemWidget extends StatelessWidget {
  CartItemWidget({super.key, required this.cart});

  Cart cart;

  @override
  Widget build(BuildContext context) {
    Size size = Constants.productItemSize;
    return Container(
      margin: EdgeInsets.only(bottom: AppDimen.h6),
      decoration: BoxDecoration(borderRadius: BorderRadius.circular(15)),
      clipBehavior: Clip.hardEdge,
      child: SizedBox(
        width: size.width,
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(
              height: size.height,
              width: size.width,
              child: ClipRRect(
                borderRadius: const BorderRadius.all(
                  Radius.circular(12.0),
                ),
                child: CachedImageWidget(
                    imageUrl: "${cart.product?.photo.toString()}",
                    fit: BoxFit.fill,
                    width: size.width,
                    height: size.height),
              ),
            ),
            Container(
              padding: const EdgeInsets.all(8.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text("${cart.product?.name}",
                      overflow: TextOverflow.ellipsis,
                      style: AppFont.componentSmall),
                  24.0.height,
                  (cart.product?.stockStatus) == "available"
                      ? Text("QTY :  ${cart.qty}",
                          style: AppFont.componentSmall)
                      : Text(Strings.productNotAvailable.tr,
                          style: AppFont.componentSmall),
                  (cart.product?.stockStatus) == "available"
                      ? Text("Rp ${cart.product?.priceSell.toCurrency}",
                          style: AppFont.componentSmall)
                      : Text('', style: AppFont.componentSmall),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
