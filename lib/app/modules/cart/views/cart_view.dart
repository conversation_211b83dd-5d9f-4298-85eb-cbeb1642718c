// ignore_for_file: invalid_use_of_protected_member

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/screen_wrapper/controller/wrapper_controller.dart';
import 'package:mobile_crm/app/modules/screen_wrapper/views/app_page_wrapper.dart';
import 'package:mobile_crm/app/utils/app_alert.dart';
import 'package:mobile_crm/app/widget/app_bar_widget/app_bar_widget.dart';
import 'package:mobile_crm/app/widget/app_dotted_separator.dart';
import 'package:mobile_crm/app/widget/button_widget/button_widget.dart';
import 'package:mobile_crm/core/extensions/color_extensions.dart';
import 'package:mobile_crm/core/extensions/double_extensions.dart';
import 'package:mobile_crm/core/extensions/int_extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/app_constants.dart';
import 'package:mobile_crm/core/values/app_strings.dart';
import 'package:mobile_crm/routes/app_pages.dart';

import '../../../utils/utils.dart';
import '../../../widget/full_screen_dialog/active_transaction/active_transaction_fdialog.dart';
import '../../../widget/html_widget.dart';
import '../controller/cart_controller.dart';

class CartScreen extends GetView<CartController> {
  const CartScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final wrapperC = Get.find<WrapperController>();
    return PageWrapper(
      onWillPop: () {
        Get.back(result: true);
        return Future.value(true);
      },
        child: Scaffold(
      backgroundColor: Colors.transparent,
      appBar: AppBarWidget(
        title: 'Cart',
        actions: [
          IconButton(
            onPressed: () {
              ActiveTransactionFullScreenDialog().show(
                  context: context,
                  transRepo: controller.repoTransaction,
                  dealRepo: controller.repoDeal);
            },
            icon: Obx(
              () => Badge(
                  isLabelVisible: controller.listTransaction.value.isNotEmpty,
                  label: Text(convertNumber(controller.listTransaction.length)),
                  child: Icon(Icons.receipt_long_outlined,
                      size: Constants.iconSize(context))),
            ),
          ),
        ],
      ),
      body: SizedBox(
        height: MediaQuery.of(context).size.height,
        child: Obx(() {
          return ListView.builder(
            itemCount: (controller.newOrder.value).length,
            itemBuilder: (context, index) {
              var item = controller.newOrder.value[index];
              item.orderList?.sort(
                (a, b) =>
                    (a.product?.name ?? '').compareTo(b.product?.name ?? ''),
              );
              var oList = item.obs;
              return Container(
                width: double.infinity,
                decoration: const BoxDecoration(color: AppColor.white),
                child: Column(
                  children: [
                    InkWell(
                      onTap: () => Get.toNamed(
                          Routes.CARTDETAIL(item.outletId.toString()))?.then((value) => value ? controller.fetchData() : null ),
                      child: Padding(
                          padding: EdgeInsets.symmetric(
                              horizontal: AppDimen.h16, vertical: 16),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    item.outlet?.name ??
                                        '',
                                    style: AppFont.componentMediumBold,
                                  ),
                                  AppDimen.h6.height,
                                  Text(
                                    "Rp${item.sumPrice().toCurrency} • ${item.sumQTY()} Item",
                                    style: AppFont.componentSmall,
                                  ),
                                ],
                              ),
                              Icon(
                                Icons.keyboard_arrow_right_rounded,
                                size: AppDimen.h18,
                                color: Colors.black,
                              )
                            ],
                          )),
                    ),
                    const DottedDivider(
                      width: 4,
                      height: 2,
                      color: AppColor.black5,
                    ),
                    InkWell(
                      onTap: () => showModalBottomSheet(
                        context: context,
                        useRootNavigator: true,
                        isScrollControlled: true,
                        constraints: BoxConstraints(
                            maxWidth: Constants.defaultMaxWidth,
                            minHeight: MediaQuery.of(context).size.height * 0.6,
                            maxHeight:
                                MediaQuery.of(context).size.height * 0.8),
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.only(
                                topLeft: Radius.circular(AppDimen.h8),
                                topRight: Radius.circular(AppDimen.h8))),
                        backgroundColor: AppColor.whiteGrey,
                        enableDrag: true,
                        builder: (context) {
                          return Column(
                            children: [
                              Container(
                                decoration: BoxDecoration(
                                    color: AppColor.white,
                                    boxShadow: const [
                                      BoxShadow(
                                          color: AppColor.black30,
                                          offset: Offset(0, 1),
                                          spreadRadius: 2,
                                          blurRadius: 2)
                                    ],
                                    borderRadius: BorderRadius.only(
                                        topLeft: Radius.circular(AppDimen.h8),
                                        topRight:
                                            Radius.circular(AppDimen.h8))),
                                padding: EdgeInsets.symmetric(
                                    horizontal: AppDimen.h8,
                                    vertical: AppDimen.h4),
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    IconButton(
                                        onPressed: () =>
                                            Navigator.of(context).pop(),
                                        icon: const Icon(
                                          Icons.close,
                                          color: AppColor.black90,
                                        )),
                                    Text(
                                      item.orderList?.firstOrNull?.outlet
                                              ?.name ??
                                          Strings.order,
                                      style: AppFont.componentSmallBold,
                                    ),
                                    TextButton(
                                      child: const Text(
                                        "",
                                      ),
                                      onPressed: () {},
                                    ),
                                  ],
                                ),
                              ),
                              Expanded(child: Obx(() {
                                return ListView.builder(
                                  shrinkWrap: true,
                                  itemCount: controller.newOrder.value[index]
                                          .orderList?.length ??
                                      0,
                                  itemBuilder: (context, indexChild) {
                                    var itemChild =
                                        oList.value.orderList![indexChild];
                                    return Container(
                                        margin: EdgeInsets.only(
                                            bottom: AppDimen.h4),
                                        padding: EdgeInsets.symmetric(
                                            horizontal: AppDimen.h12,
                                            vertical: AppDimen.h12),
                                        decoration: const BoxDecoration(
                                            color: AppColor.white),
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              "${itemChild.product?.name}",
                                              style: AppFont.componentSmallBold,
                                            ),
                                            HtmlWidget(
                                              data: itemChild
                                                      .product?.description ??
                                                  '',
                                              fontSize: 12,
                                              maxLines: 2,
                                            ),
                                            AppDimen.h4.height,
                                            Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment
                                                      .spaceBetween,
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.end,
                                              children: [
                                                Text(
                                                  "Rp${(itemChild.product?.priceSell ?? (itemChild.product?.price ?? 0)).toCurrency}",
                                                  style: AppFont.componentSmall,
                                                ),
                                                Row(
                                                  children: [
                                                    InkWell(
                                                      onTap: () {
                                                        if (itemChild.qty !=
                                                                null &&
                                                            (itemChild.qty ??
                                                                    1) >
                                                                1) {
                                                          itemChild.qty =
                                                              (itemChild.qty ??
                                                                      1) -
                                                                  1;
                                                          item.updateOrder(
                                                              itemChild);
                                                          controller
                                                              .updateCart(item);
                                                        } else {
                                                          if ((item.orderList
                                                                      ?.length ??
                                                                  0) <=
                                                              1) {
                                                            Get.back();
                                                          }
                                                          AppAlert
                                                              .showConfirmDeleteDialog(
                                                            context,
                                                            "${itemChild.product?.name}",
                                                            () {
                                                              item.removeOrder(
                                                                  itemChild);
                                                              controller
                                                                  .updateCart(
                                                                      item);
                                                              Get.back();
                                                            },
                                                          );
                                                        }
                                                      },
                                                      child: Container(
                                                        padding: EdgeInsets.all(
                                                            AppDimen.h2),
                                                        alignment:
                                                            Alignment.center,
                                                        width: AppDimen.h20,
                                                        height: AppDimen.h20,
                                                        decoration: BoxDecoration(
                                                            color: wrapperC
                                                                .getPrimaryColor()
                                                                .withAlpha(10),
                                                            borderRadius:
                                                                BorderRadius.all(
                                                                    Radius.circular(
                                                                        AppDimen
                                                                            .h6))),
                                                        child: Text(
                                                          "-",
                                                          style: AppFont
                                                              .componentSmallBold
                                                              .copyWith(
                                                                  color: wrapperC
                                                                      .getPrimaryColor()),
                                                        ),
                                                      ),
                                                    ),
                                                    Obx(() {
                                                      return Container(
                                                        width: AppDimen.h32,
                                                        height: AppDimen.h20,
                                                        alignment:
                                                            Alignment.center,
                                                        child: Text(
                                                          "${controller.newOrder.value[index].orderList?[indexChild].qty ?? 0}",
                                                          style: AppFont
                                                              .componentSmallBold,
                                                        ),
                                                      );
                                                    }),
                                                    InkWell(
                                                      onTap: () {
                                                        if (itemChild.qty !=
                                                            null) {
                                                          itemChild.qty =
                                                              (itemChild.qty ??
                                                                      1) +
                                                                  1;
                                                          item.updateOrder(
                                                              itemChild);
                                                          controller
                                                              .updateCart(item);
                                                          controller.refresh();
                                                        }
                                                      },
                                                      child: Container(
                                                        padding: EdgeInsets.all(
                                                            AppDimen.h2),
                                                        alignment:
                                                            Alignment.center,
                                                        width: AppDimen.h20,
                                                        height: AppDimen.h20,
                                                        decoration: BoxDecoration(
                                                            color: wrapperC
                                                                .getPrimaryColor()
                                                                .withAlpha(10),
                                                            borderRadius:
                                                                BorderRadius.all(
                                                                    Radius.circular(
                                                                        AppDimen
                                                                            .h6))),
                                                        child: Text(
                                                          "+",
                                                          style: AppFont
                                                              .componentSmallBold
                                                              .copyWith(
                                                                  color: wrapperC
                                                                      .getPrimaryColor()),
                                                        ),
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ],
                                            ),
                                          ],
                                        ));
                                  },
                                );
                              })),
                              Align(
                                alignment: Alignment.bottomCenter,
                                child: Padding(
                                  padding: EdgeInsets.symmetric(
                                      horizontal: AppDimen.h8,
                                      vertical: AppDimen.h4),
                                  child: PrimaryButton(
                                    onPressed: () {
                                      var items =
                                          item.orderList?.where((element) {
                                        bool itemUnavailable =
                                            element.product?.stock_status ==
                                                'unavailable';
                                        return itemUnavailable;
                                      }).toList();
                                      if (items?.isNotEmpty == true) {
                                        AppAlert.showConfirmWarningDialog(
                                            context: context,
                                            message:
                                                Strings.someCartUnavailable.tr,
                                            content: [
                                              Text(
                                                Strings.someCartUnavailable.tr,
                                                style: AppFont.componentSmall
                                                    .copyWith(
                                                        color: Colors.white),
                                              ),
                                              const Divider(),
                                              Flexible(
                                                child: Container(
                                                  width: Constants.alertWidth(
                                                      context),
                                                  constraints: BoxConstraints(
                                                      maxHeight:
                                                          MediaQuery.of(context)
                                                                  .size
                                                                  .height *
                                                              0.4),
                                                  child: ListView.builder(
                                                    shrinkWrap: true,
                                                    padding:
                                                        const EdgeInsets.all(
                                                            6.0),
                                                    itemCount:
                                                        items?.length ?? 0,
                                                    itemBuilder:
                                                        (context, index) {
                                                      var itemChild =
                                                          items![index];
                                                      return Container(
                                                        margin: const EdgeInsets
                                                            .only(bottom: 5),
                                                        child: Row(
                                                          mainAxisAlignment:
                                                              MainAxisAlignment
                                                                  .spaceBetween,
                                                          children: [
                                                            Text(
                                                              "${itemChild.product?.name}",
                                                              style: AppFont
                                                                  .paragraphSmall
                                                                  .copyWith(
                                                                      color: AppColor
                                                                          .white),
                                                            ),
                                                            Text(
                                                              "${itemChild.qty ?? 0}",
                                                              style: AppFont
                                                                  .paragraphSmall
                                                                  .copyWith(
                                                                      color: AppColor
                                                                          .white),
                                                            ),
                                                          ],
                                                        ),
                                                      );
                                                    },
                                                  ),
                                                ),
                                              ),
                                              const Divider()
                                            ],
                                            actions: [
                                              TextButton(
                                                  onPressed: () {
                                                    Get.back();
                                                  },
                                                  child: Text(
                                                    Strings.cancel.tr,
                                                    style: AppFont
                                                        .componentSmall
                                                        .copyWith(
                                                            color:
                                                                AppColor.white),
                                                  )),
                                              TextButton(
                                                  onPressed: () {
                                                    Get.back();
                                                    Get.toNamed(
                                                        Routes.CARTDETAIL(item
                                                            .outletId
                                                            .toString()))?.then((value) => value ? controller.fetchData() : null );
                                                  },
                                                  child: Text(
                                                    Strings.next.tr,
                                                    style: AppFont
                                                        .componentSmall
                                                        .copyWith(
                                                            color:
                                                                AppColor.white),
                                                  )),
                                            ]);
                                      } else {
                                        Get.toNamed(Routes.CARTDETAIL(
                                            item.outletId.toString()))?.then((value) => value ? controller.fetchData() : null );
                                      }
                                    },
                                    type: PrimaryButtonType.type5,
                                    width: MediaQuery.of(context).size.width,
                                    borderRadius: BorderRadius.all(
                                        Radius.circular(AppDimen.h6)),
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Obx(() {
                                          return Text(
                                              "${controller.newOrder.value[index].totalQTY} Item",
                                              style: AppFont.componentSmallBold
                                                  .copyWith(
                                                      color: wrapperC
                                                          .getPrimaryColor()
                                                          .changeColorBasedOnBackgroundColor()
                                                          .$1));
                                        }),
                                        Text(Strings.next.tr,
                                            style: AppFont.componentSmallBold
                                                .copyWith(
                                                    color: wrapperC
                                                        .getPrimaryColor()
                                                        .changeColorBasedOnBackgroundColor()
                                                        .$1)),
                                        Obx(() {
                                          return Text(
                                              "Rp${controller.newOrder.value[index].subTotal.toCurrency}",
                                              style: AppFont.componentSmallBold
                                                  .copyWith(
                                                      color: wrapperC
                                                          .getPrimaryColor()
                                                          .changeColorBasedOnBackgroundColor()
                                                          .$1));
                                        }),
                                      ],
                                    ),
                                  ),
                                ),
                              )
                            ],
                          );
                        },
                      ),
                      child: Container(
                        width: double.infinity,
                        alignment: Alignment.center,
                        padding: EdgeInsets.symmetric(
                            horizontal: AppDimen.h16, vertical: 16),
                        decoration: const BoxDecoration(color: AppColor.white),
                        child: Text(
                          Strings.details.tr,
                          style: AppFont.componentSmallBold,
                        ),
                      ),
                    ),
                    Divider(
                        color: AppColor.black5,
                        height: AppDimen.h6,
                        thickness: AppDimen.h6),
                  ],
                ),
              );
            },
          );
        }),
      ),
    ));
  }
}
