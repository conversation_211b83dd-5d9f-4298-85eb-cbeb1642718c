import 'package:get/get.dart';
import 'package:mobile_crm/data/repository/cart_repository.dart';
import 'package:mobile_crm/data/repository/deal_repository.dart';

import '../../../../data/repository/transaction_repository.dart';
import '../controller/cart_controller.dart';

class CartBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => CartRepositoryIml());
    Get.lazyPut(() => TransactionRepositoryIml());
    Get.lazyPut(() => DealRepositoryIml());
    Get.lazyPut(() => CartController());
  }
}
