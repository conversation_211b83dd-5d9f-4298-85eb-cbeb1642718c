import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/profile/views/widget/help_legal.dart';
import 'package:mobile_crm/app/modules/screen_wrapper/views/app_page_wrapper.dart';
import 'package:mobile_crm/app/widget/custom_refresh_indicator_widget.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';

import '../../../widget/modal_bottom_sheet/fill_province_modal_sheet.dart';
import '../../../widget/province_district/province_modal_bottom.dart';
import '../controllers/profile_controller.dart';
import 'widget/profile_widget.dart';

class ProfileScreen extends GetView<ProfileController> {
  const ProfileScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return PageWrapper(
      onWillPop: () {
        if (controller.users.value.province != null) {
          Get.back(result: controller.users.value);
          return Future.value(true);
        }
        FillProvinceBottomSheet.show(
          context: context,
          onPressed: () {
            Get.back();
            ProvinceModalBottom(
              repo: controller.repoHelper,
              callBackProvince: (value) {
                controller.province.value = value;
              },
              callBackDistrict: (value) {
                controller.district.value = value;
                controller.changeProvince();
              },
            ).toModalBottomSheet.of(Get.context!);
            // ProvinceBottomSheet.showProvince(
            //   context: context,
            //   repo: controller.repoHelper,
            //   callBackProvince: (ProvinceModel value) {
            //     controller.province.value = value;
            //   },
            //   callBackDistrict: (ProvinceModel value) async {
            //     controller.district.value = value;
            //     await controller.changeProvince();
            //   },
            // );
          },
        );
        return Future.value(false);
      },
      child: Column(
        mainAxisSize: MainAxisSize.max,
        children: [
          const ProfileAppBar(),
          Expanded(
            child: CustomRefreshIndicator(
              onRefresh: () async => await controller.refreshData(),
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                child: Column(
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    20.0.height,
                    const ProfileUserCard(),
                    20.0.height,
                    const ProfileForm(),
                    20.0.height,
                    const HelpAndLegal(),
                    20.0.height,
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
