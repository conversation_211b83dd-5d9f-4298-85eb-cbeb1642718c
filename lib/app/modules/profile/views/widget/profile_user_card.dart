import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/profile/controllers/profile_controller.dart';
import 'package:mobile_crm/app/modules/screen_wrapper/controller/wrapper_controller.dart';
import 'package:mobile_crm/app/widget/cached_image_widget.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/app_constants.dart';
import 'package:mobile_crm/data/services/analytics_service.dart';
import 'package:mobile_crm/routes/app_pages.dart';

class ProfileUserCard extends StatelessWidget {
  const ProfileUserCard({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    Size size = MediaQuery.of(context).size;
    final controller = Get.find<ProfileController>();
    final c = Get.find<WrapperController>();
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: Constants.defaultPadding),
      child: SizedBox(
        height: 150.h,
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 25),
          decoration: BoxDecoration(
            color: AppColor.white,
            borderRadius: BorderRadius.circular(15),
            boxShadow: const [
              BoxShadow(
                color: AppColor.black10,
                blurRadius: 2,
                spreadRadius: 2,
                offset: Offset(0, 2),
              )
            ],
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    10.0.height,
                    Expanded(
                      child: Obx(() {
                        return Text(
                          (controller.users.value.name == 'null' ||
                                  controller.users.value.name == null)
                              ? ''
                              : (controller.users.value.name ?? ''),
                          style: AppFont.componentMediumBold,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        );
                      }),
                    ),
                    const Spacer(),
                    Obx(() {
                      return Text(
                        "${controller.users.value.memberType}",
                        style: AppFont.componentSmallBold,
                      );
                    }),
                    Obx(() {
                      return Text(
                        "${controller.users.value.totalPoint} ${c.configApp.value.language?.point ?? ''} Point",
                        style: AppFont.componentSmallBold.copyWith(
                          color: AppColor.couponDiscount,
                        ),
                      );
                    }),
                    10.0.height,
                  ],
                ),
              ),
              SizedBox(
                height: 100.h,
                width: 100.h,
                child: InkWell(
                  onTap: () {
                    AnalyticsService.observer.analytics.logEvent(
                        name: "get_secret_id",
                        parameters: {"position": "profile"});
                    Get.toNamed(Routes.MYQRCODE);
                    // showModalBottomSheet<dynamic>(
                    //   context: context,
                    //   isScrollControlled: true,
                    //   backgroundColor: Colors.white,
                    //   constraints: BoxConstraints(
                    //       minWidth: Constants.defaultBoxConstraints(context),
                    //       maxWidth: Constants.defaultBoxConstraints(context)),
                    //   shape: RoundedRectangleBorder(
                    //       borderRadius: BorderRadius.only(
                    //           topLeft: Constants.shapeRadius,
                    //           topRight: Constants.shapeRadius)),
                    //   builder: (context) {
                    //     return Padding(
                    //       padding: EdgeInsets.all(
                    //         Constants.defaultPadding,
                    //       ),
                    //       child: Column(
                    //         mainAxisSize: MainAxisSize.min,
                    //         crossAxisAlignment: CrossAxisAlignment.center,
                    //         children: [
                    //           Icon(
                    //             Icons.drag_handle_rounded,
                    //             size: Constants.iconSizeSmall(context),
                    //             color: AppColor.black70,
                    //           ),
                    //           Text(
                    //             Strings.scanToEarnPoint.tr,
                    //             style: AppFont.componentSmall,
                    //           ),
                    //           const Divider(),
                    //           ClipRRect(
                    //             borderRadius:
                    //                 BorderRadius.circular(20), // Image border
                    //             child: SizedBox(
                    //                 width: 250.w,
                    //                 height: 250.h,
                    //                 child: GestureDetector(
                    //                   onTapDown: (details) async =>
                    //                       AppAlert.showPreviewImage(
                    //                           context: context,
                    //                           path: await StorageHelper.downloadFile("https://chart.googleapis.com/chart?chs=500x500&cht=qr&chl=${controller.users.value.barcodeUrl.toString()}&chld=M|1", 'qr').then((value) => value.path),
                    //                           canChange: false),
                    //                   child: CachedImageWidget(
                    //                       imageUrl:
                    //                           "https://chart.googleapis.com/chart?chs=500x500&cht=qr&chl=${controller.users.value.barcodeUrl.toString()}&chld=M|1"
                    //                               .toString(),
                    //                       fit: BoxFit.fill,
                    //                       width: size.width,
                    //                       height: size.height),
                    //                 )),
                    //           ),
                    //           const Divider(),
                    //           const SecretIDTimer()
                    //         ],
                    //       ),
                    //     );
                    //   },
                    // );
                  },
                  child: ClipRRect(
                    borderRadius: const BorderRadius.all(
                      Radius.circular(12.0),
                    ),
                    child: Obx(() {
                      return CachedImageWidget(
                          imageUrl:
                              controller.users.value.barcodeUrl.toString(),
                          fit: BoxFit.fill,
                          width: size.width,
                          height: size.height);
                    }),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
