// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/widget/button_widget/button_widget.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/app_constants.dart';
import 'package:mobile_crm/core/values/app_strings.dart';
import 'package:mobile_crm/data/models/server_response.dart';

import '../../controllers/profile_controller.dart';
import 'profile_widget.dart';

class ProfileButton extends StatelessWidget {
  const ProfileButton({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final c = Get.find<ProfileController>();
    double defaultConstraints = Constants.defaultBoxConstraints(context);
    return TextButtonWidget(
      onPressed: () {
        showModalBottomSheet(
          context: context,
          isScrollControlled: true,
          shape: const RoundedRectangleBorder(
              borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(25), topRight: Radius.circular(25))),
          constraints: BoxConstraints(
              minWidth: defaultConstraints, maxWidth: defaultConstraints),
          builder: (context) => ProfileBottomSheetWidget(
            type: ProfileBottomSheetWidgetType.delete,
            onPressed: () async {
              ServerResponse resp = await c.requestRemoveAccount();
              if (resp.status) {
                showModalBottomSheet(
                  context: context,
                  isScrollControlled: true,
                  shape: const RoundedRectangleBorder(
                      borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(25),
                          topRight: Radius.circular(25))),
                  constraints: BoxConstraints(
                      minWidth: defaultConstraints,
                      maxWidth: defaultConstraints),
                  builder: (context) => ProfileBottomSheetWidget(
                    type: ProfileBottomSheetWidgetType.code,
                    onPressed: () async {
                      await c.confirmRemoveAccount();
                    },
                  ),
                );
              }
            },
          ),
        );
      },
      text: Strings.deleteAccount.tr,
      type: TextButtonType.type2,
      child: Text(
        Strings.deleteAccount.tr,
        style: AppFont.componentMediumBold,
      ),
    );
  }
}
