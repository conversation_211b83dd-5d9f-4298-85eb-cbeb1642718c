import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/profile/profile_export.dart';
import 'package:mobile_crm/app/widget/button_widget/button_widget.dart';
import 'package:mobile_crm/app/widget/custom_circular_progress_widget.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/app_constants.dart';
import 'package:mobile_crm/core/values/app_strings.dart';

import 'profile_input_code.dart';

class ProfileBottomSheetWidget extends StatelessWidget {
  const ProfileBottomSheetWidget(
      {Key? key,
      this.type = ProfileBottomSheetWidgetType.delete,
      this.title = '',
      this.message = '',
      this.child = const Text(''),
      this.buttonText,
      required this.onPressed})
      : super(key: key);
  final ProfileBottomSheetWidgetType type;
  final VoidCallback? onPressed;
  final String? title;
  final String? message;
  final String? buttonText;
  final Widget? child;

  @override
  Widget build(BuildContext context) {
    final c = Get.find<ProfileController>();
    return type == ProfileBottomSheetWidgetType.delete
        ? _deleteAccount(context, c)
        : type == ProfileBottomSheetWidgetType.code
            ? _codeConfirm(context, c)
            : _basic(context, c);
  }

  Widget _deleteAccount(BuildContext context, ProfileController c) {
    return Container(
      padding:
          EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
      constraints:
          BoxConstraints(maxWidth: Constants.defaultBoxConstraints(context)),
      decoration: const BoxDecoration(
          color: AppColor.black10,
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(25), topRight: Radius.circular(25))),
      child: Padding(
        padding: EdgeInsets.all(15.h),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            10.0.height,
            SizedBox(
              width: double.infinity,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Text("Remove Account",
                      style: AppFont.paragraphLargeBold
                          .copyWith(color: Colors.redAccent)),
                  Text(
                    "Account cannot be recovered once closed",
                    style: AppFont.paragraphMedium,
                  ),
                ],
              ),
            ),
            20.0.height,
            PrimaryButton(
              onPressed: onPressed,
              text: Strings.next,
              width: double.infinity,
              type: PrimaryButtonType.type4,
              child: SizedBox(
                width: MediaQuery.of(context).size.width,
                child: Obx(() => c.isRequestDeleteLoading.value
                    ? const CustomCircularProgressIndicator()
                    : Text(
                        Strings.next,
                        style: AppFont.componentSmall
                            .copyWith(color: AppColor.white),
                        textAlign: TextAlign.center,
                      )),
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget _codeConfirm(BuildContext context, ProfileController c) {
    return Container(
      padding:
          EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
      constraints:
          BoxConstraints(maxWidth: Constants.defaultBoxConstraints(context)),
      decoration: const BoxDecoration(
          color: AppColor.black10,
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(25), topRight: Radius.circular(25))),
      child: Padding(
        padding: EdgeInsets.all(15.h),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            10.0.height,
            SizedBox(
              width: double.infinity,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Text(Strings.verificationCode,
                      style: AppFont.paragraphLargeBold),
                  Text(
                    "Enter the code sent to the email",
                    style: AppFont.paragraphMedium,
                  ),
                  30.0.height,
                  const ProfileInputCode(),
                  15.0.height
                ],
              ),
            ),
            20.0.height,
            PrimaryButton(
              onPressed: onPressed,
              text: Strings.next,
              width: double.infinity,
              type: PrimaryButtonType.type4,
              child: SizedBox(
                width: MediaQuery.of(context).size.width,
                child: Obx(() => c.isRequestDeleteLoading.value
                    ? const CustomCircularProgressIndicator()
                    : Text(
                        Strings.next,
                        style: AppFont.componentSmall
                            .copyWith(color: AppColor.white),
                        textAlign: TextAlign.center,
                      )),
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget _basic(BuildContext context, ProfileController c) {
    return Container(
      padding:
          EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
      constraints:
          BoxConstraints(maxWidth: Constants.defaultBoxConstraints(context)),
      decoration: const BoxDecoration(
          color: AppColor.black10,
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(25), topRight: Radius.circular(25))),
      child: Padding(
        padding: EdgeInsets.all(15.h),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            10.0.height,
            SizedBox(
              width: double.infinity,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Text(title ?? '', style: AppFont.paragraphLargeBold),
                  Text(
                    message ?? '',
                    style: AppFont.paragraphMedium,
                  ),
                  20.0.height,
                  child ?? Container(),
                  10.0.height
                ],
              ),
            ),
            20.0.height,
            PrimaryButton(
              onPressed: onPressed,
              text: buttonText ?? Strings.save.tr,
              width: double.infinity,
              type: PrimaryButtonType.type4,
              child: SizedBox(
                width: MediaQuery.of(context).size.width,
                child: Obx(() => c.isSaveLoading.value
                    ? const CustomCircularProgressIndicator()
                    : Align(
                        alignment: Alignment.center,
                        child: Text(
                          buttonText ?? _getDefaultButtonText(),
                          style: AppFont.componentSmall
                              .copyWith(color: AppColor.white),
                        ),
                      )),
              ),
            )
          ],
        ),
      ),
    );
  }

  String _getDefaultButtonText() {
    switch (type) {
      case ProfileBottomSheetWidgetType.basic:
        return Strings.save.tr;
      case ProfileBottomSheetWidgetType.basic2:
        return Strings.next.tr;
      case ProfileBottomSheetWidgetType.contactAdmin:
        return "Contact Admin";
      default:
        return Strings.verify.tr;
    }
  }
}

enum ProfileBottomSheetWidgetType {
  delete,
  code,
  basic,
  basic2,
  basic3,
  contactAdmin
}
