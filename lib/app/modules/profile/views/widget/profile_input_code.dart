import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/profile/controllers/profile_controller.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:pinput/pinput.dart';

class ProfileInputCode extends StatefulWidget {
  const ProfileInputCode({Key? key}) : super(key: key);

  @override
  State<ProfileInputCode> createState() => _ProfileInputCodeState();
}

class _ProfileInputCodeState extends State<ProfileInputCode> {
  final focusNode = FocusNode();

  @override
  void dispose() {
    focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<ProfileController>();
    final defaultPinTheme = PinTheme(
      width: 35,
      height: 40,
      decoration: BoxDecoration(
        color: AppColor.black10,
        borderRadius: BorderRadius.circular(5),
      ),
    );

    final cursor = Align(
      alignment: Alignment.bottomCenter,
      child: Container(
        width: 18,
        height: 2,
        margin: const EdgeInsets.only(bottom: 8),
        decoration: BoxDecoration(
          color: const Color.fromRGBO(137, 146, 160, 1),
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );

    return Pinput(
      length: 6,
      controller: controller.pinInputRemoveAccountController,
      focusNode: focusNode,
      defaultPinTheme: defaultPinTheme,
      // separator: 20.0.width,
      separatorBuilder: (index) => SizedBox(width: 20),
      focusedPinTheme: defaultPinTheme.copyWith(
        decoration: BoxDecoration(
          color: AppColor.black,
          borderRadius: BorderRadius.circular(8),
          boxShadow: const [
            BoxShadow(
              color: Color.fromRGBO(0, 0, 0, 0.*****************),
              offset: Offset(0, 3),
              blurRadius: 16,
            )
          ],
        ),
      ),
      showCursor: true,
      cursor: cursor,
    );
  }
}
