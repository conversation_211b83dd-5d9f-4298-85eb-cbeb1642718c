import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/widget/button_widget/button_widget.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/routes/app_pages.dart';

class HelpAndLegal extends StatelessWidget {
  const HelpAndLegal({super.key});

  @override
  Widget build(BuildContext context) {
    double separator = 10.0;
    Size size = MediaQuery.of(context).size;
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8.0),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 20),
        decoration: BoxDecoration(
          color: AppColor.white,
          borderRadius: BorderRadius.circular(15),
          boxShadow: const [
            BoxShadow(
              color: AppColor.black10,
              blurRadius: 2,
              spreadRadius: 2,
              offset: Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              "Help & Legal",
              style: AppFont.componentMediumBold,
            ),
            const SizedBox(
              height: 16,
            ),
            Row(
              children: [
                Expanded(
                  flex: 2,
                  child: PrimaryButton(
                    onPressed: () {
                      Get.toNamed(Routes.FAQ);
                    },
                    text: "text",
                    type: PrimaryButtonType.type4t,
                    width: size.width,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                            child: Row(
                          children: [
                            const Icon(
                              Icons.help_outline_outlined,
                              color: AppColor.couponDiscount,
                              size: 22.0,
                            ),
                            const SizedBox(
                              width: 8,
                            ),
                            Text(
                              "Help Center (FAQ)",
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                              style: AppFont.componentSmallBold
                                  .copyWith(color: AppColor.black),
                            ),
                          ],
                        )),
                        const Icon(
                          Icons.keyboard_arrow_right,
                          color: AppColor.ink02,
                        )
                      ],
                    ),
                  ),
                ),
              ],
            ),
            separator.height,
            Row(
              children: [
                Expanded(
                  flex: 2,
                  child: PrimaryButton(
                    onPressed: () {
                      Get.toNamed(Routes.PRIVACY);
                    },
                    text: "text",
                    type: PrimaryButtonType.type4t,
                    width: size.width,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                            child: Row(
                          children: [
                            const Icon(
                              Icons.privacy_tip_outlined,
                              color: AppColor.couponDiscount,
                              size: 22.0,
                            ),
                            const SizedBox(
                              width: 8,
                            ),
                            Text(
                              "Privacy Policy",
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                              style: AppFont.componentSmallBold
                                  .copyWith(color: AppColor.black),
                            ),
                          ],
                        )),
                        const Icon(
                          Icons.keyboard_arrow_right,
                          color: AppColor.ink02,
                        )
                      ],
                    ),
                  ),
                ),
              ],
            ),
            separator.height,
            Row(
              children: [
                Expanded(
                  flex: 2,
                  child: PrimaryButton(
                    onPressed: () {
                      Get.toNamed(Routes.TERMS);
                    },
                    text: "text",
                    type: PrimaryButtonType.type4t,
                    width: size.width,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                            child: Row(
                          children: [
                            const Icon(
                              Icons.file_copy_outlined,
                              color: AppColor.couponDiscount,
                              size: 22.0,
                            ),
                            const SizedBox(
                              width: 8,
                            ),
                            Text(
                              "Terms & Conditions",
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                              style: AppFont.componentSmallBold
                                  .copyWith(color: AppColor.black),
                            ),
                          ],
                        )),
                        const Icon(
                          Icons.keyboard_arrow_right,
                          color: AppColor.ink02,
                        )
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
