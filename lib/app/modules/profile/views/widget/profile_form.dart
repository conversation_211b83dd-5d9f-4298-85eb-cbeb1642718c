// import 'package:badges/badges.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/home/<USER>/home_controller.dart';
import 'package:mobile_crm/app/modules/profile/controllers/profile_controller.dart';
import 'package:mobile_crm/app/utils/toast.dart';
import 'package:mobile_crm/app/widget/button_widget/button_widget.dart';
import 'package:mobile_crm/app/widget/input_widget/input_form_widget.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/app_constants.dart';
import 'package:mobile_crm/core/values/app_strings.dart';
import 'package:mobile_crm/data/models/address_model.dart';
import 'package:mobile_crm/routes/app_pages.dart';

import '../../../../../data/models/province_model.dart';
import '../../../../../data/models/server_response.dart';
import '../../../../widget/province_district/district_modal_bottom.dart';
import '../../../../widget/province_district/province_modal_bottom.dart';
import 'profile_widget.dart';

class ProfileForm extends StatelessWidget {
  const ProfileForm({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final c = Get.find<ProfileController>();
    Size size = MediaQuery.of(context).size;
    double separator = 10.0;
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8.0),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 20),
        decoration: BoxDecoration(
          color: AppColor.white,
          borderRadius: BorderRadius.circular(15),
          boxShadow: const [
            BoxShadow(
              color: AppColor.black10,
              blurRadius: 2,
              spreadRadius: 2,
              offset: Offset(0, 2),
            )
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              Strings.profileDetail.tr,
              style: AppFont.componentMediumBold,
            ),
            const SizedBox(
              height: 16,
            ),
            Row(
              children: [
                Expanded(
                  flex: 2,
                  child: PrimaryButton(
                    onPressed: () {
                      showModalBottomSheet(
                        context: context,
                        isScrollControlled: true,
                        shape: const RoundedRectangleBorder(
                          borderRadius: BorderRadius.only(
                            topLeft: Radius.circular(25),
                            topRight: Radius.circular(25),
                          ),
                        ),
                        constraints: BoxConstraints(
                            minWidth: Constants.defaultBoxConstraints(context),
                            maxWidth: Constants.defaultBoxConstraints(context)),
                        builder: (context) => ProfileBottomSheetWidget(
                          type: ProfileBottomSheetWidgetType.contactAdmin,
                          title: Strings.nameCannotBeChanged.tr,
                          message: Strings.contactAdminToChangeName.tr,
                          buttonText: "Contact Admin",
                          child: Container(),
                          onPressed: () async {
                            Get.back();
                            await Get.find<HomeController>().sendWhatsapp();
                          },
                        ),
                      );
                    },
                    text: "text",
                    type: PrimaryButtonType.type4t,
                    width: size.width,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                            child: Row(
                          children: [
                            const Icon(
                              Icons.person_2_outlined,
                              color: AppColor.couponDiscount,
                              size: 22.0,
                            ),
                            const SizedBox(
                              width: 8,
                            ),
                            Expanded(child: Obx(() {
                              return Text(
                                (c.users.value.name == 'null' ||
                                        c.users.value.name == null)
                                    ? ''
                                    : (c.users.value.name ?? ''),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                                style: AppFont.componentSmallBold
                                    .copyWith(color: AppColor.black),
                              );
                            })),
                          ],
                        )),
                        const Icon(
                          Icons.keyboard_arrow_right,
                          color: AppColor.ink02,
                        )
                      ],
                    ),
                  ),
                ),
              ],
            ),
            separator.height,
            Row(
              children: [
                Expanded(
                  flex: 2,
                  child: Obx(() {
                    return Badge(
                      alignment: AlignmentDirectional.topStart,
                      textStyle: AppFont.componentSmall,
                      isLabelVisible:
                          c.users.value.emailVerificationStatus == "unverified",
                      label: const Text("!"),
                      child: PrimaryButton(
                        onPressed: () {
                          showModalBottomSheet(
                            context: context,
                            isScrollControlled: true,
                            shape: const RoundedRectangleBorder(
                              borderRadius: BorderRadius.only(
                                topLeft: Radius.circular(25),
                                topRight: Radius.circular(25),
                              ),
                            ),
                            constraints: BoxConstraints(
                                minWidth:
                                    Constants.defaultBoxConstraints(context),
                                maxWidth:
                                    Constants.defaultBoxConstraints(context)),
                            builder: (context) => ProfileBottomSheetWidget(
                              type: c.users.value.emailVerificationStatus ==
                                          "verified" ||
                                      c.users.value.emailVerificationStatus ==
                                          "unverified"
                                  ? ProfileBottomSheetWidgetType.basic
                                  : ProfileBottomSheetWidgetType.basic3,
                              title: Strings.updateValue
                                  .trParams({'value': Strings.email.tr}),
                              message: c.users.value.emailVerificationStatus ==
                                          "verified" ||
                                      c.users.value.emailVerificationStatus ==
                                          "unverified"
                                  ? Strings.pleaseInputYourNewValue
                                      .trParams({'value': Strings.email.tr})
                                  : Strings.updateEmailUnverified.tr,
                              child: c.users.value.emailVerificationStatus ==
                                          "verified" ||
                                      c.users.value.emailVerificationStatus ==
                                          "unverified"
                                  ? AppInputText(
                                      label: Strings.newValue.trParams(
                                          {'value': Strings.email.tr}),
                                      icon: Icons.email,
                                      keyboardType: TextInputType.emailAddress,
                                      type: InputFormType.withIcon,
                                      controller: c.tmpEmailController,
                                    )
                                  : const Text(''),
                              onPressed: () async {
                                if (c.users.value.emailVerificationStatus ==
                                        "verified" ||
                                    c.users.value.emailVerificationStatus ==
                                        "unverified") {
                                  if (!GetUtils.isEmail(
                                      c.tmpEmailController.text)) {
                                    Get.back();
                                    return Toast.show(
                                        Strings.invalidValue.trParams(
                                            {'value': Strings.email.tr}),
                                        type: ToastType.dark);
                                  }
                                  c.editUserProfile();
                                } else {
                                  c.store.authState = Strings.authEmail;
                                  c.verifyEmail();
                                }
                              },
                            ),
                          );
                        },
                        text: "text",
                        type: PrimaryButtonType.type4t,
                        width: size.width,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Expanded(
                                child: Row(
                              children: [
                                const Icon(
                                  Icons.email_outlined,
                                  color: AppColor.couponDiscount,
                                  size: 22.0,
                                ),
                                const SizedBox(
                                  width: 8,
                                ),
                                Obx(() {
                                  return Expanded(
                                    child: Text(
                                      '${c.users.value.email}',
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                      style: AppFont.componentSmallBold
                                          .copyWith(color: AppColor.black),
                                    ),
                                  );
                                }),
                              ],
                            )),
                            const Icon(
                              Icons.keyboard_arrow_right,
                              color: AppColor.ink02,
                            )
                          ],
                        ),
                      ),
                    );
                  }),
                ),
              ],
            ),
            separator.height,
            Row(
              children: [
                Expanded(
                  flex: 2,
                  child: PrimaryButton(
                    onPressed: () {
                      showModalBottomSheet(
                        context: context,
                        isScrollControlled: true,
                        shape: const RoundedRectangleBorder(
                            borderRadius: BorderRadius.only(
                                topLeft: Radius.circular(25),
                                topRight: Radius.circular(25))),
                        constraints: BoxConstraints(
                            minWidth: Constants.defaultBoxConstraints(context),
                            maxWidth: Constants.defaultBoxConstraints(context)),
                        builder: (context) => ProfileBottomSheetWidget(
                          type: ProfileBottomSheetWidgetType.basic2,
                          title: Strings.updateValue
                              .trParams({'value': Strings.phoneNumber.tr}),
                          message:
                              "${Strings.changePhoneNumberWillReqOTP.tr}\n\n${Strings.pleaseInputYourNewValue.trParams({
                                'value': Strings.phoneNumber.tr
                              })}",
                          child: AppInputText(
                            label: Strings.newValue
                                .trParams({'value': Strings.phoneNumber.tr}),
                            icon: Icons.phone,
                            keyboardType: TextInputType.phone,
                            type: InputFormType.withIcon,
                            controller: c.tmpNumberController,
                          ),
                          onPressed: () async {
                            ServerResponse resp =
                                await c.requestChangePhoneNumber();
                            if (resp.status) {
                              Get.back();
                              showModalBottomSheet(
                                context: context,
                                isScrollControlled: true,
                                shape: const RoundedRectangleBorder(
                                    borderRadius: BorderRadius.only(
                                        topLeft: Radius.circular(25),
                                        topRight: Radius.circular(25))),
                                builder: (context) => ProfileBottomSheetWidget(
                                  type: ProfileBottomSheetWidgetType.code,
                                  onPressed: () async {
                                    await c.verifyNumberByCodeEmail();
                                  },
                                ),
                              );
                            }
                          },
                        ),
                      );
                    },
                    text: "text",
                    type: PrimaryButtonType.type4t,
                    width: size.width,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                            child: Row(
                          children: [
                            const Icon(
                              Icons.phone_android_outlined,
                              color: AppColor.couponDiscount,
                              size: 22.0,
                            ),
                            const SizedBox(
                              width: 8,
                            ),
                            Obx(() {
                              return Text(
                                '${c.users.value.phone}',
                                style: AppFont.componentSmallBold
                                    .copyWith(color: AppColor.black),
                              );
                            }),
                          ],
                        )),
                        const Icon(
                          Icons.keyboard_arrow_right,
                          color: AppColor.ink02,
                        )
                      ],
                    ),
                  ),
                ),
              ],
            ),
            separator.height,
            Row(
              children: [
                Expanded(
                  flex: 2,
                  child: PrimaryButton(
                    onPressed: () {
                      if (c.users.value.dateOfBirth == null ||
                          c.users.value.dateOfBirth == 0) {
                        showModalBottomSheet(
                          context: context,
                          isScrollControlled: true,
                          shape: const RoundedRectangleBorder(
                              borderRadius: BorderRadius.only(
                                  topLeft: Radius.circular(25),
                                  topRight: Radius.circular(25))),
                          constraints: BoxConstraints(
                              minWidth:
                                  Constants.defaultBoxConstraints(context),
                              maxWidth:
                                  Constants.defaultBoxConstraints(context)),
                          builder: (context) => ProfileBottomSheetWidget(
                            type: ProfileBottomSheetWidgetType.basic,
                            title: Strings.updateValue
                                .trParams({'value': Strings.dateOfBirth.tr}),
                            message: Strings.pleaseInputYourNewValue
                                .trParams({'value': Strings.dateOfBirth.tr}),
                            child: AppInputText(
                              label: Strings.newValue
                                  .trParams({'value': Strings.dateOfBirth.tr}),
                              icon: Icons.date_range,
                              keyboardType: TextInputType.text,
                              type: InputFormType.date,
                              controller: c.birthController.value,
                              dateCallBack: (value) {
                                c.dateTime = value;
                                c.birthController.value.text =
                                    value.toLocal().toString().split(' ')[0];
                              },
                            ),
                            onPressed: () => c.changeDateBirth(),
                          ),
                        );
                      } else {
                        showModalBottomSheet(
                          context: context,
                          isScrollControlled: true,
                          shape: const RoundedRectangleBorder(
                            borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(25),
                              topRight: Radius.circular(25),
                            ),
                          ),
                          constraints: BoxConstraints(
                              minWidth:
                                  Constants.defaultBoxConstraints(context),
                              maxWidth:
                                  Constants.defaultBoxConstraints(context)),
                          builder: (context) => ProfileBottomSheetWidget(
                            type: ProfileBottomSheetWidgetType.basic3,
                            title: Strings.dateOfBirthCannotBeChanged.tr,
                            message: Strings.contactAdminToChangeDOB.tr,
                            buttonText: "Contact Admin",
                            child: Container(),
                            onPressed: () async {
                              Get.back();
                              await Get.find<HomeController>().sendWhatsapp();
                            },
                          ),
                        );
                      }
                    },
                    text: "text",
                    type: PrimaryButtonType.type4t,
                    width: size.width,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                            child: Row(
                          children: [
                            const Icon(
                              Icons.calendar_month_outlined,
                              color: AppColor.couponDiscount,
                              size: 22.0,
                            ),
                            const SizedBox(
                              width: 8,
                            ),
                            Obx(() {
                              return Text(
                                (c.users.value.dateOfBirth == null ||
                                        c.users.value.dateOfBirth == 0)
                                    ? "-"
                                    : c.users.value.dateOfBirth.toDate,
                                style: AppFont.componentSmallBold
                                    .copyWith(color: AppColor.black),
                              );
                            }),
                          ],
                        )),
                        const Icon(
                          Icons.keyboard_arrow_right,
                          color: AppColor.ink02,
                        )
                      ],
                    ),
                  ),
                ),
              ],
            ),
            separator.height,
            Row(
              children: [
                Expanded(
                  flex: 2,
                  child: PrimaryButton(
                    onPressed: () {
                      showModalBottomSheet(
                        context: context,
                        isScrollControlled: true,
                        shape: const RoundedRectangleBorder(
                            borderRadius: BorderRadius.only(
                                topLeft: Radius.circular(25),
                                topRight: Radius.circular(25))),
                        constraints: BoxConstraints(
                            minWidth: Constants.defaultBoxConstraints(context),
                            maxWidth: Constants.defaultBoxConstraints(context)),
                        builder: (context) => ProfileBottomSheetWidget(
                          type: ProfileBottomSheetWidgetType.basic,
                          title: Strings.updateGender.tr,
                          message: "",
                          child: AppInputText(
                            label: "-",
                            icon: Icons.male,
                            keyboardType: TextInputType.text,
                            type: InputFormType.gender,
                            controller: c.genderController.value,
                            genderCallBack: (value) {
                              c.genderController.value.text = value;
                            },
                          ),
                          onPressed: () => c.changeGender(),
                        ),
                      );
                    },
                    text: "text",
                    type: PrimaryButtonType.type4t,
                    width: size.width,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                            child: Row(
                          children: [
                            const Icon(
                              Icons.people_outline_outlined,
                              color: AppColor.couponDiscount,
                              size: 22.0,
                            ),
                            const SizedBox(
                              width: 8,
                            ),
                            Obx(() {
                              return Text(
                                c.users.value.gender == 1
                                    ? Strings.male.tr
                                    : c.users.value.gender == 0
                                        ? Strings.female.tr
                                        : "-",
                                style: AppFont.componentSmallBold
                                    .copyWith(color: AppColor.black),
                              );
                            }),
                          ],
                        )),
                        const Icon(
                          Icons.keyboard_arrow_right,
                          color: AppColor.ink02,
                        )
                      ],
                    ),
                  ),
                ),
              ],
            ),
            separator.height,
            Row(
              children: [
                Expanded(
                  flex: 2,
                  child: PrimaryButton(
                    onPressed: () async {
                      var result = await Get.toNamed(Routes.ADDRESS);
                      if (result.runtimeType == c.address.value.runtimeType) {
                        c.address.value =
                            result ?? (c.store.mainAddress ?? AddressModel());
                        c.store.mainAddress = result;
                      }
                    },
                    text: "text",
                    type: PrimaryButtonType.type4t,
                    width: size.width,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                            child: Row(
                          children: [
                            const Icon(
                              Icons.place_outlined,
                              color: AppColor.couponDiscount,
                              size: 22.0,
                            ),
                            const SizedBox(
                              width: 8,
                            ),
                            Flexible(child: Obx(() {
                              return Text(
                                "${c.address.value.address ?? c.address.value.province}",
                                style: AppFont.componentSmallBold
                                    .copyWith(color: AppColor.black),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              );
                            })),
                          ],
                        )),
                        const Icon(
                          Icons.keyboard_arrow_right,
                          color: AppColor.ink02,
                        )
                      ],
                    ),
                  ),
                ),
              ],
            ),
            separator.height,
            Row(
              children: [
                Expanded(
                  flex: 2,
                  child: PrimaryButton(
                    onPressed: () => ProvinceModalBottom(
                      repo: c.repoHelper,
                      callBackDistrict: (value) {
                        c.district.value = value;
                        c.changeProvince();
                      },
                      callBackProvince: (value) {
                        c.province.value = value;
                      },
                    ).toModalBottomSheet.of(context),
                    text: "text",
                    type: PrimaryButtonType.type4t,
                    width: size.width,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                            child: Row(
                          children: [
                            const Icon(
                              Icons.south_america_outlined,
                              color: AppColor.couponDiscount,
                              size: 22.0,
                            ),
                            const SizedBox(
                              width: 8,
                            ),
                            Flexible(child: Obx(() {
                              return Text(
                                c.province.value.name ??
                                    (c.users.value.province ?? ''),
                                style: AppFont.componentSmallBold
                                    .copyWith(color: AppColor.black),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              );
                            })),
                          ],
                        )),
                        const Icon(
                          Icons.keyboard_arrow_right,
                          color: AppColor.ink02,
                        )
                      ],
                    ),
                  ),
                ),
              ],
            ),
            separator.height,
            Obx(() {
              return Visibility(
                visible: (c.province.value.name != null ||
                    c.users.value.province != null),
                child: Row(
                  children: [
                    Expanded(
                      flex: 2,
                      child: PrimaryButton(
                        onPressed: () => DistrictModalBottom(
                          repo: c.repoHelper,
                          province: c.province.value,
                          callBackDistrict: (ProvinceModel value) {
                            c.district.value = value;
                          },
                        ).toModalBottomSheet.of(context),
                        text: "text",
                        type: PrimaryButtonType.type4t,
                        width: size.width,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Expanded(
                              child: Row(
                                children: [
                                  const Icon(
                                    Icons.apartment_outlined,
                                    color: AppColor.couponDiscount,
                                    size: 22.0,
                                  ),
                                  const SizedBox(
                                    width: 8,
                                  ),
                                  Flexible(child: Obx(() {
                                    return Text(
                                      c.district.value.name ??
                                          c.users.value.city ??
                                          '',
                                      style: AppFont.componentSmallBold
                                          .copyWith(color: AppColor.black),
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                    );
                                  })),
                                ],
                              ),
                            ),
                            const Icon(
                              Icons.keyboard_arrow_right,
                              color: AppColor.ink02,
                            )
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              );
            }),
          ],
        ),
      ),
    );
  }
}
