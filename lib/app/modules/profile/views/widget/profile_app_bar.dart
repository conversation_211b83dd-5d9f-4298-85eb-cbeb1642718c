import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/profile/controllers/profile_controller.dart';
import 'package:mobile_crm/app/modules/screen_wrapper/controller/wrapper_controller.dart';
import 'package:mobile_crm/app/widget/cached_image_widget.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/app_strings.dart';
import 'package:mobile_crm/routes/app_pages.dart';

import '../../../../../data/services/analytics_service.dart';
import 'profile_button.dart';

class ProfileAppBar extends StatelessWidget {
  const ProfileAppBar({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final c = Get.find<WrapperController>();
    final controller = Get.find<ProfileController>();
    return AppBar(
      title: Text(
        Strings.profile.tr,
        style: AppFont.componentLarge.copyWith(color: AppColor.white),
      ),
      centerTitle: true,
      backgroundColor: c.getPrimaryColor(),
      flexibleSpace: Obx(
        () => (c.configApp.value.asset == null)
            ? const Text('')
            : CachedImageWidget(
                height: kToolbarHeight + MediaQuery.of(context).padding.top,
                width: Get.size.width,
                fit: BoxFit.cover,
                imageUrl:
                    c.configApp.value.asset?.toolbar_background.toString() ??
                        '',
          errorWidget: (context, url, error) => const SizedBox(),
              ),
      ),
      actions: [
        IconButton(
            onPressed: () {
              AnalyticsService.observer.analytics
                  .logEvent(name: "go_to_wishlist_screen");
              Get.toNamed(Routes.WISHLIST);
            },
            icon: Icon(Icons.collections_bookmark_rounded,
                color: AppColor.white, size: AppDimen.h14)),
        GetPlatform.isAndroid && kReleaseMode
            ? PopupMenuButton<int>(
                position: PopupMenuPosition.under,
                color: AppColor.white,
                itemBuilder: (context) => [
                  PopupMenuItem<int>(
                      value: 2,
                      child: Row(
                        children: [
                          const Icon(
                            Icons.logout,
                            color: AppColor.black,
                          ),
                          const SizedBox(
                            width: 5,
                          ),
                          Text(
                            Strings.logout.tr,
                            style: AppFont.componentMediumBold,
                          )
                        ],
                      )),
                ],
                onSelected: (item) => controller.handleOptionsMenu(item),
              )
            : PopupMenuButton<int>(
                position: PopupMenuPosition.under,
                color: AppColor.white,
                itemBuilder: (context) => [
                  const PopupMenuItem<int>(value: 1, child: ProfileButton()),
                  PopupMenuItem<int>(
                      value: 2,
                      child: Row(
                        children: [
                          const Icon(
                            Icons.logout,
                            color: AppColor.black,
                          ),
                          const SizedBox(
                            width: 5,
                          ),
                          Text(
                            Strings.logout.tr,
                            style: AppFont.componentMediumBold,
                          )
                        ],
                      )),
                ],
                onSelected: (item) => controller.handleOptionsMenu(item),
              ),
      ],
    );
  }
}
