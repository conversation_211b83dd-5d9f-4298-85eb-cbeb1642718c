import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/widget/shimmer_loading_widget.dart';

import '../profile_view.dart';

class ProfileLoading extends ProfileScreen {
  const ProfileLoading({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      alignment: Alignment.center,
      padding: const EdgeInsets.symmetric(horizontal: 15),
      child: Column(
        children: [
          const SizedBox(
            height: 35,
          ),
          ShimmerWidget(
            width: Get.size.width,
            height: Get.size.height * 0.2,
          ),
          const SizedBox(
            height: 15,
          ),
          ShimmerWidget(
            radius: 5,
            width: Get.size.width,
            height: Get.size.height * 0.05,
          ),
          const SizedBox(
            height: 15,
          ),
          ShimmerWidget(
            radius: 5,
            width: Get.size.width,
            height: Get.size.height * 0.05,
          ),
          const SizedBox(
            height: 15,
          ),
          ShimmerWidget(
            radius: 5,
            width: Get.size.width,
            height: Get.size.height * 0.05,
          ),
          const SizedBox(
            height: 15,
          ),
          ShimmerWidget(
            radius: 5,
            width: Get.size.width,
            height: Get.size.height * 0.05,
          ),
        ],
      ),
    );
  }
}
