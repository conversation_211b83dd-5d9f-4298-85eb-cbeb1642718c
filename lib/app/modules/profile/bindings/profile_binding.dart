import 'package:get/get.dart';
import 'package:mobile_crm/data/repository/auth_repository.dart';
import 'package:mobile_crm/data/repository/helper_repository.dart';
import 'package:mobile_crm/data/repository/user_repository.dart';

import '../controllers/profile_controller.dart';

class ProfileBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => UserRepositoryIml());
    Get.lazyPut(() => AuthRepositoryIml());
    Get.lazyPut(() => HelperRepositoryIml());
    Get.lazyPut(() => ProfileController());
  }
}
