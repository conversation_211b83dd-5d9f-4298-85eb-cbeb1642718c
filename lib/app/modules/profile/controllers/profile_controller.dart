// ignore_for_file: prefer_typing_uninitialized_variables, body_might_complete_normally_catch_error

import 'dart:async';

import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/screen_wrapper/controller/wrapper_controller.dart';
import 'package:mobile_crm/app/utils/app_alert.dart';
import 'package:mobile_crm/app/utils/dbhelper.dart';
import 'package:mobile_crm/app/utils/logger.dart';
import 'package:mobile_crm/app/utils/toast.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/theme/app_font.dart';
import 'package:mobile_crm/core/values/app_strings.dart';
import 'package:mobile_crm/data/models/address_model.dart';
import 'package:mobile_crm/data/models/province_model.dart';
import 'package:mobile_crm/data/models/secret_id_model.dart';
import 'package:mobile_crm/data/models/server_response.dart';
import 'package:mobile_crm/data/models/user_code_model.dart';
import 'package:mobile_crm/data/models/user_model.dart';
import 'package:mobile_crm/data/providers/db/database.dart';
import 'package:mobile_crm/data/repository/auth_repository.dart';
import 'package:mobile_crm/data/repository/helper_repository.dart';
import 'package:mobile_crm/data/repository/user_repository.dart';
import 'package:mobile_crm/data/services/local_storage.dart';
import 'package:mobile_crm/domain/repository/auth_repository.dart';
import 'package:mobile_crm/domain/repository/helper_repository.dart';
import 'package:mobile_crm/domain/repository/user_repository.dart';
import 'package:mobile_crm/routes/app_pages.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

import '../../../../data/services/analytics_service.dart';
import '../../../exception/auth_exception_handler.dart';
import '../../../helper/province_district_helper.dart';
import '../../../widget/province_district/province_modal_bottom.dart';

class ProfileController extends GetxController {
  final FirebaseAuth mAuth = FirebaseAuth.instance;
  final UserRepository _repoUser = Get.find<UserRepositoryIml>();
  final AuthRepository _repoAuth = Get.find<AuthRepositoryIml>();
  final HelperRepository repoHelper = Get.find<HelperRepositoryIml>();
  final wrapperController = Get.find<WrapperController>();

  final AppDb _db = DatabaseHelper.instance.database;
  final store = LocalStorageService();

  var nameController = TextEditingController();
  var emailController = TextEditingController();
  var tmpNumberController = TextEditingController();
  var tmpEmailController = TextEditingController();
  var phoneController = TextEditingController();
  var birthController = TextEditingController().obs;
  var genderController = TextEditingController().obs;
  var pinInputRemoveAccountController = TextEditingController();
  var secretIdController = TextEditingController().obs;

  DateTime? dateTime;

  var secretId = SecretIdModel().obs;
  UserCodeModel userCodeModel = UserCodeModel();

  var isLoading = true.obs;
  var isSaveLoading = false.obs;
  var isSendSuccess = false.obs;
  var isRequestDeleteLoading = false.obs;

  var authState = "";

  var address = AddressModel().obs;
  var province = ProvinceModel().obs;
  var district = ProvinceModel().obs;
  var users = UserModel().obs;

  @override
  void onInit() async {
    Sentry.addBreadcrumb(Breadcrumb(
        type: 'debug',
        category: 'user.activity.profile.init',
        data: {"isLogIn": store.token != null},
        level: SentryLevel.debug));
    if (store.token != null) {
      getUserDetail();
    }
    super.onInit();
  }

  Future<SecretIdModel> getSecretId() async {
    isLoading.value = true;
    ServerResponse<SecretIdModel> serverResponse = await _repoUser
        .getSecretId()
        .whenComplete(() => isLoading.value = false);
    secretId.value = serverResponse.data ?? SecretIdModel();
    Sentry.addBreadcrumb(Breadcrumb(
        type: 'debug',
        category: 'user.activity.profile.get_secret_id',
        data: {"secretId": secretId.value.secretId},
        level: SentryLevel.debug));
    return secretId.value;
  }

  Future<void> getProvinceAndDistrictByGPS() async {
    if (users.value.province != null || users.value.province != '') {
      var prov = ProvinceDistrictHelper.isGeocodingAndProvinceMatch(
          address: AddressModel(province: users.value.province),
          listProv: await repoHelper.getProvince());
      if (prov != null) {
        province.value = prov;
      }
    }
  }

  Future refreshData() async {
    isLoading.value = true;
    getUserDetail();
    await Future.delayed(const Duration(seconds: 1));
  }

  void getUserDetail() async {
    try {
      users.value = await _repoUser.getUser() ?? UserModel();
      fillTextController();
      isLoading.value = false;
      if (Get.arguments == 10 && (users.value.province == null)) {
        ProvinceModalBottom(
          repo: repoHelper,
          callBackProvince: (value) {
            province.value = value;
          },
          callBackDistrict: (value) {
            district.value = value;
            changeProvince();
          },
        ).toModalBottomSheet.of(Get.context!);
      } else {
        getProvinceAndDistrictByGPS();
      }
    } catch (e, s) {
      errorLogger(pos: 'Profile Controller', error: e, stackTrace: s);
    }
  }

  Future<void> changeEmail() async {
    Sentry.addBreadcrumb(Breadcrumb(
        type: 'debug',
        category: 'user.activity.profile.change_email',
        data: {"email": tmpEmailController.text},
        level: SentryLevel.debug));
    if (!GetUtils.isEmail(tmpEmailController.text.trim())) {
      return Toast.show(
          Strings.invalidValue.trParams({'value': Strings.email.tr}));
    }

    if (users.value.emailVerified == 0) {
      return verifyEmail();
    }

    var result = await updateUserProfile(
        userModel: UserModel(email: tmpEmailController.text.trim()));
    if (result.status) {
      Toast.show(Strings.valueUpdated.trParams({'value': Strings.email.tr}),
          type: ToastType.success);
    } else {
      Toast.show('Failed to update Email', type: ToastType.error);
    }
  }

  Future<void> changeProvince() async {
    Sentry.addBreadcrumb(Breadcrumb(
        type: 'debug',
        category: 'user.activity.profile.changeProvince',
        data: {
          "province": province.value.name,
          "district": district.value.name
        },
        level: SentryLevel.debug));
    if (users.value.city == district.value.name) {
      return;
    }

    var result = await updateUserProfile(
        userModel: UserModel(
            name: nameController.text,
            province: province.value.name,
            city: district.value.name));
    if (result.status) {
      Toast.show(
          Strings.valueUpdated.trParams(
              {'value': "${Strings.province.tr}/${Strings.district.tr}"}),
          type: ToastType.success);
      await refreshData();
      Future.delayed(
          const Duration(milliseconds: 500), () async => await refreshData());
      if (Get.arguments == 10) {
        Get.back(result: users.value);
      }
    } else {
      Toast.show('Failed to update Province', type: ToastType.error);
    }
  }

  Future<void> changeUserName() async {
    Sentry.addBreadcrumb(Breadcrumb(
        type: 'debug',
        category: 'user.activity.profile.change_username',
        data: {"old_name": users.value.name, "new_name": nameController.text},
        level: SentryLevel.debug));
    if (nameController.text == users.value.name) {
      Get.back();
      return Toast.show("no Change on name");
    }

    var result = await updateUserProfile(
        userModel: UserModel(name: nameController.text));
    if (result.status) {
      Get.back();
      Toast.show(Strings.valueUpdated.trParams({'value': Strings.name.tr}),
          type: ToastType.success);
      await refreshData();
      Future.delayed(
          const Duration(milliseconds: 500), () async => await refreshData());
    } else {
      Toast.show('Failed to update Name', type: ToastType.error);
    }
  }

  Future<void> changeDateBirth() async {
    Sentry.addBreadcrumb(Breadcrumb(
        type: 'debug',
        category: 'user.activity.profile.change_dateBirth',
        data: {
          "old_birth": users.value.dateOfBirth,
          "new_birth": dateTime?.millisecondsSinceEpoch
        },
        level: SentryLevel.debug));
    if (dateTime?.millisecondsSinceEpoch ==
        DateTime.now().millisecondsSinceEpoch) {
      Get.back();
      return Toast.show("No Change on Date of birth", type: ToastType.dark);
    }

    var result = await updateUserProfile(
        userModel: UserModel(dateOfBirth: dateTime?.millisecondsSinceEpoch));
    if (result.status) {
      Get.back();
      Toast.show(
          Strings.valueUpdated.trParams({'value': Strings.dateOfBirth.tr}),
          type: ToastType.success);
      await refreshData();
      Future.delayed(
          const Duration(milliseconds: 500), () async => await refreshData());
    } else {
      Toast.show('Failed to update Date of Birth', type: ToastType.error);
    }
  }

  Future<void> changeGender() async {
    Sentry.addBreadcrumb(Breadcrumb(
        type: 'debug',
        category: 'user.activity.profile.change_gender',
        data: {
          "old_gender": users.value.gender,
          "new_gender": genderController.value.text
        },
        level: SentryLevel.debug));
    var result = await updateUserProfile(
        userModel: UserModel(
            gender: genderController.value.text == "Male"
                ? 1
                : genderController.value.text == "Female"
                    ? 0
                    : null));
    if (result.status) {
      Get.back();
      Toast.show('Gender updated', type: ToastType.success);
      await refreshData();
      Future.delayed(
          const Duration(milliseconds: 500), () async => await refreshData());
    } else {
      Toast.show('Failed to update Gender', type: ToastType.error);
    }
  }

  Future<ServerResponse<dynamic>> updateUserProfile(
      {String? token, required UserModel userModel}) async {
    isSaveLoading.value = true;
    userModel.name = nameController.text;
    AnalyticsService.observer.analytics.logEvent(
        name: "update_profile",
        parameters: {"updated": userModel.toJson().toString()});
    return await _repoUser
        .updateUserProfile(user: userModel, token: token)
        .whenComplete(() => isSaveLoading.value = false);
  }

  Future editUserProfile() async {
    isSaveLoading.value = true;
    if (nameController.text.isNotEmpty && tmpEmailController.text.isNotEmpty) {
      UserModel userModel = UserModel(
          name: nameController.text,
          dateOfBirth: dateTime == null
              ? null
              : dateTime?.millisecondsSinceEpoch ??
                  DateTime.now().millisecondsSinceEpoch,
          gender: genderController.value.text == "Male"
              ? 1
              : genderController.value.text == "Female"
                  ? 0
                  : null,
          email: tmpEmailController.text);
      ServerResponse res = await _repoUser.updateUserProfile(
          token: await mAuth.currentUser?.getIdToken() ?? '', user: userModel);

      if (res.status == true) {
        Get.back();
        Toast.show('Profile data updated', type: ToastType.success);
        refreshData();
      } else {
        switch (res.code) {
          case 32:
            Get.back();
            Toast.show(res.message, type: ToastType.error);
            break;
          default:
            Toast.show(res.message, type: ToastType.error);
        }
        isSaveLoading.value = false;
      }
      isSaveLoading.value = false;
    } else {
      Get.defaultDialog(
          title: "Failed",
          middleText: "There is data name that is still empty",
          textCancel: "Ok",
          onCancel: () {});
      isSaveLoading.value = false;
    }
  }

  void logOut() {
    AppAlert.showConfirmWarningDialog(
        context: Get.context!,
        message: Strings.confirmLogout.tr,
        actions: [
          TextButton(
              onPressed: () async {
                try{
                    await _repoAuth.logout();
                }catch (e, s) {
                  Sentry.captureException(e, stackTrace: s);
                }
                
                try{
                    await _db.inboxDao.deleteAll();
                }catch (e, s) {
                  Sentry.captureException(e, stackTrace: s);
                }
                
                await _db.dealTransactionDao.deleteAll();
                await _db.wishlistDao.deleteAllWishlist();
                await _db.wishlistCategoryDao.deleteAllWishlistCategory();
                await _db.cartDao.deleteAllCart();
                await _db.transactionNewDao.deleteAllTrans();
                store.clearUser();
                store.clearApp();
                Toast.show("Logout");
                Get.offAllNamed(Routes.HOME);
              },
              child: Text(
                "Logout",
                style: AppFont.componentSmall.copyWith(color: Colors.white),
              )),
        ]);
  }

  Future<void> removeAccountData() async {
    Sentry.addBreadcrumb(Breadcrumb(
        type: 'debug',
        category: 'user.activity.profile.removing_account_date',
        level: SentryLevel.debug));
    await _repoAuth.logout();
    await _repoUser.removeFCMToken();
    await _db.inboxDao.deleteAll();
    await _db.dealTransactionDao.deleteAll();
    await _db.wishlistDao.deleteAllWishlist();
    await _db.wishlistCategoryDao.deleteAllWishlistCategory();
    await _db.cartDao.deleteAllCart();
    await _db.transactionNewDao.deleteAllTrans();
    store.clearUser();
    store.clearApp();
    Toast.show(Strings.accountDeleted.tr, type: ToastType.dark);
    Get.offAllNamed(Routes.HOME);
  }

  Future verifyEmail() async {
    Sentry.addBreadcrumb(Breadcrumb(
        type: 'debug',
        category: 'user.activity.profile.verifyEmail',
        data: {"email": tmpEmailController.text},
        level: SentryLevel.debug));
    isSaveLoading.value = true;
    if (!GetUtils.isEmail(tmpEmailController.text)) {
      Toast.show("Invalid Email Format", type: ToastType.error);
      isSaveLoading.value = false;
    } else {
      try {
        await mAuth.currentUser?.updateEmail(users.value.email ?? '');
        await mAuth.currentUser
            ?.sendEmailVerification(ActionCodeSettings(
                androidPackageName: wrapperController
                    .configApp.value.appInfo?.android?.package_name,
                iOSBundleId:
                    wrapperController.configApp.value.appInfo?.ios?.bundle_id,
                url:
                    "${wrapperController.configApp.value.appInfo?.web?.url}/profile/1"))
            .whenComplete(() => isSaveLoading.value = false);
        Get.back();
        Toast.show(Strings.emailVerifyHasSent.tr,
            type: ToastType.dark, duration: 5);
        isSaveLoading.value = false;
      } catch (e, s) {
        isSaveLoading.value = false;
        Get.back();
        var dynamiclink =
            (wrapperController.configApp.value.appInfo?.dynamic_link.isEmpty !=
                        null
                    ? 'https://uniqcrm.page.link'
                    : wrapperController.configApp.value.appInfo?.dynamic_link)
                ?.replaceRange(0, 8, '');
        Sentry.captureMessage("Verify Email", params: [
          {
            'dynamicLink': dynamiclink,
            'appName': wrapperController
                .configApp.value.appInfo?.android?.package_name,
            'bundlerId':
                wrapperController.configApp.value.appInfo?.ios?.bundle_id
          }
        ]);
        Sentry.captureException(e, stackTrace: s);
        Toast.show('${FirebaseAuthExceptionHandler.handleException(e)}',
            type: ToastType.error, duration: 5);
        errorLogger(pos: 'PC Verify Email', error: e, stackTrace: s);
      }
    }
  }

  void fillTextController() {
    nameController.text =
        (users.value.name == 'null' || users.value.name == null)
            ? ''
            : (users.value.name ?? '');
    emailController.text = users.value.email.toString();
    tmpNumberController.text =
        users.value.phone.toString().replaceRange(0, 2, "0");
    tmpEmailController.text = emailController.text;
    birthController.value.text = users.value.dateOfBirth == null
        ? '-'
        : (users.value.dateOfBirth ?? 0).toInt().toDate;
    genderController.value.text = (users.value.gender ?? 3) == 1
        ? "Male"
        : (users.value.gender ?? 3) == 0
            ? "Female"
            : "-";
    address.value =
        store.mainAddress ?? AddressModel(address: store.user?.address ?? '');
    isLoading.value = false;
  }

  Future<ServerResponse> requestChangePhoneNumber() async {
    Sentry.addBreadcrumb(Breadcrumb(
        type: 'debug',
        category: 'user.activity.profile.request_phone_number',
        level: SentryLevel.debug));
    isRequestDeleteLoading.value = true;
    try {
      ServerResponse resp = await _repoUser.lostPhoneNumber(
          emailController.text.trim(),
          tmpNumberController.text.trim().toInternational);
      if (!resp.status) {
        AppAlert.showErrorDialog(Get.context!, resp.message);
      } else {
        userCodeModel = UserCodeModel.fromJson(resp.data);
        store.keyDeleteAccount = userCodeModel;
        isRequestDeleteLoading.value = false;
      }
      return resp;
    } catch (e, s) {
      errorLogger(pos: 'PC', error: e, stackTrace: s);
      isRequestDeleteLoading.value = false;
      return Future.error(e, s);
    }
  }

  Future<ServerResponse> requestRemoveAccount() async {
    Sentry.addBreadcrumb(Breadcrumb(
        type: 'debug',
        category: 'user.activity.profile.request_remove_account',
        level: SentryLevel.debug));
    isRequestDeleteLoading.value = true;
    try {
      ServerResponse resp = await _repoUser.requestRemoveAccount();
      userCodeModel = UserCodeModel.fromJson(resp.data);
      store.keyDeleteAccount = userCodeModel;
      isRequestDeleteLoading.value = false;
      if (!resp.status) {
        AppAlert.showErrorDialog(Get.context!, resp.message);
      }
      return resp;
    } catch (e, s) {
      errorLogger(pos: 'PC', error: e, stackTrace: s);
      isRequestDeleteLoading.value = false;
      return Future.error(e, s);
    }
  }

  Future<ServerResponse> confirmRemoveAccount() async {
    Sentry.addBreadcrumb(Breadcrumb(
        type: 'debug',
        category: 'user.activity.profile.confirm_remove_account',
        data: {"key": pinInputRemoveAccountController.text},
        level: SentryLevel.debug));
    isRequestDeleteLoading.value = true;
    try {
      userCodeModel.code = pinInputRemoveAccountController.text;
      infoLogger("PC", "UCM ${userCodeModel.toJson()}");
      ServerResponse resp = await _repoUser.removeAccount(userCodeModel);
      isRequestDeleteLoading.value = false;
      if (resp.status) {
        removeAccountData();
      } else {
        AppAlert.showErrorDialog(Get.context!, resp.message);
      }
      return resp;
    } catch (e, s) {
      errorLogger(pos: 'PC', error: e, stackTrace: s);
      isRequestDeleteLoading.value = false;
      return Future.error(e, s);
    }
  }

  verifyNumberByCodeEmail() async {
    Sentry.addBreadcrumb(Breadcrumb(
        type: 'debug',
        category: 'user.activity.profile.confirm_change_number',
        data: {'pin': pinInputRemoveAccountController.text},
        level: SentryLevel.debug));
    userCodeModel.code = pinInputRemoveAccountController.text;
    ServerResponse resp = await _repoUser
        .verifyChangePhoneNumber(
            key: userCodeModel.key ?? '', code: userCodeModel.code ?? '')
        .whenComplete(() => isLoading.value = false);

    switch (resp.status) {
      case true:
        Toast.show(Strings.yourNumberHasBeenChanged.tr,
            type: ToastType.success);
        store.authEmail = null;
        Get.offAllNamed(Routes.LOGIN);
        break;
      case false:
        AppAlert.showErrorDialog(Get.context!, resp.message);
        // Get.offAllNamed(Routes.LOGIN);
        break;
    }
  }

  void handleOptionsMenu(int value) {
    switch (value) {
      case 0:
        break;
      case 1:
        break;
      case 2:
        logOut();
        break;
      default:
        break;
    }
  }
}
