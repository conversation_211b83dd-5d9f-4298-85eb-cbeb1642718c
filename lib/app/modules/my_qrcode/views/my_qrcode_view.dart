import 'dart:math' as math;

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/utils/utils.dart';
import 'package:mobile_crm/app/widget/app_dotted_separator.dart';
import 'package:mobile_crm/app/widget/button_widget/button_widget.dart';
import 'package:mobile_crm/app/widget/cached_image_widget.dart';
import 'package:mobile_crm/app/widget/custom_circular_progress_widget.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/theme/app_color.dart';
import 'package:mobile_crm/core/theme/app_dimen.dart';
import 'package:mobile_crm/core/theme/app_font.dart';
import 'package:mobile_crm/core/values/values.dart';

import '../../screen_wrapper/views/app_page_wrapper.dart';
import '../controllers/my_qrcode_controller.dart';
import 'timer_widget/qrcode_timer.dart';

class MyQRPage extends GetView<MyQRController> {
  const MyQRPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return PageWrapper(
      onWillPop: () {
        Get.back();
        return Future.value(true);
      },
      child: Stack(
        children: [
          LayoutBuilder(
            builder: (context, constraints) {
              final boxWidth = constraints.constrainWidth();
              final boxHeight = constraints.constrainHeight();
              return Container(
                width: boxWidth,
                height: boxHeight,
                color: controller.wrapperController.getPrimaryColor(),
              );
            },
          ),
          LayoutBuilder(
            builder: (context, constraints) {
              final boxWidth = constraints.constrainWidth();
              final boxHeight = constraints.constrainHeight();
              final dashWidth = AppDimen.h30;
              final dashHeight = AppDimen.h30;
              final horizontal = (boxWidth / (2 * dashWidth)).floor();
              final vertical = (boxHeight / (2 * dashHeight)).floor();
              List<String> emoji = ["😀", "😁", "😄"];
              return SingleChildScrollView(
                physics: const NeverScrollableScrollPhysics(),
                scrollDirection: Axis.horizontal,
                child: SingleChildScrollView(
                  physics: const NeverScrollableScrollPhysics(),
                  scrollDirection: Axis.vertical,
                  child: Column(
                    children: List.generate(
                      vertical,
                      (y) => Row(
                        children: List.generate(
                          horizontal,
                          (x) => Transform.rotate(
                            angle: math.pi / randomRange(6, 12),
                            child: Cell(
                              width: AppDimen.h64,
                              height: AppDimen.h64,
                              child: ((x % 2 == 0) && (y % 2 == 0))
                                  ? Text(
                                      emoji[randomRange(0, 3)],
                                      style: TextStyle(
                                          fontSize: randomRange(45, 60).sp),
                                    )
                                  : ((x % 2 != 0) && (y % 2 != 0))
                                      ? Text(
                                          emoji[randomRange(0, 1)],
                                          style: TextStyle(
                                              fontSize: randomRange(45, 60).sp),
                                        )
                                      : const SizedBox(),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
          Scaffold(
            appBar: AppBar(
              backgroundColor: Colors.transparent,
              leading: IconButtonWidget(
                icon: Icons.arrow_back,
                color: controller.wrapperController
                    .getPrimaryColor()
                    .changeColorBasedOnBackgroundColor()
                    .$1,
                onPressed: () => Get.back(),
                size: Constants.iconSize(context),
              ),
            ),
            backgroundColor: Colors.transparent,
            bottomSheet: Container(
                padding: EdgeInsets.only(
                    left: AppDimen.h8, right: AppDimen.h8, bottom: AppDimen.h8),
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(AppDimen.h10),
                        topRight: Radius.circular(AppDimen.h10)),
                    color: controller.wrapperController.getSecondaryColor()),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.drag_handle_rounded,
                      size: Constants.iconSize(context),
                      color: AppColor.black70,
                    ),
                    Text(
                      Strings.scanToEarnPoint.tr,
                      style: AppFont.componentSmallBold.copyWith(
                          color: controller.wrapperController
                              .getSecondaryColor()
                              .changeColorBasedOnBackgroundColor()
                              .$1),
                    ),
                    Padding(
                      padding: EdgeInsets.symmetric(vertical: AppDimen.h4),
                      child: DottedDivider(
                        width: 3,
                        height: 2,
                        color: controller.wrapperController
                            .getSecondaryColor()
                            .changeColorBasedOnBackgroundColor()
                            .$1,
                      ),
                    ),
                    const SecretIdTimerQR(),
                  ],
                )),
            body: Center(
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: AppDimen.h16),
                width: AppDimen.h192 + AppDimen.h32,
                decoration: BoxDecoration(
                    color: AppColor.white,
                    borderRadius:
                        BorderRadius.all(Radius.circular(AppDimen.h12)),
                    boxShadow: const [
                      BoxShadow(
                          color: AppColor.black10,
                          blurRadius: 5,
                          spreadRadius: 4,
                          offset: Offset(0, 0))
                    ]),
                child: ClipRRect(
                  borderRadius: BorderRadius.all(Radius.circular(AppDimen.h12)),
                  child: FutureBuilder(
                      future: controller.userRepo.getUser(),
                      builder: (BuildContext context, snapshot) {
                        if (snapshot.hasData) {
                          if (snapshot.data != null) {
                            return Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                AppDimen.h16.height,
                                CachedImageWidget(
                                    height: AppDimen.h192,
                                    width: AppDimen.h192,
                                    alignment: Alignment.center,
                                    fit: BoxFit.fitWidth,
                                    imageUrl: snapshot.data?.barcodeUrl ?? ''
                                    // "https://chart.googleapis.com/chart?chs=500x500&cht=qr&chl=${snapshot.data?.barcodeUrl ?? ''}&chld=M|1"
                                    ),
                                AppDimen.h10.height,
                                Text(
                                  snapshot.data?.name ?? '',
                                  textAlign: TextAlign.center,
                                  style: AppFont.componentLarge,
                                ),
                                AppDimen.h16.height
                              ],
                            );
                          } else {
                            return const CustomCircularProgressIndicator();
                          }
                        }
                        return const CustomCircularProgressIndicator();
                      }),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class Cell extends StatelessWidget {
  final Widget child;
  final double width;
  final double height;

  const Cell(
      {super.key,
      required this.child,
      required this.width,
      required this.height});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      margin: const EdgeInsets.all(3),
      child: Center(child: child),
    );
  }
}
