import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/screen_wrapper/controller/wrapper_controller.dart';
import 'package:mobile_crm/data/repository/user_repository.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

import '../../../../data/models/secret_id_model.dart';
import '../../../../data/models/server_response.dart';

class MyQRController extends GetxController {
  final wrapperController = Get.find<WrapperController>();
  final userRepo = Get.find<UserRepositoryIml>();

  var isLoading = true.obs;
  var secretId = SecretIdModel().obs;

  Future<SecretIdModel> getSecretId() async {
    isLoading.value = true;
    ServerResponse<SecretIdModel> serverResponse = await userRepo
        .getSecretId()
        .whenComplete(() => isLoading.value = false);
    secretId.value = serverResponse.data ?? SecretIdModel();
    Sentry.addBreadcrumb(Breadcrumb(
        type: 'debug',
        category: 'user.activity.get_secret_id',
        data: {"secretId": secretId.value.secretId},
        level: SentryLevel.debug));
    return secretId.value;
  }
}
