import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/helper/deal_helper.dart';
import 'package:mobile_crm/app/modules/deal_detail/views/widget/deal_time_left.dart';
import 'package:mobile_crm/app/utils/toast.dart';
import 'package:mobile_crm/app/widget/button_widget/button_widget.dart';
import 'package:mobile_crm/app/widget/custom_circular_progress_widget.dart';
import 'package:mobile_crm/core/extensions/color_extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/values.dart';

import '../../controllers/deal_detail_controller.dart';

class FooterDeal extends StatelessWidget {
  const FooterDeal({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<DealDetailController>();
    return StreamBuilder(
        stream: controller.onCurrentDealModelChanged,
        builder: (BuildContext context, snapshot) {
          if (snapshot.hasData) {
            var deal = snapshot.data;
            (bool, String, bool, bool, Duration) publishDetail =
                DealHelper.isPromoBeenPublished(deal?.publishDate ?? 0);
            return deal?.name == null
                ? const SizedBox()
                : deal?.dealsCanBuy != 1
                    ? const SizedBox()
                    : ((deal?.maximumRedeem ?? 0) - (deal?.totalRedeem ?? 0)) ==
                            0
                        ? const SizedBox()
                        : SizedBox(
                            width: Constants.defaultMaxWidth,
                            child: Padding(
                                padding:
                                    EdgeInsets.all(Constants.defaultPadding),
                                child: SafeArea(
                                    bottom: true,
                                    child: publishDetail.$1
                                        ? Obx(() => controller.isLoading.value
                                            ? PrimaryButton(
                                                onPressed: () {},
                                                text: Strings.buyDeal.tr,
                                                width: double.infinity,
                                                type: PrimaryButtonType.type4,
                                                color: controller
                                                    .listPaletteColorImage
                                                    .firstOrNull
                                                    ?.color,
                                                child:
                                                    CustomCircularProgressIndicator(
                                                  valueColor: controller
                                                          .listPaletteColorImage
                                                          .firstOrNull
                                                          ?.color
                                                          .changeColorBasedOnBackgroundColor().$1 ??
                                                      AppColor.white,
                                                ))
                                            : Obx(() {
                                                return PrimaryButton(
                                                  onPressed: () {
                                                    controller.buyDeal();
                                                  },
                                                  text: Strings.buyDeal.tr,
                                                  color: controller
                                                      .listPaletteColorImage
                                                      .firstOrNull
                                                      ?.color,
                                                  width: double.infinity,
                                                  type: PrimaryButtonType.type4,
                                                  child: Text(
                                                      Strings.buyDeal.tr,
                                                      style: AppFont.componentSmall.copyWith(
                                                          color: controller
                                                                  .listPaletteColorImage
                                                                  .isEmpty
                                                              ? AppColor.white
                                                              : controller
                                                                  .listPaletteColorImage
                                                                  .firstOrNull
                                                                  ?.color
                                                                  .changeColorBasedOnBackgroundColor().$1)),
                                                );
                                              }))
                                        : publishDetail.$4
                                            ? DealTimeLeftWidget(
                                                time: deal?.publishDate ?? 0)
                                            : Obx(() {
                                                return PrimaryButton(
                                                  onPressed: () {
                                                    Toast.show(
                                                        Strings
                                                            .dealsNotYetOpened
                                                            .tr,
                                                        type: ToastType.dark);
                                                  },
                                                  color: controller
                                                      .listPaletteColorImage
                                                      .firstOrNull
                                                      ?.color,
                                                  text: Strings.buyDeal.tr,
                                                  width: double.infinity,
                                                  type: PrimaryButtonType.type4,
                                                  child: Text(
                                                      "${Strings.availableOn} ${publishDetail.$2}",
                                                      style: AppFont
                                                          .componentSmall
                                                          .copyWith(
                                                              color: controller
                                                                  .listPaletteColorImage
                                                                  .firstOrNull
                                                                  ?.color
                                                                  .changeColorBasedOnBackgroundColor().$1)),
                                                );
                                              }))),
                          );
          } else if (snapshot.hasError) {
            return const Icon(Icons.error_outline);
          } else {
            return const CustomCircularProgressIndicator(
              valueColor: AppColor.black90,
              backgroundColor: AppColor.white,
            );
          }
        });
  }
}
