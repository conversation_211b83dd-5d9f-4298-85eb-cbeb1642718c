import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/deal_detail/controllers/deal_detail_controller.dart';
import 'package:mobile_crm/app/modules/screen_wrapper/controller/wrapper_controller.dart';
import 'package:mobile_crm/app/utils/share_helper.dart';
import 'package:mobile_crm/app/widget/button_widget/button_widget.dart';
import 'package:mobile_crm/app/widget/cached_image_widget.dart';
import 'package:mobile_crm/core/extensions/color_extensions.dart';
import 'package:mobile_crm/core/values/values.dart';

import '../../../../../data/providers/db/database.dart';
import '../../../../utils/utils.dart';
import '../../../../widget/full_screen_dialog/active_transaction/active_transaction_fdialog.dart';

class HeaderDeal extends StatelessWidget {
  const HeaderDeal({Key? key, required this.deal}) : super(key: key);
  final DealData deal;

  @override
  Widget build(BuildContext context) {
    final WrapperController wrapperController = Get.find();
    final controller = Get.find<DealDetailController>();
    return SliverAppBar(
      backgroundColor: wrapperController.getPrimaryColor(),
      elevation: 5,
      leading: IconButtonWidget(
        image: deal.photo ?? '',
        icon: Icons.arrow_back,
        onPressed: () => Get.back(),
        size: Constants.iconSize(context),
      ),
      actions: [
        IconButton(
            onPressed: () {
              ActiveTransactionFullScreenDialog().show(
                  context: context,
                  transRepo: controller.repoTransaction,
                  dealRepo: controller.repoDeal);
            },
            icon: Obx(
              () => Badge(
                  isLabelVisible: controller.listTransaction.isNotEmpty,
                  label: Text(convertNumber(controller.listTransaction.length)),
                  child: Icon(Icons.receipt_long_outlined,
                      size: Constants.iconSize(context), color: controller.listPaletteColorImage.firstOrNull?.color.changeColorBasedOnBackgroundColor().$1,)),
            )),
        IconButtonWidget(
          image: deal.photo ?? '',
          icon: Icons.share,
          onPressed: () => shareDeal(deal),
          size: Constants.iconSize(context),
        ),
      ],
      pinned: true,
      floating: true,
      expandedHeight: Constants.voucherContainerWidth(context),
      stretch: true,
      flexibleSpace: FlexibleSpaceBar(
        stretchModes: const [StretchMode.zoomBackground],
        background: Hero(
          tag: deal.promotionId.toString(),
          child: SizedBox(
            height: Constants.voucherContainerWidth(context),
            width: double.infinity,
            child: CachedImageWidget(
              imageUrl: deal.photo.toString(),
              fit: BoxFit.fill,
              needColorFromImage: true,
              height: Constants.voucherContainerWidth(context),
              width: double.infinity,
              colorPalette: (value) {
                controller.listPaletteColorImage.value = value;
              },
            ),
          ),
        ),
      ),
    );
  }
}
