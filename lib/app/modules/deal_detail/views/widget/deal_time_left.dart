import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/helper/deal_helper.dart';
import 'package:mobile_crm/app/modules/deal_detail/controllers/deal_detail_controller.dart';
import 'package:mobile_crm/app/utils/toast.dart';
import 'package:mobile_crm/app/widget/button_widget/button_widget.dart';
import 'package:mobile_crm/app/widget/custom_circular_progress_widget.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/values.dart';

class DealTimeLeftWidget extends StatefulWidget {
  const DealTimeLeftWidget({
    Key? key,
    required this.time,
    this.onTimeChanged,
  }) : super(key: key);

  final int time;
  final ValueChanged<Duration>? onTimeChanged;

  @override
  DealTimeLeftWidgetState createState() => DealTimeLeftWidgetState();
}

class DealTimeLeftWidgetState extends State<DealTimeLeftWidget> {
  Timer? _timer;
  Duration _duration = const Duration(seconds: 1);

  void reset() {
    _timer?.cancel();
    var now = DateTime.now();
    var end = DateTime.fromMillisecondsSinceEpoch(widget.time);
    var diff = end.difference(now);
    _duration = Duration(seconds: diff.inSeconds);
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        _duration = _duration - const Duration(seconds: 1);
        if (_duration.inSeconds == 0) {
          timer.cancel();
        }
      });
    });
  }

  @override
  void initState() {
    super.initState();
    reset();
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<DealDetailController>();
    var deal = controller.deal.value;
    (bool, String, bool, bool, Duration) publishDetail =
        DealHelper.isPromoBeenPublished(deal.publishDate ?? 0);
    String timerText =
        '${_duration.inHours.remainder(24).toString().padLeft(2, '0')}:${_duration.inMinutes.remainder(60).toString().padLeft(2, '0')}:${_duration.inSeconds.remainder(60).toString().padLeft(2, '0')}';

    if (_duration.inHours.remainder(24) != 0) {
      timerText =
          '${_duration.inHours.remainder(24).toString().padLeft(2, '0')}:${_duration.inMinutes.remainder(60).toString().padLeft(2, '0')}:${_duration.inSeconds.remainder(60).toString().padLeft(2, '0')}';
    } else {
      timerText =
          '${_duration.inMinutes.remainder(60).toString().padLeft(2, '0')}:${_duration.inSeconds.remainder(60).toString().padLeft(2, '0')}';
    }
    return _duration.inSeconds.isLowerThan(1)
        ? PrimaryButton(
            onPressed: () async => await controller.buyDeal(),
            text: "Buy Deal",
            width: double.infinity,
            type: PrimaryButtonType.type4,
            child: Obx(
              () => (controller.isLoading.value)
                  ? const CustomCircularProgressIndicator()
                  : Text(Strings.buyDeal.tr,
                      style: AppFont.componentLarge
                          .copyWith(color: AppColor.white)),
            ))
        : PrimaryButton(
            onPressed: () {
              Toast.show(Strings.dealsNotYetOpened.tr, type: ToastType.dark);
            },
            text: "",
            width: double.infinity,
            type: PrimaryButtonType.type4,
            child: Text(
                "${publishDetail.$4 ? Strings.availableIn.tr : Strings.availableOn.tr} ${_duration.inDays > 0 ? publishDetail.$2 : timerText}",
                style: AppFont.componentLarge.copyWith(color: AppColor.white)),
          );
  }
}
