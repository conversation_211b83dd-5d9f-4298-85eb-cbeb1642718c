import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/helper/product_helper.dart';
import 'package:mobile_crm/app/modules/screen_wrapper/controller/wrapper_controller.dart';
import 'package:mobile_crm/app/widget/modal_bottom_sheet/app_modal_bottom_sheet.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/values.dart';
import 'package:mobile_crm/data/models/deal_model.dart';
import 'package:mobile_crm/data/models/product_model.dart';

import '../../../../../../data/providers/db/database.dart';
import '../../../../../widget/app_dotted_separator.dart';
import '../../../controllers/deal_detail_controller.dart';

class BodyTerm extends StatelessWidget {
  const BodyTerm({Key? key, required this.deal}) : super(key: key);
  final DealData deal;

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<DealDetailController>();
    final wrapperC = Get.find<WrapperController>();
    String outlet = wrapperC.configApp.value.language?.outlet ?? "Outlet";
    return Column(
      children: [
        5.0.height,
        Align(
          alignment: Alignment.centerLeft,
          child: Text(Strings.terms.tr, style: AppFont.componentSmall),
        ),
        Container(
          padding: const EdgeInsets.symmetric(vertical: 5),
          decoration: const BoxDecoration(
              border: Border(
                  bottom: BorderSide(width: 1, color: AppColor.black10))),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                Strings.availableUntil.tr,
                style: AppFont.componentSmall,
              ),
              Text(
                  (deal.endPromotionDate == null
                      ? "${deal.timeActive?.endPromotionDate.toDate}, ${deal.timeActive?.endPromotionTime?.toTimeFormat}"
                      : "${deal.endPromotionDate.toDate}, ${deal.endPromotionTime?.toTimeFormat}"),
                  style: AppFont.componentSmall)
            ],
          ),
        ),
        deal.maximumRedeem == null
            ? const SizedBox()
            : Container(
                padding: const EdgeInsets.symmetric(vertical: 5),
                decoration: const BoxDecoration(
                    border: Border(
                        bottom: BorderSide(width: 1, color: AppColor.black10))),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(Strings.remainingQuota.tr,
                        style: AppFont.componentSmall),
                    ((deal.maximumRedeem ?? 0) - (deal.totalRedeem ?? 0)) <= 0
                        ? Text(
                            Strings.promoQuotaIsOver.tr,
                            textAlign: TextAlign.center,
                            style: AppFont.componentSmallBold
                                .copyWith(color: AppColor.utilityDanger),
                          )
                        : Text(
                            "${(deal.maximumRedeem ?? 0) - (deal.totalRedeem ?? 0)}",
                            style: AppFont.componentSmall),
                  ],
                ),
              ),
        deal.promoType?.toLowerCase() == DealPromoType.special_price.name
            ? Visibility(
                visible:
                    ProductHelper.filterDuplicateProduct(deal.products ?? [])
                        .isNotEmpty,
                child: InkWell(
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: 5),
                    decoration: const BoxDecoration(
                        border: Border(
                            bottom:
                                BorderSide(width: 1, color: AppColor.black10))),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(Strings.specialPrice.tr,
                            style: AppFont.componentSmall),
                        Row(
                          children: [
                            Text(
                                "${ProductHelper.filterDuplicateProduct(deal.products ?? []).length} Products",
                                style: AppFont.componentSmall),
                            Icon(
                              Icons.keyboard_arrow_right_rounded,
                              color: AppColor.black,
                              size: Constants.iconSizeSmall(context),
                            )
                          ],
                        ),
                      ],
                    ),
                  ),
                  onTap: () {
                    showModalBottomSheet(
                        context: context,
                        isScrollControlled: true,
                        constraints: BoxConstraints(
                            minHeight: Get.size.height * 0.3,
                            maxHeight: Get.size.height * 0.8,
                            minWidth: Constants.defaultBoxConstraints(context),
                            maxWidth: Constants.defaultBoxConstraints(context)),
                        shape: const RoundedRectangleBorder(
                          borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(15.0),
                              topRight: Radius.circular(15.0)),
                        ),
                        builder: (context) {
                          var listProducts =
                              ProductHelper.filterDuplicateProduct(
                                  deal.products ?? []);
                          return Padding(
                            padding: EdgeInsets.symmetric(
                                horizontal: Constants.defaultPadding),
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: <Widget>[
                                Icon(Icons.drag_handle,
                                    size: Constants.iconSizeSmall(context)),
                                Align(
                                  alignment: Alignment.centerLeft,
                                  child: Text(Strings.specialPrice.tr,
                                      style: AppFont.componentSmall),
                                ),
                                Align(
                                  alignment: Alignment.centerLeft,
                                  child: Text("${listProducts.length} Product",
                                      style: AppFont.componentSmall),
                                ),
                                const SizedBox(height: 5),
                                Flexible(
                                  child: ListView.builder(
                                      shrinkWrap: true,
                                      itemCount: listProducts.length,
                                      itemBuilder:
                                          (BuildContext ctxt, int index) {
                                        ProductModel product =
                                            listProducts[index];
                                        return Container(
                                          padding: EdgeInsets.symmetric(
                                              vertical:
                                                  Constants.defaultPadding),
                                          decoration: const BoxDecoration(
                                              border: Border(
                                                  bottom: BorderSide(
                                                      width: 1,
                                                      color:
                                                          AppColor.black10))),
                                          child: Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              Text(
                                                product.name.toString(),
                                                style: AppFont.componentSmall,
                                              ),
                                              product.priceSellPromo == 0
                                                  ? Text(
                                                      "Rp.${product.priceSell.toCurrency}",
                                                      style: AppFont
                                                          .componentSmall)
                                                  : Row(
                                                      children: [
                                                        Text(
                                                            "Rp.${product.priceSell.toCurrency}",
                                                            style: AppFont
                                                                .componentSmall
                                                                .copyWith(
                                                                    decoration:
                                                                        TextDecoration
                                                                            .combine([
                                                                      TextDecoration
                                                                          .lineThrough
                                                                    ]),
                                                                    color: AppColor
                                                                        .black50)),
                                                        5.0.width,
                                                        Text(
                                                            "Rp.${product.pricePromo.toCurrency}",
                                                            style: AppFont
                                                                .componentSmall),
                                                      ],
                                                    )
                                            ],
                                          ),
                                        );
                                      }),
                                ),
                              ],
                            ),
                          );
                        });
                  },
                ),
              )
            : const SizedBox(),
        deal.maxQtyPromo == null
            ? const SizedBox()
            : Container(
                padding: const EdgeInsets.symmetric(vertical: 5),
                decoration: const BoxDecoration(
                    border: Border(
                        bottom: BorderSide(width: 1, color: AppColor.black10))),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      Strings.maxPurchaseAmount.trParams({"count": ""}),
                      style: AppFont.componentSmall,
                    ),
                    Text(
                      "${deal.maxQtyPromo}",
                      style: AppFont.componentSmall,
                    ),
                  ],
                ),
              ),
        Container(
          padding: const EdgeInsets.symmetric(vertical: 5),
          decoration: const BoxDecoration(
              border: Border(
                  bottom: BorderSide(width: 1, color: AppColor.black10))),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(Strings.minimumTransaction.tr,
                  style: AppFont.componentSmall),
              Text("Rp ${deal.minOrder.toCurrency}",
                  style: AppFont.componentSmall),
            ],
          ),
        ),
        Visibility(
          visible: deal.termProduct != null,
          child: Align(
            alignment: Alignment.centerLeft,
            child: InkWell(
              onTapDown: (details) {
                showModalBottomSheet(
                    isScrollControlled: true,
                    constraints: BoxConstraints(
                        minHeight: Get.size.height * 0.3,
                        maxHeight: Get.size.height * 0.8,
                        minWidth: Constants.defaultBoxConstraints(context),
                        maxWidth: Constants.defaultBoxConstraints(context)),
                    context: context,
                    shape: const RoundedRectangleBorder(
                      borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(15.0),
                          topRight: Radius.circular(15.0)),
                    ),
                    builder: (context) {
                      return Padding(
                        padding: EdgeInsets.symmetric(
                            horizontal: Constants.defaultPadding),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: <Widget>[
                            Icon(Icons.drag_handle,
                                color: AppColor.black90,
                                size: Constants.iconSizeSmall(context)),
                            Align(
                              alignment: Alignment.centerLeft,
                              child: Text(Strings.termProduct.tr,
                                  style: AppFont.componentSmallBold),
                            ),
                            const SizedBox(height: 5),
                            Flexible(
                              child: MediaQuery.removePadding(
                                context: context,
                                removeTop: true,
                                child: ListView.builder(
                                  shrinkWrap: true,
                                  itemCount:
                                      ProductHelper.filterDuplicateProduct(
                                              deal.termProduct?.products ?? [])
                                          .length,
                                  itemBuilder: (context, index) {
                                    ProductModel product =
                                        ProductHelper.filterDuplicateProduct(
                                            deal.termProduct?.products ??
                                                [])[index];
                                    return Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      mainAxisAlignment:
                                          MainAxisAlignment.start,
                                      children: [
                                        index == 0 ? 5.0.height : 0.0.height,
                                        Text(product.name ?? '',
                                            style: AppFont.componentSmall),
                                        const Divider(
                                          color: AppColor.black10,
                                        )
                                      ],
                                    );
                                  },
                                ),
                              ),
                            ),
                          ],
                        ),
                      );
                    });
              },
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  5.0.height,
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Column(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            Strings.termProduct.tr,
                            style: AppFont.componentSmall,
                          ),
                          Text(
                            Strings.minPurchaseAmount.trParams(
                                {"count": "${deal.termProduct?.qty}"}),
                            style: AppFont.paragraphSmall,
                          ),
                        ],
                      ),
                      Row(
                        children: [
                          Text(
                            "${ProductHelper.filterDuplicateProduct(deal.termProduct?.products ?? []).length} Products",
                            style: AppFont.componentSmall,
                          ),
                          Icon(
                            Icons.keyboard_arrow_right_rounded,
                            color: AppColor.black,
                            size: Constants.iconSizeSmall(context),
                          )
                        ],
                      )
                    ],
                  ),
                  const Divider(
                    color: AppColor.black10,
                  )
                ],
              ),
            ),
          ),
        ),
        InkWell(
          onTapDown: (details) =>
              ShowCustomModalBottom.showModalBottomRedeemPeriod(
                  context: context, deal: controller.deal.value),
          child: Container(
            padding: const EdgeInsets.symmetric(vertical: 5),
            decoration: const BoxDecoration(
                border: Border(
                    bottom: BorderSide(width: 1, color: AppColor.black10))),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(Strings.redeemPeriod.tr, style: AppFont.componentSmall),
                Row(
                  children: [
                    Text(
                      controller.onDays,
                      style: AppFont.componentSmall,
                    ),
                    Icon(
                      Icons.keyboard_arrow_right_rounded,
                      color: AppColor.black,
                      size: Constants.iconSizeSmall(context),
                    )
                  ],
                )
              ],
            ),
          ),
        ),
        Visibility(
          visible: deal.outlet?.isNotEmpty == true,
          child: InkWell(
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 5),
              decoration: const BoxDecoration(
                  border: Border(
                      bottom: BorderSide(width: 1, color: AppColor.black10))),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(Strings.availableAt.trParams({"outlet": outlet}),
                      style: AppFont.componentSmall),
                  Row(
                    children: [
                      Text("${deal.outlet?.length} $outlet's",
                          style: AppFont.componentSmall),
                      Icon(
                        Icons.keyboard_arrow_right_rounded,
                        color: AppColor.black,
                        size: Constants.iconSizeSmall(context),
                      )
                    ],
                  ),
                ],
              ),
            ),
            onTap: () {
              showModalBottomSheet(
                  isScrollControlled: true,
                  constraints: BoxConstraints(
                      minHeight: Get.size.height * 0.3,
                      maxHeight: Get.size.height * 0.8,
                      minWidth: Constants.defaultBoxConstraints(context),
                      maxWidth: Constants.defaultBoxConstraints(context)),
                  context: context,
                  shape: const RoundedRectangleBorder(
                    borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(15.0),
                        topRight: Radius.circular(15.0)),
                  ),
                  builder: (context) {
                    return Padding(
                      padding: EdgeInsets.symmetric(
                          horizontal: Constants.defaultPadding),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: <Widget>[
                          Icon(Icons.drag_handle,
                              size: Constants.iconSizeSmall(context)),
                          Align(
                            alignment: Alignment.centerLeft,
                            child: Text(
                                Strings.availableAt
                                    .trParams({"outlet": outlet}),
                                style: AppFont.componentSmall),
                          ),
                          Align(
                            alignment: Alignment.centerLeft,
                            child: Text("${deal.outlet?.length} $outlet's",
                                style: AppFont.componentSmallBold),
                          ),
                          5.0.height,
                          const DottedDivider(
                            color: AppColor.black10,
                            width: 3,
                            height: 1,
                          ),
                          5.0.height,
                          Flexible(
                            child: ListView.builder(
                                shrinkWrap: true,
                                itemCount: deal.outlet?.length ?? 0,
                                itemBuilder: (BuildContext ctxt, int index) {
                                  return Container(
                                    padding: EdgeInsets.symmetric(
                                        vertical: Constants.defaultPadding),
                                    decoration: const BoxDecoration(
                                        border: Border(
                                            bottom: BorderSide(
                                                width: 1,
                                                color: AppColor.black10))),
                                    child: Text(
                                      deal.outlet![index].name.toString(),
                                      style: AppFont.componentSmall,
                                    ),
                                  );
                                }),
                          ),
                        ],
                      ),
                    );
                  });
            },
          ),
        ),
      ],
    );
  }
}
