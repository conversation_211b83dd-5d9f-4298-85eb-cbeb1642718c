import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/deal_detail/controllers/deal_detail_controller.dart';
import 'package:mobile_crm/app/modules/screen_wrapper/controller/wrapper_controller.dart';
import 'package:mobile_crm/app/widget/button_widget/button_widget.dart';
import 'package:mobile_crm/app/widget/html_widget.dart';
import 'package:mobile_crm/core/extensions/int_extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/app_strings.dart';
import 'package:mobile_crm/data/providers/db/database.dart';
import 'package:flutter_html/flutter_html.dart';


class BodyTitle extends StatelessWidget {
  const BodyTitle({Key? key, required this.deal}) : super(key: key);
  final DealData deal;

  @override
  Widget build(BuildContext context) {
    final c = Get.find<WrapperController>();
    final controller = Get.find<DealDetailController>();
    ConfigData configModel = c.configApp.value;
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Flexible(
              flex: 5,
              child: Text(deal.name.toString(), style: AppFont.componentSmall),
            ),
            const SizedBox(),
            Flexible(
              flex: 3,
              child: Align(
                  alignment: Alignment.centerRight,
                  child: deal.voucherPriceType == "money"
                      ? Text(
                          "Rp ${deal.dealsValue?.toCurrency}",
                          style: AppFont.componentSmall,
                        )
                      : deal.voucherPriceType == "point"
                          ? Text(
                              "${deal.dealsValue} ${configModel.language?.point} Point",
                              style: AppFont.componentSmall,
                    textAlign: TextAlign.right,
                            )
                          : deal.voucherPriceType == "free"
                              ? Obx(() {
                                  return PrimaryButton(
                                    onPressed: () {},
                                    text: Strings.free.tr,
                                    color: controller.listPaletteColorImage
                                        .firstOrNull?.color,
                                    width: AppDimen.h64 + AppDimen.h10,
                                  );
                                })
                              : const SizedBox()),
            ),
          ],
        ),
        deal.term == null
            ? const SizedBox()
            : Html(
              data: deal.term ?? '',
              style: {
                "*": AppFont.htmlStyle.copyWith(
                  fontSize: FontSize(12.0)
                )
              },
              ),
        // deal.term == null
        //     ? const SizedBox()
        //     : HtmlWidget(
        //         data: deal.term ?? '',
        //         fontSize: 14,
        //       ),
      ],
    );
  }
}
