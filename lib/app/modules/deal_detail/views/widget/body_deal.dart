import 'package:flutter/material.dart';
import 'package:mobile_crm/core/values/values.dart';

import '../../../../../data/providers/db/database.dart';
import 'body/body_term.dart';
import 'body/body_title.dart';

class BodyDeal extends StatelessWidget {
  const BodyDeal({Key? key, required this.deal}) : super(key: key);
  final DealData deal;

  @override
  Widget build(BuildContext context) {
    return SliverToBoxAdapter(
      child: Container(
        padding: EdgeInsets.all(Constants.defaultPadding),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            BodyTitle(deal: deal),
            BodyTerm(deal: deal),
          ],
        ),
      ),
    );
  }
}
