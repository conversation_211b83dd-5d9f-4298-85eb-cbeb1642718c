import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/screen_wrapper/views/app_page_wrapper.dart';
import 'package:mobile_crm/app/utils/logger.dart';
import 'package:mobile_crm/app/widget/app_bar_widget/app_bar_widget.dart';
import 'package:mobile_crm/app/widget/app_page_empty.dart';
import 'package:mobile_crm/app/widget/custom_circular_progress_widget.dart';
import 'package:mobile_crm/core/theme/app_color.dart';
import 'package:mobile_crm/core/values/values.dart';

import '../../../../data/providers/db/database.dart';
import '../controllers/deal_detail_controller.dart';
import 'widget/deal_widget.dart';

class DealDetailScreen extends GetView<DealDetailController> {
  const DealDetailScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return PageWrapper(
      bottomNavigationBar: const FooterDeal(),
      child: Stack(
        children: [
          StreamBuilder(
              stream: controller.onCurrentDealModelChanged,
              builder: (BuildContext context, snapshot) {
                infoLogger("OnCurrentDeal", "$snapshot");
                if (snapshot.hasData) {
                  return (snapshot.data?.name != null)
                      ? CustomScrollView(
                          shrinkWrap: true,
                          slivers: [
                            HeaderDeal(deal: snapshot.data ?? DealData()),
                            BodyDeal(deal: snapshot.data ?? DealData()),
                          ],
                        )
                      : Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            const AppBarWidget(title: "Deal Detail"),
                            Flexible(
                              child: AppPageEmpty(
                                reason: Strings.valueNotFound
                                    .trParams({'value': "Deal"}),
                                buttonText: Strings.back.tr,
                                func: () => Get.back(),
                              ),
                            ),
                          ],
                        );
                }

                if (!snapshot.hasData) {
                  return Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const AppBarWidget(title: "Deal Detail"),
                      Flexible(
                        child: AppPageEmpty(
                          reason:
                              Strings.valueNotFound.trParams({'value': "Deal"}),
                          buttonText: Strings.back.tr,
                          func: () => Get.back(),
                        ),
                      ),
                    ],
                  );
                }

                if (snapshot.hasError) {
                  return AppPageEmpty(
                    func: () => Get.back(),
                    buttonText: Strings.back.tr,
                    reason: Strings.errorReason.tr,
                  );
                }

                return Column(
                  children: [
                    AppBarWidget(title: snapshot.data?.name ?? 'Deal'),
                    const Expanded(
                        child: CustomCircularProgressIndicator(
                      valueColor: AppColor.black,
                      backgroundColor: AppColor.white,
                    )),
                  ],
                );
              }),
          Obx(() => controller.isLoading.value
              ? const CustomCircularProgressIndicator()
              : const SizedBox())
        ],
      ),
    );
  }
}
