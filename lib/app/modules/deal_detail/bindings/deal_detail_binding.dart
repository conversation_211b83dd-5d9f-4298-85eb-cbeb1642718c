import 'package:get/get.dart';
import 'package:mobile_crm/data/repository/deal_repository.dart';
import 'package:mobile_crm/data/repository/transaction_repository.dart';

import '../controllers/deal_detail_controller.dart';

class DealDetailBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => DealRepositoryIml());
    Get.lazyPut(() => TransactionRepositoryIml());
    Get.lazyPut(() => DealDetailController());
  }
}
