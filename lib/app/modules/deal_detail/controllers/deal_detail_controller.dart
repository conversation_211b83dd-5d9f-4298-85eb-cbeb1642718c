import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/helper/deal_helper.dart';
import 'package:mobile_crm/app/utils/app_alert.dart';
import 'package:mobile_crm/app/utils/dbhelper.dart';
import 'package:mobile_crm/app/utils/logger.dart';
import 'package:mobile_crm/app/utils/toast.dart';
import 'package:mobile_crm/app/utils/utils.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/values.dart';
import 'package:mobile_crm/data/models/deal_payment_model.dart';
import 'package:mobile_crm/data/models/server_response.dart';
import 'package:mobile_crm/data/providers/db/database.dart';
import 'package:mobile_crm/data/repository/deal_repository.dart';
import 'package:mobile_crm/data/services/local_storage.dart';
import 'package:mobile_crm/domain/repository/deal_repository.dart';
import 'package:mobile_crm/routes/app_pages.dart';
import 'package:palette_generator/palette_generator.dart';

import '../../../../data/repository/transaction_repository.dart';
import '../../../../domain/repository/transaction_repository.dart';
import '../../../widget/modal_bottom_sheet/pay_deal_modal_sheet.dart';

class DealDetailController extends GetxController {
  final DealRepository repoDeal = Get.find<DealRepositoryIml>();
  final TransactionRepository repoTransaction =
      Get.find<TransactionRepositoryIml>();
  final store = LocalStorageService();
  final db = DatabaseHelper.instance.database;

  TextEditingController phoneNumberController = TextEditingController();
  final StreamController<DealData> _currentDealModelStreamCtrl =
      StreamController<DealData>.broadcast();

  Stream<DealData> get onCurrentDealModelChanged =>
      _currentDealModelStreamCtrl.stream;

  var deal = DealData().obs;

  var paymentDetail = DealPaymentModel().obs;
  var listTransaction = <TransactionDataData>[].obs;
  var listPaletteColorImage = <PaletteColor>[].obs;

  String onDays = '';
  String dealId = '0';

  var isLoading = false.obs;
  var isLoadDeal = false.obs;

  @override
  void onInit() async {
    dealId = Get.parameters['id'] ?? '0';
    await fetchData();
    await getAllTransaction();
    await getAllVoucher();
    super.onInit();
  }

  Future<Color> getColorFromPhoto() async {
    return await Utils.getColorFromImage(
        await onCurrentDealModelChanged.last.then((value) => value.photo) ??
            "");
  }

  void updateStream(DealData deals) {
    deal.value = deals;
    getPromoDays(deals);
    if (_currentDealModelStreamCtrl.isClosed ||
        _currentDealModelStreamCtrl.isBlank == true) {
      return;
    }

    _currentDealModelStreamCtrl.sink.add(deals);
  }

  Future<List<TransactionDataData>> getAllTransaction() async =>
      listTransaction.value =
          await repoTransaction.getAllTransaction(dealRepo: repoDeal);

  Future fetchData() async {
    try {
      isLoadDeal.value = true;
      if (Get.arguments != null) {
        updateStream(Get.arguments);
        getPromoDays(Get.arguments);
        Future.delayed(
          const Duration(milliseconds: 250),
          () {
            refreshData();
          },
        );
      } else {
        await getDealDetail(dealId)
            .whenComplete(() => isLoadDeal.value = false);
      }
      isLoadDeal.value = false;
    } catch (e, s) {
      errorLogger(pos: "DDC", error: e, stackTrace: s);
      isLoadDeal.value = false;
    }
  }

  @override
  void dispose() {
    _currentDealModelStreamCtrl.close();
    super.dispose();
  }

  @override
  void onClose() {
    _currentDealModelStreamCtrl.close();
    super.onClose();
  }

  Future refreshData() async {
    try {
      await getDealDetail(dealId).whenComplete(() => isLoadDeal.value = false);
      getPromoDays(Get.arguments);
      isLoadDeal.value = false;
    } catch (e, s) {
      if (Get.arguments != null) {
        updateStream(Get.arguments);
      }
      errorLogger(pos: "DDC", error: e, stackTrace: s);
      isLoadDeal.value = false;
    }
  }

  Future<List<DealData>> getAllVoucher() async {
    if (store.token == null) {
      return [];
    }
    return await repoDeal.getMyVoucher();
  }

  Future<bool> isTransactionSuccess({TransactionDataData? trans}) async {
    var voucher = await getAllVoucher();
    var isFound = voucher.firstWhereOrNull((element) =>
        element.promotionBuyId == trans?.dealPayment?.promotionBuyId);
    if (isFound != null) {
      await db.dealTransactionDao.deleteDeal(trans?.promoId ?? 0);
    }
    return isFound != null;
  }

  Future<(bool, DealData?)> isUserHaveSameDeals(DealData? dealModel) async {
    if (dealModel == null) {
      return const (false, null);
    }
    var result = await getAllVoucher();
    var dealsFound = result.firstWhereOrNull(
        (element) => element.promotionFkId == dealModel.promotionId);
    return (dealsFound != null, dealsFound);
  }

  Future<(bool, DealData?)> isSuccessBuyDeals(DealData? dealModel) async {
    if (dealModel == null) {
      return const (false, null);
    }
    var result = await getAllVoucher();
    var dealsFound = result.firstWhereOrNull(
        (element) => element.promotionBuyId == dealModel.promotionBuyId);
    return (dealsFound != null, dealsFound);
  }

  (TransactionDataData?, bool) isDealsHaveTransaction(
      {required List<TransactionDataData> listTransaction,
      required DealData dealModel}) {
    if (listTransaction.isEmpty || dealModel == DealData()) {
      return const (null, false);
    }
    var data = listTransaction.firstWhereOrNull((element) =>
        element.dealPayment?.dealDetail?.promotionId == dealModel.promotionId);
    return (data, data != null);
  }

  bool isDealsTransactionExpired({required int? expiredAt}) {
    if (expiredAt == null) {
      return true;
    }
    return isTimeExpired(milliSeconds: expiredAt);
  }

  Future buyDeal() async {
    if (store.token == null) {
      Future.delayed(
          const Duration(seconds: 1), () => Get.toNamed(Routes.LOGIN));
      return Toast.show("login required");
    }

    isLoading.value = true;
    var thisDeals = deal.value;

    (bool, String, bool, bool, Duration) isPublish =
        DealHelper.isPromoBeenPublished(thisDeals.publishDate ?? 0);

    if (!isPublish.$1) {
      isLoading.value = false;
      return Toast.show(Strings.dealsNotYetOpened.tr, type: ToastType.dark);
    }

    // notice user if they had a same deals
    var userHaveThisDeals = await isUserHaveSameDeals(thisDeals);
    if (userHaveThisDeals.$1) {
      isLoading.value = false;
      return AppAlert.showConfirmWarningDialog(
          context: Get.context!,
          actions: [
            TextButton(
                onPressed: () async {
                  Get.back();
                  if ((thisDeals.voucherPriceType?.toLowerCase() == "point") ||
                      (thisDeals.voucherPriceType?.toLowerCase() == "points")) {
                    isLoading.value = false;
                    return AppAlert.showConfirmWarningDialog(
                        context: Get.context!,
                        actions: [
                          TextButton(
                              onPressed: () async =>
                                  {Get.back(), await _buyDeal()},
                              child: Text(
                                Strings.buy.tr,
                                style: AppFont.componentSmall
                                    .copyWith(color: AppColor.white),
                              )),
                          TextButton(
                              onPressed: () => Get.back(),
                              child: Text(Strings.cancel.tr,
                                  style: AppFont.componentSmall
                                      .copyWith(color: AppColor.white))),
                        ],
                        message: thisDeals.dealsValue == null
                            ? Strings.confirmBuyDeal.tr
                            : "${Strings.confirmBuyDeal.tr} ${Strings.costBuyDeal.trParams({
                                    'value': thisDeals.dealsValue.toString()
                                  })}");
                  } else {
                    await _buyDeal();
                  }
                },
                child: Text(
                  Strings.buy.tr,
                  style: AppFont.componentSmall.copyWith(color: AppColor.white),
                )),
            TextButton(
                onPressed: () async {
                  Get.offAndToNamed(Routes.VOUCHERDETAIL(
                      "${userHaveThisDeals.$2?.promotionBuyId ?? 0}"));
                },
                child: Text(Strings.useDeals.tr,
                    style: AppFont.componentSmall
                        .copyWith(color: AppColor.white))),
            TextButton(
                onPressed: () => Get.back(),
                child: Text(Strings.cancel.tr,
                    style: AppFont.componentSmall
                        .copyWith(color: AppColor.white))),
          ],
          message: Strings.alreadyHaveDeal.tr);
    }

    await _buyDeal();
  }

  respondCodeBuyDeal(
      ServerResponse<DealPaymentModel> resp, DealData dealData) async {
    switch (resp.code) {
      case 0:
        isLoading.value = false;
        if (!resp.status) {
          return AppAlert.showErrorDialog(
              Get.context!, "The connection has timed out");
        }
        paymentDetail.value = resp.data ?? DealPaymentModel();
        // paymentDetail.value.dealDetail = dealData;
        paymentDetail.value.setDealDetail(dealData);

        if ((dealData.voucherPriceType?.toLowerCase() == "money") ||
            (dealData.voucherPriceType?.toLowerCase() == "money")) {
          var result = await repoTransaction
              .getPaymentMethod()
              .whenComplete(() => isLoading.value = false);
          return PayBottomSheet.show(
            context: Get.context!,
            deal: dealData,
            dealPost: paymentDetail.value,
            dealRepo: repoDeal,
            paymentMethods: result ?? [],
          );
        } else {
          AppAlert.showThanksDialog(
              context: Get.context!,
              message: Strings.transactionSuccess.tr,
              actions: [
                TextButton(
                    onPressed: () {
                      Get.back();
                      Get.offAndToNamed(Routes.VOUCHER,
                          parameters: {"promoId": "${dealData.promotionId}"});
                    },
                    child: Text(
                      Strings.useDeals.tr,
                      style: AppFont.componentSmall
                          .copyWith(color: AppColor.white),
                    )),
                TextButton(
                    onPressed: () => Get.back(),
                    child: Text(
                      Strings.close.tr,
                      style: AppFont.componentSmall
                          .copyWith(color: AppColor.white),
                    ))
              ],
              barrierDismissible: true);
        }
        break;
      case 70:
        AppAlert.showErrorDialog(Get.context!, resp.message);
        break;
      case 72:
        AppAlert.showErrorDialog(Get.context!, resp.message);
        break;
      case 75:
        AppAlert.showErrorDialog(Get.context!, resp.message);
        break;
      default:
        AppAlert.showErrorDialog(Get.context!, resp.message);
    }
  }

  Future _buyDeal() async {
    isLoading.value = true;
    if (store.user != null) {
      try {
        var thisDeals = deal.value;
        ServerResponse<DealPaymentModel> resp =
            await repoDeal.buyDeals(dealId: thisDeals.promotionId.toString());
        try {
          await respondCodeBuyDeal(resp, thisDeals);
        } catch (e, s) {
          errorLogger(pos: "Error buy deal", error: e, stackTrace: s);
        }
        isLoading.value = false;
      } catch (e, s) {
        errorLogger(pos: 'Deal Detail Controller', error: e, stackTrace: s);
        isLoading.value = false;
      }
    } else {
      isLoading.value = false;
      Get.toNamed(Routes.LOGIN);
      Toast.show("Login First", type: ToastType.info, duration: 3);
    }
  }

  Future getDealDetail(String id) async {
    isLoadDeal.value = true;
    var result = await repoDeal.getOutletDetailPromotion(promotionId: id);
    if (result.status) {
      updateStream(result.data ?? Get.arguments);
    } else {
      _currentDealModelStreamCtrl.close();
    }
    if (result.data != null ||
        ((Get.arguments is DealData?) && Get.arguments != null)) {
      getPromoDays(result.data ?? Get.arguments);
    }
  }

  void getPromoDays(DealData deal) => onDays = DealHelper.getPromoDaysV2(deal);
}
