import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/login/controller/login_controller.dart';
import 'package:mobile_crm/app/modules/login/login_export.dart';
import 'package:mobile_crm/data/repository/auth_repository.dart';
import 'package:mobile_crm/data/repository/helper_repository.dart';

class LoginBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => AuthRepositoryIml());
    Get.lazyPut(() => HelperRepositoryIml());
    Get.lazyPut(() => LostPhoneNumberController());
    Get.lazyPut<LoginController>(() => LoginController());
    Get.lazyPut(() => RegisterController());
  }
}
