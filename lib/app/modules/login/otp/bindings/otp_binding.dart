import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/login/lost_phone_number/controllers/lost_phone_number_controller.dart';
import 'package:mobile_crm/data/repository/auth_repository.dart';
import 'package:mobile_crm/data/repository/helper_repository.dart';
import 'package:mobile_crm/data/repository/user_repository.dart';

import '../../controller/login_controller.dart';
import '../../register/controllers/register_controller.dart';
import '../controllers/otp_controller.dart';

class OtpBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => UserRepositoryIml());
    Get.lazyPut(() => AuthRepositoryIml());
    Get.lazyPut(() => HelperRepositoryIml());
    Get.lazyPut(() => RegisterController(), fenix: true);
    Get.lazyPut(() => LoginController(), fenix: true);
    Get.lazyPut(() => LostPhoneNumberController(), fenix: true);
    Get.lazyPut(() => OtpController(), fenix: true);
  }
}
