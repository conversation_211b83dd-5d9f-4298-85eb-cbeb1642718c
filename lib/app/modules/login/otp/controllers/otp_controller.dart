// ignore_for_file: prefer_typing_uninitialized_variables, duplicate_ignore

import 'dart:async';

import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/exception/auth_exception_handler.dart';
import 'package:mobile_crm/app/utils/logger.dart';
import 'package:mobile_crm/app/utils/toast.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/app_strings.dart';
import 'package:mobile_crm/data/models/otp_model.dart';
import 'package:mobile_crm/data/models/server_response.dart';
import 'package:mobile_crm/data/models/user_code_model.dart';
import 'package:mobile_crm/data/models/user_model.dart';
import 'package:mobile_crm/data/repository/auth_repository.dart';
import 'package:mobile_crm/data/repository/user_repository.dart';
import 'package:mobile_crm/data/services/local_storage.dart';
import 'package:mobile_crm/domain/repository/user_repository.dart';
import 'package:mobile_crm/routes/app_pages.dart';
import 'package:sentry_flutter/sentry_flutter.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../helper/sentry_helper.dart';
import '../../../../utils/app_alert.dart';
import '../../controller/login_controller.dart';
import '../../lost_phone_number/controllers/lost_phone_number_controller.dart';
import '../../register/controllers/register_controller.dart';

class OtpController extends GetxController with WidgetsBindingObserver {
  FirebaseAuth mAuth = FirebaseAuth.instance;
  final store = LocalStorageService();
  final useFirebaseAuth = false;

  // Services
  final UserRepository _repoUser = Get.find<UserRepositoryIml>();
  final AuthRepositoryIml _repoAuth = Get.find<AuthRepositoryIml>();

  // Services
  final RegisterController _registerController = Get.find();
  final LoginController _loginController = Get.find();
  final lostPhoneNumberController = Get.find<LostPhoneNumberController>();
  String verificationCode = "";

  String _otpToken = "";
  String _phone = "";
  var smsOtpController = TextEditingController();

  var authState = "".obs;
  var onChange = 0;
  int retry = 0;
  var isLoading = false.obs;

  var oobToken = "".obs;
  var readyToCheckOobAuth = false.obs;
  var isLoadingOpenWa = false.obs;
  var isLoadingCheckLoginOobWa = false.obs;

  UserModel? user;

  @override
  void onInit() {
    WidgetsBinding.instance.addObserver(this);
    getArgs();
    String currentRoute = Get.currentRoute;
    _phone = store.authPhone ?? '';
    authState.value = store.authState ?? '';
    Sentry.addBreadcrumb(Breadcrumb(
        type: 'debug',
        category: 'user.activity.otp.controller.onInit',
        data: {'phone': _phone},
        level: SentryLevel.debug));
    //log to sentry if state is null or empty
    if (authState.value.isEmpty) {
      SentryHelper.logException(Exception("authState is empty"),
          StackTrace.current);
    }
    super.onInit();
    if (currentRoute != "/login") {
      resendOTP();
    }
  }

  @override
  void onClose() {
    WidgetsBinding.instance.removeObserver(this);
    dispose();
    super.onClose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      infoLogger('App resumed');
      checkLoginWithOob();
    }
    super.didChangeAppLifecycleState(state);
  }

  getArgs() {
    if (Get.arguments.runtimeType == UserModel) {
      user = Get.arguments;
    }
  }

  Future verifyTokenToRegister(String token) async {
    Sentry.addBreadcrumb(Breadcrumb(
        type: 'debug',
        category: 'user.activity.otp.controller.verifyRegister',
        level: SentryLevel.debug));
    try {
      ServerResponse serverResponse = await _registerController.register(token);
      _registerController.statusCodeRegister(serverResponse, token);
    } catch (e, s) {
      await SentryHelper.logException(e, s);
      errorLogger(pos: "OTP CONTROLLER REGISTER", error: e, stackTrace: s);
    }
  }

  void verifyTokenToLogin(String token) async {
    Sentry.addBreadcrumb(Breadcrumb(
        type: 'debug',
        category: 'user.activity.otp.controller.verifyLogin',
        level: SentryLevel.debug));
    try {
      isLoading.value = true;
      ServerResponse serverResponse = await _repoAuth.login(token: token);
      _loginController.statusCodeLogin(serverResponse, token);
    } catch (e, s) {
      errorLogger(pos: "OTP CONTROLLER REGISTER", error: e, stackTrace: s);
    }
  }

  Future<void> resendOTP() async {
    infoLogger("resendOtp, State value: ${authState.value}");
    if (authState.value == 'register') {
      retry += 1;
      Sentry.addBreadcrumb(Breadcrumb(
          type: 'debug',
          category: 'user.activity.otp.controller.verifyPhoneNumber_codeSent',
          data: {"retry_resend_otp": retry},
          level: SentryLevel.debug));
      if (retry >= 10) {
        //retry == 2
        return AppAlert.showConfirmInfoDialog(
            context: Get.context!,
            message: Strings.haveNotReceivedOTP.tr,
            actions: [
              TextButton(
                  onPressed: () async {
                    Get.back();
                    requestOneTimePassword();
                  },
                  child: Text(Strings.resend.tr,
                      style: AppFont.componentSmall
                          .copyWith(color: AppColor.white))),
            ]);
      }

      //retry >= 3
      if (retry > 1) {
        return AppAlert.showConfirmInfoDialog(
            context: Get.context!,
            message: Strings.needHelpWithOTPCode.tr,
            actions: [
              TextButton(
                  onPressed: () async {
                    isLoading.value = true;
                    Get.back();
                    _registerController
                        .registerV2()
                        .whenComplete(() => isLoading.value = false);
                  },
                  child: Text(Strings.sendWhatsappLink.tr,
                      style: AppFont.componentSmall
                          .copyWith(color: AppColor.white))),
              TextButton(
                  onPressed: () async {
                    Get.back();
                    requestOneTimePassword();
                  },
                  child: Text(Strings.resend.tr,
                      style: AppFont.componentSmall
                          .copyWith(color: AppColor.white))),
            ]);
      }
    }
    requestOneTimePassword();
    // infoLogger("resendOtp, should auto send wa");
    // _registerController
    //     .registerV2()
    //     .whenComplete(() => isLoading.value = false);
  }

  requestOneTimePassword() async {
    Sentry.addBreadcrumb(Breadcrumb(
        type: 'debug',
        category: 'user.activity.otp.controller.requestOTP',
        data: {"state": authState.value},
        level: SentryLevel.debug));
    isLoading.value = true;
    _phone = store.authPhone ?? user?.phone ?? "0812";
    infoLogger("Phone", "$_phone ${authState.value} ${store.authPhone}");
    switch (authState.value) {
      case "lostPhone":
        isLoading.value = false;
        break;
      case Strings.authEmail:
        isLoading.value = false;
        break;
      case Strings.authChangePhone:
        isLoading.value = false;
        break;
      default:
        if (useFirebaseAuth) {
          await verifyPhoneNumber();
        } else {
          // Toast.show("can not send sms", type: ToastType.dark);
          requestOtp(_phone.toInternational);
        }
    }
  }

  Future<void> verifyPhoneNumber() {
    return mAuth.verifyPhoneNumber(
      phoneNumber: _phone.toInternational,
      verificationCompleted: (PhoneAuthCredential credential) async {
        // try {
        //   isLoading.value = true;
        //   Sentry.addBreadcrumb(Breadcrumb(
        //       type: 'debug',
        //       category:
        //           'user.activity.otp.controller.verifyPhoneNumber_completed',
        //       data: {'token': "${credential.token}"},
        //       level: SentryLevel.debug));
        //   await mAuth.signInWithCredential(credential).then(
        //     (value) async {
        //       String tokenFirebase =
        //           await FirebaseAuth.instance.currentUser?.getIdToken() ??
        //               '';
        //       await Sentry.configureScope((scope) => scope
        //         ..setTag('state', state.value)
        //         ..setTag('Phone', _phone)
        //         ..setTag('onChange', "$onChange x")
        //         ..setTag('OTP', 'Automatic'));
        //       switch (state.value) {
        //         case "register":
        //           isLoading.value = false;
        //           await verifyRegister(tokenFirebase);
        //           break;
        //         case "login":
        //           isLoading.value = false;
        //           verifyLogin(tokenFirebase);
        //           break;
        //       }
        //     },
        //   );
        // } catch (e, s) {
        //   errorLogger(pos: "FAEE", error: e, stackTrace: s);
        //   Toast.show(FirebaseAuthExceptionHandler.handleException(e),
        //       type: ToastType.error, duration: 3);
        //   await Sentry.captureMessage(
        //       "Capture Verification Complete Failed\nState ${state.value}\n-----===== Info Data =====-----\nName : ${store.authName} ${_registerController.textNameController.value.text.trim()}\nEmail : ${store.authEmail} ${_registerController.textEmailController.value.text.trim()}\nPhone Number : ${store.authPhone}\nKey : ${store.authKey}\nError $e\nStack Trace\n$s");
        //   await Sentry.captureException(
        //     e,
        //     stackTrace: s,
        //     withScope: (scope) {
        //       return scope.setUser(SentryUser(
        //           name: _registerController.textEmailController.value.text
        //               .trim(),
        //           email: _registerController.textEmailController.value.text
        //               .trim(),
        //           data: {"Phone Number": _phone}));
        //     },
        //   );
        // }
      },
      verificationFailed: (FirebaseAuthException e) async {
        Sentry.addBreadcrumb(Breadcrumb(
            type: 'debug',
            category: 'user.activity.otp.controller.verifyPhoneNumber_failed',
            data: {"firebaseAuthException": e},
            level: SentryLevel.debug));
        errorLogger(pos: "FAE", error: e, stackTrace: e.stackTrace);
        Toast.show(FirebaseAuthExceptionHandler.handleException(e),
            type: ToastType.error, duration: 3);
        await Sentry.captureMessage(
            "Capture Verification Failed\nState ${authState.value}\n-----===== Info Data =====-----\nName : ${store.authName} ${_registerController.textNameController.value.text.trim()}\nEmail : ${store.authEmail} ${_registerController.textEmailController.value.text.trim()}\nPhone Number : ${store.authPhone}\nKey : ${store.authKey}\nError $e\nStack Trace\n${e.stackTrace}");
        await Sentry.captureException(
          e,
          stackTrace: e.stackTrace,
          withScope: (scope) {
            return scope.setUser(SentryUser(
                name: _registerController.textEmailController.value.text.trim(),
                email:
                    _registerController.textEmailController.value.text.trim(),
                data: {"Phone Number": _phone}));
          },
        );
        Get.back();
        FocusManager.instance.primaryFocus?.unfocus();
        isLoading.value = false;
      },
      codeSent: (String verificationID, int? resendToken) async {
        Sentry.addBreadcrumb(Breadcrumb(
            type: 'debug',
            category: 'user.activity.otp.controller.verifyPhoneNumber_codeSent',
            data: {"verificationID": verificationID},
            level: SentryLevel.debug));
        verificationCode = verificationID;
        Toast.show(Strings.yourCodeHasBeenSent.tr, type: ToastType.dark);
        isLoading.value = false;
      },
      codeAutoRetrievalTimeout: (String verificationID) {
        Sentry.addBreadcrumb(Breadcrumb(
            type: 'debug',
            category:
                'user.activity.otp.controller.verifyPhoneNumber_codeAutoRetrievalTimeOut',
            data: {"verificationID": verificationID},
            level: SentryLevel.debug));
        verificationCode = verificationID;
        isLoading.value = false;
      },
    );
  }

  verifyCodeOTP(String pin) async {
    await Sentry.configureScope((scope) => scope
      ..setTag('state', authState.value)
      ..setTag('Phone', _phone)
      ..setTag('Pin2', pin)
      ..setTag('onChange', "$onChange x")
      ..setTag('OTP', 'Manual'));
    switch (authState.value) {
      case "lostPhone":
        isLoading.value = true;
        verifyNumberByCodeEmail(pin);
        break;
      case Strings.authChangePhone:
        isLoading.value = true;
        verifyNumberByCodeEmail(pin);
        break;
      default:
        verifyCode(pin);
    }
  }

  verifyNumberByCodeEmail(String pin) async {
    Sentry.addBreadcrumb(Breadcrumb(
        type: 'debug',
        category: 'user.activity.otp.controller.verifyNumberByCodeEmail',
        data: {'pin': pin},
        level: SentryLevel.debug));
    String key = store.authKey ?? '';
    ServerResponse resp = await _repoUser
        .verifyChangePhoneNumber(key: key, code: pin)
        .whenComplete(() => isLoading.value = false);

    switch (resp.status) {
      case true:
        Toast.show(Strings.yourNumberHasBeenChanged.tr,
            type: ToastType.success);
        store.authEmail = null;
        Get.offAllNamed(Routes.LOGIN);
        break;
      case false:
        Toast.show(resp.message.toString(), type: ToastType.error);
        // Get.offAllNamed(Routes.LOGIN);
        break;
    }
  }

  verifyCode(String pin) async {
    if (pin.isBlank == true) {
      Toast.show('Enter Verification Code', type: ToastType.error);
      return;
    }

    Sentry.addBreadcrumb(Breadcrumb(
        type: 'debug',
        category: 'user.activity.otp.controller.verifyCode',
        data: {'pin': pin, 'state': authState.value},
        level: SentryLevel.debug));
    isLoading.value = true;

    if (authState.value == Strings.authEmail) {
      UserCodeModel userCodeModel =
          UserCodeModel(code: pin, key: store.authKey);
      var resp = await _repoUser
          .validateCodeVerification(userCodeModel)
          .whenComplete(() => isLoading.value = false);

      if (resp.status) {
        isLoading.value = false;
        Toast.show(resp.message, type: ToastType.success);
        Get.offNamed(Routes.PROFILE);
        isLoading.value = false;
      } else {
        isLoading.value = false;
        Toast.show(resp.message, type: ToastType.error);
      }
    } else {
      if (!useFirebaseAuth) {
        validateOtpCode(pin);
        return;
      }
      try {
        await mAuth
            .signInWithCredential(PhoneAuthProvider.credential(
                verificationId: verificationCode, smsCode: pin))
            .then((value) async {
          Sentry.addBreadcrumb(Breadcrumb(
              type: 'debug',
              category: 'user.activity.otp.controller.signInWithCredential',
              data: {
                'verificationCode': verificationCode,
                'pin': pin,
                'state': authState.value
              },
              level: SentryLevel.debug));
          if (value.user != null) {
            String tokenFirebase =
                await FirebaseAuth.instance.currentUser?.getIdToken() ?? '';

            switch (authState.value) {
              case Strings.authPhone:
                if (user != null) {
                  ServerResponse res = await _repoUser.updateUserProfile(
                      token: tokenFirebase, user: user ?? UserModel());

                  if (res.status == true) {
                    Toast.show('New Phone Number data update',
                        type: ToastType.success);
                    Get.offNamed(Routes.PROFILE);
                  } else {
                    Toast.show(res.message, type: ToastType.error);
                  }
                }
                break;
              case "register":
                isLoading.value = false;
                verifyTokenToRegister(tokenFirebase);
                break;
              case "login":
                isLoading.value = false;
                verifyTokenToLogin(tokenFirebase);
            }
          }
        });
        isLoading.value = false;
      } on FirebaseAuthException catch (e, s) {
        Sentry.addBreadcrumb(Breadcrumb(
            type: 'debug',
            category: 'user.activity.otp.controller.signInWithCredential',
            data: {
              'verificationCode': verificationCode,
              'pin': pin,
              'state': authState.value,
              'firebaseAuthException': e
            },
            level: SentryLevel.error));
        await Sentry.captureException(
          e,
          stackTrace: s,
          withScope: (p0) {
            p0.setTag("Number Phone", _phone);
            var scope = p0.setTag("PIN", pin);
            p0.setTag("Number Phone", _phone);
            var us = FirebaseAuth.instance.currentUser;
            scope =
                p0.setUser(SentryUser(name: us?.displayName, email: us?.email));
            scope = p0.setExtra("Verification id", verificationCode);
            return scope;
          },
        );
        await Sentry.addBreadcrumb(Breadcrumb(
            type: "login",
            level: SentryLevel.info,
            message:
                "Show message to user \"${FirebaseAuthExceptionHandler.handleException(e)}\" \nVerification code $verificationCode \nPhone Number $_phone"));
        Toast.show(FirebaseAuthExceptionHandler.handleException(e),
            type: ToastType.error, duration: 5);
        isLoading.value = false;
      } catch (e, s) {
        Sentry.addBreadcrumb(Breadcrumb(
            type: 'debug',
            category: 'user.activity.otp.controller.signInWithCredential',
            data: {
              'verificationCode': verificationCode,
              'pin': pin,
              'state': authState.value,
              'firebaseAuthException': e
            },
            level: SentryLevel.error));
        await Sentry.captureException(
          e,
          stackTrace: s,
          withScope: (p0) {
            p0.setTag("Number Phone", _phone);
            var scope = p0.setTag("PIN", pin);
            p0.setTag("Number Phone", _phone);
            var us = FirebaseAuth.instance.currentUser;
            scope =
                p0.setUser(SentryUser(name: us?.displayName, email: us?.email));
            scope = p0.setExtra("Verification id", verificationCode);
            return scope;
          },
        );
        await Sentry.addBreadcrumb(Breadcrumb(
            type: "verify",
            category: "verify.code.otp",
            level: SentryLevel.error,
            message:
                "Verification code $verificationCode \nPhone Number $_phone"));
        Toast.show('Invalid OTP', type: ToastType.error);
        errorLogger(pos: 'OTP Controller', error: e, stackTrace: s);
        isLoading.value = false;
      }
    }
  }

  void loginOobWhatsApp() async {
    isLoadingOpenWa.value = true;
    var resp = await _repoAuth.getOobWhatsApp("");
    infoLogger("data", resp.data);
    if (resp.status) {
      infoLogger("response message", resp.data["message"]);
      String message = resp.data["message"];
      String phone = resp.data["phone_destination"];
      String token = resp.data["token"];
      oobToken.value = token;
      openWhatsApp(phone, message);
    }
    isLoadingOpenWa.value = false;
  }

  void checkLoginWithOob() async {
    isLoadingCheckLoginOobWa.value = true;
    if (oobToken.value != "") {
      var res = await _repoAuth.checkOobWhatsApp(oobToken.value);
      if (res.status) {
        String authToken = res.data["token"];
        authState.value = "login";
        signInWithCustomToken(authToken);
      } else {
        Toast.show(res.message);
      }
    }
    isLoadingCheckLoginOobWa.value = false;
  }

  void openWhatsApp(String phone, String message) async {
    try {
      final url = kIsWeb
          ? "https://wa.me/$phone?text=${Uri.encodeComponent(message)}"
          : "whatsapp://send?phone=$phone&text=${Uri.encodeComponent(message)}";
      await launchUrl(Uri.parse(url));
      readyToCheckOobAuth.value = true;
    } catch (e, s) {
      errorLogger(
          pos: "OTP Controller - open whatsapp", error: e, stackTrace: s);
      if (e.runtimeType == PlatformException) {
        return Toast.show(
          "Failed to open whatsapp.",
          type: ToastType.error,
          duration: 5,
        );
      }

      Toast.show("Failed to open whatsapp. $e");
    }
  }

  signInWithCustomToken(String token) async {
    infoLogger("authstate", authState.value);
    try {
      await FirebaseAuth.instance.signInWithCustomToken(token);
      Toast.show("sign in success", type: ToastType.success, duration: 3);
      String tokenFirebase =
          await FirebaseAuth.instance.currentUser?.getIdToken() ?? '';
      isLoading.value = false;
      if (authState.value == 'register') {
        verifyTokenToRegister(tokenFirebase);
      } else if (authState.value == 'login') {
        verifyTokenToLogin(tokenFirebase);
      } else {
        //log to sentry, can not handle state, use login as default
        SentryHelper.logException(
            Exception("can not handle current state! $authState"), StackTrace.current);

        verifyTokenToLogin(tokenFirebase);
        Toast.show("can not handle current state!",
            type: ToastType.error, duration: 5);
      }
    } catch (e, s) {
      errorLogger(pos: "SIGN IN CUSTOM TOKEN", error: e, stackTrace: s);
      Toast.show(e.toString(), type: ToastType.error, duration: 5);
    }
  }

  validateOtpCode(String code) async {
    isLoading.value = true;
    try {
      final response = await _repoAuth.validateOtp(_otpToken, code);
      if (response.status) {
        Toast.show("otp is valid", type: ToastType.success, duration: 3);
        signInWithCustomToken(response.data?.token ?? '');
      } else {
        Toast.show(response.message, type: ToastType.error, duration: 5);
      }
    } catch (e, s) {
      errorLogger(pos: "OTP CONTROLLER REGISTER", error: e, stackTrace: s);
      Toast.show(e.toString(), type: ToastType.error, duration: 5);
    }
    isLoading.value = false;
    return null;
  }

  void requestOtp(String phone) async {
    Sentry.addBreadcrumb(Breadcrumb(
        type: 'debug',
        category: 'user.activity.otp.controller.requestOtp',
        level: SentryLevel.debug));
    try {
      isLoading.value = true;
      ServerResponse<OtpModel> serverResponse =
          await _repoAuth.requestOtp(phone);
      if (serverResponse.status) {
        _otpToken = serverResponse.data?.token ?? '';
        Toast.show(Strings.yourCodeHasBeenSent.tr, type: ToastType.dark);
      } else {
        Toast.show(serverResponse.message, type: ToastType.error, duration: 10);
      }
      //55: means rate limit exeeded
      if (serverResponse.code == 55) {
        Get.back();
      }
      // _loginController.statusCodeLogin(serverResponse, token);
      // serverResponse.data.token
    } catch (e, s) {
      errorLogger(pos: "OTP CONTROLLER REGISTER", error: e, stackTrace: s);
      Toast.show(e.toString(), type: ToastType.error, duration: 5);
    }
    isLoading.value = false;
  }
}
