import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/login/login_export.dart';
import 'package:mobile_crm/app/utils/toast.dart';
import 'package:mobile_crm/app/widget/button_widget/button_widget.dart';
import 'package:mobile_crm/app/widget/custom_circular_progress_widget.dart';
import 'package:mobile_crm/core/theme/app_color.dart';
import 'package:mobile_crm/core/theme/app_font.dart';
import 'package:mobile_crm/core/values/app_constants.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

class OtpButton extends StatelessWidget {
  const OtpButton({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<OtpController>();
    return SafeArea(
      bottom: true,
      child: Padding(
          key: const Key('submitCodeOTPButtonKey'),
          padding: EdgeInsets.symmetric(horizontal: Constants.defaultPadding),
          child: PrimaryButton(
            text: 'Confirm',
            type: PrimaryButtonType.type4,
            onPressed: () async {
              if (!controller.isLoading.value) {
                Sentry.addBreadcrumb(Breadcrumb(
                    type: 'debug',
                    category: 'user.activity.otp.confirm_button',
                    data: {"pin_input": controller.smsOtpController.text},
                    level: SentryLevel.debug));
                if (controller.smsOtpController.text.length < 6) {
                  return await Toast.show('Invalid OTP', type: ToastType.error);
                }
                controller
                    .verifyCodeOTP(controller.smsOtpController.text.toString());
              }
            },
            width: 150,
            child: Obx(() => (controller.isLoading.value)
                ? const CustomCircularProgressIndicator()
                : Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Confirm',
                        style: AppFont.componentSmall
                            .copyWith(color: AppColor.white),
                      ),
                      Icon(
                        Icons.arrow_circle_right,
                        size: Constants.iconSize(context),
                        color: AppColor.white,
                      )
                    ],
                  )),
          )),
    );
  }
}
