import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/theme/app_font.dart';
import 'package:mobile_crm/core/values/app_constants.dart';
import 'package:mobile_crm/core/values/app_strings.dart';
import 'package:mobile_crm/data/services/local_storage.dart';

class OtpTitle extends StatelessWidget {
  const OtpTitle({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final store = LocalStorageService();
    return Padding(
      padding: EdgeInsets.all(Constants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(Strings.verificationCode.tr, style: AppFont.paragraphLargeBold),
          10.0.height,
          Text(
            Strings.enterCodeSent.trParams({
              'value': (store.authPhone != null)
                  ? Strings.whatsappNumber.tr
                  : Strings.email.tr
            }),
            style: AppFont.paragraphMedium,
          ),
          5.0.height,
          Text(
            store.authPhone ?? (store.authEmail ?? ''),
            style: AppFont.paragraphMedium,
          ),
        ],
      ),
    );
  }
}
