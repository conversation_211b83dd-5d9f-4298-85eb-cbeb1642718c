import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/login/login_export.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/values.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

class OtpTimer extends StatefulWidget {
  const OtpTimer({Key? key}) : super(key: key);

  @override
  State<OtpTimer> createState() => _OtpTimerState();
}

class _OtpTimerState extends State<OtpTimer> {
  Timer? countdownTimer;
  Duration myDuration = const Duration(seconds: 60);

  @override
  void initState() {
    resetTimer();
    super.initState();
  }

  @override
  void dispose() {
    countdownTimer?.cancel();
    super.dispose();
  }

  void startTimer() {
    countdownTimer =
        Timer.periodic(const Duration(seconds: 1), (_) => setCountDown());
  }

  void stopTimer() {
    setState(() => countdownTimer?.cancel());
  }

  void resetTimer() {
    stopTimer();
    setState(() => myDuration = const Duration(seconds: 30));
    startTimer();
  }

  void setCountDown() {
    const reduceSecondsBy = 1;
    setState(() {
      final seconds = myDuration.inSeconds - reduceSecondsBy;
      if (seconds < 0) {
        countdownTimer?.cancel();
      } else {
        myDuration = Duration(seconds: seconds);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    String strDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = strDigits(myDuration.inMinutes.remainder(60));
    final seconds = strDigits(myDuration.inSeconds.remainder(60));
    if (myDuration.inSeconds.isEqual(0)) {
      return InkWell(
        onTap: () {
          Sentry.addBreadcrumb(Breadcrumb(
              type: 'debug',
              category: 'user.activity.otp.resend_button',
              level: SentryLevel.debug));
          Get.find<OtpController>().resendOTP();
          resetTimer();
        },
        child: RichText(
            text: TextSpan(
                text: Strings.resend.tr, style: AppFont.paragraphSmallBold)),
      );
    }
    return Center(
      child: Text(
        '$minutes:$seconds',
        style: AppFont.componentSmall,
      ),
    );
  }
}
