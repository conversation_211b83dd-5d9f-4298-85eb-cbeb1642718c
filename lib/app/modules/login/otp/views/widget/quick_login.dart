import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mobile_crm/app/modules/login/view/widget/send_chat.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/theme/app_dimen.dart';
import 'package:mobile_crm/core/theme/app_font.dart';

class QuickLoginWidget extends StatelessWidget {
  const QuickLoginWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        AppDimen.h8.height,
        GestureDetector(
          onTap: () {
            const SendChat().toModalBottomSheet.of(context);
          },
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                "Need a quicker Login? send Chat",
                style: AppFont.componentSmallBold.copyWith(color: Colors.blue),
              ),
              Icon(
                Icons.arrow_right_alt,
                size: 14.sp,
                color: Colors.blue,
              ),
            ],
          ),
        ),
        AppDimen.h4.height,
      ],
    );
  }
}
