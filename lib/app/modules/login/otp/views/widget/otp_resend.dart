import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/app_strings.dart';

import 'timer_widget.dart';

class OtpResend extends StatelessWidget {
  const OtpResend({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        RichText(
          text: TextSpan(
              text: Strings.didntReceiveCode.tr, style: AppFont.paragraphSmall),
        ),
        5.0.height,
        const OtpTimer()
      ],
    );
  }
}
