import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/screen_wrapper/controller/wrapper_controller.dart';
import 'package:mobile_crm/app/widget/cached_image_widget.dart';
import 'package:mobile_crm/data/models/config_model.dart';

class OtpAppBar extends StatelessWidget {
  const OtpAppBar({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<WrapperController>();
    Asset asset = controller.configApp.value.asset ?? Asset();
    return AppBar(
      title: const Text('OTP'),
      backgroundColor: controller.getPrimaryColor(),
      flexibleSpace: Builder(
        builder: (context) => SafeArea(
          child: SizedBox(
            height: kToolbarHeight,
            width: double.infinity,
            child: CachedImageWidget(
              fit: BoxFit.cover,
              repeat: ImageRepeat.repeat,
              imageUrl: asset.toolbar_background.toString(),
            ),
          ),
        ),
      ),
    );
  }
}
