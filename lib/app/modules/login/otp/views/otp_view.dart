import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/login/otp/views/widget/quick_login.dart';
import 'package:mobile_crm/app/modules/screen_wrapper/views/app_page_wrapper.dart';
import 'package:mobile_crm/app/widget/app_bar_widget/app_bar_widget.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';

import '../../../../../core/values/app_strings.dart';
import '../controllers/otp_controller.dart';
import 'widget/otp_widget.dart';

class OtpScreen extends GetView<OtpController> {
  const OtpScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    return PageWrapper(
      child: SingleChildScrollView(
          child: SizedBox(
        height: size.height,
        child: Stack(
          children: [
            Column(
              children: [
                AppBarWidget(title: Strings.verification.tr),
                // Spacer(),
                // Container(
                //   width: AppDimen.h64,
                //   height: AppDimen.h64,
                //   padding: const EdgeInsets.all(4),
                //   decoration: BoxDecoration(
                //       color: wrapperController.getColor()?.primary != null
                //           ? HexColor(
                //               wrapperController.getColor()?.primary ?? '')
                //           : AppColor.black90,
                //       shape: BoxShape.circle),
                //   child:
                //       Icon(Icons.phonelink_lock_outlined, size: AppDimen.h28),
                // ),
                // Spacer(),
                10.0.height,
                const OtpTitle(),
                30.0.height,
                const OtpInput(),
                30.0.height,
                const OtpResend(),
                const QuickLoginWidget(),
                const Spacer(),
                const OtpButton(),
                15.0.height
              ],
            ),
          ],
        ),
      )),
    );
  }
}
