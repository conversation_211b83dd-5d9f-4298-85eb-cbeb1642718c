import 'dart:async';
import 'dart:convert';
import 'dart:math';

import 'package:crypto/crypto.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:mobile_crm/app/exception/auth_exception_handler.dart';
import 'package:mobile_crm/app/helper/sentry_helper.dart';
import 'package:mobile_crm/app/utils/app_alert.dart';
import 'package:mobile_crm/app/utils/logger.dart';
import 'package:mobile_crm/app/utils/toast.dart';
import 'package:mobile_crm/app/widget/modal_bottom_sheet/login_modal_bottom_sheet.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/app_strings.dart';
import 'package:mobile_crm/data/models/auth_dummy_model.dart';
import 'package:mobile_crm/data/models/auth_token_model.dart';
import 'package:mobile_crm/data/models/server_response.dart';
import 'package:mobile_crm/data/repository/auth_repository.dart';
import 'package:mobile_crm/data/repository/user_repository.dart';
import 'package:mobile_crm/data/services/analytics_service.dart';
import 'package:mobile_crm/data/services/local_storage.dart';
import 'package:mobile_crm/domain/repository/auth_repository.dart';
import 'package:mobile_crm/domain/repository/user_repository.dart';
import 'package:mobile_crm/firebase_options.dart';
import 'package:mobile_crm/routes/app_pages.dart';
import 'package:sentry_flutter/sentry_flutter.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';
import 'package:url_launcher/url_launcher.dart';

class LoginController extends GetxController {
  final FirebaseAuth _firebaseAuth = FirebaseAuth.instance;
  late GoogleSignIn _googleSignIn;

  final phoneNumberController = TextEditingController();
  final passwordController = TextEditingController();

  final AuthRepository repoAuth = Get.find<AuthRepositoryIml>();
  final UserRepository _repoUser = Get.find<UserRepositoryIml>();
  final store = LocalStorageService();

  final String demoAccountPhone = "***************";
  final String demoAccountPassword = "fFG5KNvx";
  String loginMethod = "";

  ServerResponse? testingResultResponse;
  var isLoading = false.obs;
  var isGoogleLoading = false.obs;
  var isAppleLoading = false.obs;

  var authState = "login";

  int outletId = 0;

  User? user;

  @override
  void onInit() {
    _googleSignIn = GoogleSignIn(
        clientId:
            kIsWeb ? DefaultFirebaseOptions.android.androidClientId : null);
    store.authPhone = null;
    if (Get.arguments.runtimeType == int) {
      outletId = Get.arguments;
    }
    Sentry.addBreadcrumb(Breadcrumb(
        type: 'debug',
        category: 'user.activity.login.init',
        level: SentryLevel.debug));
    super.onInit();
  }

  String? getRedirectURL() {
    final url = Get.parameters['redirect'];
    if (url?.contains('profile') == true) {
      return null;
    }
    return url;
  }

  Future<bool> checkExistingPhone() async {
    isLoading.value = true;
    try {
      ServerResponse serverResponse = await _repoUser
          .checkExistingUserByNumberOrEmail(
              phoneNumber: phoneNumberController.text)
          .whenComplete(() => isLoading.value = false);

      Sentry.addBreadcrumb(Breadcrumb(
          type: 'debug',
          category: 'user.activity.login.controller.checkExistingPhone',
          data: {
            "status": serverResponse.status,
            "code": serverResponse.code,
            "message": serverResponse.message
          },
          level: SentryLevel.debug));

      if (serverResponse.code == 54) {
        return false;
      } else if (serverResponse.code == 52) {
        AppAlert.showInfoDialog(
            Get.context!, "Sorry, you account has been banned!");
        return true;
      }
      return serverResponse.status;
    } catch (e, s) {
      Toast.show(e.toString(), type: ToastType.error);
      await SentryHelper.logException(e, s);
    }
    return true;
  }

  void loginWithPhone(String phone) async {
    phone = phone.toInternational;
    Sentry.addBreadcrumb(
      Breadcrumb(
        type: 'debug',
        category: 'user.activity.login.controller.loginWithPhone',
        data: {'phone': phone},
        level: SentryLevel.debug,
      ),
    );
    isLoading.value = true;
    ServerResponse response = await _repoUser
        .checkExistingUserByNumberOrEmail(phoneNumber: phone)
        .whenComplete(() => isLoading.value = false);
    loginMethod = "Login With Phone";
    AnalyticsService.loginMethod(method: loginMethod);
    switch (response.code) {
      case 0:
        store.authPhone = phone;
        Toast.show(response.message, type: ToastType.error);
        break;
      case 1:
        phone = phone.toInternational;
        store.authPhone = phone;
        store.authState = authState;
        Get.toNamed(Routes.OTP,
            parameters: getRedirectURL() == null
                ? null
                : {'redirect': (getRedirectURL() ?? '/')});
        break;
      case 54:
        const LoginBottomSheet(type: BottomSheetWidgetType.loginDummy)
            .toModalBottomSheetNoMinHeight
            .of(Get.context!);
        break;
      default:
        isLoading.value = false;
    }
    isLoading.value = false;
  }

  Future signInWithDummy() async {
    Sentry.addBreadcrumb(Breadcrumb(
        type: 'debug',
        category: 'user.activity.login.controller.signInWithDummy',
        level: SentryLevel.debug));
    isLoading.value = true;
    try {
      loginMethod = "Login With Dummy";
      AnalyticsService.loginMethod(method: loginMethod);
      if (passwordController.text != demoAccountPassword) {
        Toast.show(Strings.thePasswordIncorrect.tr, type: ToastType.error);
        Get.back();
        passwordController.text = '';
        isLoading.value = false;
        return;
      }

      ServerResponse resp =
          await repoAuth.login(token: "$demoAccountPhone:$demoAccountPassword");
      testingResultResponse = resp;
      final data = resp.data;
      var token = AuthDummyModel.fromJson(data);
      store.token = token.token?.token ?? '';
      isLoading.value = false;
      Get.offAllNamed(getRedirectURL() ?? Routes.HOME);
      isLoading.value = false;
    } catch (e, s) {
      isLoading.value = false;
      errorLogger(
          pos: 'LOGIN CONTROLLER _ LOGIN DUMMY', error: e, stackTrace: s);
    }
  }

  Future signInWithGoogle() async {
    Sentry.addBreadcrumb(Breadcrumb(
        type: 'debug',
        category: 'user.activity.login.controller.signInWithGoogle',
        level: SentryLevel.debug));
    isGoogleLoading.value = true;
    try {
      loginMethod = "Login With Google";
      AnalyticsService.loginMethod(method: loginMethod);
      final GoogleSignInAccount? googleSignInAccount =
          await _googleSignIn.signIn();

      if (googleSignInAccount != null) {
        final GoogleSignInAuthentication googleSignInAuthentication =
            await googleSignInAccount.authentication;

        final credential = GoogleAuthProvider.credential(
          accessToken: googleSignInAuthentication.accessToken,
          idToken: googleSignInAuthentication.idToken,
        );

        final UserCredential userCredential =
            await _firebaseAuth.signInWithCredential(credential);

        user = userCredential.user;
        var tokenFirebase = await _firebaseAuth.currentUser?.getIdToken() ?? '';

        try {
          ServerResponse serverResponse =
              await repoAuth.login(token: tokenFirebase);
          isGoogleLoading.value = false;
          statusCodeLogin(serverResponse, tokenFirebase);
        } catch (e, s) {
          await sentryCustomSetTag(
              state: "Login With google",
              email: _firebaseAuth.currentUser?.email ?? 'null',
              phone: phoneNumberController.text,
              name: _firebaseAuth.currentUser?.displayName ?? '');
          await SentryHelper.logException(e, s);

          _googleSignIn.signOut();
          isGoogleLoading.value = false;
        }
      } else {
        _googleSignIn.signOut();
        Toast.show("Sign In With Google canceled by user",
            type: ToastType.info);
        isGoogleLoading.value = false;
      }
      isGoogleLoading.value = false;
    } on FirebaseAuthException catch (e) {
      await sentryCustomSetTag(
          state: "Login With google",
          email: _firebaseAuth.currentUser?.email ?? 'null',
          phone: phoneNumberController.text,
          name: _firebaseAuth.currentUser?.displayName ?? '');
      isLoading.value = true;
      await SentryHelper.logException(e, e.stackTrace);
      await Sentry.addBreadcrumb(Breadcrumb(
          type: "login",
          level: SentryLevel.info,
          message:
              "Show message to user \"${FirebaseAuthExceptionHandler.handleException(e)}\""));
      Toast.show(FirebaseAuthExceptionHandler.handleException(e),
          type: ToastType.error, duration: 5);
      errorLogger(pos: "AUTH", error: e);
      isLoading.value = false;
    } catch (e, s) {
      _googleSignIn.signOut();
      await sentryCustomSetTag(
          state: "Login With google",
          email: _firebaseAuth.currentUser?.email ?? 'null',
          phone: phoneNumberController.text,
          name: _firebaseAuth.currentUser?.displayName ?? '');
      await SentryHelper.logException(e, s);
      await Sentry.addBreadcrumb(Breadcrumb(
          type: "login",
          level: SentryLevel.info,
          message:
              "Show message to user \"Sign in with google is not available\""));
      errorLogger(pos: 'Login Controller', error: e, stackTrace: s);
      Toast.show("Login with Google is not available", type: ToastType.error);
      isGoogleLoading.value = false;
    }
  }

  Future sendAuthLink() async {
    Sentry.addBreadcrumb(Breadcrumb(
        type: 'debug',
        category: 'user.activity.login.controller.sendAuthLink',
        level: SentryLevel.debug));
    isLoading.value = true;
    ServerResponse<AuthTokenModel> serv = await repoAuth
        .loginWithLink(phone: phoneNumberController.text.toInternational)
        .whenComplete(() => isLoading.value = false);
    Get.back();

    testingResultResponse = serv;
    if (serv.status) {
      String phoneNumber = serv.data?.waSender ?? "0";
      if (!Get.testMode) {
        openWhatsapp(phoneNumber);
      }
      Toast.show("Code has been sent via whatsapp",
          type: ToastType.success, duration: 5);
    } else {
      await Sentry.captureMessage(
          "Capture Login With Link\nToast Show ${serv.message}\nResponse from server ${serv.toJson((p0) => p0)}",
          level: SentryLevel.error);
      Toast.show(serv.message,
          type: serv.status ? ToastType.dark : ToastType.error, duration: 5);
    }
  }

  Future<void> openWhatsapp(String phoneNumber) async {
    Sentry.addBreadcrumb(Breadcrumb(
        type: 'debug',
        category: 'user.activity.login.controller.openWhatsApp',
        data: {'phone': phoneNumber},
        level: SentryLevel.debug));
    try {
      String whatsappURl = "whatsapp://send/?phone=$phoneNumber";
      await launchUrl(Uri.parse(whatsappURl));
    } catch (e, s) {
      errorLogger(pos: "LC - Open whatsapp", error: e, stackTrace: s);
      if (e.runtimeType == PlatformException) {
        return AppAlert.showInfoDialog(Get.context!, Strings.whatsAppWontOpen);
      }
    }
  }

  Future singInWithEmailLink(String link) async {
    Sentry.addBreadcrumb(Breadcrumb(
        type: 'debug',
        category: 'user.activity.login.controller.signInWithEmailLink',
        level: SentryLevel.debug));
    isLoading.value = true;
    var emailAddress =
        link.substring(link.lastIndexOf('%3D') + 3, link.lastIndexOf('&'));
    if (emailAddress.isNotEmpty) {
      try {
        loginMethod = "Login With Email Link";
        AnalyticsService.loginMethod(method: loginMethod);
        await sentryCustomSetTag(
            state: "Sign In With Link",
            email: _firebaseAuth.currentUser?.email ?? 'null',
            phone: phoneNumberController.text,
            name: _firebaseAuth.currentUser?.displayName ?? '');
        await _firebaseAuth.signInWithEmailLink(
            email: emailAddress, emailLink: link);
        await nextLoginProcess();
      } on FirebaseAuthException catch (e) {
        isLoading.value = true;
        await SentryHelper.logException(e, e.stackTrace);
        await Sentry.addBreadcrumb(Breadcrumb(
            type: "login",
            level: SentryLevel.info,
            message:
                "Show message to user \"${FirebaseAuthExceptionHandler.handleException(e)}\""));
        Toast.show(FirebaseAuthExceptionHandler.handleException(e),
            type: ToastType.error, duration: 5);
        errorLogger(pos: "AUTH", error: e);
        isLoading.value = false;
      } catch (e, s) {
        isLoading.value = true;
        await SentryHelper.logException(e, s);
        await Sentry.addBreadcrumb(Breadcrumb(
            type: "login",
            level: SentryLevel.info,
            message: "Show message to user \"Login Failed, try again later\""));
        Toast.show("Login Failed, try again later",
            type: ToastType.error, duration: 5);
        errorLogger(pos: 'Login Controller', error: e, stackTrace: s);
      }
    } else {
      isLoading.value = false;
      Toast.show("Invalid Link", type: ToastType.warning);
    }
  }

  Future signInWithApple() async {
    Sentry.addBreadcrumb(Breadcrumb(
        type: 'debug',
        category: 'user.activity.login.controller.signInWithApple',
        level: SentryLevel.debug));
    isAppleLoading.value = true;
    try {
      loginMethod = "Login With Apple";
      AnalyticsService.loginMethod(method: loginMethod);
      AppleAuthProvider appleAuthProvider = AppleAuthProvider();
      appleAuthProvider.addScope('name');

      final UserCredential appleSignInAccount =
          await _firebaseAuth.signInWithProvider(appleAuthProvider);
      user = appleSignInAccount.user;
      String tokenFirebase =
          await _firebaseAuth.currentUser?.getIdToken() ?? '';

      try {
        ServerResponse serverResponse =
            await repoAuth.login(token: tokenFirebase);
        isAppleLoading.value = false;
        statusCodeLogin(serverResponse, tokenFirebase);
      } on FirebaseAuthException catch (e, s) {
        await SentryHelper.logException(e, s);
        errorLogger(pos: 'Login Controller', error: e, stackTrace: s);
        isAppleLoading.value = false;
      } catch (e, s) {
        await Sentry.captureMessage(
            "Capture Sign In With Apple\nUser Name ${user?.displayName}\nUser Email ${user?.email}\nUser Email Status ${user?.emailVerified}\nUser Phone Number ${user?.phoneNumber}\nApple auth provider ${appleAuthProvider.parameters} ${appleAuthProvider.scopes}");
        await SentryHelper.logException(e, s);
        errorLogger(pos: 'Login Controller', error: e, stackTrace: s);
        isAppleLoading.value = false;
      }
      isAppleLoading.value = false;
    } catch (e, s) {
      await SentryHelper.logException(e, s);
      await Sentry.addBreadcrumb(Breadcrumb(
          type: "login",
          level: SentryLevel.info,
          message:
              "Show message to user \"Sign In With App is not available\""));
      Toast.show('Sign In With Apple is not available', type: ToastType.error);
      errorLogger(pos: 'Login Controller', error: e, stackTrace: s);
      isAppleLoading.value = false;
    }
  }

  /// Generates a cryptographically secure random nonce, to be included in a
  /// credential request.
  String generateNonce([int length = 32]) {
    const charset =
        '0123456789ABCDEFGHIJKLMNOPQRSTUVXYZabcdefghijklmnopqrstuvwxyz-._';
    final random = Random.secure();
    return List.generate(length, (_) => charset[random.nextInt(charset.length)])
        .join();
  }

  /// Returns the sha256 hash of [input] in hex notation.
  String sha256ofString(String input) {
    final bytes = utf8.encode(input);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  Future<User?> signInWithAppleForIos() async {
    Sentry.addBreadcrumb(Breadcrumb(
        type: 'debug',
        category: 'user.activity.login.controller.signInWithAppleForIos',
        level: SentryLevel.debug));
    isAppleLoading.value = true;
    try {
      loginMethod = "Login With Apple For IOS";
      AnalyticsService.loginMethod(method: loginMethod);
      final rawNonce = generateNonce();
      final nonce = sha256ofString(rawNonce);
      final appleCredential =
          await SignInWithApple.getAppleIDCredential(scopes: [
        AppleIDAuthorizationScopes.email,
        AppleIDAuthorizationScopes.fullName,
      ], nonce: nonce);

      final oauthCredential = OAuthProvider("apple.com").credential(
        idToken: appleCredential.identityToken,
        rawNonce: rawNonce,
      );
      UserCredential userCredential =
          await _firebaseAuth.signInWithCredential(oauthCredential);
      user = userCredential.user;

      String tokenFirebase =
          await _firebaseAuth.currentUser?.getIdToken() ?? '';

      if (appleCredential.givenName != null ||
          appleCredential.familyName != null) {
        await _firebaseAuth.currentUser?.updateDisplayName(
            "${appleCredential.givenName} ${appleCredential.familyName}");
        store.authName =
            "${appleCredential.givenName} ${appleCredential.familyName}";
      }

      try {
        ServerResponse serverResponse =
            await repoAuth.login(token: tokenFirebase);
        isAppleLoading.value = false;
        statusCodeLogin(serverResponse, tokenFirebase);
      } on FirebaseAuthException catch (e, s) {
        await SentryHelper.logException(e, s);
        await Sentry.addBreadcrumb(Breadcrumb(
            type: "login",
            level: SentryLevel.info,
            message:
                "Show message to user \"${FirebaseAuthExceptionHandler.handleException(e)}\""));
        Toast.show(FirebaseAuthExceptionHandler.handleException(e),
            type: ToastType.error, duration: 5);
        errorLogger(pos: 'Login Controller', error: e, stackTrace: s);
        isAppleLoading.value = false;
      } catch (e, s) {
        await SentryHelper.logException(e, s);
        errorLogger(pos: 'Login Controller', error: e, stackTrace: s);
        isAppleLoading.value = false;
      }
      isAppleLoading.value = false;

      return user;
    } catch (e, s) {
      await SentryHelper.logException(e, s);
      errorLogger(pos: 'pos', error: e, stackTrace: s);
      isAppleLoading.value = false;
    }
    return null;
  }

  Future<void> statusCodeLogin(ServerResponse resp, String token) async {
    Sentry.addBreadcrumb(Breadcrumb(
        type: 'debug',
        category: 'user.activity.login.controller.statusCodeLogin',
        data: {'status': resp.code},
        level: SentryLevel.debug));
    testingResultResponse = resp;
    switch (resp.code) {
      case 50:
        await _firebaseAuth.signOut();
        await _googleSignIn.signOut();
        AppAlert.showConfirmWarningDialog(
            context: Get.context!,
            message: Strings.memberNotFound.tr,
            actions: [
              TextButton(
                  onPressed: () async {
                    await _googleSignIn.signOut();
                    Get.back();
                  },
                  child: Text(Strings.cancel.tr,
                      style: AppFont.componentSmall
                          .copyWith(color: AppColor.white))),
              TextButton(
                  onPressed: () async {
                    await _googleSignIn.signOut();

                    store.authPhone = phoneNumberController.text;
                    store.authEmail = user?.email;
                    store.authName ??= user?.displayName;
                    Get.toNamed(Routes.REGISTER);
                  },
                  child: Text(Strings.register.tr,
                      style: AppFont.componentSmall
                          .copyWith(color: AppColor.white))),
            ]);
        break;
      case 51:
        AppAlert.showInfoDialog(Get.context!,
            "Sorry, your Membership account has been inactivated!");

        _googleSignIn.signOut();
        Get.back();
        break;
      case 52:
        AppAlert.showInfoDialog(
            Get.context!, "Sorry, you account has been banned!");

        _googleSignIn.signOut();
        Get.back();
        break;
      case 53:
        AppAlert.showConfirmWarningDialog(
            context: Get.context!,
            message: Strings.alreadyRegisterBusiness.tr,
            actions: [
              TextButton(
                  onPressed: () async {
                    _googleSignIn.signOut();
                    Get.back();
                  },
                  child: Text(Strings.cancel.tr,
                      style: AppFont.componentSmall
                          .copyWith(color: AppColor.white))),
              TextButton(
                  onPressed: () async {
                    Get.back();
                    await _repoUser.joinOtherBusiness(token).then((value) {
                      if (value.status) {
                        Toast.show(Strings.successRegisterBusiness.tr,
                            type: ToastType.dark);
                      } else {
                        Toast.show(value.message,
                            type: ToastType.error, duration: 8);
                      }
                    });
                  },
                  child: Text(Strings.register.tr,
                      style: AppFont.componentSmall
                          .copyWith(color: AppColor.white))),
            ]);
        break;
      case 54:
        AppAlert.showInfoDialog(Get.context!,
            "Email invalid or not recognize as a valid email address");
        break;
      case 55:
        AppAlert.showInfoDialog(Get.context!,
            Strings.valueAlreadyVerified.trParams({'value': Strings.email.tr}));
        break;
      default:
        await nextLoginProcess();
        break;
    }
  }

  Future<void> sentryCustomSetTag(
      {required String state,
      required String name,
      required String phone,
      required String email}) async {
    await Sentry.configureScope((scope) => scope
      ..setTag('state', state)
      ..setTag('Login Name', name)
      ..setTag('Login Phone', phone)
      ..setTag('Login Email', email));
  }

  Future nextLoginProcess() async {
    var tokenFirebase = await _firebaseAuth.currentUser?.getIdToken() ?? '';
    var result = await repoAuth.getTokenPhoneAuth(tokenFirebase);
    if (result.status) {
      if (_firebaseAuth.currentUser?.displayName != null) {
        Toast.show('Login as ${_firebaseAuth.currentUser?.displayName}');
        await sentryCustomSetTag(
            state: "User Login",
            email: _firebaseAuth.currentUser?.email ?? 'null',
            phone: phoneNumberController.text,
            name: _firebaseAuth.currentUser?.displayName ?? '');
      }
      AnalyticsService.setUserProperty(
          uId: _firebaseAuth.currentUser?.uid,
          name: _firebaseAuth.currentUser?.displayName,
          email: _firebaseAuth.currentUser?.email,
          phone: _firebaseAuth.currentUser?.phoneNumber,
          loginType: loginMethod);

      store.token = result.data?.token;
      if (outletId != 0) {
        await _repoUser.getUser().whenComplete(() => isLoading.value = false);
        Get.offAllNamed(Routes.CARTDETAIL(outletId.toString()));
      } else {
        isLoading.value = false;
        Get.offAllNamed(getRedirectURL() ?? Routes.HOME);
      }
    } else {
      isLoading.value = false;
      await _firebaseAuth.signOut();
      await _googleSignIn.signOut();
      Sentry.captureMessage(
          "Capture login failed\n${result.toJson((p0) => p0)}",
          level: SentryLevel.error);
      switch (result.code) {
        case 56:
          AppAlert.showConfirmInfoDialog(
              context: Get.context!,
              message: Strings.valueNotVerified
                  .trParams({'value': Strings.whatsappNumber.tr}),
              barrierDismissible: false,
              actions: [
                TextButton(
                    onPressed: () async {
                      try {
                        if (result.data?.phone != null) {
                          loginWithPhone(result.data?.phone ?? "0123");
                        } else {
                          Toast.show(Strings.noPhoneNumberLinkedToThisEmail.tr,
                              type: ToastType.error, duration: 5);
                        }
                        Get.back();
                      } catch (e, s) {
                        Sentry.captureException(e, stackTrace: s);
                        errorLogger(
                            pos: Strings.verifyValue
                                .trParams({'value': Strings.whatsappNumber.tr}),
                            error: e,
                            stackTrace: s);
                      }
                    },
                    child: Text(Strings.verifyNow.tr,
                        style: AppFont.componentSmall
                            .copyWith(color: AppColor.white))),
                TextButton(
                    onPressed: () async {
                      Get.back();
                      AppAlert.showConfirmInfoDialog(
                          context: Get.context!,
                          message: Strings.toVerifyUsedLoginButton.tr,
                          barrierDismissible: false,
                          actions: [
                            TextButton(
                                onPressed: () async {
                                  Get.back();
                                },
                                child: Text(Strings.okay.tr,
                                    style: AppFont.componentSmall
                                        .copyWith(color: AppColor.white))),
                          ]);
                    },
                    child: Text(Strings.later.tr,
                        style: AppFont.componentSmall
                            .copyWith(color: AppColor.white))),
              ]);
          break;
      }
    }
  }
}
