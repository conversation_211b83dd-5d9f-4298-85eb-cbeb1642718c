import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/app_strings.dart';

class LostPhoneNumberTitle extends StatelessWidget {
  const LostPhoneNumberTitle({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(Strings.lostNumber.tr, style: AppFont.paragraphLargeBold),
          10.0.height,
          Text(
            Strings.dontLetDataLost.tr,
            style: AppFont.paragraphMedium,
          ),
          5.0.height,
        ],
      ),
    );
  }
}
