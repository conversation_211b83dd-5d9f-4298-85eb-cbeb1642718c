import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/widget/button_widget/button_widget.dart';
import 'package:mobile_crm/app/widget/custom_circular_progress_widget.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/app_constants.dart';
import 'package:mobile_crm/core/values/app_strings.dart';

import '../../controllers/lost_phone_number_controller.dart';

class LostPhoneNumberFooter extends StatelessWidget {
  const LostPhoneNumberFooter({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<LostPhoneNumberController>();
    return Padding(
      key: const Key('submitLostPhoneNumberButtonKey'),
      padding: EdgeInsets.all(Constants.defaultPadding),
      child: PrimaryButton(
        onPressed: () {
          if (controller.formKey.currentState?.validate() != null) {
            if (controller.formKey.currentState!.validate()) {
              controller.submit();
            }
          }
        },
        text: Strings.next.tr,
        type: PrimaryButtonType.type4,
        child: SizedBox(
          width: MediaQuery.of(context).size.width,
          child: Obx(() => (controller.isLoading.value)
              ? const CustomCircularProgressIndicator()
              : Align(
                  alignment: Alignment.center,
                  child: Text(
                    Strings.next.tr,
                    style:
                        AppFont.componentSmall.copyWith(color: AppColor.white),
                  ),
                )),
        ),
      ),
    );
  }
}
