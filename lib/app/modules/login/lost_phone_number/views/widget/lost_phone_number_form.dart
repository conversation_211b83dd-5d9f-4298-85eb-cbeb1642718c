import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/login/login_export.dart';
import 'package:mobile_crm/app/widget/input_widget/input_form_widget.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/values/app_strings.dart';

class LostPhoneNumberForm extends StatelessWidget {
  const LostPhoneNumberForm({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<LostPhoneNumberController>();
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 32),
      child: Form(
        key: controller.formKey,
        autovalidateMode: AutovalidateMode.always,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            AppInputText(
              key: const Key('inputEmailKey'),
              keyboardType: TextInputType.emailAddress,
              controller: controller.emailController,
              label: Strings.email.tr,
              type: InputFormType.withIcon,
              icon: Icons.person,
              validator: (value) {
                if (value == '') {
                  return Strings.valueCannotEmpty.trParams({"value": "Email"});
                }

                if (!GetUtils.isEmail(value ?? '')) {
                  return Strings.invalidValue.trParams({"value": "Email"});
                }

                return null;
              },
            ),
            10.0.height,
            AppInputText(
              key: const Key('inputPhoneNumberKey'),
              keyboardType: TextInputType.number,
              controller: controller.phoneController,
              label: Strings.phoneNumber.tr,
              type: InputFormType.withIcon,
              icon: Icons.phone,
              validator: (value) {
                if (value == '') {
                  return Strings.valueCannotEmpty
                      .trParams({"value": Strings.phoneNumber.tr});
                }

                if (!GetUtils.isPhoneNumber(value ?? '0')) {
                  return Strings.invalidValue
                      .trParams({"value": Strings.phoneNumber.tr});
                }

                return null;
              },
            ),
          ],
        ),
      ),
    );
  }
}
