import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/screen_wrapper/controller/wrapper_controller.dart';
import 'package:mobile_crm/app/widget/cached_image_widget.dart';
import 'package:mobile_crm/core/extensions/color_extensions.dart';
import 'package:mobile_crm/core/theme/app_color.dart';
import 'package:mobile_crm/core/values/app_constants.dart';

class LostPhoneNumberAppBar extends StatelessWidget {
  const LostPhoneNumberAppBar({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<WrapperController>();
    final size = Constants.logoSize(context);
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
          image: DecorationImage(
              fit: BoxFit.cover,
              repeat: ImageRepeat.repeat,
              image: NetworkImage(controller
                      .configApp.value.asset?.toolbar_background
                      ?.toString() ??
                  '')),
          borderRadius: BorderRadius.circular(25),
          color: (controller.configApp.value.asset?.color?.primary != null)
              ? HexColor(
                  controller.configApp.value.asset!.color!.primary!.toString())
              : AppColor.ink02),
      child: CachedImageWidget(
        fit: BoxFit.contain,
        width: size.width,
        height: size.height,
        imageUrl: controller.configApp.value.asset?.app_icon.toString() ?? '',
      ),
    );
  }
}
