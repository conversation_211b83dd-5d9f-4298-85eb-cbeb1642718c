import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/screen_wrapper/views/app_page_wrapper.dart';
import 'package:mobile_crm/app/widget/app_bar_widget/app_bar_widget.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';

import '../controllers/lost_phone_number_controller.dart';
import 'widget/lost_phone_number_widget.dart';

class LostPhoneNumberScreen extends GetView<LostPhoneNumberController> {
  const LostPhoneNumberScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    return Scaffold(
      body: PageWrapper(
        child: SingleChildScrollView(
          child: <PERSON><PERSON><PERSON><PERSON>(
            height: size.height,
            child: <PERSON>um<PERSON>(
              children: [
                const AppBarWidget(title: "Lost Phone Number"),
                const Spacer(),
                const LostPhoneNumberTitle(),
                25.0.height,
                const LostPhoneNumberForm(),
                const Spacer(),
                const LostPhoneNumberFooter()
              ],
            ),
          ),
        ),
      ),
    );
  }
}
