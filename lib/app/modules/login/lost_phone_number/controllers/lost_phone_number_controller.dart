import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/utils/app_alert.dart';
import 'package:mobile_crm/app/utils/toast.dart';
import 'package:mobile_crm/data/models/server_response.dart';
import 'package:mobile_crm/data/models/user_code_model.dart';
import 'package:mobile_crm/data/repository/user_repository.dart';
import 'package:mobile_crm/data/services/local_storage.dart';
import 'package:mobile_crm/routes/app_pages.dart';

import '../../../../../domain/repository/user_repository.dart';
import '../../../../utils/logger.dart';

class LostPhoneNumberController extends GetxController {
  final UserRepository _repoUser = Get.find<UserRepositoryIml>();
  final store = LocalStorageService();

  var emailController = TextEditingController();
  var phoneController = TextEditingController();
  final formKey = GlobalKey<FormState>();
  var authState = '';

  ServerResponse? testingResultResponse;
  var isLoading = false.obs;

  @override
  void onInit() {
    authState = "lostPhone";
    super.onInit();
  }

  void submit() async {
    if (!GetUtils.isEmail(emailController.text.trim())) {
      Get.back();
      return Toast.show("Invalid Email", type: ToastType.dark);
    }

    if (!GetUtils.isPhoneNumber(phoneController.text.trim())) {
      Get.back();
      return Toast.show("Invalid Phone Number", type: ToastType.dark);
    }

    isLoading.value = true;
    ServerResponse resp = await _repoUser.lostPhoneNumber(
        emailController.text.trim(), phoneController.text.trim());
    testingResultResponse = resp;
    try {
      if (resp.status == true) {
        UserCodeModel userCode = UserCodeModel.fromJson(resp.data);
        store.authKey = userCode.key;
        store.authState = authState;
        store.authEmail = emailController.text.trim();
        isLoading.value = false;
        Get.toNamed(Routes.OTP);
      } else {
        AppAlert.showInfoDialog(Get.context!, resp.message);
        isLoading.value = false;
      }
    } catch (e, s) {
      errorLogger(pos: "pos", error: e, stackTrace: s);
    }
  }
}
