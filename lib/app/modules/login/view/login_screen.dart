import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/screen_wrapper/views/app_page_wrapper.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';

import '../controller/login_controller.dart';
import 'widget/login_footer.dart';
import 'widget/login_form.dart';
import 'widget/login_logo.dart';

class LoginScreen extends GetView<LoginController> {
  const LoginScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    return PageWrapper(
        resizeToAvoidBottomInset: true,
        child: SingleChildScrollView(
            child: SizedBox(
          height: size.height,
          child: Column(
            children: [
              const Spacer(),
              const Login<PERSON>ogo(),
              const Spacer(),
              const LoginForm(),
              const Spacer(),
              const LoginFooter(),
              10.0.height,
            ],
          ),
        )));
  }
}
