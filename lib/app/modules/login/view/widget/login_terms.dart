import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/app_strings.dart';
import 'package:mobile_crm/routes/app_pages.dart';

class LoginTerms extends StatelessWidget {
  final String type;
  const LoginTerms({super.key, required this.type});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: RichText(
        text: TextSpan(
          style: AppFont.paragraphMedium,
          children: [
            type == "register"
                ? TextSpan(text: "${Strings.byRegistering.tr},")
                : TextSpan(text: "${Strings.byEntering.tr},"),
            TextSpan(text: " ${Strings.agreeWith.tr} "),
            TextSpan(
              style: AppFont.paragraphMedium.copyWith(
                fontWeight: FontWeight.bold,
              ),
              text: Strings.termCondition.tr,
              recognizer: TapGestureRecognizer()
                ..onTap = () {
                  Get.toNamed(Routes.TERMS);
                },
            ),
            TextSpan(text: " ${Strings.and.tr} "),
            TextSpan(
              style: AppFont.paragraphMedium.copyWith(
                fontWeight: FontWeight.bold,
              ),
              text: Strings.privacyPolicy.tr,
              recognizer: TapGestureRecognizer()
                ..onTap = () {
                  Get.toNamed(Routes.PRIVACY);
                },
            )
          ],
        ),
      ),
    );
  }
}
