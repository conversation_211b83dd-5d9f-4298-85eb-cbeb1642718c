import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/screen_wrapper/controller/wrapper_controller.dart';
import 'package:mobile_crm/app/widget/cached_image_widget.dart';
import 'package:mobile_crm/core/theme/app_color.dart';
import 'package:mobile_crm/core/values/app_constants.dart';

class LoginLogo extends StatelessWidget {
  const LoginLogo({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<WrapperController>();
    final size = Constants.logoSize(context);
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: controller.configApp.value.asset?.toolbar_background == null
          ? const BoxDecoration()
          : BoxDecoration(
              image: DecorationImage(
                  fit: BoxFit.cover,
                  repeat: ImageRepeat.repeat,
                  image: NetworkImage(controller
                          .configApp.value.asset?.toolbar_background
                          ?.toString() ??
                      '')),
              borderRadius: BorderRadius.circular(25),
              color: (controller.configApp.value.asset?.color?.primary != null)
                  ? controller.getPrimaryColor()
                  : AppColor.ink02),
      child: CachedImageWidget(
        fit: BoxFit.contain,
        width: size.width,
        height: size.height,
        imageUrl: controller.configApp.value.asset?.app_icon.toString() ?? '',
        errorWidget: (context, url, error) => const Text(''),
      ),
    );
  }
}
