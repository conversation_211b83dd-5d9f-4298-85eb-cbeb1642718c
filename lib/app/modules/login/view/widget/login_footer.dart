import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/widget/modal_bottom_sheet/login_modal_bottom_sheet.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/app_strings.dart';
import 'package:mobile_crm/routes/app_pages.dart';

class LoginFooter extends StatelessWidget {
  const LoginFooter({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      bottom: true,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          InkWell(
            key: const Key('lostPhoneNumberButtonKey'),
            onTap: () =>
                const LoginBottomSheet(type: BottomSheetWidgetType.lostPhone)
                    .toModalBottomSheetNoMinHeight
                    .of(context),
            child: RichText(
                text: TextSpan(children: [
              TextSpan(
                  text: Strings.lostNumber.tr, style: AppFont.paragraphSmall),
              TextSpan(
                  text: ' ${Strings.getNew.tr}',
                  style: AppFont.paragraphSmallBold)
            ])),
          ),
          10.0.height,
          InkWell(
            key: const Key('newRegisterButtonKey'),
            onTap: () => Get.toNamed(Routes.REGISTER),
            child: RichText(
                text: TextSpan(children: [
              TextSpan(
                  text: Strings.dontHaveAccount.tr,
                  style: AppFont.paragraphSmall),
              TextSpan(
                text: ' ${Strings.createNew.tr}',
                style: AppFont.paragraphSmallBold,
              )
            ])),
          )
        ],
      ),
    );
  }
}
