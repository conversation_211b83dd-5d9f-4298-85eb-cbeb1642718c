import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/login/controller/login_controller.dart';
import 'package:mobile_crm/app/modules/login/otp/views/widget/quick_login.dart';
import 'package:mobile_crm/app/modules/login/view/widget/login_terms.dart';
import 'package:mobile_crm/app/utils/app_alert.dart';
import 'package:mobile_crm/app/utils/toast.dart';
import 'package:mobile_crm/app/widget/app_dotted_separator.dart';
import 'package:mobile_crm/app/widget/button_widget/button_widget.dart';
import 'package:mobile_crm/app/widget/custom_circular_progress_widget.dart';
import 'package:mobile_crm/app/widget/input_widget/input_form_widget.dart';
import 'package:mobile_crm/app/widget/modal_bottom_sheet/login_modal_bottom_sheet.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/app_img_strings.dart';
import 'package:mobile_crm/core/values/app_strings.dart';
import 'package:mobile_crm/routes/app_pages.dart';

class LoginForm extends StatelessWidget {
  const LoginForm({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<LoginController>();
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            Strings.login.tr,
            style: AppFont.heading5,
          ),
          16.0.height,
          AppInputText(
            key: const Key('loginPhoneNumberFieldKey'),
            keyboardType: TextInputType.number,
            controller: controller.phoneNumberController,
            label: Strings.whatsappNumber.tr,
          ),
          AppDimen.h10.height,
          const LoginTerms(
            type: "login",
          ),
          AppDimen.h30.height,
          PrimaryButton(
            key: const Key('loginButtonKey'),
            onPressed: () async {
              FocusManager.instance.primaryFocus?.unfocus();
              if (!GetUtils.isPhoneNumber(
                  controller.phoneNumberController.text.trim())) {
                Toast.show(Strings.invalidPhoneNumber.tr, type: ToastType.dark);
                return;
              }
              if (controller.phoneNumberController.text
                      .trim()
                      .toInternational ==
                  controller.demoAccountPhone.toInternational) {
                const LoginBottomSheet(type: BottomSheetWidgetType.loginDummy)
                    .toModalBottomSheetNoMinHeight
                    .of(context);
                return;
              }

              bool isExist = await controller.checkExistingPhone();

              if (!isExist) {
                AppAlert.showConfirmWarningDialog(
                    context: Get.context!,
                    message: Strings.memberNotFound.tr,
                    actions: [
                      TextButton(
                          onPressed: () async {
                            Get.back();
                          },
                          child: Text(Strings.cancel.tr,
                              style: AppFont.componentSmall
                                  .copyWith(color: AppColor.white))),
                      TextButton(
                          onPressed: () async {
                            controller.store.authPhone =
                                controller.phoneNumberController.text;
                            Get.toNamed(Routes.REGISTER);
                          },
                          child: Text(Strings.register.tr,
                              style: AppFont.componentSmall
                                  .copyWith(color: AppColor.white))),
                    ]);
                return;
              }

              //showing login option
              // const LoginBottomSheet(type: BottomSheetWidgetType.verifyBy).toModalBottomSheetNoMinHeight.of(context);
              controller.loginWithPhone(controller.phoneNumberController.text);
            },
            text: Strings.login.tr,
            type: PrimaryButtonType.type4,
            child: SizedBox(
              width: double.infinity,
              child: Align(
                alignment: Alignment.center,
                child: Obx(() => (controller.isLoading.value)
                    ? const CustomCircularProgressIndicator()
                    : Text(
                        Strings.login.tr,
                        style: AppFont.componentSmall
                            .copyWith(color: AppColor.white),
                      )),
              ),
            ),
          ),
          5.0.height,
          // ((GetPlatform.isIOS) && kReleaseMode)
          //     ? Container()
          //     :
          Column(
            children: [
              const QuickLoginWidget(),
              Row(
                children: [
                  const Flexible(
                      child: DottedDivider(
                    width: 4,
                    height: 2,
                    color: AppColor.black10,
                  )),
                  AppDimen.h8.width,
                  Text(
                    Strings.orSignInUsing.tr,
                    style: AppFont.componentSmallBold
                        .copyWith(fontSize: 11.sp, color: AppColor.black50),
                  ),
                  AppDimen.h8.width,
                  const Flexible(
                      child: DottedDivider(
                    width: 4,
                    height: 2,
                    color: AppColor.black10,
                  )),
                ],
              ),
              AppDimen.h10.height,
            ],
          ),
          (GetPlatform.isIOS && kReleaseMode)
              ? Container()
              : Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Flexible(
                      child: InkWell(
                        onTapDown: (details) => controller.signInWithGoogle(),
                        child: Container(
                          height: AppDimen.h30,
                          padding: EdgeInsets.symmetric(
                              horizontal: AppDimen.h8, vertical: AppDimen.h6),
                          decoration: BoxDecoration(
                              color: AppColor.white,
                              borderRadius:
                                  const BorderRadius.all(Radius.circular(10)),
                              border:
                                  Border.all(width: 1, color: AppColor.black5)),
                          child: Obx(() {
                            return controller.isGoogleLoading.value
                                ? const CustomCircularProgressIndicator(
                                    valueColor: AppColor.black,
                                    backgroundColor: AppColor.white,
                                  )
                                : Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Image.asset(
                                        ImgStrings.googleIcon,
                                        width: AppDimen.h22,
                                        height: AppDimen.h22,
                                      ),
                                      10.0.width,
                                      Text(
                                        "Google",
                                        style: AppFont.componentSmall,
                                      ),
                                    ],
                                  );
                          }),
                        ),
                      ),
                    ),
                    15.0.width,
                    Flexible(
                      child: InkWell(
                        onTapDown: (details) => !GetPlatform.isIOS
                            ? controller.signInWithApple()
                            : controller.signInWithAppleForIos(),
                        child: Container(
                          height: AppDimen.h30,
                          padding: EdgeInsets.symmetric(
                              horizontal: AppDimen.h8, vertical: AppDimen.h6),
                          decoration: BoxDecoration(
                              color: AppColor.white,
                              borderRadius:
                                  const BorderRadius.all(Radius.circular(10)),
                              border:
                                  Border.all(width: 1, color: AppColor.black5)),
                          child: Obx(() {
                            return controller.isAppleLoading.value
                                ? const CustomCircularProgressIndicator(
                                    valueColor: AppColor.black,
                                    backgroundColor: AppColor.white,
                                  )
                                : Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Image.asset(
                                        ImgStrings.appleIcon,
                                        width: AppDimen.h22,
                                        height: AppDimen.h22,
                                      ),
                                      10.0.width,
                                      Text(
                                        "Apple",
                                        style: AppFont.componentSmall,
                                      ),
                                    ],
                                  );
                          }),
                        ),
                      ),
                    ),
                  ],
                )

          // Row(
          //   children: [
          //     (GetPlatform.isIOS && kReleaseMode) ? Container() : 5.0.height,
          //     (GetPlatform.isIOS && kReleaseMode)
          //         ? Container()
          //         : IconButtonWidget(
          //             onPressed: () {
          //               controller.signInWithGoogle();
          //             },
          //             width: double.infinity,
          //             widget: Obx(
          //               () => (controller.isGoogleLoading.value)
          //                   ? const CustomCircularProgressIndicator()
          //                   : Row(
          //                       mainAxisAlignment: MainAxisAlignment.center,
          //                       children: [
          //                         Padding(
          //                           padding:
          //                               const EdgeInsets.symmetric(vertical: 6),
          //                           child: Image.asset(ImgStrings.googleIcon),
          //                         ),
          //                         5.0.width,
          //                         Text(
          //                           Strings.signInGoogle.tr,
          //                           style: AppFont.componentSmall
          //                               .copyWith(color: AppColor.black),
          //                         )
          //                       ],
          //                     ),
          //             ),
          //           ),
          //     (GetPlatform.isIOS && kReleaseMode) ? Container() : 5.0.height,
          //     (GetPlatform.isIOS && kReleaseMode)
          //         ? Container()
          //         : IconButtonWidget(
          //             onPressed: () {
          //               !GetPlatform.isIOS
          //                   ? controller.signInWithApple()
          //                   : controller.signInWithAppleForIos();
          //             },
          //             width: double.infinity,
          //             widget: Obx(
          //               () => (controller.isAppleLoading.value)
          //                   ? const CustomCircularProgressIndicator()
          //                   : Row(
          //                       mainAxisAlignment: MainAxisAlignment.center,
          //                       children: [
          //                         Padding(
          //                           padding:
          //                               const EdgeInsets.symmetric(vertical: 6),
          //                           child: Image.asset(ImgStrings.appleIcon),
          //                         ),
          //                         5.0.width,
          //                         Text(
          //                           Strings.signInApple.tr,
          //                           style: AppFont.componentSmall
          //                               .copyWith(color: AppColor.black),
          //                         )
          //                       ],
          //                     ),
          //             ),
          //           ),
          //   ],
          // ),
        ],
      ),
    );
  }
}
