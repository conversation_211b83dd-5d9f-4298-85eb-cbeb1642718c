import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/login/controller/login_controller.dart';
import 'package:mobile_crm/app/widget/button_widget/button_widget.dart';
import 'package:mobile_crm/app/widget/custom_circular_progress_widget.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/values.dart';

import '../../../../utils/svg_icon.dart';

class LoginVerifyBy extends StatelessWidget {
  const LoginVerifyBy({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<LoginController>();
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            Strings.selectVerificationCodeMethod.tr,
            style: AppFont.paragraphMediumBold,
          ),
          AppDimen.h18.height,
          PrimaryButton(
            key: const Key('sendOTPSMSButtonKey'),
            color: Colors.black38,
            type: PrimaryButtonType.type4,
            onPressed: () {
              //Get.back();
              //controller.loginWithPhone(controller.phoneNumberController.text);

              Get.showSnackbar(
                GetSnackBar(
                  messageText: Center(
                      child: Text(
                    "Metode Dengan SMS saat ini belum dapat digunakan! Silahkan Login degan Google atau WhatsApp",
                    style: AppFont.componentSmallBold
                        .copyWith(color: AppColor.white),
                  )),
                  backgroundColor: Colors.red,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 15, vertical: 9),
                  duration: const Duration(hours: 24),
                ),
              );
            },
            text: '',
            width: double.infinity,
            child: Obx(
              () => (controller.isLoading.value)
                  ? const CustomCircularProgressIndicator()
                  : Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Text(
                          Strings.sendViaValue
                              .trParams({'value': Strings.sms.tr}),
                          style: AppFont.componentSmall
                              .copyWith(color: AppColor.white),
                        ),
                        Icon(Icons.message,
                            color: AppColor.white,
                            size: Constants.iconSize(context)),
                      ],
                    ),
            ),
          ),
          AppDimen.h12.height,
          PrimaryButton(
            key: const Key('sendAuthLinkButtonKey'),
            type: PrimaryButtonType.type4,
            onPressed: () {
              Get.back();
              controller.sendAuthLink();
            },
            text: '',
            width: double.infinity,
            child: Obx(
              () => (controller.isLoading.value)
                  ? const CustomCircularProgressIndicator()
                  : Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Text(
                          Strings.sendViaValue
                              .trParams({'value': Strings.whatsapp.tr}),
                          style: AppFont.componentSmall
                              .copyWith(color: AppColor.white),
                        ),
                        SvgIcon.svgIcon(ImgStrings.whatsapp,
                            size: Size.square(Constants.iconSize(context))),
                      ],
                    ),
            ),
          ),
        ],
      ),
    );
  }
}
