import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/login/controller/login_controller.dart';
import 'package:mobile_crm/app/widget/button_widget/button_widget.dart';
import 'package:mobile_crm/app/widget/custom_circular_progress_widget.dart';
import 'package:mobile_crm/app/widget/input_widget/input_form_widget.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/app_strings.dart';

class LoginFormDummy extends StatelessWidget {
  const LoginFormDummy({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<LoginController>();
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "Password",
            style: AppFont.heading5,
          ),
          16.0.height,
          AppInputText(
            key: const Key('loginPasswordFieldKey'),
            type: InputFormType.password,
            keyboardType: TextInputType.text,
            controller: controller.passwordController,
            label: "Password",
          ),
          AppDimen.h24.height,
          PrimaryButton(
            key: const Key('loginDummyButtonKey'),
            onPressed: () {
              controller.signInWithDummy();
            },
            text: Strings.login,
            type: PrimaryButtonType.type4,
            child: SizedBox(
              width: double.infinity,
              child: Obx(() => (controller.isLoading.value)
                  ? const CustomCircularProgressIndicator(
                      valueColor: AppColor.white,
                    )
                  : Text(
                      Strings.next.tr,
                      textAlign: TextAlign.center,
                      style: AppFont.componentSmall
                          .copyWith(color: AppColor.white),
                    )),
            ),
          ),
        ],
      ),
    );
  }
}
