import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/login/login_export.dart';
import 'package:mobile_crm/core/extensions/double_extensions.dart';
import 'package:mobile_crm/core/theme/app_dimen.dart';
import 'package:mobile_crm/core/theme/app_font.dart';

class SendChat extends StatelessWidget {
  const SendChat({super.key});

  @override
  Widget build(BuildContext context) {
    final otpController = Get.put(OtpController());

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.sp),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            "Chat Login Help",
            style: AppFont.componentLarge,
          ),
          AppDimen.h4.height,
          Text(
            "To complete your login, please send the pre-filled WhatsApp message without making any changes. After sending it, return to this screen and tap the 'Continue' button.",
            style: AppFont.componentSmall,
          ),
          AppDimen.h16.height,
          Obx(() {
            return SizedBox(
              width: double.infinity,
              child: FilledButton(
                style: FilledButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8.sp),
                  ),
                ),
                onPressed: () {
                  otpController.loginOobWhatsApp();
                },
                child: otpController.isLoadingOpenWa.value
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          color: Colors.white,
                        ),
                      )
                    : const Text("Send Message"),
              ),
            );
          }),
          Obx(() {
            return SizedBox(
              width: double.infinity,
              child: FilledButton(
                style: FilledButton.styleFrom(
                  backgroundColor: Colors.black,
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8.sp),
                  ),
                ),
                onPressed: otpController.readyToCheckOobAuth.value
                    ? otpController.checkLoginWithOob
                    : null,
                child: otpController.isLoadingCheckLoginOobWa.value
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          color: Colors.white,
                        ),
                      )
                    : const Text("Continue"),
              ),
            );
          }),
          AppDimen.h12.height,
          GestureDetector(
            onTap: () {
              Get.back();
            },
            child: Text(
              "Back to login",
              style: AppFont.componentMedium.copyWith(color: Colors.blue),
            ),
          )
        ],
      ),
    );
  }
}
