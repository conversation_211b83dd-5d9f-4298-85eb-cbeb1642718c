import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/helper/province_district_helper.dart';
import 'package:mobile_crm/app/utils/app_alert.dart';
import 'package:mobile_crm/app/utils/logger.dart';
import 'package:mobile_crm/app/utils/toast.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/values.dart';
import 'package:mobile_crm/data/models/auth_token_model.dart';
import 'package:mobile_crm/data/models/province_model.dart';
import 'package:mobile_crm/data/models/server_response.dart';
import 'package:mobile_crm/data/models/user_model.dart';
import 'package:mobile_crm/data/repository/helper_repository.dart';
import 'package:mobile_crm/data/repository/user_repository.dart';
import 'package:mobile_crm/data/services/local_storage.dart';
import 'package:mobile_crm/domain/repository/helper_repository.dart';
import 'package:mobile_crm/domain/repository/user_repository.dart';
import 'package:mobile_crm/routes/app_pages.dart';
import 'package:sentry_flutter/sentry_flutter.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../../data/repository/auth_repository.dart';
import '../../../../../domain/repository/auth_repository.dart';
import '../../../screen_wrapper/controller/wrapper_controller.dart';

class RegisterController extends GetxController {
  final UserRepository _repoUser = Get.find<UserRepositoryIml>();
  final AuthRepository _repoAuth = Get.find<AuthRepositoryIml>();
  final HelperRepository _repoHelper = Get.find<HelperRepositoryIml>();
  final wrapperController = Get.find<WrapperController>();
  final store = LocalStorageService();
  final validateFormKey = GlobalKey<FormState>();

  // Add getters for name and dob controllers
  TextEditingController get nameController => textNameController.value;
  TextEditingController get dobController => textDateOfBirthController.value;

  String? _phone;

  List<ProvinceModel> provinces = [];

  String province = "";
  String provinceId = "";
  var provinceIdObx = "".obs;

  var dateOfBirth = 0;
  var authState = "register";

  var textPhoneController = TextEditingController().obs;
  var textNameController = TextEditingController().obs;
  var textGenderController = TextEditingController().obs;
  var textEmailController = TextEditingController().obs;
  var textAddressController = TextEditingController().obs;
  var textProvinceController = TextEditingController().obs;
  var textDistrictController = TextEditingController().obs;
  var textDateOfBirthController = TextEditingController().obs;

  var loading = false.obs;

  @override
  void dispose() {
    textPhoneController.value.dispose();
    textEmailController.value.dispose();
    textNameController.value.dispose();
    textAddressController.value.dispose();
    super.dispose();
  }

  @override
  void onClose() {
    textPhoneController.value.text = "";
    textEmailController.value.text = "";
    textNameController.value.text = "";
    textAddressController.value.text = "";
    super.onClose();
  }

  @override
  void onInit() {
    _phone = store.authPhone;
    textNameController.value.text = store.authName ?? '';
    textPhoneController.value.text = store.authPhone ?? '';
    textEmailController.value.text = store.authEmail ?? '';
    Sentry.addBreadcrumb(Breadcrumb(
        type: 'debug',
        category: 'user.activity.register.controller.onInit',
        data: {'phone': _phone ?? ''},
        level: SentryLevel.debug));
    getProvinceAndDistrictByGPS();
    super.onInit();
  }

  Future<void> getProvinceAndDistrictByGPS() async {
    var result = await _repoHelper.getAddress();
    if (result.province != null || result.province != '') {
      var prov = ProvinceDistrictHelper.isGeocodingAndProvinceMatch(
          address: result, listProv: await getProvince());

      if (prov != null) {
        setProvinceAndDistrict(
            provId: prov.id ?? "16",
            prov: prov.name ?? '',
            dist: result.district ?? '');
      }
    }
  }

  void setProvinceAndDistrict(
      {required String provId, required String prov, required String dist}) {
    textProvinceController.value.text = prov;
    textDistrictController.value.text = dist;

    provinceId = provId;
    province = prov;
    provinceIdObx.value = provId;
  }

  Future<List<ProvinceModel>> getProvince() async {
    return await _repoHelper.getProvince();
  }

  Future<List<ProvinceModel>> getRegency(String id) async {
    return await _repoHelper.getRegency(provinceId: id);
  }

  Future<void> sentryCustomSetTag() async {
    await Sentry.configureScope((scope) => scope
      ..setTag('state', 'Register')
      ..setTag('Register Name', textEmailController.value.text)
      ..setTag('Register Phone', textPhoneController.value.text)
      ..setTag('Register Email', textEmailController.value.text));
  }

  void sendOTPPhoneNumberBeforeRegister() async {
    Sentry.addBreadcrumb(Breadcrumb(
        type: 'debug',
        category: 'user.activity.register.controller.register_with_phone',
        data: {
          'name': textNameController.value.text,
          'email': textEmailController.value.text,
          'phone': textPhoneController.value.text,
          'province': textProvinceController.value.text,
          'dateOfBirth': dateOfBirth,
          'gender': textGenderController.value.text.toLowerCase().trim() ==
                  "male"
              ? 1
              : textGenderController.value.text.toLowerCase().trim() == "female"
                  ? 0
                  : null,
          'city': textDistrictController.value.text
        },
        level: SentryLevel.debug));
    await sentryCustomSetTag();
    try {
      loading.value = true;
      _phone = textPhoneController.value.text.trim().toInternational;
      int isExist = await checkExistingUser(
              phone: _phone, email: textEmailController.value.text.trim())
          .whenComplete(() => loading.value = false);

      if (isExist == 1) {
        return;
      }
      store.authPhone = _phone;
      store.authState = (authState);
      Get.toNamed(Routes.OTP);
    } catch (e, s) {
      errorLogger(pos: "RC", error: e, stackTrace: s);
    }
  }

  Future<ServerResponse> register(String token) async {
    Sentry.addBreadcrumb(Breadcrumb(
        type: 'debug',
        category: 'user.activity.register.controller.register',
        data: {
          'name': textNameController.value.text,
          'email': textEmailController.value.text,
          'phone': textPhoneController.value.text,
          'province': textProvinceController.value.text,
          "dateOfBirth": dateOfBirth,
          "gender": textGenderController.value.text.toLowerCase().trim() ==
                  "male"
              ? 1
              : textGenderController.value.text.toLowerCase().trim() == "female"
                  ? 0
                  : null,
          'city': textDistrictController.value.text
        },
        level: SentryLevel.debug));
    return await _repoUser.registerMember(
        UserModel(
            name: textNameController.value.text,
            email: textEmailController.value.text.trim(),
            phone: textPhoneController.value.text.trim(),
            address: '',
            dateOfBirth: dateOfBirth,
            gender:
                textGenderController.value.text.toLowerCase().trim() == "male"
                    ? 1
                    : textGenderController.value.text.toLowerCase().trim() ==
                            "female"
                        ? 0
                        : null,
            province: textProvinceController.value.text.trim(),
            city: textDistrictController.value.text.trim()),
        token);
  }

  Future registerV2() async {
    infoLogger("registerV2, checkExistingUser starting...");
    loading.value = true;
    var check = await checkExistingUser(
        phone: textPhoneController.value.text.toLocal.trim(),
        email: textEmailController.value.text.trim());
    infoLogger("registerV2, checkExistingUser", check);
    if (check != 1) {
      Sentry.addBreadcrumb(Breadcrumb(
          type: 'debug',
          category: 'user.activity.register.controller.registerV2',
          data: {
            'name': textNameController.value.text,
            'email': textEmailController.value.text,
            'phone': textPhoneController.value.text,
            'province': textProvinceController.value.text,
            "dateOfBirth": dateOfBirth,
            "gender":
                textGenderController.value.text.toLowerCase().trim() == "male"
                    ? 1
                    : textGenderController.value.text.toLowerCase().trim() ==
                            "female"
                        ? 0
                        : null,
            'city': textDistrictController.value.text
          },
          level: SentryLevel.debug));
      var saveUser = UserModel(
          name: textNameController.value.text,
          email: textEmailController.value.text.trim(),
          phone: textPhoneController.value.text.toLocal.trim(),
          province: textProvinceController.value.text.trim(),
          dateOfBirth: dateOfBirth,
          gender: textGenderController.value.text.toLowerCase().trim() == "male"
              ? 1
              : textGenderController.value.text.toLowerCase().trim() == "female"
                  ? 0
                  : null,
          city: textDistrictController.value.text.trim());
      infoLogger("Check save user", saveUser.toJson());
      var result = await _repoUser
          .registerMemberV2(saveUser)
          .whenComplete(() => loading.value = false);

      if (result.status) {
        return AppAlert.showConfirmInfoDialog(
            context: Get.context!,
            barrierDismissible: false,
            message: Strings.registrationIsSuccessfulCheckWhatsapp.tr,
            actions: [
              TextButton(
                  onPressed: () async {
                    Get.back();
                    Get.offAndToNamed(Routes.LOGIN);
                  },
                  child: Text("Ok",
                      style: AppFont.componentSmall
                          .copyWith(color: AppColor.white))),
            ]);
      } else {
        return Toast.show(result.message, type: ToastType.error, duration: 4);
      }
    } else {
      Toast.show("checking failed!", type: ToastType.error, duration: 4);
    }
    loading.value = false;
  }

  Future<void> openWhatsapp() async {
    try {
      String number =
          wrapperController.configApp.value.contact?.whatsapp ?? '0';
      String whatsappURl = "whatsapp://send/?phone=$number";
      await launchUrl(Uri.parse(whatsappURl));
    } catch (e, s) {
      errorLogger(pos: "HC - Send whatsapp", error: e, stackTrace: s);
      if (e.runtimeType == PlatformException) {
        return Toast.show(Strings.whatsAppWontOpen.tr,
            type: ToastType.error, duration: 5);
      }

      Toast.show("Failed to open whatsapp. $e");
    }
  }

  void clear() {
    store.authPhone = null;
  }

  Future<int> checkExistingUser({String? phone, String? email}) async {
    var response = await _repoUser.checkExistingUserByNumberOrEmail(
        phoneNumber: phone, email: email, showToast: true);
    return response.code;
  }

  void statusCodeRegister(ServerResponse resp, String token) {
    switch (resp.code) {
      case 0:
        Toast.show(Strings.accountRegisteredSuccessfully.tr,
            type: ToastType.success);
        nextRegisterProcess(token);
        break;
      case 30:
        Get.back();
        AppAlert.showInfoDialog(
            Get.context!, Strings.alreadyRegisterBusiness.tr);
        break;
      case 31:
        Get.back();
        AppAlert.showInfoDialog(
            Get.context!, "User already registered at one of business");
        break;
      case 32:
        Toast.show(Strings.emailAlreadyUsedRegistered.tr,
            type: ToastType.error, duration: 8);
        Get.back();
        break;
      case 33:
        Toast.show(Strings.phoneAlreadyUsedRegistered.tr,
            type: ToastType.error, duration: 8);
        Get.back();
        break;
      default:
        Toast.show("[${resp.code}] : ${resp.message}", type: ToastType.error);
        Get.back();
        break;
    }
  }

  Future nextRegisterProcess(String tokenFirebase) async {
    Sentry.addBreadcrumb(Breadcrumb(
        type: 'debug',
        category: 'user.activity.register.controller.success_register',
        data: {
          'name': textNameController.value.text,
          'email': textEmailController.value.text,
          'phone': textPhoneController.value.text,
          'province': textProvinceController.value.text,
          'city': textDistrictController.value.text,
        },
        level: SentryLevel.debug));
    AuthTokenModel? token = await _repoAuth
        .getTokenPhoneAuth(tokenFirebase)
        .then((value) => value.data);
    store.token = token?.token;
    Get.offAllNamed(Routes.HOME);
  }
}
