import 'package:get/get.dart';
import 'package:mobile_crm/data/repository/helper_repository.dart';
import 'package:mobile_crm/data/repository/user_repository.dart';

import '../../../../../data/repository/auth_repository.dart';
import '../controllers/register_controller.dart';

class RegisterBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => UserRepositoryIml());
    Get.lazyPut(() => AuthRepositoryIml());
    Get.lazyPut(() => HelperRepositoryIml());
    Get.lazyPut(() => RegisterController(), fenix: true);
  }
}
