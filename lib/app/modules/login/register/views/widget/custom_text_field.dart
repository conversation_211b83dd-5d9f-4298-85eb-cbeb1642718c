// ignore_for_file: must_be_immutable

import 'package:flutter/material.dart';
import 'package:mobile_crm/core/values/values.dart';

class TextFieldRegister extends StatelessWidget {
  String? text;
  VoidCallback? onTap;
  IconData? icon;
  TextEditingController? controller;
  TextInputType? textInputType;

  TextFieldRegister(
      {Key? key,
      this.controller,
      this.textInputType,
      this.text,
      this.icon,
      this.onTap})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(top: 15),
      padding: const EdgeInsets.symmetric(horizontal: 15),
      decoration: BoxDecoration(
          color: Colors.grey.shade200, borderRadius: BorderRadius.circular(25)),
      child: TextField(
        onTap: onTap,
        textAlign: TextAlign.left,
        controller: controller,
        keyboardType: textInputType,
        decoration: InputDecoration(
            focusColor: Colors.black,
            icon: Icon(icon,
                color: Colors.black45, size: Constants.iconSize(context)),
            hintText: text,
            border: InputBorder.none,
            hintStyle: const TextStyle(color: Colors.black38)),
      ),
    );
  }
}
