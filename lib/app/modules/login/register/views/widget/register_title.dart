import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/values.dart';

class RegisterTitle extends StatelessWidget {
  const RegisterTitle({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(AppDimen.h16),
      child: Text(
        Strings.createNew.tr,
        style: AppFont.componentMediumBold,
      ),
    );
  }
}
