import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/login/login_export.dart';
import 'package:mobile_crm/app/widget/button_widget/button_widget.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/app_strings.dart';

class RegisterNameDobConfirmation extends StatelessWidget {
  final VoidCallback onConfirm;

  const RegisterNameDobConfirmation({
    Key? key,
    required this.onConfirm,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<RegisterController>();
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(25),
          topRight: Radius.circular(25),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.warning_amber_rounded,
                color: AppColor.warning,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                Strings.pleaseVerifyInformation.tr,
                style: AppFont.componentLarge,
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            Strings.nameAndDOBCannotBeChanged.tr,
            style: AppFont.componentMedium.copyWith(color: AppColor.ink02),
          ),
          const SizedBox(height: 24),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColor.ink005,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: AppColor.ink01),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      "${Strings.name.tr}:",
                      style: AppFont.componentMedium,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        controller.nameController.text,
                        style: AppFont.componentMediumBold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Text(
                      "${Strings.dateOfBirth.tr}:",
                      style: AppFont.componentMedium,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        controller.dobController.text,
                        style: AppFont.componentMediumBold,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),
          Text(
            Strings.isInformationCorrect.tr,
            style: AppFont.componentMedium,
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: PrimaryButton(
                  onPressed: () => Get.back(),
                  text: Strings.noNeedToEdit.tr,
                  type: PrimaryButtonType.type2,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: PrimaryButton(
                  onPressed: () {
                    Get.back();
                    onConfirm();
                  },
                  text: Strings.yesContinue.tr,
                  type: PrimaryButtonType.type2,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
