import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/login/login_export.dart';
import 'package:mobile_crm/app/modules/login/view/widget/login_terms.dart';
import 'package:mobile_crm/app/widget/button_widget/button_widget.dart';
import 'package:mobile_crm/app/widget/custom_circular_progress_widget.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/app_constants.dart';
import 'package:mobile_crm/core/values/app_strings.dart';

import 'register_confirmation.dart';
import 'register_name_dob_confirmation.dart';

class RegisterBottom extends StatelessWidget {
  const RegisterBottom({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<RegisterController>();
    return SafeArea(
      bottom: true,
      child: Container(
        margin: EdgeInsets.only(bottom: AppDimen.h8),
        padding: EdgeInsets.symmetric(horizontal: Constants.defaultPadding),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Padding(
              padding: EdgeInsets.only(left: 16, right: 16),
              child: LoginTerms(
                type: "register",
              ),
            ),
            AppDimen.h12.height,
            PrimaryButton(
              key: const Key('submitRegisterButtonKey'),
              onPressed: () {
                if (controller.validateFormKey.currentState?.validate() !=
                    null) {
                  if (controller.validateFormKey.currentState!.validate()) {
                    // First show name & DOB confirmation
                    RegisterNameDobConfirmation(
                      onConfirm: () {
                        // Then show address confirmation
                        RegisterConfirmation(
                          onPressed: () =>
                              controller.sendOTPPhoneNumberBeforeRegister(),
                        ).toModalBottomSheetNoMinHeight.of(context);
                      },
                    ).toModalBottomSheetNoMinHeight.of(context);
                  }
                }
              },
              text: Strings.next,
              type: PrimaryButtonType.type4,
              child: SizedBox(
                width: double.infinity,
                child: Align(
                  alignment: Alignment.center,
                  child: Obx(
                    () => controller.loading.value
                        ? const CustomCircularProgressIndicator()
                        : Text(
                            Strings.next.tr,
                            style: AppFont.componentSmall
                                .copyWith(color: AppColor.white),
                          ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
