import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/widget/input_widget/input_form_widget.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/theme/app_dimen.dart';
import 'package:mobile_crm/core/values/app_strings.dart';

import '../../controllers/register_controller.dart';

class RegisterForm extends StatelessWidget {
  const RegisterForm({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<RegisterController>();
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: AppDimen.h16),
      child: Form(
        key: controller.validateFormKey,
        autovalidateMode: AutovalidateMode.always,
        child: Column(
          children: [
            AppInputText(
              key: const Key('inputNameKey'),
              label: Strings.name.tr,
              type: InputFormType.withIcon,
              icon: Icons.person,
              keyboardType: TextInputType.name,
              controller: controller.textNameController.value,
              validator: (value) {
                if (value == '') {
                  return Strings.valueCannotEmpty
                      .trParams({"value": Strings.name.tr});
                }
                return null;
              },
            ),
            10.0.height,
            AppInputText(
              key: const Key('inputEmailKey'),
              label: Strings.email,
              type: InputFormType.withIcon,
              icon: Icons.email,
              keyboardType: TextInputType.emailAddress,
              controller: controller.textEmailController.value,
              validator: (value) {
                if (value == '') {
                  return Strings.valueCannotEmpty.trParams({"value": "Email"});
                }

                if (!GetUtils.isEmail(value ?? '')) {
                  return Strings.invalidValue.trParams({"value": "Email"});
                }

                return null;
              },
            ),
            10.0.height,
            AppInputText(
              key: const Key('inputPhoneNumberKey'),
              label: Strings.whatsappNumber.tr,
              type: InputFormType.withIcon,
              icon: Icons.phone,
              keyboardType: TextInputType.phone,
              controller: controller.textPhoneController.value,
              validator: (value) {
                if (value == '') {
                  return Strings.valueCannotEmpty
                      .trParams({"value": Strings.whatsappNumber.tr});
                }

                if (!GetUtils.isPhoneNumber(value ?? '0')) {
                  return Strings.invalidValue
                      .trParams({"value": Strings.whatsappNumber.tr});
                }

                return null;
              },
            ),
            Visibility(
              visible: !GetPlatform.isIOS,
              child: Column(
                children: [
                  10.0.height,
                  AppInputText(
                    key: const Key('inputDateOfBirthKey'),
                    label: Strings.dateOfBirth.tr,
                    type: InputFormType.date,
                    icon: Icons.date_range_rounded,
                    keyboardType: TextInputType.phone,
                    controller: controller.textDateOfBirthController.value,
                    dateCallBack: (value) {
                      controller.dateOfBirth = value.millisecondsSinceEpoch;
                      controller.textDateOfBirthController.value.text =
                          value.millisecondsSinceEpoch.toDate;
                    },
                    validator: (value) {
                      if (value == '') {
                        return Strings.valueCannotEmpty
                            .trParams({"value": Strings.dateOfBirth.tr});
                      }

                      return null;
                    },
                  ),
                  10.0.height,
                  AppInputText(
                    key: const Key('inputGenderKey'),
                    label: Strings.gender,
                    type: InputFormType.gender,
                    icon: Icons.male_rounded,
                    controller: controller.textGenderController.value,
                    genderCallBack: (value) {
                      controller.textGenderController.value.text = value;
                    },
                    validator: (value) {
                      print('gender: $value');
                      if (!GetPlatform.isIOS &&
                          (value == null || value == "-" || value == "")) {
                        return "Please choose your gender!";
                      }
                      return null;
                    },
                  ),
                ],
              ),
            ),
            10.0.height,
            AppInputText(
                key: const Key('inputProvinceKey'),
                label: Strings.province.tr,
                type: InputFormType.province,
                icon: Icons.location_on,
                controller: controller.textProvinceController.value,
                validator: (value) {
                  if (value == '') {
                    return Strings.valueCannotEmpty
                        .trParams({"value": Strings.province.tr});
                  }

                  return null;
                },
                keyboardType: TextInputType.none),
            10.0.height,
            Obx(() {
              return Visibility(
                visible: controller.provinceIdObx.value.isNotEmpty,
                child: AppInputText(
                    key: const Key('inputDistrictKey'),
                    label: Strings.district.tr,
                    type: InputFormType.district,
                    controller: controller.textDistrictController.value,
                    keyboardType: TextInputType.none),
              );
            }),
          ],
        ),
      ),
    );
  }
}
