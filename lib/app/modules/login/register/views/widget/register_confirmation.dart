import 'package:flutter/material.dart';
import 'package:mobile_crm/app/utils/svg_icon.dart';
import 'package:mobile_crm/app/widget/button_widget/button_widget.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/core/values/app_constants.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/values.dart';
import 'package:get/get.dart';

class RegisterConfirmation extends StatelessWidget {
  const RegisterConfirmation({super.key, required this.onPressed});

  final VoidCallback? onPressed;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(8),
      constraints: BoxConstraints(
          minWidth: Constants.defaultMaxWidth,
          maxWidth: Constants.defaultMaxWidth),
      decoration: const BoxDecoration(
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(25), topRight: Radius.circular(25))),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            Strings.makeSureAddress.tr,
            style: AppFont.paragraphMediumBold,
          ),
          AppDimen.h18.height,
          PrimaryButton(
            type: PrimaryButtonType.type4,
            onPressed: () {
              Get.back();
              onPressed?.call();
            },
            text: '',
            width: double.infinity,
            child: SizedBox(
              width: double.infinity,
              child: Align(
                alignment: Alignment.center,
                child: Text(
                  Strings.yesMyDomiciled.tr,
                  style: AppFont.componentSmall.copyWith(color: AppColor.white),
                ),
              ),
            ),
          ),
          AppDimen.h18.height,
          PrimaryButton(
            type: PrimaryButtonType.type4,
            onPressed: () {
              Get.back();
            },
            text: '',
            width: double.infinity,
            child: SizedBox(
              width: double.infinity,
              child: Align(
                alignment: Alignment.center,
                child: Text(
                  Strings.close.tr,
                  style: AppFont.componentSmall.copyWith(color: AppColor.white),
                ),
              ),
            ),
          ),
          AppDimen.h24.height
        ],
      ),
    );
  }
}
