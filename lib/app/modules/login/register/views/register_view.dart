import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/login/register/views/widget/register_widget.dart';
import 'package:mobile_crm/app/modules/screen_wrapper/views/app_page_wrapper.dart';
import 'package:mobile_crm/app/widget/app_bar_widget/app_bar_widget.dart';
import 'package:mobile_crm/core/extensions/double_extensions.dart';

import '../../../../../core/values/app_strings.dart';
import '../controllers/register_controller.dart';

class RegisterScreen extends GetView<RegisterController> {
  const RegisterScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return PageWrapper(
      onWillPop: () {
        controller.clear();
        Get.back();
        return Future.value(false);
      },
      child: Scaffold(
        backgroundColor: Colors.transparent,
        bottomNavigationBar: const RegisterBottom(),
        resizeToAvoidBottomInset: true,
        body: Column(
          children: [
            AppBarWidget(title: Strings.newAccount.tr),
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const RegisterTitle(),
                    15.0.height,
                    const RegisterForm(),
                    10.0.height,
                    // const TermsPage(),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
