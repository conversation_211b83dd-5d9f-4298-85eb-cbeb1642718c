import 'dart:io' show File;

import 'package:flutter/material.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';
import 'package:mobile_crm/app/modules/order/view/components/order_modal_bottom_sheet.dart';
import 'package:mobile_crm/app/widget/button_widget/button_widget.dart';
import 'package:mobile_crm/app/widget/cached_image_widget.dart';
import 'package:mobile_crm/core/extensions/double_extensions.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:mobile_crm/core/values/values.dart';

import '../../data/services/analytics_service.dart';

class AppAlert {
  static afterFeedback({required double star, required String comment}) {
    showDialog(
        context: Get.context!,
        builder: (BuildContext builder) => SizedBox(
              width: 150.h,
              child: AlertDialog(
                backgroundColor: AppColor.white,
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(25.r)),
                content: SingleChildScrollView(
                  child: Column(
                    children: [
                      Text(
                        "Feedback",
                        style: AppFont.componentLarge,
                      ),
                      10.0.height,
                      Text(
                        star == 1
                            ? Strings.veryBad.tr
                            : star == 2
                                ? Strings.bad.tr
                                : star == 3
                                    ? Strings.normal.tr
                                    : star == 4
                                        ? Strings.good.tr
                                        : Strings.excellent.tr,
                        style: AppFont.componentMediumBold,
                      ),
                      10.0.height,
                      RatingBar.builder(
                        initialRating: star,
                        glow: false,
                        direction: Axis.horizontal,
                        allowHalfRating: false,
                        itemCount: 5,
                        ignoreGestures: true,
                        itemPadding:
                            const EdgeInsets.symmetric(horizontal: 4.0),
                        itemBuilder: (context, _) => const Icon(
                          Icons.star,
                          color: Colors.amber,
                        ),
                        onRatingUpdate: (double value) {},
                      ),
                      10.0.height,
                      Text(comment, style: AppFont.paragraphMedium),
                      20.0.height,
                      PrimaryButton(
                        onPressed: () async {
                          Get.back();
                          AnalyticsService.observer.analytics.logEvent(
                              name: "already_feedback",
                              parameters: {"stars": star, "comment": comment});
                        },
                        text: Strings.back.tr,
                        width: 200,
                        type: PrimaryButtonType.type3,
                      ),
                    ],
                  ),
                ),
              ),
            ));
  }

  static showPreviewImage(
      {required String path,
      required BuildContext context,
      bool canChange = true}) {
    showDialog(
        context: Get.context!,
        builder: (BuildContext builder) => SizedBox(
              width: 150.h,
              child: AlertDialog(
                  backgroundColor: AppColor.white,
                  shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10)),
                  content: SingleChildScrollView(
                    child: Column(
                      children: [
                        Image.file(File(path)),
                        10.0.height,
                        canChange
                            ? PrimaryButton(
                                onPressed: () {
                                  Get.back();
                                  OrderModalBottomSheet.modalBottomChooseImage(
                                      context: context);
                                },
                                text: "",
                                type: PrimaryButtonType.type4,
                                width: double.infinity,
                                child: Text(
                                  Strings.changeImage.tr,
                                  style: AppFont.componentSmallBold
                                      .copyWith(color: AppColor.white),
                                ),
                              )
                            : const SizedBox()
                      ],
                    ),
                  )),
            ));
  }

  static showPreviewImageNetwork(
      {required String url,
      required BuildContext context}) {
    showDialog(
        context: Get.context!,
        builder: (BuildContext builder) => SizedBox(
              width: 150.h,
              child: AlertDialog(
                  backgroundColor: AppColor.white,
                  shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10)),
                  content: SingleChildScrollView(
                    child: Column(
                      children: [
                        CachedImageWidget(imageUrl: url),
                      ],
                    ),
                  )),
            ));
  }

  static Future showThanksDialog(
          {required BuildContext context,
          String message = "",
          List<Widget>? actions,
          bool barrierDismissible = false}) =>
      _showThanksDialog(context, message, actions,
          barrierDismissible: barrierDismissible);

  static Future showErrorDialog(BuildContext context, String message) =>
      _showErrorDialog(context, message);

  static Future showInfoDialog(BuildContext context, String message) =>
      _showInfoDialog(context, message);

  static Future showConfirmDeleteDialog(
          BuildContext context, String message, VoidCallback onDelete) =>
      _showConfirmDeleteDialog(context, message, onDelete);

  static Future showConfirmWarningDialog({
    required BuildContext context,
    String message = "",
    List<Widget>? content,
    List<Widget>? actions,
  }) =>
      _showConfirmWarningDialog(context, message, content, actions);

  static Future showConfirmInfoDialog(
          {required BuildContext context,
          String message = "",
          List<Widget>? content,
          List<Widget>? actions,
          bool barrierDismissible = false}) =>
      _showConfirmInfoDialog(context, message, content, actions,
          barrierDismissible: barrierDismissible);

  static Future<void> _showThanksDialog(
      BuildContext context, String message, List<Widget>? actions,
      {bool barrierDismissible = false}) async {
    return showDialog(
        context: context,
        barrierDismissible: barrierDismissible,
        builder: (BuildContext builder) => WillPopScope(
              onWillPop: () => Future.value(false),
              child: AlertDialog(
                backgroundColor: AppColor.navy,
                icon: Lottie.asset(
                  ImgStrings.lottieThanks,
                  repeat: false,
                  width: 80.h,
                  height: 80.h,
                ),
                title: Text(
                  Strings.thankYou.tr,
                  style: AppFont.componentLarge.copyWith(color: Colors.white),
                ),
                content: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      message,
                      style: AppFont.paragraphSmallBold
                          .copyWith(color: Colors.white),
                    ),
                  ],
                ),
                actions: actions ??
                    [
                      TextButton(
                          onPressed: () => Get.back(),
                          child: Text(
                            "close",
                            style: AppFont.componentSmall
                                .copyWith(color: AppColor.white),
                          ))
                    ],
              ),
            ));
  }

  static Future<void> _showErrorDialog(
      BuildContext context, String message) async {
    return showDialog(
        barrierDismissible: false,
        context: context,
        builder: (BuildContext builder) => SizedBox(
              width: Constants.alertWidth(context),
              child: AlertDialog(
                backgroundColor: AppColor.navy,
                icon: Lottie.asset(ImgStrings.lottieError,
                    repeat: false, width: 80.h, height: 80.h),
                title: Text(
                  "ERROR",
                  style: AppFont.componentLarge
                      .copyWith(color: Colors.white)
                      .copyWith(color: Colors.white),
                ),
                content: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      message,
                      style: AppFont.paragraphSmallBold
                          .copyWith(color: Colors.white),
                    ),
                  ],
                ),
                actions: [
                  TextButton(
                      onPressed: () => Get.back(),
                      child: Text(
                        Strings.close.tr,
                        style: AppFont.componentSmall
                            .copyWith(color: AppColor.white),
                      ))
                ],
              ),
            ));
  }

  static Future<void> _showInfoDialog(
      BuildContext context, String message) async {
    return showDialog(
        context: context,
        builder: (BuildContext builder) => AlertDialog(
              backgroundColor: AppColor.navy,
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(AppDimen.h24)),
              actions: [
                TextButton(
                    onPressed: () => Get.back(),
                    child: Text(
                      Strings.close.tr,
                      style: AppFont.componentSmall
                          .copyWith(color: AppColor.white),
                    ))
              ],
              elevation: 5,
              content: SingleChildScrollView(
                child: Column(
                  children: [
                    Align(
                        alignment: Alignment.centerLeft,
                        child: GestureDetector(
                            onTap: () => Get.back(),
                            child: const Icon(Icons.close,
                                color: AppColor.white))),
                    Lottie.asset(
                      ImgStrings.lottieInfo,
                      animate: false,
                      repeat: false,
                      width: 80.h,
                      height: 80.h,
                    ),
                    Text(
                      "Info",
                      style:
                          AppFont.componentLarge.copyWith(color: Colors.white),
                    ),
                    AppDimen.h10.height,
                    Text(
                      message,
                      style:
                          AppFont.componentSmall.copyWith(color: Colors.white),
                    ),
                  ],
                ),
              ),
            ));
  }

  static Future<void> _showConfirmDeleteDialog(
      BuildContext context, String message, VoidCallback onDelete) async {
    return showDialog(
        context: context,
        builder: (BuildContext builder) => SizedBox(
              width: Constants.alertWidth(context),
              child: AlertDialog(
                backgroundColor: AppColor.navy,
                icon: Lottie.asset(ImgStrings.lottieDelete,
                    animate: true, repeat: false, width: 80.h, height: 80.h),
                title: Text(
                  Strings.deleteItem.tr,
                  style: AppFont.componentLarge.copyWith(color: Colors.white),
                ),
                content: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      Strings.removeItemFromCart.trParams({'name': message}),
                      style: AppFont.paragraphSmallBold
                          .copyWith(color: Colors.white),
                    )
                  ],
                ),
                actions: [
                  TextButton(
                      onPressed: onDelete,
                      child: Text(
                        Strings.delete.tr,
                        style: AppFont.componentSmall
                            .copyWith(color: AppColor.white),
                      )),
                  TextButton(
                      onPressed: () => Get.back(),
                      child: Text(
                        Strings.cancel.tr,
                        style: AppFont.componentSmall
                            .copyWith(color: AppColor.white),
                      ))
                ],
              ),
            ));
  }

  static Future<void> _showConfirmWarningDialog(BuildContext context,
      String message, List<Widget>? content, List<Widget>? actions) async {
    return showDialog(
        context: context,
        builder: (BuildContext builder) => SizedBox(
              width: Constants.alertWidth(context),
              child: AlertDialog(
                backgroundColor: AppColor.navy,
                icon: Lottie.asset(
                  ImgStrings.lottieWarning,
                  animate: true,
                  repeat: false,
                  width: 80.h,
                  height: 80.h,
                ),
                title: Text(
                  "Warning",
                  style: AppFont.componentLarge.copyWith(color: Colors.white),
                ),
                content: SingleChildScrollView(
                  physics: const NeverScrollableScrollPhysics(),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: content ??
                        [
                          Text(
                            message,
                            style: AppFont.componentSmall
                                .copyWith(color: Colors.white),
                          ),
                        ],
                  ),
                ),
                actions: actions ??
                    [
                      TextButton(
                          onPressed: () => Get.back(),
                          child: Text(
                            Strings.close.tr,
                            style: AppFont.componentSmall
                                .copyWith(color: AppColor.white),
                          ))
                    ],
              ),
            ));
  }

  static Future<void> _showConfirmInfoDialog(BuildContext context,
      String message, List<Widget>? content, List<Widget>? actions,
      {bool barrierDismissible = false}) async {
    return showDialog(
        context: context,
        barrierDismissible: barrierDismissible,
        builder: (BuildContext builder) => SizedBox(
              width: Constants.alertWidth(context),
              child: AlertDialog(
                backgroundColor: AppColor.navy,
                icon: Lottie.asset(
                  ImgStrings.lottieInfo,
                  animate: true,
                  repeat: false,
                  width: 80.h,
                  height: 80.h,
                ),
                title: Text(
                  "Info",
                  style: AppFont.componentLarge.copyWith(color: Colors.white),
                ),
                content: SingleChildScrollView(
                  physics: const NeverScrollableScrollPhysics(),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: content ??
                        [
                          Text(
                            message,
                            style: AppFont.componentSmall
                                .copyWith(color: Colors.white),
                          ),
                        ],
                  ),
                ),
                actions: actions ??
                    [
                      TextButton(
                          onPressed: () => Get.back(),
                          child: Text(
                            Strings.close.tr,
                            style: AppFont.componentSmall
                                .copyWith(color: AppColor.white),
                          ))
                    ],
              ),
            ));
  }
}
