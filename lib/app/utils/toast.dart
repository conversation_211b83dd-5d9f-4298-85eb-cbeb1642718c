import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart' as FT;
import 'package:get/get.dart';
import 'package:mobile_crm/core/theme/themes.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../core/values/app_constants.dart';
import '../../core/values/app_strings.dart';
import '../modules/screen_wrapper/controller/wrapper_controller.dart';
import 'logger.dart';

class Toast {
  static final _controller = Get.find<WrapperController>();
  static Future<void> show(String message,
      {int duration = 3,
      ToastType type = ToastType.info,
      VoidCallback? onVisible,
      VoidCallback? onAction,
      String? onActionString,
      BuildContext? context}) async {
    if (GetPlatform.isAndroid) {
      var sdk = await _controller.getSdkVersion();
      infoLogger("SDK ", sdk);
      if ((int.tryParse(sdk) ?? 30) > 31) {
        return _showForAndroid11Above(message,
            type: type,
            duration: duration,
            onAction: onAction,
            onActionString: onActionString,
            onVisible: onVisible);
      }
    } else {
      return _showForUniversal(message,
          type: type,
          duration: duration,
          onAction: onAction,
          onActionString: onActionString,
          onVisible: onVisible);
    }
    return _showForUniversal(message,
        type: type,
        duration: duration,
        onAction: onAction,
        onActionString: onActionString,
        onVisible: onVisible);
  }

  static Future<void> _showForAndroid11Above(String message,
      {int duration = 3,
      ToastType type = ToastType.info,
      VoidCallback? onVisible,
      VoidCallback? onAction,
      String? onActionString}) async {
    FT.Fluttertoast.cancel();
    FT.Fluttertoast.showToast(
        msg: message,
        gravity: FT.ToastGravity.BOTTOM,
        timeInSecForIosWeb: 1,
        toastLength: FT.Toast.LENGTH_LONG,
        backgroundColor: type == ToastType.success
            ? AppColor.utilitySuccess
            : type == ToastType.warning
                ? AppColor.utilityWarning
                : type == ToastType.error
                    ? AppColor.utilityDanger
                    : type == ToastType.active
                        ? AppColor.utilityActive
                        : type == ToastType.dark
                            ? AppColor.utilityDark
                            : AppColor.utilityInfo,
        textColor: (type == ToastType.dark || type == ToastType.error)
            ? AppColor.white
            : AppColor.black,
        fontSize: Get.context != null ? 14.0.sp : 18.0);
  }

  static Future<void> _showForUniversal(String message,
      {int duration = 3,
      ToastType type = ToastType.info,
      VoidCallback? onVisible,
      VoidCallback? onAction,
      String? onActionString}) async {
    await Get.closeCurrentSnackbar();
    Get.showSnackbar(GetSnackBar(
      maxWidth: Constants.defaultMaxWidth,
      messageText: Text(message,
          style: AppFont.componentSmall.copyWith(
              color: type == ToastType.dark
                  ? AppColor.white
                  : type == ToastType.success
                      ? AppColor.white
                      : type == ToastType.error
                          ? AppColor.white
                          : AppColor.black)),
      borderRadius: AppDimen.h6,
      mainButton: onAction != null
          ? TextButton(
              onPressed: onAction,
              child: Text(
                (onActionString ?? Strings.retry.tr).toUpperCase(),
                style: AppFont.componentSmallBold.copyWith(
                    color: (type == ToastType.dark || type == ToastType.error)
                        ? AppColor.white
                        : AppColor.black),
              ),
            )
          : IconButton(
              icon: Icon(
                Icons.close,
                color: (type == ToastType.dark || type == ToastType.error)
                    ? AppColor.white
                    : AppColor.black,
              ),
              onPressed: () {
                Get.closeAllSnackbars();
              },
            ),
      isDismissible: true,
      snackPosition: SnackPosition.BOTTOM,
      snackStyle: SnackStyle.FLOATING,
      animationDuration: const Duration(milliseconds: 300),
      margin:
          EdgeInsets.symmetric(horizontal: AppDimen.h8, vertical: AppDimen.h6),
      duration: Duration(seconds: duration),
      backgroundColor: type == ToastType.success
          ? AppColor.utilitySuccess
          : type == ToastType.warning
              ? AppColor.utilityWarning
              : type == ToastType.error
                  ? AppColor.utilityDanger
                  : type == ToastType.active
                      ? AppColor.utilityActive
                      : type == ToastType.dark
                          ? AppColor.utilityDark
                          : AppColor.utilityInfo,
    ));
  }
}

enum ToastType { success, info, warning, error, active, dark }
