import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:mobile_crm/core/theme/themes.dart';

class SvgIcon {
  static Widget svgIcon(String path,
      {Size size = const Size(16, 16),
      VoidCallback? onPressed,
      Color color = AppColor.white}) {
    return InkWell(
      onTap: onPressed,
      child: SvgPicture.asset(
        path,
        height: size.height.h,
        width: size.width.h,
        color: color,
      ),
    );
  }
}
