import 'package:get/get.dart';
import 'package:mobile_crm/app/utils/logger.dart';
import 'package:mobile_crm/app/utils/toast.dart';
import 'package:mobile_crm/core/values/values.dart';
import 'package:url_launcher/url_launcher.dart';

Future<void> openUrl(String url,
    {LaunchMode mode = LaunchMode.externalApplication}) async {
  Uri linkUrl = Uri.parse(url);
  try {
    if (url.isEmpty) {
      return;
    } else {
      await launchUrl(linkUrl, mode: LaunchMode.externalApplication);
    }
  } catch (e, s) {
    errorLogger(pos: 'URL HELPER _ OPEN URL', error: e, stackTrace: s);
  }
}

Future<void> openMap(double latitude, double longitude) async {
  Uri googleUrl = Uri.parse(
      'https://www.google.com/maps/search/?api=1&query=$latitude,$longitude');
  try {
    await launchUrl(googleUrl, mode: LaunchMode.externalApplication);
  } catch (e, s) {
    errorLogger(pos: 'URL HELPER _ OPEN MAP', error: e, stackTrace: s);
  }
}

Future<void> openNumber(String phone) async {
  var validation = GetUtils.isPhoneNumber(phone);

  if (!validation) {
    return Toast.show(Strings.invalidPhoneNumber.tr, type: ToastType.dark);
  }
  Uri phoneUrl = Uri.parse('tel:$phone');
  try {
    await launchUrl(phoneUrl, mode: LaunchMode.externalApplication);
  } catch (e, s) {
    errorLogger(pos: 'URL HELPER _ OPEN NUMBER', error: e, stackTrace: s);
  }
}
