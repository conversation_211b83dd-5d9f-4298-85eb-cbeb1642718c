import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:image_picker/image_picker.dart';
import 'package:path_provider/path_provider.dart';

class StorageHelper {
  static Future<XFile> downloadFile(String url, String fileName) async {
    var httpClient = HttpClient();
    var request = await httpClient.getUrl(Uri.parse(url));
    var response = await request.close();
    var bytes = await consolidateHttpClientResponseBytes(response);
    String path = await pathFile(fileName, bytes);
    return XFile(path, name: fileName, bytes: bytes);
  }

  static Future<String> pathFile(String fileName, List<int> fileData) async {
    final directory = await getTemporaryDirectory();
    if (!(await directory.exists())) {
      await directory.create(recursive: true);
    }
    final path = directory.absolute.path;
    final File file = File('$path/$fileName.jpg');
    await file.writeAsBytes(fileData);
    await file.create();
    return file.path;
  }
}
