import 'package:get/get.dart';
import 'package:flutter/src/foundation/constants.dart';
import 'package:mobile_crm/app/enum/permission_type_enum.dart';
import 'package:mobile_crm/app/utils/toast.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

class PermissionHandlerHelper {
  static Future<bool> requestPermission(PermissionTypeEnum type) async {
    if (Get.testMode) {
      return false;
    }
    switch (type) {
      case PermissionTypeEnum.camera:
        return await _getStatus(Permission.camera);
      case PermissionTypeEnum.storage:
        return await _getStatus(Permission.storage);
      case PermissionTypeEnum.location:
        if (kIsWeb) {
          return false;
        }
        return await _getStatus(Permission.locationWhenInUse);
      case PermissionTypeEnum.fineLocation:
        if (kIsWeb) {
          return false;
        }
        return await _getStatus(Permission.location);
      case PermissionTypeEnum.notification:
        return await _getStatus(Permission.notification);
      case PermissionTypeEnum.manageExternalStorage:
        return await _getStatus(Permission.manageExternalStorage);
      case PermissionTypeEnum.photos:
        return await _getStatus(Permission.photos);
    }
  }

  static Future<bool> _getStatus(Permission permission) async {
    final status = await permission.status;
    Sentry.addBreadcrumb(Breadcrumb(
        type: 'debug',
        category: 'user.activity.permission.helper.getStatus',
        data: {
          'permission': status,
        },
        level: SentryLevel.debug));
    if (status.isGranted) {
      return true;
    }
    if (status.isDenied) {
      final result = await permission.request();
      if (result.isDenied) {
        Toast.show("Permission is Denied, cant use this feature",
            type: ToastType.error);
      }
      return result.isGranted;
    }

    if (status.isRestricted) {
      Toast.show("Permission is Restricted, cant use this feature",
          type: ToastType.error);
      return false;
    }

    if (status.isPermanentlyDenied) {
      await openAppSettings();
      return false;
    }
    return false;
  }

  static Future<void> requestMultiplePermission(
      List<PermissionTypeEnum> types) async {
    for (var type in types) {
      await requestPermission(type);
    }
  }
}
