import 'dart:math';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:get/get_connect/http/src/exceptions/exceptions.dart';
import 'package:logger/logger.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

import '../exception/response_code_exception.dart';

final _logPrinter = PrettyPrinter(
  methodCount: 2,
  errorMethodCount: 8,
  lineLength: 120,
  colors: true,
  printEmojis: true,
  printTime: false,
);

final _logger = Logger(printer: _logPrinter);

void errorLogger(
    {required String pos,
    required Object error,
    StackTrace? stackTrace,
    Response? response}) {
  // if(Get.testMode){
  //   return;
  // }
  if (error is! GetHttpException) {
    _logger.log(Level.error, '[$pos] Error Occurred', error, stackTrace);
  }

  if (response != null) {
    responseCodeExceptionHandler(response);
  }

  // Sentry.captureException(error, stackTrace: stackTrace);
}

void infoLogger(String pos, [Object? message]) {
  if (!Get.testMode) {
    _logger.log(
        Level.info, '[$pos] ---------- Info ----------\n${message ?? ''}');
  }
}

void printLongString(String text) {
  final RegExp pattern = RegExp('.{1,800}'); // 800 is the size of each chunk
  pattern
      .allMatches(text)
      .forEach((RegExpMatch match) => print(match.group(0)));
}

void debugLogger(dynamic request, dynamic response) {
  if (!Get.testMode) {
    _logger.log(Level.debug,
        'REQUEST ${request.method.toUpperCase()} - ${response.statusCode} - ${request.url}\n------ Header ------\n${request.headers}\n---------- Body ----------\n${response.bodyString}');
  }
}

void debugPrintStack({StackTrace? stackTrace, String? label, int? maxFrames}) {
  if (label != null) {    
    _logger.log(Level.debug, label);
  }
  if (stackTrace == null) {
    stackTrace = StackTrace.current;
  } else {
    stackTrace = FlutterError.demangleStackTrace(stackTrace);
  }
  Iterable<String> lines = stackTrace.toString().trimRight().split('\n');
  if (kIsWeb && lines.isNotEmpty) {
    // Remove extra call to StackTrace.current for web platform.
    // TODO(ferhat): remove when https://github.com/flutter/flutter/issues/37635
    // is addressed.
    lines = lines.skipWhile((String line) {
      return line.contains('StackTrace.current') ||
          line.contains('dart-sdk/lib/_internal') ||
          line.contains('dart:sdk_internal');
    });
  }
  if (maxFrames != null) {
    lines = lines.take(maxFrames);
  }
  debugPrint(FlutterError.defaultStackFilter(lines).join('\n'));
}
