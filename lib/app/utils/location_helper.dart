import 'package:geocoding/geocoding.dart';
import 'package:geolocator/geolocator.dart';
import 'package:mobile_crm/app/utils/logger.dart';

class LocationHelper {
  LocationHelper._privateConstructor();

  static final LocationHelper instance = LocationHelper._privateConstructor();

  Future<Position?> getPosition() => _determinePosition();

  Future<List<Placemark>> getPlacemarks() async {
    Position? position = await _determinePosition();

    return await placemarkFromCoordinates(
        position?.latitude ?? 0, position?.longitude ?? 0);
  }

  Future<Map<String, double>> getLatling() async {
    Position? position = await _determinePosition();
    Map<String, double> latling = {
      "latitude": position?.latitude ?? 0,
      "longitude": position?.longitude ?? 0
    };
    return latling;
  }

  Future<List<Placemark>> getMarkerPosition(latitude, longitude) async {
    return await placemarkFromCoordinates(latitude, longitude);
  }

  Future<Position?> _determinePosition() async {
    bool serviceEnabled;
    LocationPermission permission;
    serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      return null;
    }
    permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        infoLogger('LOCATION', "PERMISSION DENIED \n $permission");
        return null;
      }
    }
    if (permission == LocationPermission.deniedForever) {
      infoLogger('LOCATION',
          "LOCATION PERMISSIONS ARE PERMANENTLY DENIED, CANNOT REQUEST PERMISSIONS \n $permission");
      return null;
    }
    return await Geolocator.getCurrentPosition();
  }
}
