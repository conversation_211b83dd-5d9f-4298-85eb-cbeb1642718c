import 'dart:math';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:mobile_crm/core/extensions/int_extensions.dart';
import 'package:mobile_crm/core/values/values.dart';
import 'package:mobile_crm/data/models/tax_model.dart';
import 'package:palette_generator/palette_generator.dart';
import 'package:timeago/timeago.dart' as ago;

class Utils {
  static String millisecondToDate(int? value,
      {bool withTime = false, bool timeOnly = false}) {
    var tmp = DateTime.fromMillisecondsSinceEpoch(value ?? 0);
    DateFormat.yMMMd().format(DateTime.now());
    return timeOnly
        ? DateFormat('HH:mm').format(tmp)
        : withTime
            ? DateFormat('dd MMMM yyyy, HH:mm').format(tmp)
            : DateFormat('d MMMM y').format(tmp);
  }

  static int generateUniqueNumber(List<int> list) {
    int number;
    do {
      number = Random().nextInt(1000); // Adjust the range as needed
    } while (list
        .contains(number)); // Check if the number already exists in the list
    return number;
  }

  static int dateToMillisecond(DateTime datetime) {
    final String time = datetime.toLocal().toString().split(' ')[0];
    return DateTime.parse(time.toString()).millisecondsSinceEpoch;
  }

  static String toTimeFormat({String? time}) {
    var start = DateTime.parse("2000-01-01T${time ?? '00:00:00'}");
    var after = DateFormat(DateFormat.HOUR24_MINUTE).format(start);
    return after;
  }

  static String timeLeft(int? time) {
    var nowTime = DateTime.now();
    var endTime = DateTime.fromMillisecondsSinceEpoch(time ?? 0);

    var hours = endTime.difference(nowTime).inHours;
    var day = endTime.difference(nowTime).inDays;
    return day < 1
        ? "${hours.toString()} ${Strings.hoursLeft.tr}"
        : "${Strings.until.tr} ${time.toDate}";
  }

  static String formatCurrency(int? currency) {
    final currencyFormatter = NumberFormat('#,##0', 'ID');
    return currencyFormatter.format(currency ?? 0);
  }

  static String removeHtmlTag(String text) {
    return text.replaceAll(RegExp(r'<[^>]*>|&[^;]+;'), ' ');
  }

  static String htmlParsing(String text) {
    var data = text
        .replaceAll('\u003c', '<')
        .replaceAll('\u003e', '>')
        .replaceAll('"', '"');
    return data;
  }

  static String timeAgo(int? milis) {
    DateTime time = DateTime.fromMillisecondsSinceEpoch(milis ?? 0).toLocal();
    return ago.format(time);
  }

  static String dayAgo(int? milis) {
    DateTime now = DateTime.now();
    DateTime date = DateTime.fromMillisecondsSinceEpoch(milis ?? 0).toLocal();
    if (date.day == now.day &&
        date.month == now.month &&
        date.year == now.year) {
      return Strings.timeToday.tr;
    } else if (date.day == now.subtract(const Duration(days: 1)).day &&
        date.month == now.subtract(const Duration(days: 1)).month &&
        date.year == now.subtract(const Duration(days: 1)).year) {
      return Strings.timeYesterday.tr;
    } else if (date.isAfter(now.subtract(const Duration(days: 7)))) {
      return Strings.timePrev7D.tr;
    } else if (date.isAfter(now.subtract(const Duration(days: 30)))) {
      return Strings.timePrev30D.tr;
    } else {
      return DateFormat.yMMMd().format(date);
    }
  }

  static bool isCurrentTimeWithinRange(String? hourStart, String? hourEnd) {
    var now = DateTime.now().toLocal();
    var start = DateTime.parse("2000-01-01T${hourStart ?? '00:00:00'}");
    var end = DateTime.parse("2000-01-01T${hourEnd ?? '00:00:00'}");
    return now.hour >= start.hour && now.hour < end.hour ||
        (now.hour == start.hour && now.minute >= start.minute) ||
        (now.hour == end.hour && now.minute < end.minute);
  }

  static String dayAgoV2(int? milis) {
    DateTime now = DateTime.now();
    DateTime date = DateTime.fromMillisecondsSinceEpoch(milis ?? 0).toLocal();

    int dayDiff = now.difference(date).inDays;
    int monthDiff = (now.year - date.year) * 12 + now.month - date.month;

    if (dayDiff == 0) {
      return Strings.timeToday.tr;
    } else if (dayDiff == 1) {
      return Strings.timeYesterday.tr;
    } else if (dayDiff < 7) {
      return Strings.timeDaysAgo.trParams({'value': '$dayDiff'});
    } else if (dayDiff >= 7 && dayDiff < 14) {
      return Strings.timeWeeksAgo.trParams({'value': '1'});
    } else if (dayDiff >= 14 && dayDiff < 21) {
      return Strings.timeWeeksAgo.trParams({'value': '2'});
    } else if (dayDiff >= 21 && dayDiff < 28) {
      return Strings.timeWeeksAgo.trParams({'value': '3'});
    } else if (monthDiff >= 1 && monthDiff < 12) {
      return Strings.timeMonthsAgo.trParams({'value': '$monthDiff'});
    } else if (monthDiff >= 12) {
      int yearDiff = monthDiff ~/ 12;
      return Strings.timeYearsAgo.trParams({'value': '$yearDiff'});
    } else {
      return DateFormat.yMMMd().format(date);
    }
  }

  static bool isTimeExpired({required DateTime datetime}) {
    var now = DateTime.now();
    var item =
        DateTime.fromMillisecondsSinceEpoch(datetime.millisecondsSinceEpoch);
    return item.difference(now).isNegative;
  }

  static String convertPhoneNumber(String phone, {bool reverse = false}) {
    if (phone.isEmpty || phone.length <= 8) {
      return phone;
    }
    var tmp = phone.substring(0, 3);
    if (reverse) {
      if (tmp.contains("+62")) {
        return phone.replaceRange(0, 3, "0");
      } else if (tmp.contains("62")) {
        return phone.replaceRange(0, 2, "0");
      } else if (tmp.isNotEmpty) {
        return tmp[0] == "0" ? phone : "0$phone";
      } else {
        return phone;
      }
    }

    if (tmp.isNotEmpty) {
      if (tmp[0] == "0") {
        return phone.replaceRange(0, 1, "+62");
      } else if (tmp.contains("+62")) {
        return phone;
      } else if (tmp[0] == "6" && tmp[1] == "2") {
        return phone.replaceRange(0, 2, "+62");
      } else if (tmp[0] != "0" && tmp[0] != "+" && tmp[0] != "6") {
        return "+62$phone";
      }
    }

    return phone;
  }

  static bool isEmoji(String input) {
    RegExp regExp = RegExp(
        r"(\u00a9|\u00ae|[\u2000-\u3300]|\ud83c[\ud000-\udfff]|\ud83d[\ud000-\udfff]|\ud83e[\ud000-\udfff])");
    return regExp.hasMatch(input);
  }

  static double calculatePercentage(double value, {required double maxValue}) {
    if (value < maxValue) {
      return value / maxValue * 100;
    } else {
      return 100;
    }
  }

  static Future<Color> getColorFromImage(String image) async {
    if (image == '' || image.isEmpty) {
      return Colors.white;
    }
    PaletteGenerator paletteGenerator =
        await PaletteGenerator.fromImageProvider(NetworkImage(image));
    return paletteGenerator.colors.first;
  }
}

int randomRange(int min, int max) {
  final random = Random();
  return min + random.nextInt(max - min);
}

int calculateTax({required TaxModel tax, int priceSell = 0, int qty = 0}) {
  double sum = 0;
  switch (tax.typeTax) {
    case 'percentage':
      sum += ((priceSell * qty) * ((tax.jumlah ?? 0) / 100));
      break;
    case 'nominal':
      {
        sum += ((priceSell * qty) - (tax.jumlah ?? 0));
      }
      break;
  }
  return sum.toInt();
}

int sumTax(List<TaxModel> tax,
    {bool toRound = false, int toPrecision = 1, bool useDiscount = false}) {
  return tax.fold(
      0,
      (previousValue, element) =>
          previousValue +
          (useDiscount
              ? (element.currentTax == element.disTax
                  ? (element.currentTax ?? 0)
                  : (element.disTax ?? 0))
              : (element.currentTax ?? 0)));
}

String sumGrandTotal(List<TaxModel> tax, int total, {int? shipmentCharge}) {
  int grandTotal = (sumTax(tax, useDiscount: true)) + (total);
  int rounded = grandTotal +
      ((100 - grandTotal % 100) != 100 ? (100 - grandTotal % 100) : 0) +
      (shipmentCharge ?? 0);
  return rounded.toCurrency;
}

String convertCurrentMembership(int level, int index, int length) {
  if (level == 0 && (index + 1) == length) {
    return "Current";
  }

  return level == 0
      ? "Pass"
      : level == 1
          ? "Current"
          : "Locked";
}

String getFileSizeString({required int bytes, int decimals = 0}) {
  const suffixes = ["b", "kb", "mb", "gb", "tb"];
  var i = (log(bytes) / log(1024)).floor();
  return ((bytes / pow(1024, i)).toStringAsFixed(decimals)) + suffixes[i];
}

String translationOrderStatus(String status) {
  switch (status.toLowerCase()) {
    case "pending":
      return Strings.statusPending.tr;
    case "cancel":
      return Strings.statusCancel.tr;
    case "accept":
      return Strings.statusAccept.tr;
    case "payment_verification":
      return Strings.statusPaymentVerification.tr;
    case "payment_verified":
      return Strings.orderIsProcessed.tr;
    case "payment_reject":
      return Strings.statusPaymentReject.tr;
    case "reject":
      return Strings.statusReject.tr;
    case "received":
      return Strings.statusReceived.tr;
    case "arrived":
      return Strings.statusArrived.tr;
    case "expired":
      return Strings.statusExpired.tr;
    case "already_paid":
      return Strings.statusAlreadyPaid.tr;
    case "ready":
      return Strings.statusReady.tr;
    default:
      return "Unknown";
  }
}

bool isDayMeetRequirement({List<String>? weekday}) =>
    weekday?.firstWhereOrNull((element) =>
                element.toString().toLowerCase() ==
                DateFormat(DateFormat.WEEKDAY)
                    .format(DateTime.now())
                    .toLowerCase()) !=
            null
        ? true
        : false;

bool isDateMeetRequirement({int? dateStart, int? dateEnd}) {
  var dS = DateTime.fromMillisecondsSinceEpoch(dateStart ?? 0);
  var dE = DateTime.fromMillisecondsSinceEpoch(dateEnd ?? 0);
  return dE.difference(dS).inSeconds.isGreaterThan(0);
}

bool isTimeMeetRequirement({String? timeStart, String? timeEnd}) {
  var now = DateTime.now();
  var start = DateTime.parse("2000-01-01T${timeStart ?? '00:00:00'}");
  var end = DateTime.parse("2000-01-01T${timeEnd ?? '00:00:00'}");
  return now.hour >= start.hour && now.hour < end.hour ||
      (now.hour == start.hour && now.minute >= start.minute) ||
      (now.hour == end.hour && now.minute < end.minute);
}

int getMemberInfoLevel(double currentValue, double prevValue,
    double targetValue, double nextLevel) {
  if (currentValue >= targetValue && nextLevel != targetValue) {
    // Pass
    return 0;
  }

  if ((currentValue < targetValue && currentValue >= prevValue)) {
    // Current
    return 1;
  }

  // locked
  return 2;
}

String convertNumber(int num) {
  if (num > 1000) {
    int numDividedBy1000 = (num / 1000).floor();
    return "${numDividedBy1000}k+";
  } else if (num > 100) {
    return "99+";
  } else {
    return num.toString();
  }
}

bool isTimeExpired({required int milliSeconds}) {
  var now = DateTime.now();
  var item = DateTime.fromMillisecondsSinceEpoch(milliSeconds);
  return item.difference(now).isNegative;
}

String simplifyStockNumber(int stock) {
  if (stock >= 1000000) {
    return '${(stock / 1000000).toStringAsFixed(0)}JT';
  } else if (stock >= 1000) {
    return '${(stock / 1000).toStringAsFixed(stock % 1000 == 0 ? 0 : 1)}RB';
  } else {
    return stock.toString();
  }
}