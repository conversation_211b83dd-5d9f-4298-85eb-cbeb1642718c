// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/app/utils/logger.dart';
import 'package:mobile_crm/app/utils/storage_helper.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/data/providers/db/database.dart';
import 'package:mobile_crm/data/repository/app_repository.dart';
import 'package:mobile_crm/data/services/dynamic_links_service.dart';
import 'package:sentry_flutter/sentry_flutter.dart';
import 'package:share_plus/share_plus.dart';

import '../../data/services/analytics_service.dart';

final _repo = Get.find<AppRepositoryIml>();

Future<void> shareProduct(Product? product) async {
  if (product == null) {
    return;
  }
  String baseDescription =
      "\"${product.name}\", Rp ${(product.price.toCurrency)}. \n${product.description}.";
  String productId = product.productId.toString();
  String productName = product.name ?? '';
  String? productImage = product.photo;
  Sentry.addBreadcrumb(Breadcrumb(
      type: 'debug',
      category: 'user.activity.share.product',
      data: {"description": baseDescription, "product_id": productId},
      level: SentryLevel.debug));

  return await _shareItem(
      ShareType.product, productId, productName, baseDescription, productImage);
}

Future<void> shareDeal(DealData? deal) async {
  if (deal == null) {
    return;
  }

  String baseDescription =
      "\"${deal.name}\", valid until ${deal.timeActive?.endPromotionDate.toDate}.\n${deal.term.toString().removeHtmlTag}.";
  String dealId = (deal.promotionId ?? 0).toString();
  String? image = deal.photo;
  AnalyticsService.observer.analytics
      .logEvent(name: "share_deal", parameters: {"deal_name": deal.name});
  Sentry.addBreadcrumb(Breadcrumb(
      type: 'debug',
      category: 'user.activity.share.deal',
      data: {"description": baseDescription, "deal_id": deal.promotionId},
      level: SentryLevel.debug));
  return await _shareItem(
      ShareType.deal, dealId, deal.name.toString(), baseDescription, image);
}

Future<ShareResult> shareQR(String image, String name, String message) async {
  try {
    var file = await StorageHelper.downloadFile(image, name);
    final RenderBox? box = Get.context!.findRenderObject() as RenderBox?;
    var result = await Share.shareXFiles([file],
        text: message,
        subject: file.name,
        sharePositionOrigin: box!.localToGlobal(Offset.zero) & box.size);

    AnalyticsService.observer.analytics.logShare(
        contentType: "share_qr", itemId: file.name, method: result.raw);

    return result;
  } catch (e, s) {
    errorLogger(pos: "Share QR", error: e, stackTrace: s);
    return Future.error(e);
  }
}

Future _shareItem(
    ShareType type, String id, String name, String description, String? image,
    {String? tableInput}) async {
  try {
    var resp = await _repo.getShareLink(type.name, id);
    if (resp.status) {
      return _shareLinkWithImage(
          name: name,
          description: description,
          url: resp.data?.shortLink ?? '',
          image: image);
    }
  } catch (e) {
    // AppAlert.showDebug(e.toString());
    return _createDynamicLink(type, id, name, description, image,
        tableInput: tableInput);
  }
}

void _createDynamicLink(
    ShareType type, String id, String name, String description, String? image,
    {String? tableInput}) async {
  try {
    String url = await DynamicLinksService.createDynamicLink(
        type.name.toString(), id,
        tableInput: tableInput);
    await _shareLinkWithImage(
        name: name, description: description, url: url, image: image);
  } catch (e, s) {
    // AppAlert.showDebug(e.toString());
    errorLogger(
        pos: "SHARE HELPER _ SHARE ${type.toString()}",
        error: e,
        stackTrace: s);
  }
}

Future _shareLinkWithImage(
    {required String name,
    required String description,
    required String url,
    String? image}) async {
  final RenderBox? box = Get.context!.findRenderObject() as RenderBox?;
  String bodyText = "Check this out: $description";
  String footerText = url;
  String shareText = '$bodyText\n$footerText';

  try {
    if (image == null || image == '') {
      AnalyticsService.observer.analytics
          .logShare(contentType: "share", itemId: name, method: "nil");
      await Share.share(shareText,
          subject: name,
          sharePositionOrigin: box!.localToGlobal(Offset.zero) & box.size);
      infoLogger("share no image", "message");
    } else {
      var file = await StorageHelper.downloadFile(image, name);
      var result = await Share.shareXFiles([file],
          text: shareText,
          subject: name,
          sharePositionOrigin: box!.localToGlobal(Offset.zero) & box.size);
      AnalyticsService.observer.analytics
          .logShare(contentType: "share", itemId: name, method: result.raw);
    }
  } catch (e, s) {
    await Share.share(shareText,
        subject: name,
        sharePositionOrigin: box!.localToGlobal(Offset.zero) & box.size);
    AnalyticsService.observer.analytics
        .logShare(contentType: "share", itemId: name, method: "nil");
    errorLogger(
        pos: "SHARE HELPER _ SHARE WITH IMAGE", error: e, stackTrace: s);
  }
}

enum ShareType { outlet, product, deal }

String truncateText(String text, {int maxLength = 10}) {
  return text.length > maxLength ? '${text.substring(0, maxLength)}...' : text;
}
