import 'package:get/get.dart';
import 'package:mobile_crm/app/modules/screen_wrapper/controller/wrapper_controller.dart';
import 'package:mobile_crm/data/providers/db/database.dart';
import 'package:mobile_crm/data/repository/app_repository.dart';
import 'package:mobile_crm/data/repository/config_repository.dart';
import 'package:mobile_crm/data/repository/notification_me_repository.dart';
import 'package:mobile_crm/data/repository/product_repository.dart';

class DependencyCreator {
  static init() {
    try {
      Get.find<AppDb>();
    } catch (e) {
      Get.put(AppDb(), permanent: true);
    }
    Get.lazyPut(() => ConfigRepositoryIml());
    Get.lazyPut(() => AppRepositoryIml());
    Get.lazyPut(() => NotificationMeRepositoryIml());
    Get.lazyPut(() => ProductRepositoryIml());
    Get.lazyPut(() => ProductRepositoryIml());
    Get.put(WrapperController(Get.find<ConfigRepositoryIml>()),
        permanent: true);
  }
}
