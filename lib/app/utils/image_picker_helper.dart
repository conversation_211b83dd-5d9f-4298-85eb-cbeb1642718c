import 'package:image_picker/image_picker.dart';
import 'package:mobile_crm/app/enum/permission_type_enum.dart';
import 'package:mobile_crm/app/utils/logger.dart';
import 'package:mobile_crm/app/utils/utils.dart';

import 'permission_handler_helper.dart';

class ImagePickerHelper {
  static Future<XFile?> getImageFromGallery() async {
    try {
      final ImagePicker picker = ImagePicker();
      XFile? xFile =
          await picker.pickImage(source: ImageSource.gallery, imageQuality: 50);
      int? fileSize = await xFile?.length();
      infoLogger("Get Image Gallery",
          "File Gallery ${getFileSizeString(bytes: fileSize ?? 0)}");
      return xFile;
    } catch (e, s) {
      errorLogger(pos: "Get Image Gallery", error: e, stackTrace: s);
      return Future.error("$e");
    }
  }

  static Future<XFile?> getImageFromCamera() async {
    try {
      if (await PermissionHandlerHelper.requestPermission(
              PermissionTypeEnum.camera) ==
          false) {
        return Future.error("Permission Denied");
      }
      final ImagePicker picker = ImagePicker();
      XFile? xFile =
          await picker.pickImage(source: ImageSource.camera, imageQuality: 50);
      // infoLogger("Get Image Camera",
      //     "File Camera ${getFileSizeString(bytes: fileSize ?? 1000)}");
      return xFile;
    } catch (e, s) {
      errorLogger(pos: "Get Image Camera", error: e, stackTrace: s);
      return Future.error("$e");
    }
  }

  static Future<List<XFile>?> getMultiImage() async {
    final ImagePicker picker = ImagePicker();
    return await picker.pickMultiImage();
  }
}
