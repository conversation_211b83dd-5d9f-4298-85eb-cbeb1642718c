import 'dart:convert';

import 'package:get/get_utils/get_utils.dart';
import 'package:mobile_crm/app/utils/logger.dart';
import 'package:mobile_crm/app/utils/utils.dart';
import 'package:mobile_crm/data/models/product_group_model.dart';
import 'package:mobile_crm/data/providers/db/local/dao/dao.dart';

import '../../data/models/available_model.dart';
import '../../data/models/product_model.dart';
import '../../data/models/variant_model.dart';
import '../../data/providers/db/database.dart';
import '../utils/dbhelper.dart';

class ProductHelper {
  static final AppDb _db = DatabaseHelper.instance.database;

  /// Take one if there is more than one of the same product
  static List<ProductModel> filterDuplicateProduct(
      List<ProductModel> products) {
    List<ProductModel> listP = [];
    List<ProductModel> prod = [];
    prod.addAll(products);
    prod.sort(
      (a, b) => (a.productId ?? 0).compareTo(b.productId ?? 0),
    );
    for (var index = 0; index < (prod.length); index++) {
      if (index == 0 || (prod[index - 1].productId != prod[index].productId)) {
        listP.add(prod[index]);
      }
    }
    listP.sort((a, b) => (a.name ?? '').compareTo(b.name ?? ''));
    return listP;
  }

  static List<ProductGroupModel> filterBySubCategory(
      {required List<Product> products, int? outletId, OutletDao? outletDao}) {
    products
        .sort((a, b) => (a.subcategory ?? '').compareTo((b.subcategory ?? '')));
    List<ProductGroupModel> productGroupBySub = [];
    products.asMap().forEach((index, product) {
      if (index == 0 ||
          product.subcategory != products[index - 1].subcategory) {
        productGroupBySub.add(ProductGroupModel(
            categoryName: product.subcategory ?? '',
            categoryPosition: product.subcategoryPosition ?? 9999));
      }
    });

    productGroupBySub.asMap().forEach((index, category) {
      var sortProduct = products
          .where((element) =>
              element.subcategory == productGroupBySub[index].categoryName)
          .toList();
      if (outletId != null) {
        sortProduct = filterProductByStockStatus(
            products: sortProduct, outletId: outletId);
      } else {
        sortProduct = filterProductByAvailability(
            products: sortProduct, outletDao: outletDao);
      }
      productGroupBySub[index].products = sortProduct;
    });

    // infoLogger('productGroupBySub',productGroupBySub.toString());
    // infoLogger('productGroupBySubJson', jsonEncode(productGroupBySub));
    // infoLogger('productGroupBySub - product', jsonEncode(products));

    productGroupBySub
        .sort((a, b) => a.categoryPosition.compareTo(b.categoryPosition));
    return productGroupBySub;
  }

  static List<Product> filterProductByAvailability(
      {required List<Product> products, OutletDao? outletDao}) {
    List<Product> allProductFiltered = [];
    List<Product> availableProduct = [];
    List<Product> unavailableProduct = [];

    for (var product in products) {
      if (product.availability == 'available') {
        availableProduct.add(product);
      } else {
        unavailableProduct.add(product);
      }
    }

    availableProduct.sort((a, b) => (a.name ?? '').compareTo(b.name ?? ''));
    unavailableProduct.sort((a, b) => (a.name ?? '').compareTo(b.name ?? ''));

    allProductFiltered.addAll(availableProduct);
    allProductFiltered.addAll(unavailableProduct);
    return allProductFiltered;
  }

  static AvailableModel? isProductAvailableOnThisOutlet(
      {required Product product, required int outletId}) {
    if (product.variant?.isNotEmpty == true && product.variant != null) {
      AvailableModel? found;
      List<VariantModel> variants = product.variant ?? [];
      for (var element in variants) {
        var cek = element.available
            ?.firstWhereOrNull((element) => element.outletId == outletId);
        if (cek?.enableOrder?.status == "enable") {
          found = cek;
          break;
        }
      }
      return found;
    }
    return product.available
        ?.firstWhere((element) => element.outletId == outletId);
  }

  static List<Product> filterProductByStockStatus(
      {required List<Product> products, required int outletId}) {
    List<Product> allProductFiltered = [];
    List<Product> availableProduct = [];
    List<Product> unavailableProduct = [];

    for (var product in products) {
      var available =
          isProductAvailableOnThisOutlet(product: product, outletId: outletId);
      if (available?.enableOrder?.status == 'enable') {
        availableProduct.add(product);
      } else {
        unavailableProduct.add(product);
      }
    }

    availableProduct.sort((a, b) => (a.name ?? '').compareTo(b.name ?? ''));
    unavailableProduct.sort((a, b) => (a.name ?? '').compareTo(b.name ?? ''));

    allProductFiltered.addAll(availableProduct);
    allProductFiltered.addAll(unavailableProduct);
    return allProductFiltered;
  }

  static bool isProductAvailableOnHours(Product? product) {
    if (product != null) {
      return Utils.isCurrentTimeWithinRange(product.hourStart, product.hourEnd);
    }
    return false;
  }

  static Future<Product> sortAvailableAtDistance(
      {required Product product}) async {
    var outlet = await _db.outletDao.getAllOutlet();
    if (product.available != null) {
      List<AvailableModel> available = [];
      product.available?.forEach((element) async {
        var outletId =
            outlet.firstWhereOrNull((o) => o.outlet_id == element.outletId);
        element.outlet = outletId;
        available.add(element);
      });
      available.sort(
        (a, b) =>
            (a.outlet?.distance ?? '').compareTo(b.outlet?.distance ?? ''),
      );
      product.available = available;
    } else if (product.variant != null) {
      List<VariantModel> variantList = [];
      product.variant?.forEach((variant) {
        List<AvailableModel> available = [];
        variant.available?.forEach((element) async {
          var outletId =
              outlet.firstWhereOrNull((o) => o.outlet_id == element.outletId);
          element.outlet = outletId;
          available.add(element);
        });
        available.sort(
          (a, b) =>
              (a.outlet?.distance ?? '').compareTo(b.outlet?.distance ?? ''),
        );
        variant.available = available;
        variantList.add(variant);
      });
    }
    return product;
  }

  static Product? getProductByDetailId(
      {required List<Product> products, required int productDetailId}) {
    for (var product in products) {
      if (product.available != null) {
        var f = product.available?.firstWhereOrNull(
            (element) => element.productDetailId == productDetailId);
        if (f != null) {
          var prod = product;
          prod.available = [f];
          return prod;
        }
      } else if (product.variant != null) {
        for (var variant in product.variant!) {
          var f = variant.available?.firstWhereOrNull(
              (element) => element.productDetailId == productDetailId);
          if (f != null) {
            var vari = VariantModel(name: variant.name, available: [f]);
            var prod = Product(name: product.name, variant: [vari]);
            return prod;
          }
        }
      } else if (product.product_detail_id == productDetailId) {
        return product;
      }
    }
    return products.firstWhereOrNull(
        (element) => element.product_detail_id == productDetailId);
  }
}
