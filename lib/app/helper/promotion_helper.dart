import 'package:mobile_crm/core/extensions/extensions.dart';

import '../../data/providers/db/database.dart';

class PromotionHelper {
  static List<DealData> filterPromotionByActive(List<DealData> promotion) {
    List<DealData> masterPromotion = [];
    List<DealData> nonActivePromotion = [];
    List<DealData> activePromotion = [];

    for (var element in promotion) {
      for (var timeActive in element.timeActive?.dayActive ?? <String>[]) {
        int index = element.timeActive?.dayActive?.indexOf(timeActive) ?? 0;
        if (timeActive.dayIsToday) {
          activePromotion.add(element);
          break;
        }

        if (!timeActive.dayIsToday &&
            (index >= ((element.timeActive?.dayActive?.length ?? -1) - 1))) {
          nonActivePromotion.add(element);
          break;
        }
      }
    }

    activePromotion.sort(
      (a, b) => (a.name ?? '').compareTo(b.name ?? ''),
    );
    masterPromotion.addAll(activePromotion);
    masterPromotion.addAll(nonActivePromotion);

    return masterPromotion;
  }
}
