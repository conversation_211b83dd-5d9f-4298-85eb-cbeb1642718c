import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:mobile_crm/data/models/cart_model.dart';

import '../../core/values/app_strings.dart';
import '../../data/providers/db/database.dart';
import '../utils/utils.dart';

class DealHelper {
  static (bool, String, bool, bool, Duration) isPromoBeenPublished(
      int publishDate) {
    String dayPos = "";
    bool isUnder24Hours = false;
    bool isUnderWeekDay = false;
    var now = DateTime.now();
    var publishTime = DateTime.fromMillisecondsSinceEpoch(publishDate);
    var diff = publishTime.difference(now);
    if (diff.inDays > 6) {
      isUnder24Hours = false;
      isUnderWeekDay = false;
      dayPos =
          "${publishTime.day} ${DateFormat(DateFormat.YEAR_ABBR_MONTH).format(publishTime.toLocal())}";
    }

    if (diff.inDays > 0 && diff.inDays <= 6) {
      isUnderWeekDay = true;
      isUnder24Hours = false;
      dayPos = "${diff.inDays} more days";
    }

    if (diff.inHours <= 24) {
      isUnderWeekDay = true;
      isUnder24Hours = true;
    }
    return (diff.isNegative, dayPos, isUnder24Hours, isUnderWeekDay, diff);
  }

  static List<String> getPromoDays({required DealData deal}) {
    List<String> promoDays = [];
    List nums = [
      deal.sunday,
      deal.monday,
      deal.tuesday,
      deal.wednesday,
      deal.thursday,
      deal.friday,
      deal.saturday
    ];

    if (nums[0] == 1) promoDays.add("Sunday");
    if (nums[1] == 1) promoDays.add("Monday");
    if (nums[2] == 1) promoDays.add("Tuesday");
    if (nums[3] == 1) promoDays.add("Wednesday");
    if (nums[4] == 1) promoDays.add("Thursday");
    if (nums[5] == 1) promoDays.add("Friday");
    if (nums[6] == 1) promoDays.add("Saturday");
    return promoDays;
  }

  static String getPromoDaysV2(DealData deal) {
    List<String> redeemPeriod =
        deal.timeActive?.dayActive ?? getPromoDays(deal: deal);
    List<String> days = [];

    for (var day in redeemPeriod) {
      days.add(day.substring(0, 2));
    }

    if (days.isNotEmpty) {
      if (redeemPeriod.length == 1) {
        return "$redeemPeriod"
                .replaceAll("[", "")
                .replaceAll("]", "")
                .capitalize
                ?.tr ??
            '-';
      } else if (days.length == 7) {
        return Strings.everyDay.tr;
      } else if (days.length > 1) {
        return "$days".replaceAll("[", "").replaceAll("]", "").capitalize ??
            '-';
      }
    }

    return '-';
  }

  static List<DealData> dealSortFilter(List<DealData> deals) {
    List<DealData> listDealUnPublishedTop = [];
    List<DealData> listDealPublishMiddle = [];
    List<DealData> listDealPublishExceedQuota = [];
    List<DealData> listDealUnPublishedBottom = [];
    List<DealData> listDeals = [];

    for (var element in deals) {
      var deal = isPromoBeenPublished(element.publishDate ?? 0);
      bool dealIsUnder24Hours = deal.$3;
      bool dealIsPublished = deal.$1;
      bool dealIsOverQuotaOrExceed =
          ((element.maximumRedeem ?? 0) - (element.totalRedeem ?? 0) <= 0);
      if (!dealIsPublished && dealIsUnder24Hours) {
        listDealUnPublishedTop.add(element);
        continue;
      }

      if (dealIsPublished && dealIsOverQuotaOrExceed) {
        listDealPublishExceedQuota.add(element);
        continue;
      }

      if (dealIsPublished && !dealIsOverQuotaOrExceed) {
        listDealPublishMiddle.add(element);
        continue;
      }

      if (!dealIsPublished && !dealIsUnder24Hours) {
        listDealUnPublishedBottom.add(element);
        continue;
      }
    }

    if (listDealUnPublishedTop.isNotEmpty) {
      listDealUnPublishedTop.sort(
        (a, b) => (a.publishDate ?? 0).compareTo(b.publishDate ?? 0),
      );
      listDeals.addAll(listDealUnPublishedTop);
    }
    if (listDealPublishMiddle.isNotEmpty) {
      listDealPublishMiddle.sort((a, b) => (a.timeActive?.endPromotionDate ?? 0)
          .compareTo(b.timeActive?.endPromotionDate ?? 0));
      listDeals.addAll(listDealPublishMiddle);
    }
    if (listDealPublishExceedQuota.isNotEmpty) {
      listDeals.addAll(listDealPublishExceedQuota);
    }
    if (listDealUnPublishedBottom.isNotEmpty) {
      listDealUnPublishedBottom = listDealUnPublishedBottom
          .where((dl) => isPromoBeenPublished(dl.publishDate ?? 0).$4)
          .toList();
      listDealUnPublishedBottom.sort(
        (a, b) => (a.publishDate ?? 0).compareTo(b.publishDate ?? 0),
      );
      listDeals.addAll(listDealUnPublishedBottom);
    }
    return listDeals;
  }

  static bool isVoucherCanBeUserBasedOnDate(DealData voucher) {
    bool isDayMeetReq =
        isDayMeetRequirement(weekday: DealHelper.getPromoDays(deal: voucher));
    if (!isDayMeetReq) {
      return false;
    }
    bool isDateMeetReq = isDateMeetRequirement(
        dateStart: voucher.startPromotionDate,
        dateEnd: voucher.endPromotionDate);
    if (!isDateMeetReq) {
      return false;
    }

    bool isTimeMeetReq = isTimeMeetRequirement(
        timeStart: voucher.startPromotionTime,
        timeEnd: voucher.endPromotionTime);
    if (!isTimeMeetReq) {
      return false;
    }

    return true;
  }

  bool isCartQualifiedBasedOnTermProduct(DealData dealData, CartModel cart) {
    int totalQtyBuyTermProduct = 0;
    if (dealData.termProduct != null) {
      if (dealData.termProduct?.qty == 0) {
        return true;
      } else {
        for (var term in dealData.termProduct?.products ?? []) {
          if (term.productDetailFkId == cart.product_detail_id) {
            totalQtyBuyTermProduct += cart.qty ?? 0;
            break;
          }
        }
      }
    }
    return totalQtyBuyTermProduct >= (dealData.termProduct?.qty ?? 0);
  }
}
