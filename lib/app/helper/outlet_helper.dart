import '../../data/providers/db/database.dart';

class OutletHelper {
  static List<Outlet> sortOutlet({required List<Outlet> outlets}) {
    outlets.sort((a, b) {
      var editValueA = a.distance?.replaceAll("km", "").trim();
      double doubleA = double.tryParse(editValueA ?? "0.0") ?? 0.0;
      var editValueB = b.distance?.replaceAll("km", "").trim();
      double doubleB = double.tryParse(editValueB ?? "0.0") ?? 0.0;
      if ((doubleA == 0.0) && (doubleB == 0.0)) {
        return (a.name ?? "").compareTo(b.name ?? "");
      }
      return doubleA.compareTo(doubleB);
    });
    return outlets;
  }
}
