import 'dart:async';

import 'package:mobile_crm/data/services/local_storage.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

import '../utils/logger.dart';

class SentryHelper {
  static final _store = LocalStorageService();

  static Future<void> logException(dynamic exception, StackTrace? stackTrace,
      {Map<String, dynamic>? tags}) async {
    await Sentry.captureException(exception, stackTrace: stackTrace,
        withScope: (scope) {
      var user = _store.user;
      scope.setUser(SentryUser(
        name: user?.name,
        email: user?.email,
        id: user?.memberId.toString(),
      ));
      scope.setTag("user", user?.name ?? "Not Sign in");
    });
  }

  FutureOr<SentryEvent?> beforeSend(SentryEvent event, Hint hint) async {
    infoLogger("Sentry Event Before Send",
        "${event.throwable}\nRuntime Type ${event.throwable.runtimeType}\nRuntime Type String ${event.throwable.runtimeType.toString()}");
    switch (event.throwable.runtimeType.toString()) {
      case "LateError":
        if (event.throwable.toString().contains('minTextAdapt')) {
          return null;
        }
        if (event.throwable.toString().contains("Field '_controller")) {
          return null;
        }
        break;
      case "_TypeError":
      // if (event.throwable.toString().contains("Field '_controller")) {
      //   return null;
      // }
        break;
      case "minified:UI":
        if (event.throwable.toString().contains("NoSuchMethodError")) {
          return null;
        }
        break;
      case "minified:aF":
        if (event.throwable.toString().contains("BindingError")) {
          return null;
        }
        break;
      case "minified:Mz":
        if (event.throwable.toString().contains("TimeoutException")) {
          return null;
        }
        break;
      case "_Exception":
        if (event.throwable.toString().contains('errno = 101')) {
          return null;
        }
        if (event.throwable.toString().contains('errno = 8')) {
          return null;
        }
        break;
      case "_AssertionError":
        break;
      case "AssertionErrorImpl":
        break;
      case "HandshakeException":
        if (event.throwable.toString().contains(
            "HandshakeException: Connection terminated during handshake")) {
          return null;
        }
        break;
      case "TimeoutException":
        if (event.throwable
            .toString()
            .contains("Timeout occurred trying to load from NetworkImage")) {
          return null;
        }
        break;
      case "GetHttpException":
        if (event.throwable.toString().contains('HandshakeException')) {
          return null;
        }

        if (event.throwable.toString().contains('Read failed')) {
          return null;
        }

        if (event.throwable
            .toString()
            .contains('Connection closed before full header was received')) {
          return null;
        }

        if (event.throwable.toString().contains('Connection reset by peer')) {
          return null;
        }

        if (event.throwable.toString().contains('Unauthorised')) {
          return null;
        }

        if (event.throwable.toString().contains('errno = 7')) {
          return null;
        }

        if (event.throwable.toString().contains('errno = 101')) {
          return null;
        }

        if (event.throwable.toString().contains('errno = 104')) {
          return null;
        }

        if (event.throwable.toString().contains('StatusCode : 503')) {
          return null;
        }

        if (event.throwable
            .toString()
            .contains('Software caused connection abort')) {
          return null;
        }

        if (event.throwable.toString().contains('TimeoutException')) {
          return null;
        }

        if (event.throwable.toString().contains('timed out')) {
          return null;
        }
        break;
      case "FlutterError":
        if (event.throwable.toString().contains('Zone mismatch')) {
          return null;
        }
        break;
      case "FetchDataException":
        if (event.throwable
            .toString()
            .contains('Error During Communication Error')) {
          return null;
        }
        break;
      case "ClientException":
        if (event.throwable
            .toString()
            .contains('Connection closed before full header was received')) {
          return null;
        }
        break;
      case "PlatformException":
        if (event.throwable
            .toString()
            .contains('PermissionHandler.PermissionManager')) {
          return null;
        }
        break;
      default:
        return event;
    }
    return event;
  }
}
