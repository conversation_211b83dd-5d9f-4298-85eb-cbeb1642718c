import 'package:mobile_crm/core/extensions/extensions.dart';

import '../../data/models/address_model.dart';
import '../../data/models/province_model.dart';

class ProvinceDistrictHelper {
  /// Filter List Province and matching each province with province by geocoding
  static ProvinceModel? isGeocodingAndProvinceMatch(
      {required AddressModel address, required List<ProvinceModel> listProv}) {
    var splitProvince = address.province?.split(" ");
    List<ProvinceModel> newList = [];
    for (var element in listProv) {
      int matchCount = 0;
      splitProvince?.forEach((el) {
        bool result =
            element.name?.toLowerCase().contains(el.toLowerCase()) ?? false;
        if (result) {
          matchCount += 1;
        }
      });

      if (matchCount > 0) {
        ProvinceModel newProv = ProvinceModel(
            id: element.id, match: matchCount, name: element.name);
        newList.add(newProv);
      }
    }
    newList.sort(
      (a, b) => (a.match ?? 0).compareTo(b.match ?? 0),
    );
    return newList.firstOrNull;
  }
}
