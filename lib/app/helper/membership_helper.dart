import 'package:drift/drift.dart' as drift;
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/data/models/user_model.dart';

import '../../data/providers/db/database.dart';
import '../utils/logger.dart';

class MembershipHelper {
  /// Filter Membership
  /// 1 pass
  /// 2 current
  /// 3 next level
  /// 4 new level
  static List<MembershipData> filterMembership(
      {required UserModel user, required List<MembershipData> listMembership}) {
    List<MembershipData> newList = [];
    List<MembershipData> unlocked = [];
    List<MembershipData> locked = [];
    List<MembershipData> active = [];
    int current = user.getCurrentTotalPoint() - (user.totalPointLost ?? 0);

    if(current.isNegative){
      current = 0;
    }

    listMembership.sort(
      (a, b) => (a.point_target ?? 0).compareTo(b.point_target ?? 0),
    );
    for (var element in listMembership) {
      int index = listMembership.indexOf(element);
      int nextIndex = index + 1;
      int prevIndex = index - 1;

      if ((nextIndex > (listMembership.length - 1))) {
        nextIndex = index;
      }

      if ((prevIndex < 0)) {
        prevIndex = index;
      }

      int target = element.point_target ?? 0;
      int nextTarget = listMembership[nextIndex].point_target ?? 0;
      int prevTarget = listMembership[prevIndex].point_target ?? 0;

      infoLogger("pos 0",
          "${current.toCurrency} | ${prevTarget.toCurrency} | ${target.toCurrency} | ${nextTarget.toCurrency} | ${element.name} ${element.point_target.toCurrency} | ${user.getCurrentTotalSpent().toCurrency} ${user.totalSpendLost.toCurrency}");
      if (index == 0 && current >= (target) && current < nextTarget) {
        infoLogger("pos 1",
            "${current.toCurrency} | ${prevTarget.toCurrency} | ${target.toCurrency} | ${nextTarget.toCurrency} | ${element.name}");
        // return element;
        active.add(element.copyWith(current: const drift.Value(2)));
      } else if (index == listMembership.length - 1 && current >= prevTarget) {
        infoLogger("pos 2",
            "${current.toCurrency} | ${prevTarget.toCurrency} | ${target.toCurrency} | ${nextTarget.toCurrency} | ${element.name}");
        // return element;
        locked.add(element.copyWith(current: const drift.Value(3)));
      } else if (current >= prevTarget &&
          current >= target &&
          current < nextTarget) {
        infoLogger("pos 3",
            "${current.toCurrency} | ${prevTarget.toCurrency} | ${target.toCurrency} | ${nextTarget.toCurrency} | ${element.name}");
        active.add(element.copyWith(current: const drift.Value(2)));
        // return element;
      } else if (current > prevTarget &&
          current > target &&
          current < nextTarget){
        infoLogger("pos 4",
            "${current.toCurrency} | ${prevTarget.toCurrency} | ${target.toCurrency} | ${nextTarget.toCurrency} | ${element.name}");
        active.add(element.copyWith(current: const drift.Value(2)));
      } else if (current > prevTarget && current > target){
        infoLogger("pos 5",
            "${current.toCurrency} | ${prevTarget.toCurrency} | ${target.toCurrency} | ${nextTarget.toCurrency} | ${element.name}");
        unlocked.add(element.copyWith(current: const drift.Value(1)));
      } else if (current < nextTarget){
        infoLogger("pos 6",
            "${current.toCurrency} | ${prevTarget.toCurrency} | ${target.toCurrency} | ${nextTarget.toCurrency} | ${element.name}");
        locked.add(element.copyWith(current: const drift.Value(3)));
      }
    }
    newList.addAll(active);
    newList.addAll(locked);
    newList.addAll(unlocked);
    return newList;
  }
}
