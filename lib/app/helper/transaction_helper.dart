import 'package:get/get.dart';
import 'package:mobile_crm/app/enum/order_type_enum.dart';
import 'package:mobile_crm/core/extensions/extensions.dart';
import 'package:mobile_crm/data/models/order_model.dart';
import 'package:mobile_crm/domain/repository/deal_repository.dart';

import '../../data/providers/db/database.dart';
import '../../domain/repository/transaction_repository.dart';

class TransactionHelper {
  /// Used by [getAllTransaction] on transaction repository
  static Future<List<TransactionDataData>> filterTransaction(
      {required List<TransactionDataData> listTrans,
      DealRepository? dealRepository,
      required TransactionRepository transactionRepo}) async {
    var myTransaction =
        listTrans.where((element) => element.dealPayment != null).toList();
    if (dealRepository != null) {
      var myVoucher = await dealRepository.getMyVoucher();
      for (var tr in myTransaction) {
        var isSuccess = _isTransactionSuccess(
            myVoucher, tr.dealPayment?.promotionBuyId ?? 0);
        if (isSuccess) {
          transactionRepo.deleteDealRecord(tr.promoId ?? 0);
        }
      }
    }
    return await transactionRepo.getAllRecord();
  }

  static bool _isTransactionSuccess(
      List<DealData>? listVoucher, int promoBuyId) {
    var isFound = listVoucher?.firstWhereOrNull((element) {
      return element.promotionBuyId == promoBuyId;
    });
    return isFound != null;
  }

  static List<String> filterOrderType({OrderTypeModel? orderTypeModel}) {
    List<String> orderTypeList = [];
    bool isPickupOn = orderTypeModel?.pickup?.toLowerCase() == "on";
    bool isDeliveryOn = orderTypeModel?.delivery?.toLowerCase() == "on";
    bool isSelfOrderOn = orderTypeModel?.selfOrder?.toLowerCase() == "on";
    bool isDineInOn = orderTypeModel?.dinein?.toLowerCase() == "on";
    bool isInternalDeliveryOn =
        orderTypeModel?.internalDelivery?.toLowerCase() == "on";

    if (isSelfOrderOn) {
      orderTypeList.add(
          OrderTypeEnum.self_order.name.removeUnderscore.capitalizeFirst ?? '');
    }

    if (isDineInOn) {
      orderTypeList.add(
          OrderTypeEnum.dine_in.name.removeUnderscore.capitalizeFirst ?? '');
    }

    if (isPickupOn) {
      orderTypeList.add(OrderTypeEnum.pickup.name.capitalizeFirst ?? '');
    }

    if (isDeliveryOn) {
      orderTypeList.add(OrderTypeEnum.delivery.name.capitalizeFirst ?? '');
    }

    if (isInternalDeliveryOn) {
      orderTypeList.add(OrderTypeEnum
              .internal_delivery.name.removeUnderscore.capitalizeFirst ??
          '');
    }

    return orderTypeList;
  }
}
