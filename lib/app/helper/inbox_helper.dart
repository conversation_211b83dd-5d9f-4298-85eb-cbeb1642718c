import '../../data/providers/db/database.dart';

class InboxHelper {
  static List<InboxData> filterInbox(List<InboxData> items) {
    items.sort(
      (a, b) => (b.data_created ?? 0).compareTo(a.data_created ?? 0),
    );
    List<InboxData> newInboxList = [];
    for (int i = 0; i < items.length; i++) {
      var item = items[i];

      if (i == items.length - 1 ||
          item.notification_id != items[i + 1].notification_id) {
        newInboxList.add(item);
      }
    }
    return newInboxList;
  }
}
