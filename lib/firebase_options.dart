// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyAzFI4I3FW71XQKJNrjDZDQzaVlJIneZWY',
    appId: '1:129617012485:web:1228c79d3d768d78d7396c',
    messagingSenderId: '129617012485',
    projectId: 'uniq-core',
    authDomain: 'uniq-core.firebaseapp.com',
    databaseURL: 'https://uniq-core.firebaseio.com',
    storageBucket: 'uniq-core.appspot.com',
    measurementId: 'G-34LGE5TKNK',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyDpqkIVh-LWqzewYbzwtevE_7lIm5PkUfg',
    appId: '1:129617012485:android:952d43992388b7a0d7396c',
    messagingSenderId: '129617012485',
    projectId: 'uniq-core',
    databaseURL: 'https://uniq-core.firebaseio.com',
    storageBucket: 'uniq-core.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyDsBxJQeAjPDDug9Q_JI4-RY16m9rHxOWk',
    appId: '1:129617012485:ios:010e767fd586d54ad7396c',
    messagingSenderId: '129617012485',
    projectId: 'uniq-core',
    databaseURL: 'https://uniq-core.firebaseio.com',
    storageBucket: 'uniq-core.appspot.com',
    androidClientId: '129617012485-095fepno9a1am69cs45em6c0vu5grt13.apps.googleusercontent.com',
    iosClientId: '129617012485-k4rkblnoe41f8h1bufg1tsisfsba45ur.apps.googleusercontent.com',
    iosBundleId: 'id.uniq.crm.yamiepanda',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyDsBxJQeAjPDDug9Q_JI4-RY16m9rHxOWk',
    appId: '1:129617012485:ios:9b4c67f7ddba41d5d7396c',
    messagingSenderId: '129617012485',
    projectId: 'uniq-core',
    databaseURL: 'https://uniq-core.firebaseio.com',
    storageBucket: 'uniq-core.appspot.com',
    androidClientId: '129617012485-095fepno9a1am69cs45em6c0vu5grt13.apps.googleusercontent.com',
    iosClientId: '129617012485-lqmlvsepskbo3mdgo1ltk5k6h0avnpgk.apps.googleusercontent.com',
    iosBundleId: 'id.uniq.crm',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyAzFI4I3FW71XQKJNrjDZDQzaVlJIneZWY',
    appId: '1:129617012485:web:b8fc7098537cc5c9d7396c',
    messagingSenderId: '129617012485',
    projectId: 'uniq-core',
    authDomain: 'uniq-core.firebaseapp.com',
    databaseURL: 'https://uniq-core.firebaseio.com',
    storageBucket: 'uniq-core.appspot.com',
    measurementId: 'G-B2CX9W2VJ7',
  );

}