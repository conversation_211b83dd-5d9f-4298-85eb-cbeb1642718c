import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:mobile_crm/data/services/local_storage.dart';
import 'package:mobile_crm/routes/app_pages.dart';

class AuthMiddleware extends GetMiddleware {
  final store = LocalStorageService();
  @override
  RouteSettings? redirect(String? route) {
    return store.token == null
        ? RouteSettings(name: "${Routes.LOGIN}?redirect=${route ?? ''}")
        : null;
  }
}
