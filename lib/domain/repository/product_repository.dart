import 'package:mobile_crm/data/models/product_group_model.dart';
import 'package:mobile_crm/data/models/search_model.dart';
import 'package:mobile_crm/data/models/server_response.dart';
import 'package:mobile_crm/data/models/unit_conversion_model.dart';
import 'package:mobile_crm/data/providers/db/database.dart';

import '../../data/models/product/link_menu_model.dart';
import '../../data/models/tax_model.dart';

abstract class ProductRepository {
  /// Get All Product Remote and Local
  Future<List<Product>> getProduct();
  Future<List<TaxModel>> getAllTaxes();
  Future<List<TaxModel>> getTaxByProductDetailId(int? productDetailId);

  Future<List<Product>> getProductLocal();

  Future<List<ProductRecomendationData>> getProductRecomendation(int outletId);

  /// Return Filtered Product by category from [getProduct] method
  Future<List<ProductGroupModel>> getFilteredProduct();

  Future<Product?> getProductById(String id);
  Future<Product?> getProductByDetailId(String id);

  /// Search History Product
  Future<List<SearchModel>> getSearchHistoryProduct();

  Future<bool> saveSearchHistoryProduct({String? search});

  /// Link Menu
  Future<List<LinkMenuModel>> getAllLinkMenu();
  Future<List<LinkMenuModel>> getLinkMenuBy(
      {int? outletId, int? productId, int? productDetailId});

  /// Unit conversion
  Future<List<UnitConversionModel>> getUnitConversion();

  Future<ServerResponse> addNotifyProduct(
      {required int productDetailId, required int qty});
}
