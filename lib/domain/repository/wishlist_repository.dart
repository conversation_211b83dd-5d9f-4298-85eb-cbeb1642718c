import 'package:mobile_crm/app/enum/wishlist_enum.dart';
import 'package:mobile_crm/data/models/server_response.dart';
import 'package:mobile_crm/data/models/wishlist_model.dart';
import 'package:mobile_crm/data/providers/db/database.dart';

abstract class WishlistRepository {
  Future<ServerResponse<WishlistData>> addWishlist({required Product product});

  Future<List<WishlistData>> getAllWishlist({WishlistEnum? format});

  Future<List<WishlistData>> getAllWishlistByCategoryId(int id);

  Future<WishlistData?> getSingleWishlistByProductFkId(int productFkId);

  Future<ServerResponse> deleteWishlist(String crmProductWishlistId);

  Future<int> countWishlist();

  // Category
  Future<ServerResponse> setWishlistCategory(
      {required String categoryId, required String wishlistId});

  Future<List<WishlistCategoryData>> getWishlistCategory();

  Future<ServerResponse<WishlistCategoryData>> addWishlistCategory(
      {required WishlistCategoryModel wishlistCategoryModel,
      required String wishlistId});
}
