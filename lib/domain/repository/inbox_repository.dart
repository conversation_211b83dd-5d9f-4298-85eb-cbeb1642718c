import 'package:mobile_crm/data/models/server_response.dart';
import 'package:mobile_crm/data/providers/db/database.dart';

abstract class InboxRepository {
  // Get Inbox
  Future<List<InboxData>> getInbox();

  Future<ServerResponse<InboxData>> getDetailInbox(String id);

  Future<List<InboxData>> getInboxPagination({required int page, int limit});

  Future<void> updateInbox(InboxData value);

  Future<int> getTotalInbox();
}
