import 'package:mobile_crm/data/models/deal_payment_model.dart';
import 'package:mobile_crm/data/models/server_response.dart';

import '../../data/models/deals_payment_model.dart';
import '../../data/models/secret_id_model.dart';
import '../../data/providers/db/database.dart';

abstract class DealRepository {
  Future<List<DealData>> getAll();

  Future<List<DealData>> getMyVoucher();

  Future<List<DealData>> getOutletPromotion(
      {List<String>? type, required String outletId});

  Future<ServerResponse<DealData>> getOutletDetailPromotion(
      {required String promotionId});

  Future<DealsPaymentModel?> getDealsPayment(
      {required String promotionId,
      required String paymentType,
      String? phoneNumber});

  Future<ServerResponse<DealData>> getDealDetail({required String dealId});

  Future<DealData?> getDetailMyVoucher({required String promotionId});

  Future<bool> notifyMe({required String dealId});

  Future<ServerResponse<DealPaymentModel>> buyDeals({required String dealId});

  Future<SecretIdModel?> getMyVoucherSecretId(String promotionId);
  Future<ServerResponse> refundVoucher(String promotionBuyId);
}
