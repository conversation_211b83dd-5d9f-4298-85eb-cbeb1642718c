import 'package:mobile_crm/data/models/delivery_price_model.dart';
import 'package:mobile_crm/data/providers/db/database.dart';

abstract class OutletRepository {
  Future<List<Outlet>> getOutlet();

  Future<Outlet?> getOutletById(int id);

  Future<Outlet?> getOutletDetailById(int id);

  Future<DeliveryPrice?> getOutletDeliveryPrice(
      Map<String, dynamic> latlng, int id);

  Future<List<Product>> getOutletProductByOutletId(String id);

  void insertLastOrderType(int outletId, String orderType);

  Future<LastTypeEntityData?> getLastOrderType(int outletId);
}
