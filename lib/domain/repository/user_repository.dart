import 'package:mobile_crm/data/models/address_model.dart';
import 'package:mobile_crm/data/models/secret_id_model.dart';
import 'package:mobile_crm/data/models/server_response.dart';
import 'package:mobile_crm/data/models/user_code_model.dart';
import 'package:mobile_crm/data/models/user_model.dart';
import 'package:mobile_crm/data/providers/db/database.dart';

abstract class UserRepository {
  Future<UserModel?> getUser();

  Future<ServerResponse<SecretIdModel>> getSecretId();

  Future<List<PointData>> getPointHistory();

  Future<ServerResponse> updateUserProfile(
      {String? token, required UserModel user});

  Future<ServerResponse> requestRemoveAccount();

  Future<ServerResponse<UserCodeModel>> requestEmailVerify();

  Future<ServerResponse> resendEmailConfirmation(String email);

  Future<ServerResponse> lostPhoneNumber(String email, String phone);

  Future<ServerResponse<UserCodeModel>> verifyChangePhoneNumber(
      {required String key, required String code});

  Future<ServerResponse> removeAccount(UserCodeModel user);

  Future<ServerResponse> validateCodeVerification(UserCodeModel user);

  Future<ServerResponse> registerMember(UserModel user, String token);

  Future<ServerResponse> registerMemberV2(UserModel user);

  Future<ServerResponse> joinOtherBusiness(String token);

  Future<ServerResponse<UserModel>> checkExistingUserByNumberOrEmail(
      {String? phoneNumber, String? email, bool? showToast});

  // Address
  Future<List<AddressModel>> getAddress();

  Future<ServerResponse> addAddress(AddressModel address);

  Future<ServerResponse> deleteAddress(String id);

  Future<ServerResponse> updateAddress(AddressModel address);

  // Voucher
  Future<ServerResponse<SecretIdModel>> getVoucherSecretId(String promotionId);

  Future<ServerResponse<DealData>> getDetailVoucher(String promotionId);

  Future<List<DealData>> getUserVoucherGroup();

  Future<List<DealData>?> getUserVoucher();

  // Deal
  Future<ServerResponse> buyDeal(String dealId, transactionId);

  // Auth Token
  Future<ServerResponse> updateFCMToken();

  Future<ServerResponse> removeFCMToken();

// Future<void> logout();
}
