import 'package:mobile_crm/data/models/server_response.dart';
import 'package:mobile_crm/data/models/transaction_new_model.dart';
import 'package:mobile_crm/data/providers/db/database.dart';

abstract class CartRepository {
  Future<List<Cart>> getCart();

  Future<List<Cart>> getCartLocal();

  Future<List<Cart>> getCartByOutletId(int id);

  Future<int> getTotalCart();

  Stream<List<Cart>> watchCartLocal();

  Stream<List<Cart>> watchCartLocalByOutlet({required int outletId});

  Future<int> addProductToCartLocal({required Cart cart});

  Future<int> removeProductToCartLocal({required Cart cart});

  Future<int> removeAllCartByCartIdLocal({required Cart cart});

  Future<int> updateProductToCartLocal({required Cart cart});

  Future<ServerResponse> addCart(int productId);

  Future<ServerResponse> removeSelfOrder(List<Cart> cart);

  Future<ServerResponse> updateBatchCarts({required int outletId});

  Future<ServerResponse> updateAllCarts(List<Cart> value);

  Future updateCartNote(Cart cart);

  Future<int> removeAllCart();

  Future<int> insertTransactionOrder(TransactionNewModel transactionNewModel);
  Future<int> updateTransactionOrder(TransactionNewModel transactionNewModel,
      {bool isOnOrder = false});
  Future<int> deleteTransactionOrder(TransactionNewModel transactionNewModel,
      {bool isOnOrder = false});
  Future<List<TransactionNewModel>> getAllTransactionOrder({bool? isOnOrder});
  Future<TransactionNewModel?> getTransactionOrderByOutletId(int outletId,
      {bool isOnOrder = false, TransactionNewModel? transactionNewModel});
}
