import 'package:image_picker/image_picker.dart';
import 'package:mobile_crm/data/models/feedback_model.dart';
import 'package:mobile_crm/data/models/order_detail_model.dart';
import 'package:mobile_crm/data/models/order_model.dart';
import 'package:mobile_crm/data/models/payment_method_model.dart';
import 'package:mobile_crm/data/models/payments_model.dart';
import 'package:mobile_crm/data/models/self_order_model.dart';
import 'package:mobile_crm/data/models/server_response.dart';
import 'package:mobile_crm/data/models/transaction/feedback/rating_review_model.dart';
import 'package:mobile_crm/data/models/transaction/feedback/rating_summary_model.dart';
import 'package:mobile_crm/data/models/transaction_history_model.dart';
import 'package:mobile_crm/data/models/transaction_new_model.dart';
import 'package:mobile_crm/data/providers/db/database.dart';
import 'package:mobile_crm/domain/repository/deal_repository.dart';

import '../../data/models/transaction_data_model.dart';

abstract class TransactionRepository {
  // Get Transaction Remote
  Future<ServerResponse<OrderModel>> addTransactionSelfOrder(
      SelfOrder selfOrder);

  Future<ServerResponse<TransactionNewModel>> createTransactionSelfOrderV2(
      TransactionNewModel transactionNewModel);

  Future<ServerResponse> getStatusSelfOrder(
      {required TransactionNewModel transactionNewModel});

  Future<TransactionHistoryModel> getTransactionHistoryDetail(String id);

  Future<ServerResponse> postGiveFeedback(FeedbackModel feedback);

  Future<ServerResponse> postGiveFeedbackV2(FeedbackModel feedback);

  Stream<List<TransactionNewTableData>> streamAllNewTransaction();

  Future<ServerResponse<OrderDetailModel>> createTransaction(
      SelfOrder selfOrder);

  Future<ServerResponse<TransactionNewModel>> createTransaction2(
      TransactionNewModel transactionNewModel);

  Future<ServerResponse<PaymentMethodDetail>> createTransactionPayment(
      TransactionNewModel newModel, PaymentMethodDetail paymentMethodModel);

  Future<ServerResponse<OrderDetailModel>> getOrderDetail(String id);
  Future<ServerResponse<TransactionNewModel>> getOrderDetail2(String id);

  Future<ServerResponse<OrderDetailModel>> updateOrderDetail(
      String orderSalesId, String status, XFile? file);

  Future<ServerResponse<PaymentDataModel>> getPaymentDetailOnline(
      String invoice, PaymentDataModel paymentData);

  Future<Outlet?> getOrderTypeSetting({required String outletId});

  Future<List<PaymentMethodModel>?> getPaymentMethod(
      {String? orderType, String? outletId});

  // Transaction History Local
  Future<List<TransactionDataData>> getAllTransaction(
      {DealRepository? dealRepo});

  Future<List<TransactionNewModel>> getAllTransactionOrder({bool? isOnOrder});

  Future<List<TransactionDataData>> getAllRecord();
  Stream<List<TransactionDataData>> streamAllRecord();

  Future<TransactionDataData?> insertRecord(TransactionDataModel entity);

  Future<int> deleteTransactionRecord(int id);

  Future<bool> updateTransactionRecord(TransactionDataData entity);

  Future<int> deleteDealRecord(int id);

  Future<ServerResponse<RatingSummaryModel>> getRatingSummary(
      {required int outletId});
  Future<ServerResponseList<RatingReviewModel>> getReview(
      {required int outletId});
}
