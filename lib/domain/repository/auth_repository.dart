import 'package:mobile_crm/data/models/auth_otp_model.dart';
import 'package:mobile_crm/data/models/auth_token_model.dart';
import 'package:mobile_crm/data/models/otp_model.dart';
import 'package:mobile_crm/data/models/server_response.dart';

abstract class AuthRepository {
  // Login
  Future<ServerResponse> login({required String token});

  Future<ServerResponse<AuthTokenModel>> loginWithLink({required String phone});

  Future<ServerResponse<AuthTokenModel>> getTokenPhoneAuth(String token);

  Future<void> logout();

  Future<ServerResponse<OtpModel>> requestOtp(String phone);
  Future<ServerResponse<AuthOtpModel>> validateOtp(String token, String code);

  Future<ServerResponse> getOobWhatsApp(String phone);
  Future<ServerResponse> checkOobWhatsApp(String token);
}
