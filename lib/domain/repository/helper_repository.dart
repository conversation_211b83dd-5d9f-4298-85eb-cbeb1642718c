import 'package:mobile_crm/data/models/address_model.dart';
import 'package:mobile_crm/data/models/province_model.dart';

abstract class HelperRepository {
  /// Get Province / Provinsi
  Future<List<ProvinceModel>> getProvince();

  /// Get Regency / Kabupaten with parameter [provinceId]
  Future<List<ProvinceModel>> getRegency({required String provinceId});

  /// Get Address by geocoding. Required lating
  Future<AddressModel> getAddress();
}
