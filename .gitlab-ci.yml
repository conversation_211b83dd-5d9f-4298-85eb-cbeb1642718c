image: uniqdev/android-fastlane:flutter-jdk17-3.27.1

variables:
  ICON_URL: "https://storage.googleapis.com/uniq-187911.appspot.com/crm/app-assets/asset-crm-yamie.zip"

# You can override the included template(s) by including variable overrides
# SAST customization: https://docs.gitlab.com/ee/user/application_security/sast/#customizing-the-sast-settings
# Secret Detection customization: https://docs.gitlab.com/ee/user/application_security/secret_detection/#customizing-settings
# Dependency Scanning customization: https://docs.gitlab.com/ee/user/application_security/dependency_scanning/#customizing-the-dependency-scanning-settings
# Note that environment variables can be set in several places
# See https://docs.gitlab.com/ee/ci/variables/#cicd-variable-precedence
stages:
  - test
  - build
  - deploy

before_script:
  - chmod +x versioning.sh
  - chmod +x version.sh
  - source ./version.sh
  - ./versioning.sh && VERSION=`cut -d ',' -f2 version.conf`
  - cat $ENV_FASTLANE > android/fastlane/.env
  - cat $KEYSTORE > android/key.properties
  - cat $KEYSTORE_FILE | base64 -d > android/app/debug.keystore
  - cat $GOOGLE_PLAY_SERVICE > android/app/google-services.json
  - cat $AVAILABLE_MODEL > lib/data/models/available_model.g.dart
  - cat android/fastlane/.env
  - cd android && bundle install && cd ..

cache:
  key: ${CI_PROJECT_ID}
  paths:
    - build
    - /sdk
    - /var/lib/gems
    - /root/.pub-cache
    - /home/<USER>/.pub-cache
    - /home/<USER>/flutter/bin/cache
    # - /var/lib/gems/

# sast:
#   stage: test
# include:
# - template: Security/SAST.gitlab-ci.yml

signing_report:
  when: manual
  script:
    - cd android && ./gradlew signingReport

.distributeApk:
  stage: build
  only:
    - testing
  when: manual
  environment:
    name: development
  script:
    - echo $VERSION
    - cat $ENV > .env
    - flutter pub get
    - cat $AVAILABLE_MODEL > lib/data/models/available_model.g.dart
    - dart run build_runner build --delete-conflicting-outputs || true    
    - ls -al
    - rm .env
    - flutter build apk --release --flavor $FLAVOR --build-number $VERSION --build-name=$VERSION
    - cd android && bundle exec fastlane distribute_app variant:$FLAVOR
  cache:
    key: build-app
    paths:
      - build/app/
  artifacts:
    when: always
    paths:
      - version.conf
      - build/app/outputs/bundle/release
      - build/app/outputs/flutter-apk
    expire_in: never

distributeAppDev:
  extends:
    - .distributeApk
  variables:
    FLAVOR: dev

.buildWeb:
  stage: build
  before_script:
    - cat $ENV > .env
  when: manual
  script:
    - wget -O icons.zip $ICON_URL
    - unzip -d web -o icons.zip 
    - flutter pub get
    - flutter pub run build_runner build --delete-conflicting-outputs || true
    - ls -al
    - rm .env
    # - cat $FIREBASE_CONFIG > web/firebase_config.jsda
    - flutter build web --release --web-renderer canvaskit 
  cache:
    key: build-web
    paths:
      - build/web/

buildWebDevelopment:
  extends: .buildWeb
  only:
    - testing
  environment:
    name: development
  variables:
    ICON_URL: "https://storage.googleapis.com/uniq-187911.appspot.com/crm/app-assets/crm-asset-dev.zip"

buildWebDev:
  extends: .buildWeb
  only:
    - testing
  environment:
    name: development
  before_script:
    - cat $ENV_DEV > .env
  variables:
    ICON_URL: "https://storage.googleapis.com/uniq-187911.appspot.com/crm/app-assets/crm-asset-dev.zip"


buildWebAlive:
  extends: .buildWeb
  only:
    - master
  environment:
    name: production
  variables:
    ICON_URL: "https://storage.googleapis.com/uniq-187911.appspot.com/crm/app-assets/asset-crm-alive.zip"


.deployWebApp:
  image: node:lts-alpine3.14
  stage: deploy
  when: manual
  cache:
    key: build-web
    paths:
      - build/web/
  before_script:
    - npm install -g firebase-tools
  script:
    # - firebase use $ALIAS
    # - firebase deploy --token $FIREBASE_TOKEN
    - firebase deploy --only hosting:$SITE_NAME --token $FIREBASE_TOKEN


deployWebAppDevelopment:
  extends: .deployWebApp
  only:
    - testing
  needs: [ "buildWebDev" ]
  environment:
    name: development
  variables:
    ALIAS: default
    SITE_NAME: qa

deployWebAppDev:
  extends: .deployWebApp
  only:
    - testing
  needs: [ "buildWebDevelopment" ]
  environment:
    name: development
  variables:
    ALIAS: default
    SITE_NAME: crm    

deployWebAppAlive:
  extends: .deployWebApp
  only:
    - master
  needs: [ "buildWebDevelopment" ]
  environment:
    name: staging
  variables:
    ALIAS: default
    SITE_NAME: alive

.buildAppRelease:
  stage: build
  when: manual
  script:
    - echo $VERSION
    - cat $KEYSTORE_PROPERTIES_YAMIE > android/key.properties
    - cat $KEYSTORE_YAMIE | base64 -d > android/app/release.key
    - cat $FIREBASE_OPTIONS > lib/firebase_options.dart
    - cat $ENV > .env
    - flutter pub get
    - cat $AVAILABLE_MODEL > lib/data/models/available_model.g.dart
    - dart run build_runner build --delete-conflicting-outputs || true
    - cat $AVAILABLE_MODEL > lib/data/models/available_model.g.dart
    - cat lib/data/models/available_model.g.dart
    - rm .env
    - flutter build appbundle --release --flavor $FLAVOR --build-number $VERSION_CODE --build-name=$VERSION_NAME
    - cd android && bundle exec fastlane publishGooglePlay variant:$FLAVOR
  cache:
    key: build-app-bundle
    paths:
      - build/app/outputs/bundle
  artifacts:
    when: always
    paths:
      - version.conf
      - build/app/outputs/bundle/release
    expire_in: never

buildReleaseYamie:
  extends:
    - .buildAppRelease
  variables:
    FLAVOR: yamiepanda 
  environment:
    name: production

buildReleaseAlive:
  extends:
    - .buildAppRelease
  variables:
    FLAVOR: alive 
  environment:
    name: production    