#!/bin/bash

# Get current timestamp and take first 10 digits
export VERSION_CODE=$(date +%s%N | cut -c1-10)

# Get the latest git tag (fallback to 0.0.0)
LATEST_TAG=$(git describe --tags --abbrev=0 2>/dev/null || echo "0.0.0")

# Remove any leading 'v' characters (handles v, vv, vvv, etc.)
CLEAN_TAG=$(printf '%s' "$LATEST_TAG" | sed 's/^v\+//')

# Get total number of commits
TOTAL_COMMITS=$(git rev-list --count HEAD 2>/dev/null || echo "0")

# Split the cleaned tag into components
IFS='.' read -r MAJOR MINOR PATCH <<< "$CLEAN_TAG"

# Create VERSION_NAME by combining tag with commit count
export VERSION_NAME="${MAJOR:-0}.${MINOR:-0}.${TOTAL_COMMITS}"
