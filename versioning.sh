#!/bin/bash

FILE=version.conf
DEFAULT_VERSION=10
if [ ! -f "$FILE" ]; then
    echo "generate default version"
    echo "$DEFAULT_VERSION" > $FILE
fi

if [ "$VERSION" != "" ]
then
    echo "$VERSION" > $FILE
fi

oldVersion=`cut -d ',' -f2 $FILE`  
VERSION=`expr $oldVersion + 1`
sed -i "s/$oldVersion\$/$VERSION/g" $FILE

# echo "$CI_JOB_ID"

last_three_digits=$(echo "$VERSION" | rev | cut -c 1-3 | rev)
VERSION_NAME=$((10#$last_three_digits))
export VERSION_NAME
export VERSION