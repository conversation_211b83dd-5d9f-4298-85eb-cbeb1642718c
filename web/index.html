<!DOCTYPE html>
<html>
<head>
    <base href="$FLUTTER_BASE_HREF">
    
    <meta charset="UTF-8">
    <meta content="IE=Edge" http-equiv="X-UA-Compatible">
    <meta name="description" content="Gym">
    
    <script defer src="sql-wasm.js"></script>
    <script defer src="main.dart.js" type="application/javascript"></script>
    <script src="https://www.gstatic.com/firebasejs/9.22.2/firebase-analytics.js"></script>
    <!-- iOS meta tags & icons -->
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-title" content="UNIQ CRM App">

<!--    <meta name="google-signin-client_id" content="129617012485-095fepno9a1am69cs45em6c0vu5grt13.apps.googleusercontent.com">-->

    <link rel="apple-touch-icon" href="icons/icon-192x192.png">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="favicon-yamie.ico"/>
    
    <title>UNIQ CRM App</title>
    <link rel="manifest" href="manifest.json">
    <!-- Renderer specifier -->
    <script>
    // The value below is injected by flutter build, do not touch.
    var serviceWorkerVersion = '{{flutter_service_worker_version}}';
  </script>

    <script src="https://apis.google.com/js/platform.js" async defer></script>
    <script type="text/javascript" src="https://appleid.cdn-apple.com/appleauth/static/jsapi/appleid/1/en_US/appleid.auth.js"></script>
    <script src="flutter.js" defer></script>

</head>
<body>
<style>
    body {
      background-color: #ffffff;
    }
  </style>

 <!-- Loading indicator -->
 <div id="loading">
  <style>
    body {
      inset: 0;
      overflow: hidden;
      margin: 0;
      padding: 0;
      position: fixed;
    }

    #loading {
      align-items: center;
      display: flex;
      height: 100%;
      justify-content: center;
      width: 100%;
    }

    #loading img {
      animation: 1s ease-in-out 0s infinite alternate breathe;
      opacity: .66;
      transition: opacity .4s;
    }

    #loading.main_done img {
      opacity: 1;
    }

    #loading.init_done img {
      animation: .33s ease-in-out 0s 1 forwards zooooom;
      opacity: .05;
    }

    @keyframes breathe {
      from {
        transform: scale(1)
      }

      to {
        transform: scale(0.95)
      }
    }

    @keyframes zooooom {
      from {
        transform: scale(1)
      }

      to {
        transform: scale(10)
      }
    }
  </style>
  <img src="icons/icon-192x192.png" alt="Loading..." />
</div>


<script>
  {{flutter_bootstrap_js}}
  window.addEventListener('load', function () {
    {{flutter_js}}
    {{flutter_build_config}}
    
    var loading = document.querySelector('#loading');
    _flutter.loader.load({
      serviceWorker: {
        serviceWorkerVersion: serviceWorkerVersion,
      }
    }).then(function (engineInitializer) {
      loading.classList.add('main_done');
      return engineInitializer.initializeEngine();
    }).then(function (appRunner) {
      loading.classList.add('init_done');
      return appRunner.runApp();
    }).then(function (app) {
      // Wait a few milliseconds so users can see the "zoooom" animation
      // before getting rid of the "loading" div.
      window.setTimeout(function () {
        loading.remove();
      }, 200);
    });
  });
</script>
</body>
</html>
