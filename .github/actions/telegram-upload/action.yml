name: 'Upload to Telegram'
description: 'Upload a file to Telegram channel/group'

inputs:
  file_path:
    description: 'Path to the file to upload'
    required: true
  chat_id:
    description: 'Telegram chat ID to send the file to'
    required: true
  token:
    description: 'Telegram bot token'
    required: true
  message:
    description: 'Message to send with the file'
    required: true

runs:
  using: "composite"
  steps:
    - name: Send File to Telegram
      shell: bash
      run: |
        curl -F document=@"${{ inputs.file_path }}" \
             -F caption="${{ inputs.message }}" \
             "https://api.telegram.org/bot${{ inputs.token }}/sendDocument?chat_id=${{ inputs.chat_id }}" 