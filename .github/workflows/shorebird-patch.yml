name: Shorebird Patch

on:
  push:
    branches:
      - 'patch/**'
  pull_request:
    branches:
      - 'patch/**'
  workflow_dispatch:  # Allows manual trigger

jobs:
  shorebird-patch:
    name: Create Shorebird Patch
    runs-on: ubuntu-latest
    environment: development

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.29.0'
          channel: 'stable'
          cache: true

      - name: Extract version from branch name
        run: |
          BRANCH_NAME=${GITHUB_HEAD_REF:-${GITHUB_REF#refs/heads/}}
          VERSION=${BRANCH_NAME#patch/}
          echo "RELEASE_VERSION=$VERSION" >> $GITHUB_ENV

      - name: 🐦 Setup Shorebird
        uses: shorebirdtech/setup-shorebird@v1
        with:
          cache: true

      - name: Setup environment files
        env:
          ENV: ${{ secrets.ENV_YAMIE }}
          SHOREBIRD_TOKEN: ${{ secrets.SHOREBIRD_TOKEN }}
          KEYSTORE_PROPERTIES: ${{ secrets.KEYSTORE_PROPERTIES_YAMIE }}
          KEYSTORE_FILE: ${{ secrets.KEYSTORE_YAMIE }}
          FIREBASE_OPTIONS: ${{ secrets.FIREBASE_OPTIONS }}
        run: |
          echo "$ENV" > .env
          export SHOREBIRD_TOKEN="${{ secrets.SHOREBIRD_TOKEN }}"
          echo "$KEYSTORE_PROPERTIES" > android/key.properties
          echo "$KEYSTORE_FILE" | base64 -d > android/app/release.key
          echo "$FIREBASE_OPTIONS" > lib/firebase_options.dart

      - name: Set up Java
        uses: actions/setup-java@v4
        with:
          distribution: 'temurin'
          java-version: '17'

      - name: Generate Files
        run: |
          flutter pub get
          dart run build_runner build --delete-conflicting-outputs 

      - name: 🚀 Create Patch
        id: shorebird-patch
        uses: shorebirdtech/shorebird-patch@v0
        env:
          SHOREBIRD_TOKEN: ${{ secrets.SHOREBIRD_TOKEN }}
        with:
          platform: android
          release-version: ${{ env.RELEASE_VERSION }}
          flavor: yamiepanda
          args: --flavor yamiepanda --no-confirm --allow-asset-diffs --release-version=${{ env.RELEASE_VERSION }}

      - name: Send Notification to Telegram
        uses: appleboy/telegram-action@master
        with:
          to: ${{ secrets.TELEGRAM_CHAT_ID }}
          token: ${{ secrets.TELEGRAM_BOT_TOKEN }}
          message: |
            🚀 Shorebird Patch Created
            📱 Version: ${{ env.RELEASE_VERSION }}
            🔧 Branch: ${{ github.ref_name }}
            
            Patch has been created successfully!
