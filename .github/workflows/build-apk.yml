name: Build APK

on:
  pull_request:
    branches: [ dev ]
    types: [ opened, synchronize, reopened ]
  push:
    branches: [ dev, testingx ]
  workflow_dispatch:  # Allows manual trigger

env:
  FLUTTER_VERSION: '3.32.0'
  ICON_URL: 'https://storage.googleapis.com/uniq-187911.appspot.com/crm/app-assets/crm-asset-dev.zip'

jobs:
  build-android:
    name: Build Android APK
    runs-on: ubuntu-latest
    environment: development  # Matching your GitLab environment

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Set up JDK
        uses: actions/setup-java@v4
        with:
          distribution: 'temurin'
          java-version: '17'

      # Cache Android SDK
      - name: Cache Android SDK
        uses: actions/cache@v4
        with:
          path: |
            /usr/local/lib/android/sdk
            ~/.android/cache
          key: ${{ runner.os }}-android-sdk-${{ hashFiles('**/*.gradle*') }}
          restore-keys: |
            ${{ runner.os }}-android-sdk-

      # Cache Gradle dependencies
      - name: Cache Gradle Dependencies
        uses: actions/cache@v4
        with:
          path: |
            ~/.gradle/caches
            ~/.gradle/wrapper
          key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
          restore-keys: |
            ${{ runner.os }}-gradle-

      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: ${{ env.FLUTTER_VERSION }}
          channel: 'stable'
          cache: true

      - name: Get version
        run: |
          source ./version.sh          
          echo "VERSION_NAME=$VERSION_NAME" >> $GITHUB_ENV
          echo "VERSION_CODE=$VERSION_CODE" >> $GITHUB_ENV

      - name: Setup environment files
        env:
          ENV_FASTLANE: ${{ secrets.ENV_FASTLANE }}
          KEYSTORE: ${{ secrets.KEYSTORE }}
          KEYSTORE_FILE: ${{ secrets.KEYSTORE_FILE }}
          GOOGLE_PLAY_SERVICE: ${{ secrets.GOOGLE_PLAY_SERVICE }}
          ENV: ${{ secrets.ENV }}
        run: |
          echo "$ENV_FASTLANE" > android/fastlane/.env
          echo "$KEYSTORE" > android/key.properties
          echo "$KEYSTORE_FILE" | base64 -d > android/app/debug.keystore
          echo "$GOOGLE_PLAY_SERVICE" > android/app/google-services.json          
          echo "$ENV" | base64 -d > .env
          echo "$ENV" | base64 -d > web/env.txt
          cat .env

      - name: Install dependencies
        run: |
          flutter pub get
          dart run build_runner build --delete-conflicting-outputs
          cat .env

      - name: Build APK
        run: |
          flutter build apk --release --flavor dev --build-number ${{ env.VERSION_CODE }} --build-name=${{ env.VERSION_NAME }}

      - name: Rename and prepare APK
        run: |
          CURRENT_DATE=$(date +'%Y%m%d')
          mkdir -p artifacts
          cp build/app/outputs/flutter-apk/app-dev-release.apk artifacts/mobile-crm-app-dev-${CURRENT_DATE}.apk
          echo "APK_PATH=artifacts/mobile-crm-app-dev-${CURRENT_DATE}.apk" >> $GITHUB_ENV
          echo "ZIP_PATH=artifacts/mobile-crm-app-dev-${CURRENT_DATE}.zip" >> $GITHUB_ENV
          echo "BUILD_DATE=${CURRENT_DATE}" >> $GITHUB_ENV
          cd artifacts && zip mobile-crm-app-dev-${CURRENT_DATE}.zip mobile-crm-app-dev-${CURRENT_DATE}.apk      

      - name: Upload to Telegram
        uses: ./.github/actions/telegram-upload
        with:
          file_path: ${{ env.ZIP_PATH }}
          chat_id: ${{ secrets.TELEGRAM_CHAT_ID }}
          token: ${{ secrets.TELEGRAM_BOT_TOKEN }}
          message: |
            🚀 New Mobile CRM Dev Build
            📱 Version: ${{ env.VERSION_NAME }} (${{ env.VERSION_CODE }})
            📅 Build Date: ${{ env.BUILD_DATE }}
            🔧 Branch: ${{ github.ref_name }}
            🌐 Web : https://uniq-crm-qa.web.app
            
            Please test the latest build and report any issues.
            Note: The APK is compressed in a ZIP file.

      - name: Upload APK to Telegram
        if: false
        uses: appleboy/telegram-action@master
        with:
          to: ${{ secrets.TELEGRAM_CHAT_ID }}
          token: ${{ secrets.TELEGRAM_BOT_TOKEN }}
          message: |
            🚀 New Mobile CRM Dev Build
            📱 Version: ${{ env.VERSION_NAME }} (${{ env.VERSION_CODE }})
            📅 Build Date: ${{ env.BUILD_DATE }}
            🔧 Branch: ${{ github.ref_name }}
            
            Please test the latest build and report any issues.
            Note: The APK is compressed in a ZIP file.
          document: ${{ env.ZIP_PATH }}

      - name: Upload APK as artifact
        if: false
        uses: actions/upload-artifact@v4
        with:
          name: app-release
          path: |
            ${{ env.APK_PATH }}
          retention-days: 3

  build-and-deploy-web:
    name: Build and Deploy Web
    runs-on: ubuntu-latest
    environment: development
    # needs: build-android  # Run after Android build completes

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: ${{ env.FLUTTER_VERSION }}
          channel: 'stable'
          cache: true

      - name: Download and extract icons
        run: |
          wget -O icons.zip ${{ env.ICON_URL }}
          unzip -d web -o icons.zip

      - name: Setup environment files
        env:
          ENV: ${{ secrets.ENV }}
        run: |
          echo "$ENV" | base64 -d > .env

      - name: Install dependencies
        run: |
          flutter pub get
          dart run build_runner build --delete-conflicting-outputs

      - name: Build Web
        run: |
          flutter build web --release

      - name: Setup Firebase Tools
        run: npm install -g firebase-tools

      - name: Deploy to Firebase
        env:
          FIREBASE_TOKEN: ${{ secrets.FIREBASE_TOKEN }}
        run: |
          firebase deploy --only hosting:qa --token ${{ env.FIREBASE_TOKEN }}

      - name: Upload web build artifact
        uses: actions/upload-artifact@v4
        with:
          name: web-build
          path: build/web
          retention-days: 3