name: Deploy to Google Play

on:
  push:
    branches: [ main, testing ]  # Only run on main branch
  workflow_dispatch:    # Allow manual triggers

env:
  FLUTTER_VERSION: '3.32.8'

jobs:
  deploy-android:
    name: Deploy to Google Play
    runs-on: ubuntu-latest
    environment: production
    strategy:
      matrix:
        flavor: [yamiepanda] #, alive

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Set up JDK
        uses: actions/setup-java@v4
        with:
          distribution: 'temurin'
          java-version: '17'

      - name: Setup Ruby
        uses: ruby/setup-ruby@v1
        with:
          ruby-version: '3.0'
          bundler-cache: true
          working-directory: 'android'

      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: ${{ env.FLUTTER_VERSION }}
          channel: 'stable'
          cache: true

      - name: Get version
        run: |
          source ./version.sh
          echo "VERSION_NAME=$VERSION_NAME" >> $GITHUB_ENV
          echo "VERSION_CODE=$VERSION_CODE" >> $GITHUB_ENV

      - name: Setup environment files
        env:
          ENV_FASTLANE: ${{ secrets.ENV_FASTLANE }}
          KEYSTORE_PROPERTIES_YAMIE: ${{ secrets.KEYSTORE_PROPERTIES_YAMIE }}
          KEYSTORE_YAMIE: ${{ secrets.KEYSTORE_YAMIE }}
          FIREBASE_OPTIONS: ${{ secrets.FIREBASE_OPTIONS }}
          ENV: ${{ secrets.ENV_YAMIE }}
          GOOGLE_PLAY_SERVICE: ${{ secrets.GOOGLE_PLAY_SERVICE }}
          GOOGLE_PLAY_KEY: ${{ secrets.GOOGLE_PLAY_KEY }}
        run: |
          echo "$ENV_FASTLANE" > android/fastlane/.env
          echo "$KEYSTORE_PROPERTIES_YAMIE" > android/key.properties
          echo "$KEYSTORE_YAMIE" | base64 -d > android/app/release.key
          echo "$FIREBASE_OPTIONS" > lib/firebase_options.dart
          echo "$ENV" > .env
          echo "$GOOGLE_PLAY_SERVICE" > android/app/google-services.json
          echo "$GOOGLE_PLAY_KEY" > android/app/keys.json

      - name: Install dependencies
        run: |
          flutter pub get
          dart run build_runner build --delete-conflicting-outputs 

      - name: Build App Bundle
        run: |
          flutter build appbundle --release --flavor ${{ matrix.flavor }} --build-number ${{ env.VERSION_CODE }} --build-name=${{ env.VERSION_NAME }}

      - name: Upload to Telegram
        uses: ./.github/actions/telegram-upload
        with:
          file_path: 'build/app/outputs/bundle/yamiepandaRelease/app-yamiepanda-release.aab'
          chat_id: ${{ secrets.TELEGRAM_CHAT_ID }}
          token: ${{ secrets.TELEGRAM_BOT_TOKEN }}
          message: |
              🚀 New Mobile CRM Yamie (App Bundle)
              📱 Version: ${{ env.VERSION_NAME }} (${{ env.VERSION_CODE }})
              🔧 Branch: ${{ github.ref_name }}

      - name: Deploy to Google Play
        working-directory: android
        run: |
          bundle install
          bundle exec fastlane publishGooglePlay variant:${{ matrix.flavor }}
